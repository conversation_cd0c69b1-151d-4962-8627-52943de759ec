
// 确保在测试环境中正确mock VSCode API
global.vscode = {
  window: {
    createOutputChannel: jest.fn().mockReturnValue({
      appendLine: jest.fn(),
      append: jest.fn(),
      clear: jest.fn(),
      show: jest.fn(),
      hide: jest.fn(),
      dispose: jest.fn()
    }),
    createTextEditorDecorationType: jest.fn().mockReturnValue({
      dispose: jest.fn()
    }),
    createStatusBarItem: jest.fn().mockReturnValue({
      text: '',
      tooltip: '',
      command: '',
      show: jest.fn(),
      hide: jest.fn(),
      dispose: jest.fn()
    }),
    showInformationMessage: jest.fn(),
    showErrorMessage: jest.fn(),
    showWarningMessage: jest.fn(),
    visibleTextEditors: []
  },
  workspace: {
    workspaceFolders: [
      {
        uri: {
          fsPath: '/test/workspace'
        },
        name: 'test-workspace'
      }
    ],
    createFileSystemWatcher: jest.fn().mockReturnValue({
      onDidCreate: jest.fn().mockReturnValue({
        dispose: jest.fn()
      }),
      onDidChange: jest.fn().mockReturnValue({
        dispose: jest.fn()
      }),
      onDidDelete: jest.fn().mockReturnValue({
        dispose: jest.fn()
      }),
      dispose: jest.fn()
    }),
    onDidChangeTextDocument: jest.fn().mockReturnValue({ dispose: jest.fn() }),
    onDidChangeWorkspaceFolders: jest.fn().mockReturnValue({ dispose: jest.fn() }),
    onDidOpenTextDocument: jest.fn().mockReturnValue({ dispose: jest.fn() }),
    onDidCloseTextDocument: jest.fn().mockReturnValue({ dispose: jest.fn() }),
    onDidSaveTextDocument: jest.fn().mockReturnValue({ dispose: jest.fn() }),
    fs: {
      readFile: jest.fn(),
      writeFile: jest.fn(),
      stat: jest.fn()
    },
    getConfiguration: jest.fn((section) => ({
      get: jest.fn((key) => {
        if (section === 'code-partner') {
          const defaults = {
            usePythonService: true,
            pythonEndpoint: 'http://127.0.0.1:5555/api/v1/completion',
            openAIApiKey: '',
            model: 'qwen2.5-coder:3b',
            statisticsEndpoint: 'http://127.0.0.1:5555/api/v1/statistics/events',
            enableStatistics: true,
            maxTokens: 256,
            temperature: 0.2,
            enableInlineCompletion: true,
            enableDiffHighlightCompletion: true,
            enableIndexing: true,
            autoIndexing: true,
            languageExtensions: ['*'],
            graphWeight: 0.4,
            bm25Weight: 0.3,
            embeddingWeight: 0.3,
            logLevel: 'INFO',
            enableDebugLog: false
          };
          return defaults[key];
        }
        return undefined;
      }),
      update: jest.fn()
    }))
  },
  Uri: {
    file: jest.fn((path) => ({ fsPath: path, toString: () => `file://${path}` }))
  },
  Position: jest.fn().mockImplementation(function(line, character) {
    return {
      line,
      character,
      translate: jest.fn(),
      with: jest.fn(),
      compareTo: jest.fn()
    };
  }),
  Range: jest.fn().mockImplementation((start, end) => ({
    start,
    end,
    isEmpty: jest.fn(),
    isSingleLine: jest.fn(),
    contains: jest.fn(),
    isEqual: jest.fn(),
    union: jest.fn(),
    intersection: jest.fn()
  })),
  EventEmitter: jest.fn().mockImplementation(() => ({
    event: jest.fn(),
    fire: jest.fn(),
    dispose: jest.fn()
  })),
  ExtensionContext: jest.fn().mockImplementation(() => ({
    subscriptions: [],
    globalState: {
      get: jest.fn(),
      update: jest.fn()
    },
    globalStorageUri: {
      fsPath: '/test/storage'
    }
  })),
  languages: {
    registerCompletionItemProvider: jest.fn(),
    registerCodeLensProvider: jest.fn()
  },
  commands: {
    registerCommand: jest.fn(),
    executeCommand: jest.fn()
  },
  StatusBarAlignment: {
    Left: 1,
    Right: 2
  },
  StatusBarItem: jest.fn().mockImplementation(() => ({
    text: '',
    tooltip: '',
    command: '',
    show: jest.fn(),
    hide: jest.fn(),
    dispose: jest.fn()
  })),
  ConfigurationTarget: {
    Global: 1,
    Workspace: 2
  },
  authentication: {
    getSession: jest.fn().mockResolvedValue(undefined)
  },
  extensions: {
    getExtension: jest.fn().mockReturnValue({ 
      packageJSON: { version: '1.0.0' },
      exports: {
        getAPI: jest.fn().mockReturnValue({
          repositories: [{
            getConfig: jest.fn().mockResolvedValue('test-user')
          }]
        })
      }
    })
  },
  version: '1.80.0',
  env: {
    language: 'en',
    machineId: 'test-machine-id',
    sessionId: 'test-session-id'
  }
};

// 使用getter定义activeTextEditor，支持jest.spyOn
let _activeTextEditor = {
  document: {
    uri: { fsPath: '/test/workspace/test.ts' },
    languageId: 'typescript',
    fileName: '/test/workspace/test.ts',
    lineAt: jest.fn(() => ({ 
      text: 'test', 
      range: { 
        start: { line: 0, character: 0 }, 
        end: { line: 0, character: 4 } 
      } 
    })),
    getText: jest.fn(() => 'test'),
    lineCount: 1
  },
  selection: {
    active: { line: 0, character: 0 }
  },
  setDecorations: jest.fn()
};

// 确保activeTextEditor属性存在且可配置
Object.defineProperty(global.vscode.window, 'activeTextEditor', {
  get: () => _activeTextEditor,
  set: (val) => { _activeTextEditor = val; },
  configurable: true,
  enumerable: true
});

// 同步初始化OutputChannelManager实例
try {
  const { OutputChannelManager } = require('./src/common/log/outputChannel');
  if (OutputChannelManager) {
    global.outputChannelManagerInstance = new OutputChannelManager();
  }
} catch (error) {
  console.warn('OutputChannelManager初始化失败:', error.message);
}
