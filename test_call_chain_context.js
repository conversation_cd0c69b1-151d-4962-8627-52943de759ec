// 测试调用链上下文收集的简单脚本

const vscode = require('vscode');
const { getCurrentFunctionContextDynamic } = require('./src/components/code_context/collect');

async function testCallChainContext() {
  console.log('=== 测试调用链上下文收集 ===');
  
  try {
    // 模拟一个活动的编辑器
    const mockDocument = {
      uri: { fsPath: '/test/sample.c' },
      getText: () => `
int helper_function() {
    return 42;
}

int main() {
    int result = helper_function();
    return result;
}
      `.trim()
    };
    
    const mockEditor = {
      document: mockDocument,
      selection: {
        active: { line: 5, character: 10 } // 在 main 函数内部
      }
    };
    
    // 模拟 vscode.window.activeTextEditor
    vscode.window = { activeTextEditor: mockEditor };
    
    // 调用函数
    const result = await getCurrentFunctionContextDynamic();
    
    console.log('结果:', {
      currentFunction: result.currentFunction?.name,
      relatedContextCount: result.relatedContext.length,
      similarContextCount: result.similarContext.length,
      contextSummary: result.contextSummary
    });
    
    if (result.relatedContext.length > 0) {
      console.log('相关上下文内容:');
      result.relatedContext.forEach((item, index) => {
        console.log(`${index + 1}. ${item.source} - ${item.contextType}`);
        console.log(`   内容: ${item.content.substring(0, 200)}...`);
      });
    }
    
  } catch (error) {
    console.error('测试失败:', error);
  }
}

// 运行测试
testCallChainContext();
