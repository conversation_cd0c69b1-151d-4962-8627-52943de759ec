好的，以下是 Code Partner 插件中与索引相关的主要命令及其作用总结：

---

### 1. `code-partner.reinitializeWorkspaceIndex`

**中文名称**：重新初始化工作区索引  
**作用**：

- 手动初始化索引的主命令。
- 会在 `.vscode` 目录下自动创建 `code-partner.json` 配置文件（如不存在）。
- 然后执行全量索引，生成和保存索引数据。
- 适用于首次使用或需要重建索引时。

---

### 2. `code-partner.forceReinitializeIndex`

**中文名称**：强制重新初始化索引  
**作用**：

- 清理现有索引数据（包括内存和持久化存储）。
- 然后根据配置文件（如有）或默认配置，重新执行全量索引。
- 适用于索引数据损坏或需要彻底重建索引的场景。

---

### 3. `code-partner.loadRepoIndex`

**中文名称**：加载代码仓库索引  
**作用**：

- 手动触发索引加载。
- 如果已存在索引数据，则加载到内存；否则会尝试执行一次索引。
- 适用于需要手动刷新索引状态时。

---

### 4. `code-partner.cleanIndexData`

**中文名称**：清理索引数据  
**作用**：

- 清理所有索引相关的持久化数据和内存数据。
- 不会自动重建索引。
- 适用于需要彻底清空索引数据的场景。

---

### 5. `code-partner.updateRepoIndex`

**中文名称**：更新代码仓库索引  
**作用**：

- 增量更新索引，仅处理新增、删除、变更的文件。
- 适用于日常开发中代码有变动时，快速同步索引。

---

### 6. `code-partner.showRepoIndex`

**中文名称**：查看索引内容  
**作用**：

- 展示当前索引的主要内容（如索引的文件、函数、关系等）。
- 适用于调试和验证索引效果。

---

### 7. `code-partner.queryRelatedFunctions`

**中文名称**：查询相关函数  
**作用**：

- 基于索引数据，查询与当前函数相关的调用、被调用关系。
- 适用于代码理解和导航。

---

### 8. `code-partner.cleanIndexData`

**中文名称**：清理索引数据  
**作用**：

- 清理所有索引相关的持久化数据和内存数据。
- 不会自动重建索引。
- 适用于需要彻底清空索引数据的场景。

---

## 自动化行为

- **插件激活时**：如果 `.vscode/code-partner.json` 存在且 `enableIndexing: true`，则自动加载索引数据（或自动恢复）。
- **文件变更/保存/删除**：自动触发增量索引更新。

## Debug Scripts

调试脚本已移动到 `debug_scripts/` 目录中，包含各种用于调试代码图索引系统的脚本。详细说明请查看 `debug_scripts/README.md`。

使用方法：
```bash
cd debug_scripts
node script_name.js
```
