/**
 * Windows 兼容的文件复制工具
 * 解决 Windows 上符号链接、权限和路径问题
 */

const fs = require('fs-extra');
const path = require('path');

/**
 * 简化的复制函数：先删除目标，再复制
 * @param {string} source 源路径
 * @param {string} dest 目标路径
 * @param {object} options 复制选项
 * @returns {boolean} 复制是否成功
 */
function windowsCompatibleCopy(source, dest, options = {}) {
    const { verbose = false } = options;

    try {
        // 1. 检查源路径是否存在
        if (!fs.existsSync(source)) {
            if (verbose) {
                console.log(`❌ 源路径不存在: ${source}`);
            }
            return false;
        }

        // 2. 如果目标存在，直接删除
        if (fs.existsSync(dest)) {
            if (verbose) {
                console.log(`🔄 删除已存在的目标: ${dest}`);
            }
            fs.removeSync(dest);
        }

        // 3. 确保目标目录存在
        fs.ensureDirSync(path.dirname(dest));

        // 4. 直接复制（解引用符号链接）
        fs.copySync(source, dest, {
            overwrite: true,
            preserveTimestamps: true,
            dereference: true  // 解引用符号链接
        });

        if (verbose) {
            console.log(`✅ 复制成功: ${path.basename(source)}`);
        }
        return true;

    } catch (error) {
        if (verbose) {
            console.error(`❌ 复制失败: ${source} -> ${dest}`);
            console.error(`   错误: ${error.message}`);
        }
        return false;
    }
}



/**
 * 批量复制模块（简化版本）
 * @param {Array} modules 模块列表
 * @param {string} sourceDir 源目录
 * @param {string} destDir 目标目录
 * @param {boolean} verbose 是否输出详细信息
 * @returns {Object} 复制结果统计
 */
function copyModulesWindowsCompatible(modules, sourceDir, destDir, verbose = false) {
    const results = {
        success: [],
        failed: [],
        total: modules.length
    };

    for (const moduleName of modules) {
        const moduleSource = path.join(sourceDir, moduleName);
        const moduleDest = path.join(destDir, moduleName);

        if (verbose) {
            console.log(`📦 复制模块: ${moduleName}`);
        }

        if (windowsCompatibleCopy(moduleSource, moduleDest, { verbose })) {
            results.success.push(moduleName);
            if (verbose) {
                console.log(`✅ 模块复制成功: ${moduleName}`);
            }
        } else {
            results.failed.push(moduleName);
            if (verbose) {
                console.log(`❌ 模块复制失败: ${moduleName}`);
            }
        }
    }

    return results;
}

module.exports = {
    windowsCompatibleCopy,
    copyModulesWindowsCompatible
};
