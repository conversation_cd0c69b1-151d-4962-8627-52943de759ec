#!/usr/bin/env node

/**
 * 统一构建脚本 - 协调主进程和 Worker 的分离构建
 */

const { buildMain } = require('./build-main');
const { buildWorkers } = require('./build-workers');
const fs = require('fs-extra');
const path = require('path');

const production = process.argv.includes('--production');
const watch = process.argv.includes('--watch');

/**
 * 清理构建目录
 */
async function cleanBuildDir() {
    console.log('🧹 清理构建目录...');
    const distDir = path.join(__dirname, '..', 'dist');
    if (fs.existsSync(distDir)) {
        fs.removeSync(distDir);
        console.log('✅ 构建目录已清理');
    }
}

/**
 * 分离构建：主进程和 Worker 分别构建
 */
async function separatedBuild() {
    try {
        console.log('🚀 开始分离构建...\n');
        
        // 1. 清理构建目录
        await cleanBuildDir();
        
        // 2. 先运行 copy-modules.js 确保 node_modules 准备好
        console.log('📦 准备 node_modules...\n');
        const { spawn } = require('child_process');

        await new Promise((resolve, reject) => {
            const copyProcess = spawn('node', ['copy-modules.js'], {
                stdio: 'inherit',
                cwd: process.cwd()
            });

            copyProcess.on('close', (code) => {
                if (code === 0) {
                    resolve();
                } else {
                    reject(new Error(`copy-modules.js 失败，退出码: ${code}`));
                }
            });

            copyProcess.on('error', reject);
        });

        console.log('✅ node_modules 准备完成\n');

        // 3. 并行构建主进程和 Worker（现在 node_modules 已准备好）
        console.log('📦 并行构建主进程和 Worker...\n');

        const buildPromises = [
            buildMain().catch(error => ({ type: 'main', error })),
            buildWorkers().catch(error => ({ type: 'workers', error }))
        ];

        const results = await Promise.allSettled(buildPromises);
        
        // 检查构建结果
        let hasErrors = false;
        for (const result of results) {
            if (result.status === 'rejected') {
                console.error(`❌ 构建失败:`, result.reason);
                hasErrors = true;
            } else if (result.value && result.value.error) {
                console.error(`❌ ${result.value.type} 构建失败:`, result.value.error);
                hasErrors = true;
            }
        }
        
        if (hasErrors) {
            throw new Error('分离构建失败');
        }
        
        console.log('\n🎉 分离构建完成！');
        console.log('📋 构建架构:');
        console.log('  - 主进程: dist/extension.js + dist/components/');
        console.log('  - Worker: dist/workers/ (独立的 WASM 资源)');
        console.log('  - 资源隔离: 主进程和 Worker 各自管理依赖');
        
    } catch (error) {
        console.error('\n❌ 分离构建失败:', error);
        process.exit(1);
    }
}

/**
 * 主函数 - 执行分离构建
 */
async function build() {
    if (watch) {
        console.log('⚠️ Watch 模式暂不支持分离构建');
        console.log('💡 请使用: npm run build 进行一次性构建');
        process.exit(1);
    }
    
    await separatedBuild();
}

// 运行构建
build().catch(error => {
    console.error('构建失败:', error);
    process.exit(1);
});

// 导出函数供其他脚本使用
module.exports = { build, separatedBuild };
