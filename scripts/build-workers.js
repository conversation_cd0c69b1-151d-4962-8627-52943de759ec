#!/usr/bin/env node

/**
 * Worker 专用构建脚本
 * 专门处理 Worker 的依赖和 WASM 文件管理
 */

const esbuild = require('esbuild');
const fs = require('fs-extra');
const path = require('path');

/**
 * Worker 构建配置
 */
const WORKER_CONFIGS = [
    {
        name: 'File parser worker for code analysis',
        entry: 'src/components/code_graph/workers/fileParserWorker.ts',
        output: 'dist/workers/fileParserWorker.mjs',
        format: 'esm'
    }
];

/**
 * 复制 Worker 专用的 WASM 资源
 */
async function copyWorkerAssets() {
    console.log('\n📦 复制 Worker 专用资源...\n');
    
    // 创建 Worker 资源目录
    const workerAssetsDir = path.join(__dirname, '..', 'dist', 'workers', 'assets');
    fs.ensureDirSync(workerAssetsDir);
    
    const nodeModulesDir = path.join(__dirname, '..', 'node_modules');
    
    // 复制 web-tree-sitter 核心 WASM
    const webTreeSitterWasm = path.join(nodeModulesDir, 'web-tree-sitter', 'tree-sitter.wasm');
    if (fs.existsSync(webTreeSitterWasm)) {
        fs.copySync(webTreeSitterWasm, path.join(workerAssetsDir, 'tree-sitter.wasm'));
        console.log('✅ 复制 tree-sitter.wasm 到 Worker 资源目录');
    }
    
    // 复制语言模块 WASM 文件
    const languageModules = [
        { name: 'tree-sitter-c', file: 'tree-sitter-c.wasm' },
        { name: 'tree-sitter-np', file: 'tree-sitter-np.wasm' }
    ];

    for (const module of languageModules) {
        // 首先尝试从 dist/node_modules 复制（copy-modules.js 已经处理过的）
        let sourceWasm = path.join(__dirname, '..', 'dist', 'node_modules', module.name, module.file);
        if (!fs.existsSync(sourceWasm)) {
            // 回退到原始 node_modules
            sourceWasm = path.join(nodeModulesDir, module.name, module.file);
        }

        if (fs.existsSync(sourceWasm)) {
            fs.copySync(sourceWasm, path.join(workerAssetsDir, module.file));
            console.log(`✅ 复制 ${module.file} 到 Worker 资源目录`);
        } else {
            console.log(`⚠️ ${module.file} 不存在，跳过`);
            console.log(`   尝试的路径: ${sourceWasm}`);
            console.log(`   备用路径: ${path.join(nodeModulesDir, module.name, module.file)}`);
        }
    }
    
    // 同时复制到 Worker 脚本目录（用于直接访问）
    const workerScriptDir = path.join(__dirname, '..', 'dist', 'workers');
    if (fs.existsSync(webTreeSitterWasm)) {
        fs.copySync(webTreeSitterWasm, path.join(workerScriptDir, 'tree-sitter.wasm'));
        console.log('✅ 复制 tree-sitter.wasm 到 Worker 脚本目录');
    }
    
    console.log('✅ Worker 资源复制完成');
}

/**
 * 构建单个 Worker
 */
async function buildWorker(config) {
    console.log(`📦 构建: ${config.name}`);
    console.log(`   入口: ${config.entry}`);
    console.log(`   输出: ${config.output}`);
    console.log(`   格式: ${config.format}`);
    
    try {
        // 确保输出目录存在
        fs.ensureDirSync(path.dirname(config.output));
        
        const buildOptions = {
            entryPoints: [config.entry],
            bundle: true,
            outfile: config.output,
            format: config.format,
            platform: 'node',
            target: 'node16',
            sourcemap: false,
            minify: false,
            // Worker 专用的外部依赖配置
            external: [
                // 保持这些作为外部依赖，由 Worker 环境提供
                'vscode'
            ],
            // Worker 专用的 banner，设置 WASM 文件定位
            banner: {
                js: config.format === 'esm' ? `
// Worker 专用 WASM 文件定位配置 (ESM)
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { existsSync } from 'fs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// 设置 Worker 环境的 WASM 文件查找路径
const workerDir = __dirname;
const workerAssetsDir = join(workerDir, 'assets');

// 设置 Module.locateFile 用于 Emscripten WASM 加载
if (typeof global !== 'undefined') {
    if (!global.Module) global.Module = {};
    global.Module.locateFile = function(filename) {
        // 优先从 Worker 资源目录查找
        const assetPath = join(workerAssetsDir, filename);
        if (existsSync(assetPath)) {
            return assetPath;
        }

        // 回退到 Worker 脚本目录
        const scriptPath = join(workerDir, filename);
        if (existsSync(scriptPath)) {
            return scriptPath;
        }

        // 默认路径
        return filename;
    };
}
` : `
// Worker 专用 WASM 文件定位配置 (CJS)
(function() {
    const path = require('path');
    const fs = require('fs');

    // 设置 Worker 环境的 WASM 文件查找路径
    const workerDir = __dirname;
    const workerAssetsDir = path.join(workerDir, 'assets');

    // 设置 Module.locateFile 用于 Emscripten WASM 加载
    if (typeof global !== 'undefined') {
        if (!global.Module) global.Module = {};
        global.Module.locateFile = function(filename) {
            // 优先从 Worker 资源目录查找
            const assetPath = path.join(workerAssetsDir, filename);
            if (fs.existsSync(assetPath)) {
                return assetPath;
            }

            // 回退到 Worker 脚本目录
            const scriptPath = path.join(workerDir, filename);
            if (fs.existsSync(scriptPath)) {
                return scriptPath;
            }

            // 默认路径
            return filename;
        };
    }
})();
`
            }
        };
        
        await esbuild.build(buildOptions);
        console.log(`   ✅ 成功: ${config.entry} -> ${config.output}`);
        return true;
    } catch (error) {
        console.error(`   ❌ 错误构建 ${config.entry}:`, error.message);
        return false;
    }
}

/**
 * 构建所有 Worker
 */
async function buildAllWorkers() {
    console.log('\n🔧 构建 Worker 文件...\n');
    
    let successCount = 0;
    let errorCount = 0;
    
    for (const config of WORKER_CONFIGS) {
        const success = await buildWorker(config);
        if (success) {
            successCount++;
        } else {
            errorCount++;
        }
    }
    
    console.log(`\n🎉 Worker 构建总结:`);
    console.log(`   ✅ 成功: ${successCount}`);
    console.log(`   ❌ 错误: ${errorCount}`);
    
    if (errorCount > 0) {
        throw new Error(`Worker 构建失败，${errorCount} 个错误`);
    }
}

/**
 * 主构建函数
 */
async function buildWorkers() {
    try {
        console.log('🚀 开始 Worker 专用构建...');
        
        // 1. 复制 Worker 专用资源
        await copyWorkerAssets();
        
        // 2. 构建所有 Worker
        await buildAllWorkers();
        
        console.log('\n🎉 Worker 构建完成！');
    } catch (error) {
        console.error('\n❌ Worker 构建失败:', error);
        process.exit(1);
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    buildWorkers();
}

module.exports = { buildWorkers };
