#!/usr/bin/env node

/**
 * 主进程专用构建脚本
 * 专门处理主进程的依赖和资源管理
 */

const esbuild = require('esbuild');
const fs = require('fs-extra');
const path = require('path');
const { copyModulesWindowsCompatible } = require('./windows-copy-utils');

/**
 * 主进程构建配置
 */
const MAIN_CONFIGS = [
    {
        name: 'Extension main entry',
        entry: 'src/extension.ts',
        output: 'dist/extension.js',
        format: 'cjs'
    },
    {
        name: 'Code graph components (including WorkerPool)',
        entry: 'src/components/code_graph/index.ts',
        output: 'dist/components/code_graph/index.js',
        format: 'cjs'
    }
    // 移除单独的 WorkerPool 构建 - 现在它会被打包进 code_graph/index.js
];

/**
 * 复制主进程专用的资源
 */
async function copyMainAssets() {
    console.log('\n📦 复制主进程专用资源...\n');
    
    // 创建主进程资源目录
    const mainAssetsDir = path.join(__dirname, '..', 'dist', 'assets');
    fs.ensureDirSync(mainAssetsDir);
    
    // 复制配置文件
    const configFiles = [
        'package.json',
        'README.md'
    ];
    
    for (const file of configFiles) {
        const sourcePath = path.join(__dirname, '..', file);
        const destPath = path.join(__dirname, '..', 'dist', file);
        
        if (fs.existsSync(sourcePath)) {
            fs.copySync(sourcePath, destPath);
            console.log(`✅ 复制 ${file} 到 dist 目录`);
        }
    }
    
    // 复制必要的 node_modules（主进程也需要解析代码）
    const essentialModules = [
        'vscode-languageserver',
        'vscode-languageserver-textdocument',
        'web-tree-sitter',
        'tree-sitter-c',
        'tree-sitter-np'
    ];
    
    const nodeModulesSource = path.join(__dirname, '..', 'node_modules');
    const nodeModulesDest = path.join(__dirname, '..', 'dist', 'node_modules');
    
    // 使用 Windows 兼容的复制工具
    console.log(`📦 开始复制 ${essentialModules.length} 个必要模块...`);

    const copyResults = copyModulesWindowsCompatible(
        essentialModules,
        nodeModulesSource,
        nodeModulesDest,
        true // verbose 输出
    );

    // 输出复制结果
    console.log(`\n📊 模块复制结果:`);
    console.log(`✅ 成功: ${copyResults.success.length} 个`);
    console.log(`❌ 失败: ${copyResults.failed.length} 个`);

    if (copyResults.success.length > 0) {
        console.log(`✅ 成功复制的模块: ${copyResults.success.join(', ')}`);
    }

    if (copyResults.failed.length > 0) {
        console.log(`❌ 复制失败的模块: ${copyResults.failed.join(', ')}`);

        // 如果有关键模块复制失败，抛出错误
        const criticalModules = ['web-tree-sitter', 'tree-sitter-c'];
        const failedCritical = copyResults.failed.filter(module => criticalModules.includes(module));

        if (failedCritical.length > 0) {
            throw new Error(`关键模块复制失败: ${failedCritical.join(', ')}`);
        }
    }
    
    // 主进程通过 Worker 进行解析，不需要直接访问 WASM
    // WASM 资源由 Worker 构建脚本管理
    console.log('ℹ️ 主进程通过 Worker 进行解析，WASM 资源由 Worker 构建管理');

    console.log('✅ 主进程资源复制完成');
}

/**
 * 构建单个主进程组件
 */
async function buildMainComponent(config) {
    console.log(`📦 构建: ${config.name}`);
    console.log(`   入口: ${config.entry}`);
    console.log(`   输出: ${config.output}`);
    console.log(`   格式: ${config.format}`);
    
    try {
        // 确保输出目录存在
        fs.ensureDirSync(path.dirname(config.output));
        
        const buildOptions = {
            entryPoints: [config.entry],
            bundle: true,
            outfile: config.output,
            format: config.format,
            platform: 'node',
            target: 'node16',
            sourcemap: true,
            minify: false,
            // 主进程的外部依赖配置
            external: [
                'vscode'
                // 主进程也需要解析代码，所以包含 tree-sitter 模块
            ],
            // 主进程专用的 banner
            banner: {
                js: `
// 主进程专用配置
(function() {
    // 设置主进程的模块解析路径
    const path = require('path');
    const Module = require('module');
    
    // 添加 dist/node_modules 到模块搜索路径
    const originalResolveFilename = Module._resolveFilename;
    Module._resolveFilename = function(request, parent, isMain) {
        try {
            return originalResolveFilename.call(this, request, parent, isMain);
        } catch (err) {
            // 尝试从 dist/node_modules 解析
            if (parent && parent.filename) {
                const distNodeModules = path.resolve(__dirname, 'node_modules');
                const newRequest = path.join(distNodeModules, request);
                if (require('fs').existsSync(newRequest)) {
                    return newRequest;
                }
            }
            throw err;
        }
    };
})();
`
            }
        };
        
        await esbuild.build(buildOptions);
        console.log(`   ✅ 成功: ${config.entry} -> ${config.output}`);
        return true;
    } catch (error) {
        console.error(`   ❌ 错误构建 ${config.entry}:`, error.message);
        return false;
    }
}

/**
 * 构建所有主进程组件
 */
async function buildAllMainComponents() {
    console.log('\n🔧 构建主进程组件...\n');
    
    let successCount = 0;
    let errorCount = 0;
    
    for (const config of MAIN_CONFIGS) {
        const success = await buildMainComponent(config);
        if (success) {
            successCount++;
        } else {
            errorCount++;
        }
    }
    
    console.log(`\n🎉 主进程构建总结:`);
    console.log(`   ✅ 成功: ${successCount}`);
    console.log(`   ❌ 错误: ${errorCount}`);
    
    if (errorCount > 0) {
        throw new Error(`主进程构建失败，${errorCount} 个错误`);
    }
}

/**
 * 主构建函数
 */
async function buildMain() {
    try {
        console.log('🚀 开始主进程专用构建...');
        
        // 1. 复制主进程专用资源
        await copyMainAssets();
        
        // 2. 构建所有主进程组件
        await buildAllMainComponents();
        
        console.log('\n🎉 主进程构建完成！');
    } catch (error) {
        console.error('\n❌ 主进程构建失败:', error);
        process.exit(1);
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    buildMain();
}

module.exports = { buildMain };
