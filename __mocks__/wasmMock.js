// Mock for WebAssembly modules
module.exports = {
  // 提供基本的 wasm 模块接口
  instantiate: jest.fn().mockResolvedValue({
    instance: {
      exports: {
        // 模拟基本的 wasm 导出函数
        memory: new WebAssembly.Memory({ initial: 256 }),
        // 添加其他可能需要的导出
      }
    }
  }),
  
  // 模拟 wasm 模块的默认导出
  default: {
    instantiate: jest.fn().mockResolvedValue({
      instance: {
        exports: {
          memory: new WebAssembly.Memory({ initial: 256 }),
        }
      }
    })
  }
}; 