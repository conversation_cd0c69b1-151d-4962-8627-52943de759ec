// Tree-sitter mock for testing environment
class MockParser {
  constructor() {
    this.language = null;
  }

  setLanguage(language) {
    this.language = language;
  }

  parse(content) {
    return {
      rootNode: {
        type: 'program',
        text: content,
        startPosition: { row: 0, column: 0 },
        endPosition: { row: content.split('\n').length - 1, column: 0 },
        namedChildren: []
      }
    };
  }
}

class MockLanguage {
  static load() {
    return Promise.resolve(new MockLanguage());
  }
}

// 添加静态方法
MockParser.init = jest.fn().mockResolvedValue();

module.exports = {
  Parser: MockParser,
  Language: MockLanguage
}; 