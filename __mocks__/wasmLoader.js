const fs = require('fs');
const path = require('path');

// 尝试加载真实的 wasm 文件
function loadWasmFile(wasmPath) {
  try {
    // 检查文件是否存在
    if (fs.existsSync(wasmPath)) {
      const wasmBuffer = fs.readFileSync(wasmPath);
      return {
        buffer: wasmBuffer,
        // 模拟 WebAssembly.instantiate 的返回值
        instantiate: jest.fn().mockResolvedValue({
          instance: {
            exports: {
              memory: new WebAssembly.Memory({ initial: 256 }),
              // 添加其他可能需要的导出
            }
          }
        })
      };
    }
  } catch (error) {
    console.warn(`Failed to load wasm file: ${wasmPath}`, error.message);
  }
  
  // 如果加载失败，返回 mock
  return {
    instantiate: jest.fn().mockResolvedValue({
      instance: {
        exports: {
          memory: new WebAssembly.Memory({ initial: 256 }),
        }
      }
    })
  };
}

// 映射 wasm 文件路径
const wasmMap = {
  'tree-sitter-np/tree-sitter-np.wasm': path.join(__dirname, '../dist/node_modules/tree-sitter-np/tree-sitter-np.wasm'),
  'tree-sitter-c/tree-sitter-c.wasm': path.join(__dirname, '../dist/node_modules/tree-sitter-c/tree-sitter-c.wasm'),
};

module.exports = function(modulePath) {
  const wasmFile = wasmMap[modulePath];
  if (wasmFile) {
    return loadWasmFile(wasmFile);
  }
  
  // 默认返回 mock
  return {
    instantiate: jest.fn().mockResolvedValue({
      instance: {
        exports: {
          memory: new WebAssembly.Memory({ initial: 256 }),
        }
      }
    })
  };
}; 