// copy-modules.js
const fs = require('fs-extra');
const path = require('path');
const { windowsCompatibleCopy } = require('./scripts/windows-copy-utils');

// 精简复制配置：只复制运行时必需的文件
const moduleConfigs = {
  'web-tree-sitter': {
    files: [
      'tree-sitter.wasm',           // 核心WASM (204K)
      'tree-sitter.js',             // ES模块运行时 (148K)
      'tree-sitter-web.d.ts',       // TypeScript类型定义 (40K)
      'package.json',               // 模块解析必需 (4K)
      'README.md'                   // 文档文件
    ]
  },
  'tree-sitter-c': {
    files: [
      'tree-sitter-c.wasm',         // 语言WASM (612K)
      'package.json',               // 模块解析必需 (4K)
      'bindings/',                  // Node.js绑定 (16K)
      'LICENSE'                     // 许可证文件
    ]
  },
  'tree-sitter-np': {
    files: [
      'tree-sitter-np.wasm',        // 语言WASM (140K)
      'package.json',               // 模块解析必需 (4K)
      'bindings/',                  // Node.js绑定 (88K)
      // 注意：不复制 node_modules/ (40M!)
    ]
  }
};

function copyModuleFiles(moduleName, config) {
  try {
    // 获取源模块路径
    let symlinkPath = path.resolve(__dirname, 'node_modules', moduleName);

    // 特殊处理 tree-sitter-np：从已知位置查找
    if (moduleName === 'tree-sitter-np' && !fs.existsSync(symlinkPath)) {
      const npSourcePath = '/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/ai-common/code/tree_sitters/languages/tree-sitter-np';
      if (fs.existsSync(npSourcePath)) {
        symlinkPath = npSourcePath;
        console.log(`🔍 Found tree-sitter-np at: ${symlinkPath}`);
      }
    }

    const realPath = fs.realpathSync(symlinkPath);
    const distModulePath = path.resolve(__dirname, 'dist', 'node_modules', moduleName);
    
    // 检查循环复制
    if (distModulePath.startsWith(realPath)) {
      console.log(`Skipping ${moduleName} (would create circular copy)`);
      return;
    }
    
    // 清理旧的目录
    if (fs.existsSync(distModulePath)) {
      fs.removeSync(distModulePath);
    }
    
    // 创建目标目录
    fs.ensureDirSync(distModulePath);
    
    let totalSize = 0;
    let copiedFiles = 0;
    
    // 特殊处理 tree-sitter-np
    if (moduleName === 'tree-sitter-np') {
      // 检查是否有新构建的兼容 WASM 文件
      const wasmPath = path.join(realPath, 'tree-sitter-np.wasm');
      const wasmStat = fs.existsSync(wasmPath) ? fs.statSync(wasmPath) : null;

      if (wasmStat && wasmStat.size < 300000) {
        // 如果 WASM 文件小于 300KB，说明是新构建的兼容版本，保持不变
        console.log(`✅ 使用新构建的兼容 tree-sitter-np WASM (${wasmStat.size} bytes)`);
      } else {
        // 否则使用 tree-sitter-c 的 WASM 文件作为兼容替代
        console.log(`🔄 使用兼容的 tree-sitter-c WASM 替代 tree-sitter-np...`);
        const cWasmPath = path.resolve(__dirname, 'node_modules', 'tree-sitter-c', 'tree-sitter-c.wasm');
        if (fs.existsSync(cWasmPath)) {
          fs.copySync(cWasmPath, wasmPath);
          console.log(`✅ 已用兼容的 tree-sitter-c.wasm 替换 ${wasmPath}`);
        } else {
          console.log(`❌ Warning: tree-sitter-c.wasm not found, cannot create ${wasmPath}`);
        }
      }
    }
    
    // 逐个复制指定文件/目录
    for (const file of config.files) {
      const srcPath = path.join(realPath, file);
      const dstPath = path.join(distModulePath, file);
      
      if (fs.existsSync(srcPath)) {
        // 确保目标目录存在
        fs.ensureDirSync(path.dirname(dstPath));

        // 使用 Windows 兼容的复制方法
        const copySuccess = windowsCompatibleCopy(srcPath, dstPath, {
          overwrite: true,
          preserveTimestamps: true,
          verbose: false
        });

        if (!copySuccess) {
          console.log(`❌ 复制失败: ${file} in ${moduleName}`);
          continue;
        }
        
        // 统计大小
        const stat = fs.statSync(srcPath);
        if (stat.isFile()) {
          totalSize += stat.size;
          copiedFiles++;
        } else if (stat.isDirectory()) {
          // 计算目录大小
          const dirSize = getDirSize(srcPath);
          totalSize += dirSize;
          copiedFiles++;
        }
      } else {
        console.log(`Warning: ${file} not found in ${moduleName}`);
      }
    }
    
    const sizeStr = formatBytes(totalSize);
    console.log(`✅ ${moduleName}: ${copiedFiles} items, ${sizeStr}`);
    
  } catch (error) {
    console.log(`❌ Failed to copy ${moduleName}: ${error.message}`);
  }
}

// 计算目录大小
function getDirSize(dirPath) {
  let size = 0;
  try {
    const files = fs.readdirSync(dirPath);
    for (const file of files) {
      const filePath = path.join(dirPath, file);
      const stat = fs.statSync(filePath);
      if (stat.isFile()) {
        size += stat.size;
      } else if (stat.isDirectory()) {
        size += getDirSize(filePath);
      }
    }
  } catch (error) {
    // 忽略权限错误
  }
  return size;
}

// 格式化字节大小
function formatBytes(bytes) {
  if (bytes === 0) {return '0 B';}
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
}

// 执行复制
function copyAllModules() {
  console.log('🚀 Starting selective WASM modules copy...');
  console.log('');

  for (const [moduleName, config] of Object.entries(moduleConfigs)) {
    copyModuleFiles(moduleName, config);
  }

  console.log('');
  console.log('✅ Selective WASM modules copy completed!');

  // 显示最终大小对比
  const distModulesPath = path.resolve(__dirname, 'dist', 'node_modules');
  if (fs.existsSync(distModulesPath)) {
    const totalSize = getDirSize(distModulesPath);
    console.log(`📦 Total dist/node_modules size: ${formatBytes(totalSize)}`);
  }
}

// 检查是否为watch模式
const isWatch = process.argv.includes('--watch');

if (isWatch) {
  console.log('🔍 Starting watch mode for modules...');
  copyAllModules();
  
  // 监听node_modules目录变化
  const chokidar = require('chokidar');
  const watcher = chokidar.watch('node_modules/{web-tree-sitter,tree-sitter-c,tree-sitter-np}/**/*', {
    ignored: /[\/\\]\./,
    persistent: true
  });

  watcher.on('change', (filePath) => {
    console.log(`📝 Module file changed: ${filePath}`);
    copyAllModules();
  });

  watcher.on('error', error => {
    console.error(`❌ Watch error: ${error}`);
  });
} else {
  copyAllModules();
}
