// A launch configuration that compiles the extension and then opens it inside a new window
// Use IntelliSense to learn about possible attributes.
// Hover to view descriptions of existing attributes.
// For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
{
	"version": "0.2.0",
	"configurations": [
		{
			"name": "Run Extension",
			"type": "extensionHost",
			"request": "launch",
			"args": [
				"--extensionDevelopmentPath=${workspaceFolder}"
			],
			"outFiles": [
				"${workspaceFolder}/dist/**/*.js"
			],
			"preLaunchTask": "npm: compile"
		},

		{
			"name": "调试当前测试文件",
			"type": "node",
			"request": "launch",
			"program": "${workspaceFolder}/node_modules/.bin/jest",
			"args": [
				"--runInBand",
				"--no-cache",
				"${file}"
			],
			"console": "integratedTerminal",
			"internalConsoleOptions": "neverOpen",
			"env": {
				"NODE_ENV": "test"
			}
		},
		{
			"name": "调试所有测试",
			"type": "node",
			"request": "launch",
			"program": "${workspaceFolder}/node_modules/.bin/jest",
			"args": [
				"--runInBand",
				"--no-cache"
			],
			"console": "integratedTerminal",
			"internalConsoleOptions": "neverOpen",
			"env": {
				"NODE_ENV": "test"
			}
		}
	]
}
