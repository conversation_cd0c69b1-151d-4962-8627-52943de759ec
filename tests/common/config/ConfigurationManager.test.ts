
// Mock VSCode API
jest.mock('vscode', () => ({
  window: {
    createOutputChannel: jest.fn().mockReturnValue({
      appendLine: jest.fn(),
      append: jest.fn(),
      clear: jest.fn(),
      show: jest.fn(),
      hide: jest.fn(),
      dispose: jest.fn()
    }),
    showInformationMessage: jest.fn(),
    showErrorMessage: jest.fn(),
    showWarningMessage: jest.fn()
  },
  workspace: {
    workspaceFolders: [
      {
        uri: {
          fsPath: '/test/workspace'
        },
        name: 'test-workspace'
      }
    ],
    createFileSystemWatcher: jest.fn().mockReturnValue({
      onDidCreate: jest.fn().mockReturnValue({
        dispose: jest.fn()
      }),
      onDidChange: jest.fn().mockReturnValue({
        dispose: jest.fn()
      }),
      onDidDelete: jest.fn().mockReturnValue({
        dispose: jest.fn()
      }),
      dispose: jest.fn()
    }),
    fs: {
      readFile: jest.fn(),
      writeFile: jest.fn(),
      stat: jest.fn()
    }
  },
  Uri: {
    file: jest.fn((path) => ({ fsPath: path }))
  },
  EventEmitter: jest.fn().mockImplementation(() => ({
    event: jest.fn(),
    fire: jest.fn(),
    dispose: jest.fn()
  })),
  ExtensionContext: jest.fn().mockImplementation(() => ({
    subscriptions: [],
    globalState: {
      get: jest.fn(),
      update: jest.fn()
    },
    globalStorageUri: {
      fsPath: '/test/storage'
    }
  }))
}));

import { ConfigurationManager } from 'src/common/config/ConfigurationManager';

// Mock vscode
jest.mock('vscode', () => {
  const mockConfig = {
    get: jest.fn(),
    update: jest.fn()
  };

  return {
    workspace: {
      getConfiguration: jest.fn().mockReturnValue(mockConfig),
      onDidChangeConfiguration: jest.fn()
    },
    ConfigurationTarget: {
      Global: 1,
      Workspace: 2
    },
    EventEmitter: jest.fn(() => ({
      event: jest.fn(),
      fire: jest.fn()
    }))
  };
});

describe('ConfigurationManager', () => {
  let configManager: ConfigurationManager;

  beforeEach(() => {
    jest.clearAllMocks();
    // 重置单例
    (ConfigurationManager as any).instance = undefined;
    configManager = ConfigurationManager.getInstance();
  });

  describe('getInstance', () => {
    it('应该返回单例实例', () => {
      const instance1 = ConfigurationManager.getInstance();
      const instance2 = ConfigurationManager.getInstance();
      expect(instance1).toBe(instance2);
    });
  });

  describe('getConfiguration', () => {
    it('应该返回默认配置当没有自定义配置时', () => {
      const mockConfig = {
        get: jest.fn().mockReturnValue(undefined),
        update: jest.fn()
      };
      const mockVscode = require('vscode');
      mockVscode.workspace.getConfiguration.mockReturnValue(mockConfig);

      const config = configManager.getConfiguration();

      expect(config.usePythonService).toBe(true);
      expect(config.pythonEndpoint).toBe('http://127.0.0.1:5555/api/v1/completion');
      expect(config.maxTokens).toBe(256);
      expect(config.temperature).toBe(0.2);
    });
  });

  describe('get', () => {
    it('应该返回指定配置项的值', () => {
      const mockConfig = {
        get: jest.fn().mockReturnValue(512),
        update: jest.fn()
      };
      const mockVscode = require('vscode');
      mockVscode.workspace.getConfiguration.mockReturnValue(mockConfig);

      // 重置单例以使用新的mock
      (ConfigurationManager as any).instance = undefined;
      const newConfigManager = ConfigurationManager.getInstance();

      const maxTokens = newConfigManager.get('maxTokens');

      expect(maxTokens).toBe(512);
    });
  });

  describe('update', () => {
    it('应该更新配置项', async () => {
      const mockConfig = {
        get: jest.fn(),
        update: jest.fn()
      };
      const mockVscode = require('vscode');
      mockVscode.workspace.getConfiguration.mockReturnValue(mockConfig);

      // 重置单例以使用新的mock
      (ConfigurationManager as any).instance = undefined;
      const newConfigManager = ConfigurationManager.getInstance();

      await newConfigManager.update('maxTokens', 1024);

      expect(mockConfig.update).toHaveBeenCalledWith('maxTokens', 1024, 1);
    });
  });

  describe('updateWorkspace', () => {
    it('应该更新工作区配置项', async () => {
      const mockConfig = {
        get: jest.fn(),
        update: jest.fn()
      };
      const mockVscode = require('vscode');
      mockVscode.workspace.getConfiguration.mockReturnValue(mockConfig);

      // 重置单例以使用新的mock
      (ConfigurationManager as any).instance = undefined;
      const newConfigManager = ConfigurationManager.getInstance();

      await newConfigManager.updateWorkspace('maxTokens', 1024);

      expect(mockConfig.update).toHaveBeenCalledWith('maxTokens', 1024, 2);
    });
  });

  describe('validateConfiguration', () => {
    it('应该验证有效的配置', () => {
      const mockConfig = {
        get: jest.fn()
          .mockReturnValueOnce(false) // usePythonService
          .mockReturnValueOnce('test-key') // openAIApiKey
          .mockReturnValueOnce(0.4) // graphWeight
          .mockReturnValueOnce(0.3) // bm25Weight
          .mockReturnValueOnce(0.3) // embeddingWeight
          .mockReturnValueOnce(0.5) // temperature
          .mockReturnValueOnce(256), // maxTokens
        update: jest.fn()
      };
      const mockVscode = require('vscode');
      mockVscode.workspace.getConfiguration.mockReturnValue(mockConfig);

      const validation = configManager.validateConfiguration();

      expect(validation.isValid).toBe(true);
      expect(validation.errors).toHaveLength(0);
    });

    it('应该检测权重配置错误', () => {
      const mockConfig = {
        get: jest.fn()
          .mockReturnValueOnce(false) // usePythonService
          .mockReturnValueOnce('http://127.0.0.1:5555/api/v1/completion') // pythonEndpoint
          .mockReturnValueOnce('test-key') // openAIApiKey
          .mockReturnValueOnce('qwen2.5-coder:3b') // model
          .mockReturnValueOnce('http://127.0.0.1:5555/api/v1/statistics/events') // statisticsEndpoint
          .mockReturnValueOnce(true) // enableStatistics
          .mockReturnValueOnce(256) // maxTokens
          .mockReturnValueOnce(0.2) // temperature
          .mockReturnValueOnce(true) // enableInlineCompletion
          .mockReturnValueOnce(true) // enableDiffHighlightCompletion
          .mockReturnValueOnce(true) // enableIndexing
          .mockReturnValueOnce(true) // autoIndexing
          .mockReturnValueOnce(['*']) // languageExtensions
          .mockReturnValueOnce(0.5) // graphWeight - 修改为0.5
          .mockReturnValueOnce(0.3) // bm25Weight
          .mockReturnValueOnce(0.3) // embeddingWeight - 总和为1.1，应该无效
          .mockReturnValueOnce('INFO') // logLevel
          .mockReturnValueOnce(false), // enableDebugLog
        update: jest.fn()
      };
      const mockVscode = require('vscode');
      mockVscode.workspace.getConfiguration.mockReturnValue(mockConfig);

      // 重置单例以使用新的mock
      (ConfigurationManager as any).instance = undefined;
      const newConfigManager = ConfigurationManager.getInstance();

      const validation = newConfigManager.validateConfiguration();

      expect(validation.isValid).toBe(false);
      expect(validation.errors.length).toBeGreaterThan(0);
    });
  });

  describe('getConfigurationSummary', () => {
    it('应该返回配置摘要', () => {
      const mockConfig = {
        get: jest.fn()
          .mockReturnValueOnce(false) // usePythonService
          .mockReturnValueOnce('http://127.0.0.1:5555/api/v1/completion') // pythonEndpoint
          .mockReturnValueOnce('test-key') // openAIApiKey
          .mockReturnValueOnce('qwen2.5-coder:3b') // model
          .mockReturnValueOnce('http://127.0.0.1:5555/api/v1/statistics/events') // statisticsEndpoint
          .mockReturnValueOnce(true) // enableStatistics
          .mockReturnValueOnce(256) // maxTokens
          .mockReturnValueOnce(0.2) // temperature
          .mockReturnValueOnce(true) // enableInlineCompletion
          .mockReturnValueOnce(true) // enableDiffHighlightCompletion
          .mockReturnValueOnce(true) // enableIndexing
          .mockReturnValueOnce(true) // autoIndexing
          .mockReturnValueOnce(['*']) // languageExtensions
          .mockReturnValueOnce(0.4) // graphWeight
          .mockReturnValueOnce(0.3) // bm25Weight
          .mockReturnValueOnce(0.3) // embeddingWeight
          .mockReturnValueOnce('INFO') // logLevel - 确保返回字符串
          .mockReturnValueOnce(false), // enableDebugLog
        update: jest.fn()
      };
      const mockVscode = require('vscode');
      mockVscode.workspace.getConfiguration.mockReturnValue(mockConfig);

      // 重置单例以使用新的mock
      (ConfigurationManager as any).instance = undefined;
      const newConfigManager = ConfigurationManager.getInstance();

      const summary = newConfigManager.getConfigurationSummary();

      expect(summary).toContain('Code Partner 配置摘要');
      expect(summary).toContain('LLM服务: OpenAI');
      expect(summary).toContain('统计服务: 启用');
      expect(summary).toContain('行内补全: 启用');
      expect(summary).toContain('差异高亮: 启用');
      expect(summary).toContain('代码索引: 启用');
      expect(summary).toContain('日志级别: INFO');
      expect(summary).toContain('权重配置: 图谱(0.4) + BM25(0.3) + 向量(0.3)');
    });
  });
}); 