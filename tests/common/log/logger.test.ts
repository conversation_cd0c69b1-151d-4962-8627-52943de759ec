import * as assert from 'assert';
import { globalLogger as logger } from 'src/common/log/logger';

describe('Logger', () => {
  describe('log', () => {
    test('应该记录日志消息', () => {
      const message = 'Test log message';
      
      try {
        logger.info(message);
        // 如果没有抛出错误，说明日志记录成功
        assert.ok(true);
      } catch (error) {
        assert.fail('不应该抛出错误');
      }
    });

    test('应该处理空消息', () => {
      try {
        logger.info('');
        assert.ok(true);
      } catch (error) {
        assert.fail('不应该抛出错误');
      }
    });

    test('应该处理null消息', () => {
      try {
        logger.info(null as any);
        assert.ok(true);
      } catch (error) {
        assert.fail('不应该抛出错误');
      }
    });

    test('应该处理复杂字符串', () => {
      const complexMessage = 'Complex message with special chars: !@#$%^&*()_+-=[]{}|;:,.<>?`~\\\\/\\"\\\'';
      try {
        logger.info(complexMessage);
        assert.ok(true);
      } catch (error) {
        assert.fail('不应该抛出错误');
      }
    });

    test('应该处理Unicode字符', () => {
      const unicodeMessage = '中文测试 🚀 🌟 💻';
      
      try {
        logger.info(unicodeMessage);
        assert.ok(true);
      } catch (error) {
        assert.fail('不应该抛出错误');
      }
    });

    test('应该处理多行消息', () => {
      const multiLineMessage = 'Line 1\nLine 2\nLine 3';
      
      try {
        logger.info(multiLineMessage);
        assert.ok(true);
      } catch (error) {
        assert.fail('不应该抛出错误');
      }
    });
  });

  describe('并发测试', () => {
    test('应该能处理并发日志记录', async () => {
      const promises: Promise<void>[] = [];
      const iterations = 100;

      for (let i = 0; i < iterations; i++) {
        promises.push(
          new Promise<void>((resolve) => {
            logger.info(`Concurrent test message ${i}`);
            resolve();
          })
        );
      }

      await Promise.all(promises);
      assert.ok(true, '所有并发日志都应该成功记录');
    });
  });

  describe('边界情况', () => {
    test('应该处理超长消息', () => {
      const longMessage = 'a'.repeat(10000);
      try {
        logger.info(longMessage);
        assert.ok(true);
      } catch (error) {
        assert.fail('不应该抛出错误');
      }
    });

    test('应该处理特殊字符', () => {
      const specialChars = '\\n\\r\\t\\b\\f\\\\\'"';
      try {
        logger.info(specialChars);
        assert.ok(true);
      } catch (error) {
        assert.fail('不应该抛出错误');
      }
    });

    test('应该处理控制字符', () => {
      const controlChars = '\x00\x01\x02\x03\x04\x05\x06\x07\x08\x09\x0A\x0B\x0C\x0D\x0E\x0F';
      
      try {
        logger.info(controlChars);
        assert.ok(true);
      } catch (error) {
        assert.fail('不应该抛出错误');
      }
    });
  });
}); 