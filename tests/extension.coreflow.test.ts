import * as assert from 'assert';
import * as extension from 'src/extension';

describe('核心流程-扩展激活与命令注册', () => {
  let context: any;

  beforeEach(() => {
    context = { subscriptions: [] };
  });

  it('应能激活扩展并注册命令', async () => {
    try {
      await extension.activate(context as any);
      // 检查是否注册了命令
      assert.ok(context.subscriptions.length > 0, '应注册命令');
    } catch (e) {
      assert.fail('扩展激活不应抛出异常');
    }
  });

  it('应能处理激活过程中的异常', async () => {
    // 模拟异常context
    const errorContext = null;
    try {
      await extension.activate(errorContext as any);
      // 理论上应抛出异常
      assert.ok(true);
    } catch (e) {
      assert.ok(e instanceof Error || typeof e === 'object');
    }
  });

  it('应导出deactivate函数', () => {
    assert.ok(typeof extension.deactivate === 'function', '应导出deactivate函数');
  });
}); 