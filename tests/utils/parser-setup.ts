import * as fs from 'fs';
import * as path from 'path';
import { Language, Parser } from 'web-tree-sitter';

export interface ParserSetup {
  cParser: Parser;
  npParser: Parser | null;
}

export async function setupParsers(): Promise<ParserSetup> {
  // 初始化 Parser
  await Parser.init();
  
  // 设置 C 语言解析器
  const cParser = new Parser();
  const cWasmPath = path.resolve(__dirname, '../../node_modules/tree-sitter-c/tree-sitter-c.wasm');
  const cWasmBuffer = fs.readFileSync(cWasmPath);
  const C = await Language.load(cWasmBuffer);
  cParser.setLanguage(C);

  // 尝试设置 NP 语言解析器
  let npParser: Parser | null = null;
  try {
    npParser = new Parser();
    const npWasmPath = path.resolve(__dirname, '../../node_modules/tree-sitter-np/tree-sitter-np.wasm');
    const npWasmBuffer = fs.readFileSync(npWasmPath);
    const NP = await Language.load(npWasmBuffer);
    npParser.setLanguage(NP);
  } catch (error) {
    console.warn('NP 语言解析器不可用，跳过 NP 相关测试');
    npParser = null;
  }

  return { cParser, npParser };
}

// 辅助方法：递归查找指定类型的节点
export function findNodesByType(node: any, type: string): any[] {
  const results: any[] = [];
  
  if (node.type === type) {
    results.push(node);
  }
  
  for (const child of node.namedChildren) {
    if (child) {
      results.push(...findNodesByType(child, type));
    }
  }
  
  return results;
}

// 辅助方法：从函数定义或调用中提取函数名
export function findFunctionName(node: any): string | null {
  if (node.type === 'function_definition') {
    // 函数定义：查找 identifier
    const identifier = node.namedChildren.find((child: any) => child.type === 'identifier');
    return identifier ? identifier.text : null;
  } else if (node.type === 'call_expression') {
    // 函数调用：查找 function
    const functionNode = node.namedChildren.find((child: any) => child.type === 'identifier');
    return functionNode ? functionNode.text : null;
  }
  return null;
} 