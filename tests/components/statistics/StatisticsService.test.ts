import axios from 'axios';
import { StatisticsService } from 'src/components/statistics/StatisticsService';
import { StatisticsEventType } from 'src/components/types/statistics';

// Mock axios
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

describe('StatisticsService', () => {
    let statisticsService: StatisticsService;

    beforeEach(() => {
        statisticsService = StatisticsService.getInstance();
        jest.clearAllMocks();
    });

    describe('getInstance', () => {
        it('应该返回单例实例', () => {
            const instance1 = StatisticsService.getInstance();
            const instance2 = StatisticsService.getInstance();
            expect(instance1).toBe(instance2);
        });
    });

    describe('reportCompletionTriggered', () => {
        it('应该成功上报续写触发事件', async () => {
            const mockResponse = {
                data: {
                    success: true,
                    message: '统计事件上报成功',
                    request_id: 'req-123',
                    timestamp: '2024-01-01T12:00:00'
                }
            };
            (mockedAxios as any).mockResolvedValueOnce(mockResponse);

            const scope = {
                filePath: '/test/file.ts',
                language: 'typescript',
                functionName: 'testFunction',
                className: 'TestClass',
                moduleName: 'test'
            };

            const context = {
                beforeCursor: 'function test() {',
                afterCursor: '}',
                relatedCodeSnippets: [],
                similarCodeSnippets: [],
                environments: []
            };

            await statisticsService.reportCompletionTriggered(scope, context);

            expect(mockedAxios).toHaveBeenCalledWith(
                expect.objectContaining({
                    method: 'POST',
                    url: 'http://127.0.0.1:5555/api/v1/statistics/events',
                    headers: { 'Content-Type': 'application/json' },
                    data: expect.objectContaining({
                        eventType: 'COMPLETION_TRIGGERED'
                    })
                })
            );
        });
    });

    describe('reportCompletionFeedback', () => {
        it('应该成功上报续写接受事件', async () => {
            const mockResponse = {
                data: {
                    success: true,
                    message: '统计事件上报成功',
                    request_id: 'req-123',
                    timestamp: '2024-01-01T12:00:00'
                }
            };
            (mockedAxios as any).mockResolvedValueOnce(mockResponse);

            const scope = {
                filePath: '/test/file.ts',
                language: 'typescript',
                functionName: 'testFunction'
            };

            const context = {
                beforeCursor: 'function test() {',
                afterCursor: '}',
                relatedCodeSnippets: [],
                similarCodeSnippets: [],
                environments: []
            };

            await statisticsService.reportCompletionFeedback(
                StatisticsEventType.COMPLETION_ACCEPTED,
                scope,
                context,
                'console.log("test");',
                '// TODO: add logging'
            );

            expect(mockedAxios).toHaveBeenCalledWith(
                expect.objectContaining({
                    method: 'POST',
                    url: 'http://127.0.0.1:5555/api/v1/statistics/events',
                    headers: { 'Content-Type': 'application/json' },
                    data: expect.objectContaining({
                        eventType: 'COMPLETION_ACCEPTED'
                    })
                })
            );
        });

        it('应该成功上报续写拒绝事件', async () => {
            const mockResponse = {
                data: {
                    success: true,
                    message: '统计事件上报成功',
                    request_id: 'req-123',
                    timestamp: '2024-01-01T12:00:00'
                }
            };
            (mockedAxios as any).mockResolvedValueOnce(mockResponse);

            const scope = {
                filePath: '/test/file.ts',
                language: 'typescript',
                functionName: 'testFunction'
            };

            const context = {
                beforeCursor: 'function test() {',
                afterCursor: '}',
                relatedCodeSnippets: [],
                similarCodeSnippets: [],
                environments: []
            };

            await statisticsService.reportCompletionFeedback(
                StatisticsEventType.COMPLETION_REJECTED,
                scope,
                context,
                'console.log("test");'
            );

            expect(mockedAxios).toHaveBeenCalledWith(
                expect.objectContaining({
                    method: 'POST',
                    url: 'http://127.0.0.1:5555/api/v1/statistics/events',
                    headers: { 'Content-Type': 'application/json' },
                    data: expect.objectContaining({
                        eventType: 'COMPLETION_REJECTED'
                    })
                })
            );
        });
    });

    describe('reportCompletionResponse', () => {
        it('应该成功上报续写响应事件', async () => {
            const mockResponse = {
                data: {
                    success: true,
                    message: '统计事件上报成功',
                    request_id: 'req-123',
                    timestamp: '2024-01-01T12:00:00'
                }
            };
            (mockedAxios as any).mockResolvedValueOnce(mockResponse);

            const scope = {
                filePath: '/test/file.ts',
                language: 'typescript',
                functionName: 'testFunction'
            };

            const context = {
                beforeCursor: 'function test() {',
                afterCursor: '}',
                relatedCodeSnippets: [],
                similarCodeSnippets: [],
                environments: []
            };

            const config = {
                model: 'gpt-4',
                temperature: 0.2,
                max_tokens: 256
            };

            await statisticsService.reportCompletionResponse(
                scope,
                context,
                '请补全这个函数',
                'console.log("completed");',
                config
            );

            expect(mockedAxios).toHaveBeenCalledWith(
                expect.objectContaining({
                    method: 'POST',
                    url: 'http://127.0.0.1:5555/api/v1/statistics/events',
                    headers: { 'Content-Type': 'application/json' },
                    data: expect.objectContaining({
                        eventType: 'COMPLETION_TRIGGERED'
                    })
                })
            );
        });
    });

    describe('reportEvent', () => {
        it('应该成功上报自定义事件', async () => {
            const mockResponse = {
                data: {
                    success: true,
                    message: '统计事件上报成功',
                    request_id: 'req-123',
                    timestamp: '2024-01-01T12:00:00'
                }
            };
            (mockedAxios as any).mockResolvedValueOnce(mockResponse);

            const event = {
                eventType: StatisticsEventType.COMPLETION_TRIGGERED,
                timestamp: Date.now(),
                sessionId: 'test-session',
                data: {
                    userInfo: {
                        name: 'test',
                        nickname: 'test',
                        userType: 'developer'
                    },
                    scope: {
                        filePath: '/test/file.ts',
                        language: 'typescript'
                    }
                }
            };

            const response = await statisticsService.reportEvent(event);

            expect(mockedAxios).toHaveBeenCalledWith(
                expect.objectContaining({
                    method: 'POST',
                    url: 'http://127.0.0.1:5555/api/v1/statistics/events',
                    headers: { 'Content-Type': 'application/json' },
                    data: event
                })
            );
            expect(response.success).toBe(true);
        });
    });

    describe('错误处理', () => {
        it('应该处理网络错误而不抛出异常', async () => {
            (mockedAxios as any).mockRejectedValueOnce(new Error('Network error'));

            const scope = {
                filePath: '/test/file.ts',
                language: 'typescript'
            };

            const context = {
                beforeCursor: 'test',
                afterCursor: '',
                relatedCodeSnippets: [],
                similarCodeSnippets: [],
                environments: []
            };

            // 不应该抛出异常
            await expect(
                statisticsService.reportCompletionTriggered(scope, context)
            ).resolves.not.toThrow();
        });

        it('应该处理HTTP错误而不抛出异常', async () => {
            const mockResponse = { data: { success: false, status: 500, statusText: 'Internal Server Error' } };
            (mockedAxios as any).mockResolvedValueOnce(mockResponse);

            const scope = {
                filePath: '/test/file.ts',
                language: 'typescript'
            };

            const context = {
                beforeCursor: 'test',
                afterCursor: '',
                relatedCodeSnippets: [],
                similarCodeSnippets: [],
                environments: []
            };

            // 不应该抛出异常
            await expect(
                statisticsService.reportCompletionTriggered(scope, context)
            ).resolves.not.toThrow();
        });
    });
}); 