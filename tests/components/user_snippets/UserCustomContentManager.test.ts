import { UserCustomContentManager } from '../../../src/components/user_snippets/UserCustomContentManager';

// Mock vscode
jest.mock('vscode', () => ({
  workspace: {
    workspaceFolders: [
      {
        uri: {
          fsPath: '/test/workspace'
        }
      }
    ]
  },
  env: {
    appRoot: '/test/app'
  }
}));

// Mock fs
jest.mock('fs/promises', () => ({
  mkdir: jest.fn(),
  readFile: jest.fn(),
  writeFile: jest.fn()
}));

describe('UserCustomContentManager', () => {
  let manager: UserCustomContentManager;
  let mockFs: any;

  beforeEach(() => {
    jest.clearAllMocks();
    mockFs = require('fs/promises');
    
    // 重置单例
    (UserCustomContentManager as any).instance = undefined;
    manager = UserCustomContentManager.getInstance();
  });

  describe('getInstance', () => {
    it('应该返回单例实例', () => {
      const instance1 = UserCustomContentManager.getInstance();
      const instance2 = UserCustomContentManager.getInstance();
      expect(instance1).toBe(instance2);
    });
  });

  describe('代码片段管理', () => {
    it('应该能够添加代码片段', async () => {
      mockFs.readFile.mockRejectedValue({ code: 'ENOENT' });
      mockFs.writeFile.mockResolvedValue(undefined);

      const snippetData = {
        name: '测试片段',
        description: '这是一个测试片段',
        code: 'function test() { console.log("hello"); }',
        language: 'javascript',
        tags: ['test', 'function'],
        priority: 8,
        isEnabled: true
      };

      const result = await manager.addSnippet(snippetData);

      expect(result.id).toBeDefined();
      expect(result.name).toBe(snippetData.name);
      expect(result.code).toBe(snippetData.code);
      expect(result.createdAt).toBeDefined();
      expect(result.updatedAt).toBeDefined();
    });

    it('应该能够更新代码片段', async () => {
      const existingConfig = {
        snippets: [{
          id: 'test-id',
          name: '原始片段',
          code: 'original code',
          language: 'javascript',
          tags: [],
          priority: 5,
          createdAt: Date.now() - 1000, // 确保创建时间早于更新时间
          updatedAt: Date.now() - 1000,
          isEnabled: true
        }],
        rules: [],
        version: '1.0.0',
        lastUpdated: Date.now()
      };

      mockFs.readFile.mockResolvedValue(JSON.stringify(existingConfig));
      mockFs.writeFile.mockResolvedValue(undefined);

      const updates = {
        name: '更新后的片段',
        priority: 9
      };

      const result = await manager.updateSnippet('test-id', updates);

      expect(result.name).toBe('更新后的片段');
      expect(result.priority).toBe(9);
      expect(result.updatedAt).toBeGreaterThan(result.createdAt);
    });

    it('应该能够删除代码片段', async () => {
      const existingConfig = {
        snippets: [{
          id: 'test-id',
          name: '要删除的片段',
          code: 'code to delete',
          language: 'javascript',
          tags: [],
          priority: 5,
          createdAt: Date.now(),
          updatedAt: Date.now(),
          isEnabled: true
        }],
        rules: [],
        version: '1.0.0',
        lastUpdated: Date.now()
      };

      mockFs.readFile.mockResolvedValue(JSON.stringify(existingConfig));
      mockFs.writeFile.mockResolvedValue(undefined);

      const result = await manager.deleteSnippet('test-id');

      expect(result).toBe(true);
    });

    it('应该能够获取所有代码片段', async () => {
      const existingConfig = {
        snippets: [
          {
            id: 'snippet1',
            name: '片段1',
            code: 'code1',
            language: 'javascript',
            tags: [],
            priority: 5,
            createdAt: Date.now(),
            updatedAt: Date.now(),
            isEnabled: true
          },
          {
            id: 'snippet2',
            name: '片段2',
            code: 'code2',
            language: 'typescript',
            tags: [],
            priority: 7,
            createdAt: Date.now(),
            updatedAt: Date.now(),
            isEnabled: false
          }
        ],
        rules: [],
        version: '1.0.0',
        lastUpdated: Date.now()
      };

      mockFs.readFile.mockResolvedValue(JSON.stringify(existingConfig));

      const snippets = await manager.getAllSnippets();

      expect(snippets).toHaveLength(2);
      expect(snippets[0].name).toBe('片段1');
      expect(snippets[1].name).toBe('片段2');
    });

    it('应该能够获取启用的代码片段', async () => {
      const existingConfig = {
        snippets: [
          {
            id: 'snippet1',
            name: '启用片段',
            code: 'code1',
            language: 'javascript',
            tags: [],
            priority: 5,
            createdAt: Date.now(),
            updatedAt: Date.now(),
            isEnabled: true
          },
          {
            id: 'snippet2',
            name: '禁用片段',
            code: 'code2',
            language: 'typescript',
            tags: [],
            priority: 7,
            createdAt: Date.now(),
            updatedAt: Date.now(),
            isEnabled: false
          }
        ],
        rules: [],
        version: '1.0.0',
        lastUpdated: Date.now()
      };

      mockFs.readFile.mockResolvedValue(JSON.stringify(existingConfig));

      const snippets = await manager.getEnabledSnippets();

      expect(snippets).toHaveLength(1);
      expect(snippets[0].name).toBe('启用片段');
    });
  });

  describe('规则管理', () => {
    it('应该能够添加规则', async () => {
      mockFs.readFile.mockRejectedValue({ code: 'ENOENT' });
      mockFs.writeFile.mockResolvedValue(undefined);

      const ruleData = {
        name: '测试规则',
        description: '这是一个测试规则',
        content: '使用有意义的变量名',
        language: 'javascript',
        priority: 6,
        isEnabled: true
      };

      const result = await manager.addRule(ruleData);

      expect(result.id).toBeDefined();
      expect(result.name).toBe(ruleData.name);
      expect(result.content).toBe(ruleData.content);
      expect(result.createdAt).toBeDefined();
      expect(result.updatedAt).toBeDefined();
    });

    it('应该能够获取适用的规则', async () => {
      const existingConfig = {
        snippets: [],
        rules: [
          {
            id: 'rule1',
            name: 'JavaScript规则',
            content: 'JS规则内容',
            language: 'javascript',
            priority: 5,
            createdAt: Date.now(),
            updatedAt: Date.now(),
            isEnabled: true
          },
          {
            id: 'rule2',
            name: '通用规则',
            content: '通用规则内容',
            language: undefined,
            priority: 7,
            createdAt: Date.now(),
            updatedAt: Date.now(),
            isEnabled: true
          },
          {
            id: 'rule3',
            name: 'Python规则',
            content: 'Python规则内容',
            language: 'python',
            priority: 6,
            createdAt: Date.now(),
            updatedAt: Date.now(),
            isEnabled: true
          }
        ],
        version: '1.0.0',
        lastUpdated: Date.now()
      };

      mockFs.readFile.mockResolvedValue(JSON.stringify(existingConfig));

      const results = await manager.getApplicableRules('javascript');

      expect(results).toHaveLength(3); // 返回所有规则，只是标记是否适用
      expect(results.find(r => r.rule.name === 'JavaScript规则')?.isApplicable).toBe(true);
      expect(results.find(r => r.rule.name === '通用规则')?.isApplicable).toBe(true);
      expect(results.find(r => r.rule.name === 'Python规则')?.isApplicable).toBe(false);
    });
  });

  describe('片段匹配', () => {
    it('应该能够找到匹配的代码片段', async () => {
      const existingConfig = {
        snippets: [
          {
            id: 'snippet1',
            name: '排序函数',
            code: 'function sort(arr) { return arr.sort(); }',
            language: 'javascript',
            tags: ['sort', 'array', 'function'],
            priority: 8,
            createdAt: Date.now(),
            updatedAt: Date.now(),
            isEnabled: true
          },
          {
            id: 'snippet2',
            name: '字符串处理',
            code: 'function trim(str) { return str.trim(); }',
            language: 'javascript',
            tags: ['string', 'trim'],
            priority: 6,
            createdAt: Date.now(),
            updatedAt: Date.now(),
            isEnabled: true
          }
        ],
        rules: [],
        version: '1.0.0',
        lastUpdated: Date.now()
      };

      mockFs.readFile.mockResolvedValue(JSON.stringify(existingConfig));

      const results = await manager.findMatchingSnippets(
        'function sortArray(arr) { return arr.sort(); }',
        'javascript',
        2
      );

      expect(results.length).toBeGreaterThan(0);
      // 排序函数应该匹配度更高
      const sortSnippet = results.find(r => r.snippet.name === '排序函数');
      expect(sortSnippet).toBeDefined();
      expect(sortSnippet?.similarity).toBeGreaterThan(0);
    });
  });

  describe('配置管理', () => {
    it('应该能够导出配置', async () => {
      const existingConfig = {
        snippets: [
          {
            id: 'snippet1',
            name: '测试片段',
            code: 'test code',
            language: 'javascript',
            tags: [],
            priority: 5,
            createdAt: Date.now(),
            updatedAt: Date.now(),
            isEnabled: true
          }
        ],
        rules: [
          {
            id: 'rule1',
            name: '测试规则',
            content: 'test rule',
            language: 'javascript',
            priority: 5,
            createdAt: Date.now(),
            updatedAt: Date.now(),
            isEnabled: true
          }
        ],
        version: '1.0.0',
        lastUpdated: Date.now()
      };

      mockFs.readFile.mockResolvedValue(JSON.stringify(existingConfig));

      const configJson = await manager.exportConfig();
      const parsed = JSON.parse(configJson);

      expect(parsed.snippets).toHaveLength(1);
      expect(parsed.rules).toHaveLength(1);
      expect(parsed.version).toBe('1.0.0');
    });

    it('应该能够导入配置', async () => {
      mockFs.writeFile.mockResolvedValue(undefined);

      const configToImport = {
        snippets: [
          {
            id: 'imported-snippet',
            name: '导入的片段',
            code: 'imported code',
            language: 'typescript',
            tags: ['imported'],
            priority: 7,
            createdAt: Date.now(),
            updatedAt: Date.now(),
            isEnabled: true
          }
        ],
        rules: [
          {
            id: 'imported-rule',
            name: '导入的规则',
            content: 'imported rule content',
            language: 'typescript',
            priority: 6,
            createdAt: Date.now(),
            updatedAt: Date.now(),
            isEnabled: true
          }
        ],
        version: '1.0.0',
        lastUpdated: Date.now()
      };

      await manager.importConfig(JSON.stringify(configToImport));

      expect(mockFs.writeFile).toHaveBeenCalled();
    });

    it('应该能够重置到默认配置', async () => {
      mockFs.writeFile.mockResolvedValue(undefined);

      await manager.resetToDefaults();

      expect(mockFs.writeFile).toHaveBeenCalled();
      const writeCall = mockFs.writeFile.mock.calls[0];
      const writtenConfig = JSON.parse(writeCall[1]);
      expect(writtenConfig.snippets).toHaveLength(0);
      expect(writtenConfig.rules).toHaveLength(0);
      expect(writtenConfig.version).toBe('1.0.0');
    });
  });
});