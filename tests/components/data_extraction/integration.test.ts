
// Mock VSCode API
jest.mock('vscode', () => ({
  window: {
    createOutputChannel: jest.fn().mockReturnValue({
      appendLine: jest.fn(),
      append: jest.fn(),
      clear: jest.fn(),
      show: jest.fn(),
      hide: jest.fn(),
      dispose: jest.fn()
    }),
    showInformationMessage: jest.fn(),
    showErrorMessage: jest.fn(),
    showWarningMessage: jest.fn()
  },
  workspace: {
    workspaceFolders: [
      {
        uri: {
          fsPath: '/test/workspace'
        },
        name: 'test-workspace'
      }
    ],
    createFileSystemWatcher: jest.fn().mockReturnValue({
      onDidCreate: jest.fn().mockReturnValue({
        dispose: jest.fn()
      }),
      onDidChange: jest.fn().mockReturnValue({
        dispose: jest.fn()
      }),
      onDidDelete: jest.fn().mockReturnValue({
        dispose: jest.fn()
      }),
      dispose: jest.fn()
    }),
    fs: {
      readFile: jest.fn(),
      writeFile: jest.fn(),
      stat: jest.fn()
    }
  },
  Uri: {
    file: jest.fn((path) => ({ fsPath: path }))
  },
  EventEmitter: jest.fn().mockImplementation(() => ({
    event: jest.fn(),
    fire: jest.fn(),
    dispose: jest.fn()
  })),
  ExtensionContext: jest.fn().mockImplementation(() => ({
    subscriptions: [],
    globalState: {
      get: jest.fn(),
      update: jest.fn()
    },
    globalStorageUri: {
      fsPath: '/test/storage'
    }
  }))
}));

import { DataExtractor } from 'src/components/data_extraction/DataExtractor';
import { SFTSample } from 'src/components/data_extraction/types';

describe('数据提取功能集成测试', () => {
  let extractor: DataExtractor;
  let mockWorkspace: any;

  beforeEach(() => {
    extractor = new DataExtractor();
    
    // Mock vscode.workspace.fs.readFile
    mockWorkspace = {
      fs: {
        readFile: async () => Buffer.from('')
      }
    };
    
    // Mock vscode.workspace
    (global as any).vscode = {
      workspace: mockWorkspace,
      Uri: {
        file: jest.fn(f => ({ fsPath: f }))
      }
    };
  });

  describe('完整流程测试', () => {
    test('应该从复杂C代码中提取所有类型的样本', async () => {
      // 准备复杂的测试数据
      const testFilePath = '/test/path/complex.c';
      const testCode = `
#include <stdio.h>
#include <stdlib.h>

// 工具函数：计算阶乘
int factorial(int n) {
    if (n <= 1) {
        return 1;
    }
    return n * factorial(n - 1);
}

// 工具函数：计算斐波那契数
int fibonacci(int n) {
    if (n <= 1) {
        return n;
    }
    return fibonacci(n - 1) + fibonacci(n - 2);
}

// 主函数
int main() {
    int x = 10;
    int y = 20;
    
    // 计算阶乘
    int fact_result = factorial(x);
    printf("Factorial of %d: %d\\n", x, fact_result);
    
    // 计算斐波那契数
    int fib_result = fibonacci(y);
    printf("Fibonacci of %d: %d\\n", y, fib_result);
    
    return 0;
}
      `;

      mockWorkspace.fs.readFile = async () => Buffer.from(testCode);

      // 测试所有策略
      const [statementSamples, functionSamples, contextSamples] = await Promise.all([
        extractor.extractSFTData(testFilePath, 'statement'),
        extractor.extractSFTData(testFilePath, 'function'),
        extractor.extractSFTData(testFilePath, 'function_with_context')
      ]);

      // 验证语句级样本
      expect(statementSamples.length).toBeGreaterThanOrEqual(0);
      statementSamples.forEach((sample: SFTSample) => {
        expect(sample.hasOwnProperty('id')).toBe(true);
        expect(sample.file).toBe(testFilePath);
        expect(sample.language).toBe('c');
        expect(sample.hasOwnProperty('expected')).toBe(true);
        expect(sample.meta?.type).toBe('statement');
      });

      // 验证函数级样本
      expect(functionSamples.length).toBeGreaterThanOrEqual(0);
      functionSamples.forEach((sample: SFTSample) => {
        expect(sample.hasOwnProperty('id')).toBe(true);
        expect(sample.file).toBe(testFilePath);
        expect(sample.language).toBe('c');
        expect(sample.hasOwnProperty('expected')).toBe(true);
        expect(sample.meta?.type).toBe('function');
      });

      // 验证函数+上下文样本
      expect(contextSamples.length).toBeGreaterThanOrEqual(0);
      contextSamples.forEach((sample: SFTSample) => {
        expect(sample.hasOwnProperty('id')).toBe(true);
        expect(sample.file).toBe(testFilePath);
        expect(sample.language).toBe('c');
        expect(sample.hasOwnProperty('expected')).toBe(true);
        expect(sample.meta?.type).toBe('function_with_context');
      });
    });

    test('应该从NP代码中提取所有类型的样本', async () => {
      // 准备NP语言测试数据
      const testFilePath = '/test/path/complex.asm';
      const testCode = `
// NP语言复杂示例
function calculate_sum {
    statement1;
    statement2;
    return result;
}

function process_data {
    call calculate_sum;
    statement3;
    if condition {
        statement4;
    }
}

function main_function {
    call process_data;
    statement5;
}
      `;

      mockWorkspace.fs.readFile = async () => Buffer.from(testCode);

      // 测试所有策略
      const [statementSamples, functionSamples, contextSamples] = await Promise.all([
        extractor.extractSFTData(testFilePath, 'statement'),
        extractor.extractSFTData(testFilePath, 'function'),
        extractor.extractSFTData(testFilePath, 'function_with_context')
      ]);

      // 验证所有样本的语言类型
      [statementSamples, functionSamples, contextSamples].forEach(samples => {
        samples.forEach((sample: SFTSample) => {
          expect(sample.language).toBe('np');
        });
      });
    });

    test('应该使用extractAllStrategies提取所有策略', async () => {
      // 准备测试数据
      const testFilePath = '/test/path/test.c';
      const testCode = `
int add(int a, int b) {
    return a + b;
}

int main() {
    int result = add(1, 2);
    return result;
}
      `;

      mockWorkspace.fs.readFile = async () => Buffer.from(testCode);

      // 执行全策略提取
      const results = await extractor.extractAllStrategies(testFilePath);

      // 验证结果结构
      expect(results.hasOwnProperty('statement')).toBe(true);
      expect(results.hasOwnProperty('function')).toBe(true);
      expect(results.hasOwnProperty('function_with_context')).toBe(true);

      // 验证每种策略的结果类型
      expect(results.statement.every(s => s.meta?.type === 'statement')).toBe(true);
      expect(results.function.every(s => s.meta?.type === 'function')).toBe(true);
      expect(results.function_with_context.every(s => s.meta?.type === 'function_with_context')).toBe(true);
    });
  });

  describe('错误处理集成测试', () => {
    test('应该处理文件不存在的情况', async () => {
      const testFilePath = '/test/path/nonexistent.c';

      mockWorkspace.fs.readFile = async () => {
        throw new Error('File not found');
      };

      // 测试所有策略都应该返回空数组
      const [statementSamples, functionSamples, contextSamples] = await Promise.all([
        extractor.extractSFTData(testFilePath, 'statement'),
        extractor.extractSFTData(testFilePath, 'function'),
        extractor.extractSFTData(testFilePath, 'function_with_context')
      ]);

      expect(statementSamples.length).toBe(0);
      expect(functionSamples.length).toBe(0);
      expect(contextSamples.length).toBe(0);
    });

    test('应该处理语法错误的情况', async () => {
      const testFilePath = '/test/path/invalid.c';
      const invalidCode = 'int main() { invalid syntax {';

      mockWorkspace.fs.readFile = async () => Buffer.from(invalidCode);

      // 测试所有策略都应该优雅处理语法错误
      const [statementSamples, functionSamples, contextSamples] = await Promise.all([
        extractor.extractSFTData(testFilePath, 'statement'),
        extractor.extractSFTData(testFilePath, 'function'),
        extractor.extractSFTData(testFilePath, 'function_with_context')
      ]);

      // 应该返回空数组而不是抛出错误
      expect(Array.isArray(statementSamples)).toBe(true);
      expect(Array.isArray(functionSamples)).toBe(true);
      expect(Array.isArray(contextSamples)).toBe(true);
    });

    test('应该处理不支持的语言类型', async () => {
      const testFilePath = '/test/path/unsupported.xyz';
      const unsupportedCode = 'some unsupported language code';

      mockWorkspace.fs.readFile = async () => Buffer.from(unsupportedCode);

      // 测试所有策略都应该返回空数组
      const [statementSamples, functionSamples, contextSamples] = await Promise.all([
        extractor.extractSFTData(testFilePath, 'statement'),
        extractor.extractSFTData(testFilePath, 'function'),
        extractor.extractSFTData(testFilePath, 'function_with_context')
      ]);

      expect(statementSamples.length).toBe(0);
      expect(functionSamples.length).toBe(0);
      expect(contextSamples.length).toBe(0);
    });
  });

  describe('样本质量验证', () => {
    test('应该生成高质量的语句级样本', async () => {
      const testFilePath = '/test/path/quality.c';
      const testCode = `
int main() {
    int x = 10;
    int y = 20;
    int sum = x + y;
    printf("Sum: %d\\n", sum);
    return 0;
}
      `;

      mockWorkspace.fs.readFile = async () => Buffer.from(testCode);

      const samples = await extractor.extractSFTData(testFilePath, 'statement');

      samples.forEach((sample: SFTSample) => {
        // 验证基本属性
        expect(sample.hasOwnProperty('id')).toBe(true);
        expect(sample.file).toBe(testFilePath);
        expect(sample.language).toBe('c');

        // 验证内容质量
        expect(sample.expected?.trim().length).toBeGreaterThan(0);
        if (sample.above_cursor) {
          expect(sample.above_cursor.trim().length).toBeGreaterThan(0);
        }
        if (sample.below_cursor) {
          expect(sample.below_cursor.trim().length).toBeGreaterThan(0);
        }

        // 验证元数据
        expect(sample.meta?.type).toBe('statement');
        expect(sample.meta?.hasOwnProperty('nodeType')).toBe(true);
        expect(sample.meta?.hasOwnProperty('line')).toBe(true);
        expect(sample.meta?.hasOwnProperty('column')).toBe(true);
      });
    });

    test('应该生成高质量的函数级样本', async () => {
      const testFilePath = '/test/path/quality.c';
      const testCode = `
int add(int a, int b) {
    return a + b;
}

int subtract(int a, int b) {
    return a - b;
}
      `;

      mockWorkspace.fs.readFile = async () => Buffer.from(testCode);

      const samples = await extractor.extractSFTData(testFilePath, 'function');

      samples.forEach((sample: SFTSample) => {
        // 验证基本属性
        expect(sample.hasOwnProperty('id')).toBe(true);
        expect(sample.file).toBe(testFilePath);
        expect(sample.language).toBe('c');

        // 验证内容质量
        expect(sample.expected?.trim().length).toBeGreaterThan(0);
        expect(sample.expected).toMatch(/^(int add|int subtract)/);

        // 验证元数据
        expect(sample.meta?.type).toBe('function');
        expect(sample.meta?.hasOwnProperty('nodeType')).toBe(true);
        expect(sample.meta?.hasOwnProperty('line')).toBe(true);
        expect(sample.meta?.hasOwnProperty('column')).toBe(true);
      });
    });
  });
}); 