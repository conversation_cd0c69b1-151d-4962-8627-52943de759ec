
// Mock VSCode API
jest.mock('vscode', () => ({
  window: {
    createOutputChannel: jest.fn().mockReturnValue({
      appendLine: jest.fn(),
      append: jest.fn(),
      clear: jest.fn(),
      show: jest.fn(),
      hide: jest.fn(),
      dispose: jest.fn()
    }),
    showInformationMessage: jest.fn(),
    showErrorMessage: jest.fn(),
    showWarningMessage: jest.fn()
  },
  workspace: {
    workspaceFolders: [
      {
        uri: {
          fsPath: '/test/workspace'
        },
        name: 'test-workspace'
      }
    ],
    createFileSystemWatcher: jest.fn().mockReturnValue({
      onDidCreate: jest.fn().mockReturnValue({
        dispose: jest.fn()
      }),
      onDidChange: jest.fn().mockReturnValue({
        dispose: jest.fn()
      }),
      onDidDelete: jest.fn().mockReturnValue({
        dispose: jest.fn()
      }),
      dispose: jest.fn()
    }),
    fs: {
      readFile: jest.fn(),
      writeFile: jest.fn(),
      stat: jest.fn()
    }
  },
  Uri: {
    file: jest.fn((path) => ({ fsPath: path }))
  },
  EventEmitter: jest.fn().mockImplementation(() => ({
    event: jest.fn(),
    fire: jest.fn(),
    dispose: jest.fn()
  })),
  ExtensionContext: jest.fn().mockImplementation(() => ({
    subscriptions: [],
    globalState: {
      get: jest.fn(),
      update: jest.fn()
    },
    globalStorageUri: {
      fsPath: '/test/storage'
    }
  }))
}));

import { ASTStatementLevelExtractionStrategy } from 'src/components/data_extraction/ASTStatementLevelExtractionStrategy';
import { SFTSample } from 'src/components/data_extraction/types';

describe('ASTStatementLevelExtractionStrategy', () => {
  let strategy: ASTStatementLevelExtractionStrategy;
  let mockWorkspace: any;

  beforeEach(() => {
    strategy = new ASTStatementLevelExtractionStrategy();
    
    // Mock vscode.workspace.fs.readFile
    mockWorkspace = {
      fs: {
        readFile: async () => Buffer.from('')
      }
    };
    
    // Mock vscode.workspace
    (global as any).vscode = {
      workspace: mockWorkspace,
      Uri: {
        file: jest.fn(f => ({ fsPath: f }))
      }
    };
  });

  describe('extractSamples', () => {
    test('应该从C语言文件中提取语句级样本', async () => {
      // 准备测试数据
      const testFilePath = '/test/path/test.c';
      const testCode = `
#include <stdio.h>

int main() {
    int x = 10;
    printf("Hello, World!\\n");
    return 0;
}
      `;

      mockWorkspace.fs.readFile = async () => Buffer.from(testCode);

      // 执行测试
      const samples = await strategy.extractSamples(testFilePath);

      // 验证结果
      expect(samples).toBeDefined();
      expect(Array.isArray(samples)).toBe(true);
      expect(samples.length).toBeGreaterThanOrEqual(0);

      // 验证样本结构
      samples.forEach((sample: SFTSample) => {
        expect(sample.hasOwnProperty('id')).toBe(true);
        expect(sample.file).toBe(testFilePath);
        expect(sample.language).toBe('c');
        expect(sample.hasOwnProperty('expected')).toBe(true);
        expect(sample.hasOwnProperty('above_cursor')).toBe(true);
        expect(sample.hasOwnProperty('below_cursor')).toBe(true);
        expect(sample.hasOwnProperty('meta')).toBe(true);
        expect(sample.meta?.type).toBe('statement');
        expect(sample.meta?.hasOwnProperty('nodeType')).toBe(true);
        expect(sample.meta?.hasOwnProperty('line')).toBe(true);
        expect(sample.meta?.hasOwnProperty('column')).toBe(true);
      });
    });

    test('应该处理空文件', async () => {
      const testFilePath = '/test/path/empty.c';
      const emptyCode = '';

      mockWorkspace.fs.readFile = async () => Buffer.from(emptyCode);

      const samples = await strategy.extractSamples(testFilePath);

      expect(samples).toBeDefined();
      expect(Array.isArray(samples)).toBe(true);
      expect(samples.length).toBe(0);
    });

    test('应该处理无效语法', async () => {
      const testFilePath = '/test/path/invalid.c';
      const invalidCode = 'int main() { invalid syntax }';

      mockWorkspace.fs.readFile = async () => Buffer.from(invalidCode);

      const samples = await strategy.extractSamples(testFilePath);

      expect(samples).toBeDefined();
      expect(Array.isArray(samples)).toBe(true);
      expect(samples.length).toBe(0);
    });

    test('应该处理不支持的文件类型', async () => {
      const testFilePath = '/test/path/unsupported.xyz';
      const unsupportedCode = 'some code';

      mockWorkspace.fs.readFile = async () => Buffer.from(unsupportedCode);

      const samples = await strategy.extractSamples(testFilePath);

      expect(samples).toBeDefined();
      expect(Array.isArray(samples)).toBe(true);
      expect(samples.length).toBe(0);
    });
  });
}); 