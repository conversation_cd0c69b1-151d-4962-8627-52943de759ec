
// Mock VSCode API
jest.mock('vscode', () => ({
  window: {
    createOutputChannel: jest.fn().mockReturnValue({
      appendLine: jest.fn(),
      append: jest.fn(),
      clear: jest.fn(),
      show: jest.fn(),
      hide: jest.fn(),
      dispose: jest.fn()
    }),
    showInformationMessage: jest.fn(),
    showErrorMessage: jest.fn(),
    showWarningMessage: jest.fn()
  },
  workspace: {
    workspaceFolders: [
      {
        uri: {
          fsPath: '/test/workspace'
        },
        name: 'test-workspace'
      }
    ],
    createFileSystemWatcher: jest.fn().mockReturnValue({
      onDidCreate: jest.fn().mockReturnValue({
        dispose: jest.fn()
      }),
      onDidChange: jest.fn().mockReturnValue({
        dispose: jest.fn()
      }),
      onDidDelete: jest.fn().mockReturnValue({
        dispose: jest.fn()
      }),
      dispose: jest.fn()
    }),
    fs: {
      readFile: jest.fn(),
      writeFile: jest.fn(),
      stat: jest.fn()
    }
  },
  Uri: {
    file: jest.fn((path) => ({ fsPath: path }))
  },
  EventEmitter: jest.fn().mockImplementation(() => ({
    event: jest.fn(),
    fire: jest.fn(),
    dispose: jest.fn()
  })),
  ExtensionContext: jest.fn().mockImplementation(() => ({
    subscriptions: [],
    globalState: {
      get: jest.fn(),
      update: jest.fn()
    },
    globalStorageUri: {
      fsPath: '/test/storage'
    }
  }))
}));

import { DataExtractor } from 'src/components/data_extraction/DataExtractor';

describe('DataExtractor', () => {
  let extractor: DataExtractor;
  let mockWorkspace: any;

  beforeEach(() => {
    // Mock vscode.workspace.fs.readFile
    mockWorkspace = {
      fs: {
        readFile: jest.fn().mockResolvedValue(Buffer.from(''))
      }
    };

    // Mock vscode.workspace
    (global as any).vscode = {
      workspace: mockWorkspace,
      Uri: {
        file: jest.fn(f => ({ fsPath: f }))
      }
    };

    extractor = new DataExtractor();
  });

  describe('extractSFTData', () => {
    test('should extract data using statement strategy', async () => {
      const testCode = `
        int main() {
          int x = 10;
          printf("Hello, World!\\n");
          return 0;
        }
      `;

      mockWorkspace.fs.readFile.mockResolvedValue(Buffer.from(testCode));

      const samples = await extractor.extractSFTData('test.c', 'statement');
      expect(samples).toBeDefined();
      expect(Array.isArray(samples)).toBe(true);
    });

    test('should extract data using function strategy', async () => {
      const testCode = `
        int add(int a, int b) {
          return a + b;
        }

        int main() {
          return add(1, 2);
        }
      `;

      mockWorkspace.fs.readFile.mockResolvedValue(Buffer.from(testCode));

      const samples = await extractor.extractSFTData('test.c', 'function');
      expect(samples).toBeDefined();
      expect(Array.isArray(samples)).toBe(true);
    });

    test('should extract data using function_with_context strategy', async () => {
      const testCode = `
        int add(int a, int b) {
          return a + b;
        }

        int main() {
          int sum = add(1, 2);
          return sum;
        }
      `;

      mockWorkspace.fs.readFile.mockResolvedValue(Buffer.from(testCode));

      const samples = await extractor.extractSFTData('test.c', 'function_with_context');
      expect(samples).toBeDefined();
      expect(Array.isArray(samples)).toBe(true);
    });

    test('should handle file read errors', async () => {
      mockWorkspace.fs.readFile.mockRejectedValue(new Error('File not found'));

      const samples = await extractor.extractSFTData('nonexistent.c');
      expect(samples).toEqual([]);
    });

    test('should handle invalid strategy', async () => {
      await expect(extractor.extractSFTData('test.c', 'invalid' as any)).rejects.toThrow('不支持的提取策略');
    });
  });

  describe('extractAllStrategies', () => {
    test('should extract data using all strategies', async () => {
      const testCode = `
        int add(int a, int b) {
          return a + b;
        }

        int main() {
          int sum = add(1, 2);
          return sum;
        }
      `;

      mockWorkspace.fs.readFile.mockResolvedValue(Buffer.from(testCode));

      const results = await extractor.extractAllStrategies('test.c');
      expect(results).toBeDefined();
      expect(results.statement).toBeDefined();
      expect(results.function).toBeDefined();
      expect(results.function_with_context).toBeDefined();
      expect(Array.isArray(results.statement)).toBe(true);
      expect(Array.isArray(results.function)).toBe(true);
      expect(Array.isArray(results.function_with_context)).toBe(true);
    });

    test('should handle file read errors', async () => {
      mockWorkspace.fs.readFile.mockRejectedValue(new Error('File not found'));

      const results = await extractor.extractAllStrategies('nonexistent.c');
      expect(results).toEqual({
        statement: [],
        function: [],
        function_with_context: []
      });
    });
  });

  describe('extractSFTDataFromWorkspace', () => {
    test('should extract data from workspace', async () => {
      const mockFiles = [
        'test1.c',
        'test2.np',
        'test3.unsupported'
      ];

      // Mock workspace files
      (global as any).vscode.workspace.findFiles = jest.fn().mockResolvedValue(
        mockFiles.map(f => ({ fsPath: f }))
      );

      const samples = await extractor.extractSFTDataFromWorkspace();
      expect(samples).toBeDefined();
      expect(Array.isArray(samples)).toBe(true);
    });

    test('should handle workspace scan errors', async () => {
      // Mock workspace file scan error
      (global as any).vscode.workspace.findFiles = jest.fn().mockRejectedValue(
        new Error('Workspace scan failed')
      );

      const samples = await extractor.extractSFTDataFromWorkspace();
      expect(samples).toEqual([]);
    });
  });

  describe('error handling', () => {
    test('should handle null parameters', async () => {
      const samples = await extractor.extractSFTData(null as any);
      expect(samples).toEqual([]);
    });

    test('should handle undefined parameters', async () => {
      const samples = await extractor.extractSFTData(undefined as any);
      expect(samples).toEqual([]);
    });
  });
}); 