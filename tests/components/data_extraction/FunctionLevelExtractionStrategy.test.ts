
// Mock VSCode API
jest.mock('vscode', () => ({
  window: {
    createOutputChannel: jest.fn().mockReturnValue({
      appendLine: jest.fn(),
      append: jest.fn(),
      clear: jest.fn(),
      show: jest.fn(),
      hide: jest.fn(),
      dispose: jest.fn()
    }),
    showInformationMessage: jest.fn(),
    showErrorMessage: jest.fn(),
    showWarningMessage: jest.fn()
  },
  workspace: {
    workspaceFolders: [
      {
        uri: {
          fsPath: '/test/workspace'
        },
        name: 'test-workspace'
      }
    ],
    createFileSystemWatcher: jest.fn().mockReturnValue({
      onDidCreate: jest.fn().mockReturnValue({
        dispose: jest.fn()
      }),
      onDidChange: jest.fn().mockReturnValue({
        dispose: jest.fn()
      }),
      onDidDelete: jest.fn().mockReturnValue({
        dispose: jest.fn()
      }),
      dispose: jest.fn()
    }),
    fs: {
      readFile: jest.fn(),
      writeFile: jest.fn(),
      stat: jest.fn()
    }
  },
  Uri: {
    file: jest.fn((path) => ({ fsPath: path }))
  },
  EventEmitter: jest.fn().mockImplementation(() => ({
    event: jest.fn(),
    fire: jest.fn(),
    dispose: jest.fn()
  })),
  ExtensionContext: jest.fn().mockImplementation(() => ({
    subscriptions: [],
    globalState: {
      get: jest.fn(),
      update: jest.fn()
    },
    globalStorageUri: {
      fsPath: '/test/storage'
    }
  }))
}));

import { FunctionLevelExtractionStrategy } from 'src/components/data_extraction/FunctionLevelExtractionStrategy';
import { SFTSample } from 'src/components/data_extraction/types';

describe('FunctionLevelExtractionStrategy', () => {
  let strategy: FunctionLevelExtractionStrategy;
  let mockWorkspace: any;

  beforeEach(() => {
    strategy = new FunctionLevelExtractionStrategy();
    
    // Mock vscode.workspace.fs.readFile
    mockWorkspace = {
      fs: {
        readFile: async () => Buffer.from('')
      }
    };
    
    // Mock vscode.workspace
    (global as any).vscode = {
      workspace: mockWorkspace,
      Uri: {
        file: jest.fn(f => ({ fsPath: f }))
      }
    };
  });

  describe('extractSamples', () => {
    test('应该从C语言文件中提取函数级样本', async () => {
      // 准备测试数据
      const testFilePath = '/test/path/test.c';
      const testCode = `
#include <stdio.h>

int add(int a, int b) {
    int result = a + b;
    return result;
}

int main() {
    int x = 10;
    printf("Hello, World!\\n");
    return 0;
}
      `;

      mockWorkspace.fs.readFile = async () => Buffer.from(testCode);

      // 执行测试
      const samples = await strategy.extractSamples(testFilePath);

      // 验证结果
      expect(samples).toBeDefined();
      expect(Array.isArray(samples)).toBe(true);
      expect(samples.length).toBeGreaterThanOrEqual(0);
    });

    test('应该从NP语言文件中提取函数级样本', async () => {
      // 准备测试数据
      const testFilePath = '/test/path/test.asm';
      const testCode = `
function test_function {
    statement1;
    statement2;
    return;
}

function another_function {
    call test_function;
    statement3;
}
      `;

      mockWorkspace.fs.readFile = async () => Buffer.from(testCode);

      // 执行测试
      const samples = await strategy.extractSamples(testFilePath);

      // 验证结果
      expect(samples).toBeDefined();
      expect(Array.isArray(samples)).toBe(true);

      // 验证样本结构
      samples.forEach((sample: SFTSample) => {
        expect(sample.hasOwnProperty('id')).toBe(true);
        expect(sample.file).toBe(testFilePath);
        expect(sample.language).toBe('np');
        expect(sample.hasOwnProperty('expected')).toBe(true);
        expect(sample.hasOwnProperty('meta')).toBe(true);
        expect(sample.meta?.type).toBe('function');
      });
    });

    test('应该处理不支持的语言类型', async () => {
      // 准备测试数据
      const testFilePath = '/test/path/test.unsupported';
      const testCode = 'some unsupported code';

      mockWorkspace.fs.readFile = async () => Buffer.from(testCode);

      // 执行测试
      const samples = await strategy.extractSamples(testFilePath);

      // 验证结果
      expect(samples).toBeDefined();
      expect(Array.isArray(samples)).toBe(true);
      expect(samples.length).toBe(0);
    });

    test('应该处理文件读取错误', async () => {
      // 准备测试数据
      const testFilePath = '/test/path/nonexistent.c';

      mockWorkspace.fs.readFile = async () => {
        throw new Error('File not found');
      };

      // 执行测试
      const samples = await strategy.extractSamples(testFilePath);

      // 验证结果
      expect(samples).toBeDefined();
      expect(Array.isArray(samples)).toBe(true);
      expect(samples.length).toBe(0);
    });

    test('应该处理AST解析错误', async () => {
      // 准备测试数据
      const testFilePath = '/test/path/invalid.c';
      const invalidCode = 'invalid c code {';

      mockWorkspace.fs.readFile = async () => Buffer.from(invalidCode);

      // 执行测试
      const samples = await strategy.extractSamples(testFilePath);

      // 验证结果
      expect(samples).toBeDefined();
      expect(Array.isArray(samples)).toBe(true);
      // 即使AST解析失败，也应该返回空数组而不是抛出错误
      expect(samples.length).toBeGreaterThanOrEqual(0);
    });
  });

  describe('函数识别验证', () => {
    test('应该正确识别函数定义', async () => {
      // 准备测试数据
      const testFilePath = '/test/path/test.c';
      const testCode = `
int add(int a, int b) {
    return a + b;
}

void print_hello() {
    printf("Hello\\n");
}

static int internal_function() {
    return 42;
}
      `;

      mockWorkspace.fs.readFile = async () => Buffer.from(testCode);

      // 执行测试
      const samples = await strategy.extractSamples(testFilePath);

      // 验证结果
      expect(samples.length).toBeGreaterThanOrEqual(0);

      // 验证至少有一个样本包含有效的函数内容
      const hasValidFunction = samples.some((sample: SFTSample) => 
        sample.expected && 
        sample.expected.trim().length > 0 &&
        (sample.expected.includes('int add') || 
         sample.expected.includes('void print_hello') ||
         sample.expected.includes('static int internal_function'))
      );

      expect(hasValidFunction || samples.length === 0).toBe(true);
    });

    test('应该正确设置函数样本ID', async () => {
      // 准备测试数据
      const testFilePath = '/test/path/test.c';
      const testCode = `
int add(int a, int b) {
    return a + b;
}
      `;

      mockWorkspace.fs.readFile = async () => Buffer.from(testCode);

      // 执行测试
      const samples = await strategy.extractSamples(testFilePath);

      // 验证结果
      samples.forEach((sample: SFTSample) => {
        const expectedPattern = new RegExp(`^${testFilePath.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}::.*$`);
        expect(expectedPattern.test(sample.id)).toBe(true);
      });
    });

    test('应该提取函数元数据', async () => {
      // 准备测试数据
      const testFilePath = '/test/path/test.c';
      const testCode = `
int add(int a, int b) {
    return a + b;
}
      `;

      mockWorkspace.fs.readFile = async () => Buffer.from(testCode);

      // 执行测试
      const samples = await strategy.extractSamples(testFilePath);

      // 验证结果
      samples.forEach((sample: SFTSample) => {
        expect(sample.meta?.hasOwnProperty('type')).toBe(true);
        expect(sample.meta?.hasOwnProperty('nodeType')).toBe(true);
        expect(sample.meta?.hasOwnProperty('line')).toBe(true);
        expect(sample.meta?.hasOwnProperty('column')).toBe(true);
        expect(sample.meta?.type).toBe('function');
      });
    });
  });
}); 