
// Mock VSCode API
jest.mock('vscode', () => ({
  window: {
    createOutputChannel: jest.fn().mockReturnValue({
      appendLine: jest.fn(),
      append: jest.fn(),
      clear: jest.fn(),
      show: jest.fn(),
      hide: jest.fn(),
      dispose: jest.fn()
    }),
    showInformationMessage: jest.fn(),
    showErrorMessage: jest.fn(),
    showWarningMessage: jest.fn()
  },
  workspace: {
    workspaceFolders: [
      {
        uri: {
          fsPath: '/test/workspace'
        },
        name: 'test-workspace'
      }
    ],
    createFileSystemWatcher: jest.fn().mockReturnValue({
      onDidCreate: jest.fn().mockReturnValue({
        dispose: jest.fn()
      }),
      onDidChange: jest.fn().mockReturnValue({
        dispose: jest.fn()
      }),
      onDidDelete: jest.fn().mockReturnValue({
        dispose: jest.fn()
      }),
      dispose: jest.fn()
    }),
    fs: {
      readFile: jest.fn(),
      writeFile: jest.fn(),
      stat: jest.fn()
    }
  },
  Uri: {
    file: jest.fn((path) => ({ fsPath: path }))
  },
  EventEmitter: jest.fn().mockImplementation(() => ({
    event: jest.fn(),
    fire: jest.fn(),
    dispose: jest.fn()
  })),
  ExtensionContext: jest.fn().mockImplementation(() => ({
    subscriptions: [],
    globalState: {
      get: jest.fn(),
      update: jest.fn()
    },
    globalStorageUri: {
      fsPath: '/test/storage'
    }
  }))
}));

import * as AllTypes from 'src/components/types/index';
import {
    StatisticsEventType,
    UserType
} from 'src/components/types/index';

describe('Types Import/Export Tests', () => {
  describe('Bulk Import Test', () => {
    it('should export enums correctly', () => {
      expect(AllTypes.UserType).toBeDefined();
      expect(AllTypes.StatisticsEventType).toBeDefined();
      expect(AllTypes.UserType.HUAWEI).toBe('huawei');
      expect(AllTypes.StatisticsEventType.COMPLETION_TRIGGERED).toBe('COMPLETION_TRIGGERED');
    });

    it('should export runtime values correctly', () => {
      // Only test runtime values, not TypeScript interfaces
      expect(AllTypes).toHaveProperty('StatisticsEventType');
      expect(AllTypes).toHaveProperty('UserType');
    });
  });

  describe('Selective Import Test', () => {
    it('should allow selective imports of enums', () => {
      expect(StatisticsEventType).toBeDefined();
      expect(UserType).toBeDefined();
      expect(StatisticsEventType.COMPLETION_ACCEPTED).toBe('COMPLETION_ACCEPTED');
      expect(UserType.GITHUB).toBe('github');
    });

    it('should allow creating instances with imported types', () => {
      // Test that we can use the types in type annotations
      const userInfo = {
        id: 'test',
        name: 'Test User',
        nickname: 'test',
        department: 'Engineering'
      };

      const contextItem = {
        source: 'graph' as const,
        score: 0.95,
        content: 'test content'
      };

      expect(userInfo.name).toBe('Test User');
      expect(contextItem.source).toBe('graph');
    });
  });

  describe('Type Compatibility Test', () => {
    it('should maintain type compatibility between related types', () => {
      // Test that IUserInfo can be used where expected
      const userInfo = {
        id: 'user-1',
        name: 'Test User',
        nickname: 'test',
        department: 'Engineering'
      };

      // This should work without type errors
      const completionMeta = {
        fileContent: 'test',
        rawContextList: [],
        selectedContext: [],
        environment: {
          platform: 'test',
          deviceInfo: 'test',
          osInfo: 'test',
          vscodeVersion: '1.0.0',
          extensionVersion: '1.0.0'
        },
        user: userInfo // IUserInfo should be compatible here
      };

      expect(completionMeta.user.name).toBe('Test User');
    });

    it('should allow enum values to be used in type definitions', () => {
      const eventType: StatisticsEventType = StatisticsEventType.COMPLETION_ACCEPTED;
      expect(eventType).toBe('COMPLETION_ACCEPTED');
    });
  });

  describe('Module Structure Test', () => {
    it('should have correct module structure', () => {
      // Test that the module exports are properly structured
      const moduleKeys = Object.keys(AllTypes);
      
      // Should have at least the enums we know exist
      expect(moduleKeys.length).toBeGreaterThan(0);
      
      // Should include the enums we know exist
      expect(moduleKeys).toContain('StatisticsEventType');
      expect(moduleKeys).toContain('UserType');
    });
  });
}); 