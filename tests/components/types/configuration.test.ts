
// Mock VSCode API
jest.mock('vscode', () => ({
  window: {
    createOutputChannel: jest.fn().mockReturnValue({
      appendLine: jest.fn(),
      append: jest.fn(),
      clear: jest.fn(),
      show: jest.fn(),
      hide: jest.fn(),
      dispose: jest.fn()
    }),
    showInformationMessage: jest.fn(),
    showErrorMessage: jest.fn(),
    showWarningMessage: jest.fn()
  },
  workspace: {
    workspaceFolders: [
      {
        uri: {
          fsPath: '/test/workspace'
        },
        name: 'test-workspace'
      }
    ],
    createFileSystemWatcher: jest.fn().mockReturnValue({
      onDidCreate: jest.fn().mockReturnValue({
        dispose: jest.fn()
      }),
      onDidChange: jest.fn().mockReturnValue({
        dispose: jest.fn()
      }),
      onDidDelete: jest.fn().mockReturnValue({
        dispose: jest.fn()
      }),
      dispose: jest.fn()
    }),
    fs: {
      readFile: jest.fn(),
      writeFile: jest.fn(),
      stat: jest.fn()
    }
  },
  Uri: {
    file: jest.fn((path) => ({ fsPath: path }))
  },
  EventEmitter: jest.fn().mockImplementation(() => ({
    event: jest.fn(),
    fire: jest.fn(),
    dispose: jest.fn()
  })),
  ExtensionContext: jest.fn().mockImplementation(() => ({
    subscriptions: [],
    globalState: {
      get: jest.fn(),
      update: jest.fn()
    },
    globalStorageUri: {
      fsPath: '/test/storage'
    }
  }))
}));

import { ConfigurationManager } from 'src/common/config/ConfigurationManager';

// Mock vscode
jest.mock('vscode', () => ({
  workspace: {
    getConfiguration: jest.fn((section) => ({
      get: jest.fn((key) => {
        if (section === 'code-partner') {
          switch (key) {
            case 'usePythonService':
              return true;
            case 'pythonEndpoint':
              return 'http://127.0.0.1:5555/api/v1/completion';
            case 'openAIApiKey':
              return 'test-api-key';
            case 'statisticsEndpoint':
              return 'https://api.example.com/stats';
            case 'enableStatistics':
              return true;
            case 'maxTokens':
              return 1000;
            case 'temperature':
              return 0.7;
            case 'enableInlineCompletion':
              return true;
            case 'enableDiffHighlightCompletion':
              return true;
            case 'enableIndexing':
              return true;
            case 'autoIndexing':
              return true;
            case 'languageExtensions':
              return ['typescript', 'javascript'];
            case 'graphWeight':
              return 0.4;
            case 'bm25Weight':
              return 0.3;
            case 'embeddingWeight':
              return 0.3;
            case 'logLevel':
              return 'INFO';
            case 'enableDebugLog':
              return false;
            default:
              return undefined;
          }
        }
        return undefined;
      }),
      update: jest.fn(),
      onDidChangeConfiguration: jest.fn()
    })),
    onDidChangeConfiguration: jest.fn()
  },
  ConfigurationTarget: {
    Global: 1,
    Workspace: 2
  },
  EventEmitter: jest.fn().mockImplementation(() => ({
    fire: jest.fn(),
    event: jest.fn()
  }))
}));

describe('Configuration Integration Tests', () => {
  let configManager: ConfigurationManager;

  beforeEach(() => {
    configManager = ConfigurationManager.getInstance();
  });

  describe('Configuration Reading', () => {
    it('should read complete configuration', () => {
      const config = configManager.getConfiguration();
      expect(config).toEqual({
        usePythonService: true,
        pythonEndpoint: 'http://127.0.0.1:5555/api/v1/completion',
        openAIApiKey: 'test-api-key',
        model: 'qwen2.5-coder:3b',
        statisticsEndpoint: 'https://api.example.com/stats',
        enableStatistics: true,
        maxTokens: 1000,
        temperature: 0.7,
        enableInlineCompletion: true,
        enableDiffHighlightCompletion: true,
        enableIndexing: true,
        autoIndexing: true,
        languageExtensions: ['typescript', 'javascript'],
        graphWeight: 0.4,
        bm25Weight: 0.3,
        embeddingWeight: 0.3,
        logLevel: 'INFO',
        enableDebugLog: false
      });
    });

    it('should read individual configuration values', () => {
      expect(configManager.get('usePythonService')).toBe(true);
      expect(configManager.get('openAIApiKey')).toBe('test-api-key');
      expect(configManager.get('maxTokens')).toBe(1000);
      expect(configManager.get('temperature')).toBe(0.7);
      expect(configManager.get('enableStatistics')).toBe(true);
    });
  });

  describe('Configuration Validation', () => {
    it('should validate configuration correctly', () => {
      const validation = configManager.validateConfiguration();
      expect(validation.isValid).toBe(true);
      expect(validation.errors).toHaveLength(0);
    });

    it('should detect invalid weight configuration', () => {
      // Mock invalid weights - 需要mock所有配置项，因为getConfiguration会调用所有配置
      const mockGet = jest.fn((key) => {
        // 返回默认值，但权重设置为无效值
        const defaults = {
          usePythonService: true,
          pythonEndpoint: 'http://127.0.0.1:5555/api/v1/completion',
          openAIApiKey: '',
          statisticsEndpoint: 'http://127.0.0.1:5555/api/v1/statistics/events',
          enableStatistics: true,
          maxTokens: 256,
          temperature: 0.2,
          enableInlineCompletion: true,
          enableDiffHighlightCompletion: true,
          enableIndexing: true,
          languageExtensions: ['*'],
          graphWeight: 0.5, // 无效权重
          bm25Weight: 0.5,  // 无效权重
          embeddingWeight: 0.5, // 无效权重
          logLevel: 'INFO',
          enableDebugLog: false
        };
        return defaults[key as keyof typeof defaults];
      });

      const mockConfig = {
        get: mockGet,
        update: jest.fn(),
        onDidChangeConfiguration: jest.fn()
      };

      require('vscode').workspace.getConfiguration.mockReturnValue(mockConfig);

      // 重置单例以使用新的mock
      (ConfigurationManager as any).instance = undefined;
      const newConfigManager = ConfigurationManager.getInstance();
      const validation = newConfigManager.validateConfiguration();

      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain('权重配置总和应为1.0，当前为1.50');
    });
  });

  describe('Configuration Updates', () => {
    it('should update configuration values', async () => {
      const mockUpdate = jest.fn().mockResolvedValue(undefined);
      const mockConfig = {
        get: jest.fn(),
        update: mockUpdate,
        onDidChangeConfiguration: jest.fn()
      };

      require('vscode').workspace.getConfiguration.mockReturnValue(mockConfig);

      // 重置单例以使用新的mock
      (ConfigurationManager as any).instance = undefined;
      const newConfigManager = ConfigurationManager.getInstance();

      await newConfigManager.update('maxTokens', 2000);
      expect(mockUpdate).toHaveBeenCalledWith('maxTokens', 2000, 1);
    });

    it('should update workspace configuration', async () => {
      const mockUpdate = jest.fn().mockResolvedValue(undefined);
      const mockConfig = {
        get: jest.fn(),
        update: mockUpdate,
        onDidChangeConfiguration: jest.fn()
      };

      require('vscode').workspace.getConfiguration.mockReturnValue(mockConfig);

      // 重置单例以使用新的mock
      (ConfigurationManager as any).instance = undefined;
      const newConfigManager = ConfigurationManager.getInstance();

      await newConfigManager.updateWorkspace('temperature', 0.5);
      expect(mockUpdate).toHaveBeenCalledWith('temperature', 0.5, 2);
    });
  });

  describe('Configuration Summary', () => {
    it('should generate configuration summary', () => {
      const summary = configManager.getConfigurationSummary();
      expect(summary).toContain('LLM服务: Python');
      expect(summary).toContain('统计服务: 启用');
      expect(summary).toContain('行内补全: 启用');
    });
  });
}); 