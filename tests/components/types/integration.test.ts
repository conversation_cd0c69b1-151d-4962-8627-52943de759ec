
// Mock VSCode API
jest.mock('vscode', () => ({
  window: {
    createOutputChannel: jest.fn().mockReturnValue({
      appendLine: jest.fn(),
      append: jest.fn(),
      clear: jest.fn(),
      show: jest.fn(),
      hide: jest.fn(),
      dispose: jest.fn()
    }),
    showInformationMessage: jest.fn(),
    showErrorMessage: jest.fn(),
    showWarningMessage: jest.fn()
  },
  workspace: {
    workspaceFolders: [
      {
        uri: {
          fsPath: '/test/workspace'
        },
        name: 'test-workspace'
      }
    ],
    createFileSystemWatcher: jest.fn().mockReturnValue({
      onDidCreate: jest.fn().mockReturnValue({
        dispose: jest.fn()
      }),
      onDidChange: jest.fn().mockReturnValue({
        dispose: jest.fn()
      }),
      onDidDelete: jest.fn().mockReturnValue({
        dispose: jest.fn()
      }),
      dispose: jest.fn()
    }),
    fs: {
      readFile: jest.fn(),
      writeFile: jest.fn(),
      stat: jest.fn()
    }
  },
  Uri: {
    file: jest.fn((path) => ({ fsPath: path }))
  },
  EventEmitter: jest.fn().mockImplementation(() => ({
    event: jest.fn(),
    fire: jest.fn(),
    dispose: jest.fn()
  })),
  ExtensionContext: jest.fn().mockImplementation(() => ({
    subscriptions: [],
    globalState: {
      get: jest.fn(),
      update: jest.fn()
    },
    globalStorageUri: {
      fsPath: '/test/storage'
    }
  }))
}));

import { buildEnvironmentAndUserInfo } from 'src/components/code_completion/utils/environmentBuilder';
import { StatisticsService } from 'src/components/statistics/StatisticsService';
import {
    CompletionMeta,
    CompletionRequestPayload,
    IUserInfo,
    StatisticsEventType
} from 'src/components/types/index';

// Mock vscode - 使用更完整的mock
jest.mock('vscode', () => ({
  version: '1.80.0',
  env: {
    language: 'en'
  },
  window: {
    createOutputChannel: jest.fn(() => ({
      appendLine: jest.fn(),
      show: jest.fn(),
      clear: jest.fn(),
      dispose: jest.fn()
    })),
    showInformationMessage: jest.fn(),
    showErrorMessage: jest.fn(),
    showWarningMessage: jest.fn(),
    activeTextEditor: {
      document: {
        uri: { fsPath: '/test/file.ts' },
        languageId: 'typescript',
        lineAt: jest.fn(() => ({ text: 'function test() { return true; }' })),
        getText: jest.fn(() => 'function test() { return true; }'),
        lineCount: 1
      },
      selection: {
        active: { line: 0, character: 10 }
      }
    }
  },
  workspace: {
    name: 'test-workspace',
    workspaceFile: { toString: () => 'file:///test/workspace.code-workspace' },
    workspaceFolders: [
      { uri: { fsPath: '/test/folder1' } },
      { uri: { fsPath: '/test/folder2' } }
    ],
    getConfiguration: jest.fn((section) => ({
      get: jest.fn((key) => {
        if (section === 'workbench' && key === 'colorTheme') { return 'dark+'; }
        if (section === 'editor' && key === 'fontSize') { return 14; }
        if (section === 'code-partner') {
          // 返回默认配置值
          const defaults = {
            usePythonService: true,
            pythonEndpoint: 'http://127.0.0.1:5555/api/v1/completion',
            openAIApiKey: '',
            model: 'qwen2.5-coder:3b',
            statisticsEndpoint: 'http://127.0.0.1:5555/api/v1/statistics/events',
            enableStatistics: true,
            maxTokens: 256,
            temperature: 0.2,
            enableInlineCompletion: true,
            enableDiffHighlightCompletion: true,
            enableIndexing: true,
            autoIndexing: true,
            languageExtensions: ['*'],
            graphWeight: 0.4,
            bm25Weight: 0.3,
            embeddingWeight: 0.3,
            logLevel: 'INFO',
            enableDebugLog: false
          };
          return defaults[key as keyof typeof defaults];
        }
        return undefined;
      }),
      update: jest.fn()
    })),
    onDidChangeConfiguration: jest.fn()
  },
  extensions: {
    getExtension: jest.fn(() => ({
      packageJSON: { version: '1.0.0' }
    }))
  },
  EventEmitter: jest.fn().mockImplementation(() => ({
    fire: jest.fn(),
    event: jest.fn()
  })),
  Position: jest.fn().mockImplementation((line, character) => ({ line, character })),
  Range: jest.fn().mockImplementation((start, end) => ({ start, end }))
}));

// Mock UserService
jest.mock('src/components/user/UserService', () => ({
  getCurrentUserInfo: jest.fn()
}));

describe('Types Integration Tests', () => {
  describe('Environment and User Info Integration', () => {
    it('should build environment and user info with correct types', async () => {
      const mockUserInfo: IUserInfo = {
        id: 'test-user',
        name: 'Test User',
        nickname: 'test',
        department: 'Engineering'
      };

      const { getCurrentUserInfo } = require('src/components/user/UserService');
      getCurrentUserInfo.mockResolvedValue(mockUserInfo);

      const { environment, user } = await buildEnvironmentAndUserInfo();

      // Verify environment info structure
      expect(environment).toMatchObject({
        platform: expect.any(String),
        deviceInfo: expect.any(String),
        osInfo: expect.any(String),
        vscodeVersion: '1.80.0',
        extensionVersion: '1.0.0',
        workspaceInfo: {
          name: 'test-workspace',
          uri: 'file:///test/workspace.code-workspace',
          folders: ['/test/folder1', '/test/folder2']
        }
      });

      // Verify user info structure
      expect(user).toEqual(mockUserInfo);
    });
  });

  describe('Completion Request Payload Integration', () => {
    it('should create a valid CompletionRequestPayload', async () => {
      const mockUserInfo: IUserInfo = {
        id: 'test-user',
        name: 'Test User',
        nickname: 'test',
        department: 'Engineering'
      };

      const { getCurrentUserInfo } = require('src/components/user/UserService');
      getCurrentUserInfo.mockResolvedValue(mockUserInfo);

      const { environment, user } = await buildEnvironmentAndUserInfo();

      const completionMeta: CompletionMeta = {
        fileContent: 'function test() {\n  // test content\n}',
        rawContextList: [],
        selectedContext: [],
        fileName: 'test.ts',
        languageId: 'typescript',
        cursorLine: 2,
        cursorColumn: 3,
        environment,
        user
      };

      const payload: CompletionRequestPayload = {
        prompt: 'Complete the function',
        modelConfig: {
          model: 'test-model',
          max_tokens: 100,
          temperature: 0.2,
          stream: false
        },
        meta: completionMeta,
        priority: 'normal',
        tags: ['test', 'completion']
      };

      // Verify payload structure
      expect(payload.prompt).toBe('Complete the function');
      expect(payload.modelConfig.model).toBe('test-model');
      expect(payload.meta.fileName).toBe('test.ts');
      expect(payload.meta.languageId).toBe('typescript');
      expect(payload.meta.environment).toBe(environment);
      expect(payload.meta.user).toBe(user);
      expect(payload.priority).toBe('normal');
      expect(payload.tags).toEqual(['test', 'completion']);
    });
  });

  describe('Statistics Service Integration', () => {
    it('should report completion feedback with correct types', async () => {
      const statisticsService = StatisticsService.getInstance();

      // Mock the reportEvent method to avoid actual network calls
      const mockReportEvent = jest.fn().mockResolvedValue(undefined);
      (statisticsService as any).reportEventInternal = mockReportEvent;

      const mockUserInfo: IUserInfo = {
        id: 'test-user',
        name: 'Test User',
        nickname: 'test',
        department: 'Engineering'
      };

      const { getCurrentUserInfo } = require('src/components/user/UserService');
      getCurrentUserInfo.mockResolvedValue(mockUserInfo);

      const { environment, user } = await buildEnvironmentAndUserInfo();

      const completionMeta: CompletionMeta = {
        fileContent: 'function test() {\n  return true;\n}',
        rawContextList: [],
        selectedContext: [],
        fileName: 'test.ts',
        languageId: 'typescript',
        cursorLine: 2,
        cursorColumn: 3,
        environment,
        user
      };

      await statisticsService.reportCurrentCompletionFeedback(
        StatisticsEventType.COMPLETION_ACCEPTED,
        'return true;',
        '',
        'User accepted the suggestion'
      );

      // Verify that reportEvent was called with correct structure
      expect(mockReportEvent).toHaveBeenCalledWith(
        expect.objectContaining({
          eventType: StatisticsEventType.COMPLETION_ACCEPTED,
          data: expect.objectContaining({
            suggestionText: 'return true;',
            originalText: '',
            feedback: 'User accepted the suggestion'
          })
        })
      );
    });
  });

  describe('Type Compatibility in Real Scenarios', () => {
    it('should maintain type compatibility in completion workflow', async () => {
      const statisticsService = StatisticsService.getInstance();

      // Mock the reportEvent method to avoid actual network calls
      const mockReportEvent = jest.fn().mockResolvedValue(undefined);
      (statisticsService as any).reportEventInternal = mockReportEvent;

      const mockUserInfo: IUserInfo = {
        id: 'test-user',
        name: 'Test User',
        nickname: 'test',
        department: 'Engineering'
      };

      const { getCurrentUserInfo } = require('src/components/user/UserService');
      getCurrentUserInfo.mockResolvedValue(mockUserInfo);

      const { environment, user } = await buildEnvironmentAndUserInfo();

      const completionMeta: CompletionMeta = {
        fileContent: 'function test() {\n  return true;\n}',
        rawContextList: [],
        selectedContext: [],
        fileName: 'test.ts',
        languageId: 'typescript',
        cursorLine: 2,
        cursorColumn: 3,
        environment,
        user
      };

      const payload: CompletionRequestPayload = {
        prompt: 'Complete the function',
        modelConfig: {
          model: 'test-model',
          max_tokens: 100,
          temperature: 0.2,
          stream: false
        },
        meta: completionMeta,
        priority: 'normal',
        tags: ['test', 'completion']
      };

      const completionText = 'return true;';

      // 模拟用户接受建议
      await statisticsService.reportCurrentCompletionFeedback(
        StatisticsEventType.COMPLETION_ACCEPTED,
        completionText,
        '',
        'User accepted the suggestion'
      );

      // Verify payload structure
      expect(payload.prompt).toBe('Complete the function');
      expect(payload.modelConfig.model).toBe('test-model');
      expect(payload.meta.fileName).toBe('test.ts');
      expect(payload.meta.languageId).toBe('typescript');
      expect(payload.meta.user).toBe(user);
      expect(payload.meta.environment).toBe(environment);
      expect(mockReportEvent).toHaveBeenCalledWith(
        expect.objectContaining({
          eventType: StatisticsEventType.COMPLETION_ACCEPTED,
          data: expect.objectContaining({
            suggestionText: completionText
          })
        })
      );
    });
  });
}); 