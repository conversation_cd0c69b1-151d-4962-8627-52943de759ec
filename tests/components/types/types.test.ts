
// Mock VSCode API
jest.mock('vscode', () => ({
  window: {
    createOutputChannel: jest.fn().mockReturnValue({
      appendLine: jest.fn(),
      append: jest.fn(),
      clear: jest.fn(),
      show: jest.fn(),
      hide: jest.fn(),
      dispose: jest.fn()
    }),
    showInformationMessage: jest.fn(),
    showErrorMessage: jest.fn(),
    showWarningMessage: jest.fn()
  },
  workspace: {
    workspaceFolders: [
      {
        uri: {
          fsPath: '/test/workspace'
        },
        name: 'test-workspace'
      }
    ],
    createFileSystemWatcher: jest.fn().mockReturnValue({
      onDidCreate: jest.fn().mockReturnValue({
        dispose: jest.fn()
      }),
      onDidChange: jest.fn().mockReturnValue({
        dispose: jest.fn()
      }),
      onDidDelete: jest.fn().mockReturnValue({
        dispose: jest.fn()
      }),
      dispose: jest.fn()
    }),
    fs: {
      readFile: jest.fn(),
      writeFile: jest.fn(),
      stat: jest.fn()
    }
  },
  Uri: {
    file: jest.fn((path) => ({ fsPath: path }))
  },
  EventEmitter: jest.fn().mockImplementation(() => ({
    event: jest.fn(),
    fire: jest.fn(),
    dispose: jest.fn()
  })),
  ExtensionContext: jest.fn().mockImplementation(() => ({
    subscriptions: [],
    globalState: {
      get: jest.fn(),
      update: jest.fn()
    },
    globalStorageUri: {
      fsPath: '/test/storage'
    }
  }))
}));

import {
    ASTNode,
    CodeEdge,
    // code_completion types
    CompletionRequestPayload,
    CompletionScope,
    ContextItem,
    ContextProvider,
    ContextSnippet,
    DataExtractionStrategy,
    EmbeddingContextProvider,
    EnvironmentInfo,
    GraphCodeNode,
    GraphContextProvider,
    // user types
    IUser,
    IUserInfo,
    KeywordContextProvider,
    // code_graph types
    Language,
    NodeKind,
    RetrievalFusionConfig,
    // data_extraction types
    SFTSample,
    // code_context types
    SourceLocation,
    Scope as StatisticsCompletionScope,
    // statistics types
    StatisticsEventType,
    UserInfo as StatisticsUserInfo,
    StatisticsEvent as UnifiedStatisticsEvent,
    UserType
} from 'src/components/types/index';

describe('Types Definition Tests', () => {
  describe('Code Completion Types', () => {
    it('should define CompletionRequestPayload correctly', () => {
      const payload: CompletionRequestPayload = {
        prompt: 'test prompt',
        modelConfig: {
          model: 'test-model',
          max_tokens: 100,
          temperature: 0.2,
          stream: false
        },
        meta: {
          fileContent: 'test content',
          rawContextList: [],
          selectedContext: [],
          fileName: 'test.ts',
          languageId: 'typescript',
          cursorLine: 1,
          cursorColumn: 1,
          environment: {
            platform: 'test-platform',
            deviceInfo: 'test-device',
            osInfo: 'test-os',
            vscodeVersion: '1.0.0',
            extensionVersion: '1.0.0'
          },
          user: {
            id: 'test-user',
            name: 'Test User',
            nickname: 'test',
            department: 'Engineering'
          }
        }
      };

      expect(payload.prompt).toBe('test prompt');
      expect(payload.modelConfig.model).toBe('test-model');
      expect(payload.meta.environment?.platform).toBe('test-platform');
      expect(payload.meta.user?.name).toBe('Test User');
    });

    it('should define EnvironmentInfo correctly', () => {
      const envInfo: EnvironmentInfo = {
        platform: 'darwin-x64',
        deviceInfo: 'darwin-x64-node-v20.0.0',
        osInfo: 'darwin-node-v20.0.0',
        vscodeVersion: '1.80.0',
        extensionVersion: '1.0.0',
        workspaceInfo: {
          name: 'test-workspace',
          uri: 'file:///test/workspace.code-workspace',
          folders: ['/test/folder1', '/test/folder2']
        }
      };

      expect(envInfo.platform).toBe('darwin-x64');
      expect(envInfo.workspaceInfo?.name).toBe('test-workspace');
    });

    it('should define ContextSnippet correctly', () => {
      const snippet: ContextSnippet = {
        contextType: 'semantic',
        codeSnippetType: 'function',
        fileName: 'test.ts',
        content: 'function test() {}',
        startLine: 1,
        startColumn: 1,
        endLine: 1,
        endColumn: 20,
        label: 'test function'
      };

      expect(snippet.contextType).toBe('semantic');
      expect(snippet.codeSnippetType).toBe('function');
      expect(snippet.label).toBe('test function');
    });

    it('should define CompletionScope correctly', () => {
      const scope: CompletionScope = {
        scopeType: 'function',
        name: 'testFunction',
        startLine: 1,
        startColumn: 1,
        endLine: 10,
        endColumn: 1
      };

      expect(scope.scopeType).toBe('function');
      expect(scope.name).toBe('testFunction');
    });
  });

  describe('User Types', () => {
    it('should define IUserInfo correctly', () => {
      const userInfo: IUserInfo = {
        id: 'user-123',
        name: 'John Doe',
        nickname: 'john',
        department: 'Engineering'
      };

      expect(userInfo.id).toBe('user-123');
      expect(userInfo.name).toBe('John Doe');
    });

    it('should define UserType enum correctly', () => {
      expect(UserType.HUAWEI).toBe('huawei');
      expect(UserType.GITHUB).toBe('github');
      expect(UserType.MICROSOFT).toBe('microsoft');
      expect(UserType.GITLAB).toBe('gitlab');
      expect(UserType.DEBUG).toBe('debug');
    });

    it('should define IUser interface structure', () => {
      // This test verifies the interface structure exists
      const mockUser: IUser = {
        type: UserType.GITHUB,
        info: {
          id: 'test',
          name: 'Test User',
          nickname: 'test',
          department: 'Engineering'
        },
        getRaw: () => ({}),
        init: async () => {},
        isLoggedIn: () => true
      };

      expect(mockUser.type).toBe(UserType.GITHUB);
      expect(mockUser.info?.name).toBe('Test User');
      expect(typeof mockUser.getRaw).toBe('function');
      expect(typeof mockUser.init).toBe('function');
      expect(typeof mockUser.isLoggedIn).toBe('function');
    });
  });

  describe('Statistics Types', () => {
    it('should define StatisticsEventType enum correctly', () => {
      expect(StatisticsEventType.COMPLETION_TRIGGERED).toBe('COMPLETION_TRIGGERED');
      expect(StatisticsEventType.COMPLETION_ACCEPTED).toBe('COMPLETION_ACCEPTED');
      expect(StatisticsEventType.COMPLETION_REJECTED).toBe('COMPLETION_REJECTED');
    });

    it('should define StatisticsCompletionScope correctly', () => {
      const scope: StatisticsCompletionScope = {
        filePath: '/test/file.ts',
        language: 'typescript',
        functionName: 'testFunction',
        className: 'TestClass',
        moduleName: 'test-module'
      };

      expect(scope.filePath).toBe('/test/file.ts');
      expect(scope.language).toBe('typescript');
      expect(scope.functionName).toBe('testFunction');
    });

    it('should define Context correctly', () => {
      const context = {
        beforeCursor: 'function test() {',
        afterCursor: '}',
        relatedCodeSnippets: [],
        similarCodeSnippets: [],
        environments: []
      };

      expect(context.beforeCursor).toBe('function test() {');
      expect(context.afterCursor).toBe('}');
      expect(context.relatedCodeSnippets).toHaveLength(0);
    });

    it('should define UnifiedStatisticsEvent correctly', () => {
      const event: UnifiedStatisticsEvent = {
        eventType: StatisticsEventType.COMPLETION_TRIGGERED,
        timestamp: Date.now(),
        sessionId: 'session-123',
        data: {
          userInfo: {
            name: 'Test User',
            nickname: 'test',
            department: 'Engineering',
            userType: 'github'
          }
        }
      };

      expect(event.eventType).toBe(StatisticsEventType.COMPLETION_TRIGGERED);
      expect(event.sessionId).toBe('session-123');
      expect(event.data.userInfo?.name).toBe('Test User');
    });
  });

  describe('Code Graph Types', () => {
    it('should define Language type correctly', () => {
      const languages: Language[] = ['c', 'go', 'python', 'javascript', 'typescript', 'java', 'csharp'];
      languages.forEach(lang => {
        expect(typeof lang).toBe('string');
      });
    });

    it('should define NodeKind type correctly', () => {
      const kinds: NodeKind[] = ['module', 'function', 'variable', 'class', 'interface', 'type', 'macro'];
      kinds.forEach(kind => {
        expect(typeof kind).toBe('string');
      });
    });

    it('should define GraphCodeNode correctly', () => {
      const node: GraphCodeNode = {
        id: 'node-1',
        name: 'testFunction',
        type: 'function',
        language: 'typescript',
        kind: 'function',
        file: 'test.ts',
        module: 'test-module',
        definition: 'function testFunction() {}',
        documentation: 'Test function documentation',
        location: {
          start: { line: 1, column: 1 },
          end: { line: 1, column: 25 }
        }
      };

      expect(node.id).toBe('node-1');
      expect(node.name).toBe('testFunction');
      expect(node.type).toBe('function');
      expect(node.language).toBe('typescript');
    });

    it('should define CodeEdge correctly', () => {
      const edge: CodeEdge = {
        from: 'node-1',
        to: 'node-2',
        type: 'calls'
      };

      expect(edge.from).toBe('node-1');
      expect(edge.to).toBe('node-2');
      expect(edge.type).toBe('calls');
    });

    it('should define ASTNode correctly', () => {
      const astNode: ASTNode = {
        type: 'function_definition',
        kind: 'function',
        name: 'testFunction',
        text: 'function testFunction() {}',
        children: [],
        location: {
          start: { line: 1, column: 1 },
          end: { line: 1, column: 25 }
        },
        language: 'typescript',
        file: 'test.ts',
        metadata: {
          isStatic: false,
          visibility: 'public',
          returnType: 'void',
          parameters: [
            { name: 'param1', type: 'string' }
          ]
        }
      };

      expect(astNode.type).toBe('function_definition');
      expect(astNode.kind).toBe('function');
      expect(astNode.metadata?.returnType).toBe('void');
    });
  });

  describe('Data Extraction Types', () => {
    it('should define SFTSample correctly', () => {
      const sample: SFTSample = {
        id: 'sample-1',
        file: 'test.ts',
        functionName: 'testFunction',
        language: 'typescript',
        expected: 'return true;',
        above_cursor: 'function testFunction() {',
        below_cursor: '}',
        contexts: ['function testFunction() {', '}'],
        meta: { line: 1 }
      };

      expect(sample.id).toBe('sample-1');
      expect(sample.language).toBe('typescript');
      expect(sample.expected).toBe('return true;');
    });

    it('should define DataExtractionStrategy correctly', () => {
      const strategy: DataExtractionStrategy = {
        extract: async (filePath: string, code: string, options?: any) => {
          return [];
        }
      };

      expect(typeof strategy.extract).toBe('function');
    });
  });

  describe('Code Context Types', () => {
    it('should define SourceLocation correctly', () => {
      const location: SourceLocation = {
        file: 'test.ts',
        start: { line: 1, column: 1 },
        end: { line: 10, column: 1 }
      };

      expect(location.file).toBe('test.ts');
      expect(location.start.line).toBe(1);
      expect(location.end.line).toBe(10);
    });

    it('should define ContextItem correctly', () => {
      const item: ContextItem = {
        source: 'graph',
        contextType: 'related',
        score: 0.95,
        content: 'function test() {}',
        location: {
          file: 'test.ts',
          start: { line: 1, column: 1 },
          end: { line: 1, column: 20 }
        }
      };

      expect(item.source).toBe('graph');
      expect(item.contextType).toBe('related');
      expect(item.score).toBe(0.95);
      expect(item.content).toBe('function test() {}');
    });

    it('should define RetrievalFusionConfig correctly', () => {
      const config: RetrievalFusionConfig = {
        graphWeight: 0.4,
        bm25Weight: 0.3,
        embeddingWeight: 0.3
      };

      expect(config.graphWeight).toBe(0.4);
      expect(config.bm25Weight).toBe(0.3);
      expect(config.embeddingWeight).toBe(0.3);
    });

    it('should define ContextProvider interface structure', () => {
      const provider: ContextProvider = {
        search: async (query: string, contextItems: ContextItem[]) => {
          return [];
        }
      };

      expect(typeof provider.search).toBe('function');
    });

    it('should define specialized context providers', () => {
      const keywordProvider: KeywordContextProvider = {
        search: async (query: string, contextItems: ContextItem[]) => [],
        searchByKeywords: async (code: string) => []
      };

      const graphProvider: GraphContextProvider = {
        search: async (query: string, contextItems: ContextItem[]) => [],
        getRelatedContext: async (functionNode: any) => [],
        getSimilarContext: async (functionNode: any) => []
      };

      const embeddingProvider: EmbeddingContextProvider = {
        search: async (query: string, contextItems: ContextItem[]) => [],
        searchByEmbedding: async (code: string) => []
      };

      expect(typeof keywordProvider.searchByKeywords).toBe('function');
      expect(typeof graphProvider.getRelatedContext).toBe('function');
      expect(typeof graphProvider.getSimilarContext).toBe('function');
      expect(typeof embeddingProvider.searchByEmbedding).toBe('function');
    });
  });

  describe('Type Compatibility Tests', () => {
    it('should allow IUserInfo to be used where StatisticsUserInfo is expected', () => {
      const userInfo: IUserInfo = {
        id: 'user-1',
        name: 'Test User',
        nickname: 'test',
        department: 'Engineering'
      };

      // This should work without type errors
      const statisticsUserInfo: StatisticsUserInfo = {
        name: userInfo.name,
        nickname: userInfo.nickname,
        department: userInfo.department,
        userType: 'github'
      };

      expect(statisticsUserInfo.name).toBe(userInfo.name);
      expect(statisticsUserInfo.nickname).toBe(userInfo.nickname);
    });

    it('should allow CompletionScope to be converted to StatisticsCompletionScope', () => {
      const completionScope: CompletionScope = {
        scopeType: 'function',
        name: 'testFunction',
        startLine: 1,
        startColumn: 1,
        endLine: 10,
        endColumn: 1
      };

      const statisticsScope: StatisticsCompletionScope = {
        filePath: 'test.ts',
        language: 'typescript',
        functionName: completionScope.name,
        className: undefined,
        moduleName: undefined
      };

      expect(statisticsScope.functionName).toBe(completionScope.name);
    });
  });
}); 