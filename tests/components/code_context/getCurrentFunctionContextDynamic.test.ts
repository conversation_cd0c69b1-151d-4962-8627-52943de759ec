import * as vscode from 'vscode';
import { getCurrentFunctionContextDynamic } from '../../../src/components/code_context/collect';
import { DEFAULT_CONTEXT_FUSION_CONFIG } from '../../../src/components/code_context/config';
import { ParserManager } from '../../../src/components/code_graph/parser/ParserManager';
import { RepoIndexerManager } from '../../../src/components/code_graph/repoIndexer/repoIndexerManager';
import { ASTNodeKind } from '../../../src/components/code_graph/types/ast';

// Mock VSCode API
jest.mock('vscode', () => ({
  window: {
    activeTextEditor: null
  },
  Position: jest.fn(),
  Range: jest.fn()
}));

// Mock ParserManager
jest.mock('../../../src/components/code_graph/parser/ParserManager');

// Mock RepoIndexerManager
jest.mock('../../../src/components/code_graph/repoIndexer/repoIndexerManager');

describe('getCurrentFunctionContextDynamic', () => {
  let mockEditor: any;
  let mockDocument: any;
  let mockParserManager: jest.Mocked<ParserManager>;
  let mockRepoIndexerManager: any;

  beforeEach(() => {
    // 重置所有 mock
    jest.clearAllMocks();

    // Mock document
    mockDocument = {
      uri: {
        fsPath: '/test/file.js'
      },
      getText: jest.fn(),
      lineAt: jest.fn(),
      lineCount: 100
    };

    // Mock editor
    mockEditor = {
      document: mockDocument,
      selection: {
        active: new vscode.Position(10, 5)
      }
    };

    // Mock ParserManager
    mockParserManager = new ParserManager() as jest.Mocked<ParserManager>;
    (ParserManager as jest.MockedClass<typeof ParserManager>).mockImplementation(() => mockParserManager);

    // Mock RepoIndexerManager
    mockRepoIndexerManager = {
      getInstance: jest.fn().mockReturnValue({
        getRepoIndexer: jest.fn().mockReturnValue(null)
      })
    };
    (RepoIndexerManager as any).getInstance = mockRepoIndexerManager.getInstance;
  });

  describe('when no active editor', () => {
    it('should return empty result', async () => {
      (vscode.window as any).activeTextEditor = null;

      const result = await getCurrentFunctionContextDynamic();

      expect(result).toEqual({
        currentFunction: null,
        relatedContext: [],
        similarContext: [],
        allContext: [],
        contextSummary: ''
      });
    });
  });

  describe('when no function found at position', () => {
    it('should return empty result', async () => {
      (vscode.window as any).activeTextEditor = mockEditor;
      mockParserManager.findFunctionAtPosition.mockResolvedValue(null);

      const result = await getCurrentFunctionContextDynamic();

      expect(result).toEqual({
        currentFunction: null,
        relatedContext: [],
        similarContext: [],
        allContext: [],
        contextSummary: ''
      });
    });
  });

  describe('when function found but no indexer available', () => {
    it('should return result with current function only', async () => {
      (vscode.window as any).activeTextEditor = mockEditor;
      
      const mockAstNode = {
        name: 'testFunction',
        type: 'function_definition',
        originalType: 'function',
        kind: ASTNodeKind.FUNCTION,
        text: 'function testFunction() {}',
        children: [],
        location: {
          start: { line: 10, column: 0 },
          end: { line: 12, column: 1 }
        },
        language: 'javascript',
        file: '/test/file.js'
      };

      mockParserManager.findFunctionAtPosition.mockResolvedValue(mockAstNode);

      const result = await getCurrentFunctionContextDynamic();

      expect(result.currentFunction).toBeTruthy();
      expect(result.currentFunction?.name).toBe('testFunction');
      expect(result.relatedContext).toEqual([]);
      expect(result.similarContext).toEqual([]);
      expect(result.allContext).toEqual([]);
    });
  });

  describe('when function found with indexer available', () => {
    it('should return enhanced context with multiple sources', async () => {
      (vscode.window as any).activeTextEditor = mockEditor;
      
      const mockAstNode = {
        name: 'testFunction',
        type: 'function_definition',
        originalType: 'function',
        kind: ASTNodeKind.FUNCTION,
        text: 'function testFunction() {}',
        children: [],
        location: {
          start: { line: 10, column: 0 },
          end: { line: 12, column: 1 }
        },
        language: 'javascript',
        file: '/test/file.js'
      };

      // Mock indexer with some data
      const mockRepoIndexer = {
        findNodeInFile: jest.fn().mockReturnValue({
          id: 'test-node-1',
          name: 'testFunction',
          type: 'function'
        }),
        findSimilarFunctionsByName: jest.fn().mockReturnValue([
          {
            id: 'similar-1',
            name: 'similarFunction',
            type: 'function',
            definition: 'function similarFunction() { return "test"; }'
          }
        ])
      };

      mockRepoIndexerManager.getInstance.mockReturnValue({
        getRepoIndexer: jest.fn().mockReturnValue(mockRepoIndexer)
      });

      mockParserManager.findFunctionAtPosition.mockResolvedValue(mockAstNode);

      const result = await getCurrentFunctionContextDynamic();

      expect(result.currentFunction).toBeTruthy();
      expect(result.currentFunction?.name).toBe('testFunction');
      // 注意：由于我们使用了真实的 ContextCollector，这里的结果可能为空
      // 因为其他 Provider 可能没有被正确 mock
    });
  });

  describe('with custom config', () => {
    it('should use provided fusion config', async () => {
      (vscode.window as any).activeTextEditor = mockEditor;
      
      const customConfig = {
        ...DEFAULT_CONTEXT_FUSION_CONFIG,
        graphWeight: 0.6,
        bm25Weight: 0.2,
        embeddingWeight: 0.2
      };

      const mockAstNode = {
        name: 'testFunction',
        type: 'function_definition',
        originalType: 'function',
        kind: ASTNodeKind.FUNCTION,
        text: 'function testFunction() {}',
        children: [],
        location: {
          start: { line: 10, column: 0 },
          end: { line: 12, column: 1 }
        },
        language: 'javascript',
        file: '/test/file.js'
      };

      mockParserManager.findFunctionAtPosition.mockResolvedValue(mockAstNode);

      const result = await getCurrentFunctionContextDynamic(customConfig);

      expect(result.currentFunction).toBeTruthy();
      // 验证配置被正确传递（通过检查结果的某些特征）
    });
  });

  describe('error handling', () => {
    it('should handle parser errors gracefully', async () => {
      (vscode.window as any).activeTextEditor = mockEditor;
      mockParserManager.findFunctionAtPosition.mockRejectedValue(new Error('Parser error'));

      const result = await getCurrentFunctionContextDynamic();

      expect(result).toEqual({
        currentFunction: null,
        relatedContext: [],
        similarContext: [],
        allContext: [],
        contextSummary: ''
      });
    });
  });

  describe('context summary generation', () => {
    it('should generate meaningful context summary', async () => {
      (vscode.window as any).activeTextEditor = mockEditor;
      
      const mockAstNode = {
        name: 'testFunction',
        type: 'function_definition',
        originalType: 'function',
        kind: ASTNodeKind.FUNCTION,
        text: 'function testFunction() {}',
        children: [],
        location: {
          start: { line: 10, column: 0 },
          end: { line: 12, column: 1 }
        },
        language: 'javascript',
        file: '/test/file.js'
      };

      mockParserManager.findFunctionAtPosition.mockResolvedValue(mockAstNode);

      const result = await getCurrentFunctionContextDynamic();

      expect(result.contextSummary).toContain('testFunction');
      expect(typeof result.contextSummary).toBe('string');
    });
  });
});
