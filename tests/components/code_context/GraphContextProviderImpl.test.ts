import { GraphContextProviderImpl } from 'src/components/code_context/GraphContextProviderImpl';
import { ContextItem } from 'src/components/code_context/types';
import { RepoIndexer } from 'src/components/code_graph/repoIndexer/repoIndexer';
import { ASTNode, ASTNodeKind } from 'src/components/code_graph/types/ast';

describe('GraphContextProviderImpl', () => {
    let mockRepoIndexer: jest.Mocked<RepoIndexer>;
    let provider: GraphContextProviderImpl;

    beforeEach(() => {
        mockRepoIndexer = {
            findNodeInFile: jest.fn(),
            findCallersByName: jest.fn(),
            findCalleesByName: jest.fn(),
            findSimilarFunctionsByName: jest.fn()
        } as any;
        provider = new GraphContextProviderImpl(mockRepoIndexer);
    });

    describe('getRelatedContext', () => {
        it('能根据AST节点获取相关上下文', async () => {
            const functionNode: ASTNode = {
                originalType: 'function_definition',
                name: 'testFunction',
                kind: ASTNodeKind.FUNCTION,
                language: 'typescript',
                file: 'test.ts',
                children: []
            };

            mockRepoIndexer.findNodeInFile.mockReturnValue({
                id: `test.ts::${ASTNodeKind.FUNCTION}::testFunction`,
                name: 'testFunction',
                type: 'function',
                language: 'typescript',
                kind: ASTNodeKind.FUNCTION,
                file: 'test.ts'
            });
            mockRepoIndexer.findCallersByName.mockReturnValue([
                {
                    id: `test.ts::${ASTNodeKind.FUNCTION}::callerFunction`,
                    name: 'callerFunction',
                    type: 'function',
                    language: 'typescript',
                    kind: ASTNodeKind.FUNCTION,
                    definition: 'function callerFunction() { testFunction(); }',
                    file: 'test.ts'
                }
            ]);
            mockRepoIndexer.findCalleesByName.mockReturnValue([]);

            const results = await provider.getRelatedContext(functionNode);
            expect(results.length).toBeGreaterThan(0);
            expect(results[0].source).toBe('graph');
            expect(results[0].content).toContain('callerFunction');
        });

        it('无匹配节点时返回空数组', async () => {
            const functionNode: ASTNode = {
                originalType: 'function_definition',
                name: 'nonexistentFunction',
                kind: ASTNodeKind.FUNCTION,
                language: 'typescript',
                file: 'test.ts',
                children: []
            };

            mockRepoIndexer.findNodeInFile.mockReturnValue(undefined);

            const results = await provider.getRelatedContext(functionNode);
            expect(results.length).toBe(0);
        });
    });

    describe('search', () => {
        it('能根据文本进行调用链检索', async () => {
            const contextItems: ContextItem[] = [
                { source: 'graph', contextType: 'related', score: 1, content: 'function caller() { test(); }' },
                { source: 'graph', contextType: 'related', score: 1, content: 'function callee() { return true; }' }
            ];

            const results = await provider.search('caller', contextItems);
            // TODO: 待实现调用链文本检索逻辑后补充断言
            expect(Array.isArray(results)).toBeTruthy();
        });

        it('能处理空输入', async () => {
            const results = await provider.search('', []);
            expect(results.length).toBe(0);
        });
    });
}); 