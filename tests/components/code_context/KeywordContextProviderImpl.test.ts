import { KeywordContextProviderImpl } from 'src/components/code_context/KeywordContextProviderImpl';
import { ContextItem } from 'src/components/code_context/types';

describe('KeywordContextProviderImpl', () => {
    const provider = new KeywordContextProviderImpl();
    const contextItems: ContextItem[] = [
        { source: 'bm25', contextType: 'similar', score: 0, content: 'function add(a, b) { return a + b; }' },
        { source: 'bm25', contextType: 'similar', score: 0, content: 'function subtract(a, b) { return a - b; }' },
        { source: 'bm25', contextType: 'similar', score: 0, content: 'let sum = add(1, 2);' },
        { source: 'bm25', contextType: 'similar', score: 0, content: 'let diff = subtract(3, 1);' },
    ];

    describe('searchByKeywords', () => {
        it('能根据代码内容进行检索', async () => {
            const code = 'function add(x, y) { return x + y; }';
            const results = await provider.searchByKeywords(code);
            expect(results.length).toBeGreaterThan(0);
            expect(results[0].source).toBe('bm25');
            expect(results[0].content).toBe(code);
        });

        it('能处理空代码输入', async () => {
            const results = await provider.searchByKeywords('');
            expect(results.length).toBe(0);
        });
    });

    describe('search', () => {


        it('能根据关键字检索相关内容', async () => {
            const results = await provider.search('add', contextItems);
            expect(results.length).toBeGreaterThan(0);
            expect(results[0].content).toContain('add');
        });

        it('无匹配时返回空数组', async () => {
            const results = await provider.search('multiply', contextItems);
            expect(results.length).toBe(0);
        });

        it('能根据相关性排序', async () => {
            const results = await provider.search('add', contextItems);
            expect(results.length).toBeGreaterThan(0);
            expect(results[0].content).toContain('add');
            if (results.length > 1) {
                expect(results[0].score).toBeGreaterThanOrEqual(results[1].score);
            }
        });

        it('支持多关键字检索', async () => {
            const results = await provider.search('add subtract', contextItems);
            expect(results.length).toBeGreaterThan(0);
            // 检查结果中包含add或subtract
            expect(results.some(item => item.content.includes('add'))).toBeTruthy();
            expect(results.some(item => item.content.includes('subtract'))).toBeTruthy();
        });
    });
}); 