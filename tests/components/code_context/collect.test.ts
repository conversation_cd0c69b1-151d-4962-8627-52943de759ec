
// Mock VSCode API
jest.mock('vscode', () => ({
  window: {
    createOutputChannel: jest.fn().mockReturnValue({
      appendLine: jest.fn(),
      append: jest.fn(),
      clear: jest.fn(),
      show: jest.fn(),
      hide: jest.fn(),
      dispose: jest.fn()
    }),
    showInformationMessage: jest.fn(),
    showErrorMessage: jest.fn(),
    showWarningMessage: jest.fn()
  },
  workspace: {
    workspaceFolders: [
      {
        uri: {
          fsPath: '/test/workspace'
        },
        name: 'test-workspace'
      }
    ],
    createFileSystemWatcher: jest.fn().mockReturnValue({
      onDidCreate: jest.fn().mockReturnValue({
        dispose: jest.fn()
      }),
      onDidChange: jest.fn().mockReturnValue({
        dispose: jest.fn()
      }),
      onDidDelete: jest.fn().mockReturnValue({
        dispose: jest.fn()
      }),
      dispose: jest.fn()
    }),
    fs: {
      readFile: jest.fn(),
      writeFile: jest.fn(),
      stat: jest.fn()
    }
  },
  Uri: {
    file: jest.fn((path) => ({ fsPath: path }))
  },
  EventEmitter: jest.fn().mockImplementation(() => ({
    event: jest.fn(),
    fire: jest.fn(),
    dispose: jest.fn()
  })),
  ExtensionContext: jest.fn().mockImplementation(() => ({
    subscriptions: [],
    globalState: {
      get: jest.fn(),
      update: jest.fn()
    },
    globalStorageUri: {
      fsPath: '/test/storage'
    }
  }))
}));

import { debugRepoIndexerStatus, getCurrentFunctionContextDynamic } from 'src/components/code_context/collect';
import { ParserManager } from 'src/components/code_graph/parser/ParserManager';
import { RepoIndexerManager } from 'src/components/code_graph/repoIndexer/repoIndexerManager';
import * as vscode from 'vscode';

// Mock vscode
jest.mock('vscode', () => ({
    window: {
        activeTextEditor: null
    },
    Position: jest.fn().mockImplementation((line, character) => ({ line, character })),
    Uri: {
        file: jest.fn().mockImplementation((path) => ({ fsPath: path }))
    },
    workspace: {
        fs: {
            readFile: jest.fn()
        }
    }
}));

// Mock RepoIndexerManager
jest.mock('src/components/code_graph/repoIndexer/repoIndexerManager', () => ({
    RepoIndexerManager: {
        getInstance: jest.fn().mockReturnValue({
            getRepoIndexer: jest.fn()
        })
    }
}));

// Mock ParserManager
jest.mock('src/components/code_graph/parser/ParserManager', () => ({
    ParserManager: jest.fn().mockImplementation(() => ({
        findFunctionAtPosition: jest.fn()
    }))
}));

describe('collect.ts - getCurrentFunctionContext', () => {
    let mockRepoIndexer: any;
    let mockParserManager: any;

    beforeEach(() => {
        // 重置所有mock
        jest.clearAllMocks();

        // 设置默认的mock实现
        mockRepoIndexer = {
            findNodeInFile: jest.fn(),
            findAllNodesByName: jest.fn(),
            findRelatedFunctionsByName: jest.fn(),
            findSimilarFunctionsByName: jest.fn()
        };

        mockParserManager = {
            findFunctionAtPosition: jest.fn()
        };

        (RepoIndexerManager.getInstance as jest.Mock).mockReturnValue({
            getRepoIndexer: jest.fn().mockReturnValue(mockRepoIndexer)
        });

        (ParserManager as unknown as jest.Mock).mockImplementation(() => mockParserManager);
    });

    describe('当找不到graphNode时的fallback逻辑', () => {
        it('应该基于AST节点创建临时上下文当找不到graphNode时', async () => {
            // 设置mock
            const mockEditor = {
                document: {
                    uri: { fsPath: '/test/workspace/test.c' }
                },
                selection: {
                    active: { line: 5, character: 10 }
                }
            };

            const mockAstNode = {
                name: 'testFunction',
                type: 'function_definition',
                kind: 'function',
                text: 'int testFunction(int x) { return x * 2; }',
                location: {
                    start: { line: 4, column: 0 },
                    end: { line: 6, column: 1 }
                }
            };

            // Mock vscode.window.activeTextEditor
            (vscode.window as any).activeTextEditor = mockEditor;

            // Mock ParserManager返回AST节点
            mockParserManager.findFunctionAtPosition.mockResolvedValue(mockAstNode);

            // Mock RepoIndexer找不到对应的graphNode
            mockRepoIndexer.findNodeInFile.mockReturnValue(undefined);
            mockRepoIndexer.findAllNodesByName.mockReturnValue([]);

            // 执行测试
            const result = await getCurrentFunctionContextDynamic();

            // 验证结果
            expect(result).toBeDefined();
            expect(result.currentFunction).not.toBeNull();
            expect(result.currentFunction?.name).toBe('testFunction');
            expect(result.currentFunction?.type).toBe('function');
            expect(result.currentFunction?.file).toBe('/test/workspace/test.c');
            expect(result.relatedFunctions).toEqual([]);
            expect(result.context).toContain('当前函数: testFunction');
            expect(result.context).toContain('文件: /test/workspace/test.c');
        });

        it('应该尝试不同的匹配策略当找不到graphNode时', async () => {
            // 设置mock
            const mockEditor = {
                document: {
                    uri: { fsPath: '/test/workspace/test.c' }
                },
                selection: {
                    active: { line: 5, character: 10 }
                }
            };

            const mockAstNode = {
                name: 'testFunction',
                type: 'function_definition',
                kind: 'function',
                text: 'int testFunction(int x) { return x * 2; }'
            };

            const mockGraphNode = {
                id: 'test.c::function::testFunction',
                name: 'testFunction',
                type: 'function',
                language: 'c',
                kind: 'function',
                file: 'test.c',
                definition: 'int testFunction(int x) { return x * 2; }'
            };

            // Mock vscode.window.activeTextEditor
            (vscode.window as any).activeTextEditor = mockEditor;

            // Mock ParserManager返回AST节点
            mockParserManager.findFunctionAtPosition.mockResolvedValue(mockAstNode);

            // Mock RepoIndexer的策略1失败，策略2成功
            mockRepoIndexer.findNodeInFile
                .mockReturnValueOnce(undefined) // 策略1：使用'function'类型
                .mockReturnValueOnce(mockGraphNode); // 策略2：使用'function'类型（kind）

            mockRepoIndexer.findRelatedFunctionsByName.mockReturnValue([]);
            mockRepoIndexer.findSimilarFunctionsByName.mockReturnValue([]);

            // 执行测试
            const result = await getCurrentFunctionContextDynamic();

            // 验证结果
            expect(result).toBeDefined();
            expect(result.currentFunction).not.toBeNull();
            expect(result.currentFunction?.name).toBe('testFunction');
            expect(result.relatedFunctions).toEqual([]);
            expect(result.context).toContain('当前函数: testFunction');

            // 验证调用了多次findNodeInFile
            expect(mockRepoIndexer.findNodeInFile).toHaveBeenCalledTimes(2);
        });

        it('应该使用同名节点当所有策略都失败时', async () => {
            // 设置mock
            const mockEditor = {
                document: {
                    uri: { fsPath: '/test/workspace/test.c' }
                },
                selection: {
                    active: { line: 5, character: 10 }
                }
            };

            const mockAstNode = {
                name: 'testFunction',
                type: 'function_definition',
                kind: 'function',
                text: 'int testFunction(int x) { return x * 2; }'
            };

            const mockGraphNode = {
                id: 'other.c::function::testFunction',
                name: 'testFunction',
                type: 'function',
                language: 'c',
                kind: 'function',
                file: 'other.c',
                definition: 'int testFunction(int x) { return x * 2; }'
            };

            // Mock vscode.window.activeTextEditor
            (vscode.window as any).activeTextEditor = mockEditor;

            // Mock ParserManager返回AST节点
            mockParserManager.findFunctionAtPosition.mockResolvedValue(mockAstNode);

            // Mock RepoIndexer所有策略都失败，但找到同名节点
            mockRepoIndexer.findNodeInFile.mockReturnValue(undefined);
            mockRepoIndexer.findAllNodesByName.mockReturnValue([mockGraphNode]);
            mockRepoIndexer.findRelatedFunctionsByName.mockReturnValue([]);
            mockRepoIndexer.findSimilarFunctionsByName.mockReturnValue([]);

            // 执行测试
            const result = await getCurrentFunctionContextDynamic();

            // 验证结果
            expect(result).toBeDefined();
            expect(result.currentFunction).not.toBeNull();
            expect(result.currentFunction?.name).toBe('testFunction');
            expect(result.currentFunction?.file).toBe('other.c'); // 使用找到的同名节点
            expect(result.relatedFunctions).toEqual([]);
            expect(result.context).toContain('当前函数: testFunction');
            expect(result.context).toContain('文件: other.c');
        });
    });

    describe('正常情况', () => {
        it('应该正常返回graphNode当找到匹配时', async () => {
            // 设置mock
            const mockEditor = {
                document: {
                    uri: { fsPath: '/test/workspace/test.c' }
                },
                selection: {
                    active: { line: 5, character: 10 }
                }
            };

            const mockAstNode = {
                name: 'testFunction',
                type: 'function_definition',
                kind: 'function',
                text: 'int testFunction(int x) { return x * 2; }'
            };

            const mockGraphNode = {
                id: 'test.c::function::testFunction',
                name: 'testFunction',
                type: 'function',
                language: 'c',
                kind: 'function',
                file: 'test.c',
                definition: 'int testFunction(int x) { return x * 2; }'
            };

            const mockRelatedFunctions = [
                {
                    id: 'test.c::function::callerFunction',
                    name: 'callerFunction',
                    type: 'function',
                    language: 'c',
                    kind: 'function',
                    file: 'test.c',
                    definition: 'void callerFunction() { testFunction(5); }'
                }
            ];

            // Mock vscode.window.activeTextEditor
            (vscode.window as any).activeTextEditor = mockEditor;

            // Mock ParserManager返回AST节点
            mockParserManager.findFunctionAtPosition.mockResolvedValue(mockAstNode);

            // Mock RepoIndexer找到匹配的graphNode
            mockRepoIndexer.findNodeInFile.mockReturnValue(mockGraphNode);
            mockRepoIndexer.findRelatedFunctionsByName.mockReturnValue(mockRelatedFunctions);
            mockRepoIndexer.findSimilarFunctionsByName.mockReturnValue([]);

            // 执行测试
            const result = await getCurrentFunctionContextDynamic();

            // 验证结果
            expect(result).toBeDefined();
            expect(result.currentFunction).toBe(mockGraphNode);
            expect(result.relatedFunctions).toEqual(mockRelatedFunctions);
            expect(result.context).toContain('当前函数: testFunction');
            expect(result.context).toContain('相关函数:');
            expect(result.context).toContain('函数名: callerFunction');
        });
    });

    describe('边界情况', () => {
        it('应该在没有活动编辑器时返回空结果', async () => {
            // Mock vscode.window.activeTextEditor为null
            (vscode.window as any).activeTextEditor = null;

            const result = await getCurrentFunctionContextDynamic();

            expect(result.currentFunction).toBeNull();
            expect(result.relatedFunctions).toEqual([]);
            expect(result.context).toBe('');
        });

        it('应该在RepoIndexer未初始化时返回空结果', async () => {
            // 设置mock
            const mockEditor = {
                document: {
                    uri: { fsPath: '/test/workspace/test.c' }
                },
                selection: {
                    active: { line: 5, character: 10 }
                }
            };

            // Mock vscode.window.activeTextEditor
            (vscode.window as any).activeTextEditor = mockEditor;

            // Mock RepoIndexerManager返回null
            (RepoIndexerManager.getInstance as jest.Mock).mockReturnValue({
                getRepoIndexer: jest.fn().mockReturnValue(null)
            });

            const result = await getCurrentFunctionContextDynamic();

            expect(result.currentFunction).toBeNull();
            expect(result.relatedFunctions).toEqual([]);
            expect(result.context).toBe('');
        });

        it('应该在AST解析失败时返回空结果', async () => {
            // 设置mock
            const mockEditor = {
                document: {
                    uri: { fsPath: '/test/workspace/test.c' }
                },
                selection: {
                    active: { line: 5, character: 10 }
                }
            };

            // Mock vscode.window.activeTextEditor
            (vscode.window as any).activeTextEditor = mockEditor;

            // Mock ParserManager返回null
            mockParserManager.findFunctionAtPosition.mockResolvedValue(null);

            const result = await getCurrentFunctionContextDynamic();

            expect(result.currentFunction).toBeNull();
            expect(result.relatedFunctions).toEqual([]);
            expect(result.context).toBe('');
        });
    });

    describe('RepoIndexer自动恢复', () => {
        it('应该在getRepoIndexer时自动尝试恢复索引数据', async () => {
            // 设置mock
            const mockEditor = {
                document: {
                    uri: { fsPath: '/test/workspace/test.c' }
                },
                selection: {
                    active: { line: 5, character: 10 }
                }
            };

            const mockAstNode = {
                name: 'testFunction',
                type: 'function_definition',
                kind: 'function',
                text: 'int testFunction(int x) { return x * 2; }'
            };

            const mockGraphNode = {
                id: 'test.c::function::testFunction',
                name: 'testFunction',
                type: 'function',
                language: 'c',
                kind: 'function',
                file: 'test.c',
                definition: 'int testFunction(int x) { return x * 2; }'
            };

            // Mock vscode.window.activeTextEditor
            (vscode.window as any).activeTextEditor = mockEditor;

            // Mock ParserManager返回AST节点
            mockParserManager.findFunctionAtPosition.mockResolvedValue(mockAstNode);

            // 模拟RepoIndexerManager的自动恢复逻辑
            const originalGetRepoIndexer = RepoIndexerManager.getInstance().getRepoIndexer;
            let recoveryAttempted = false;

            (RepoIndexerManager.getInstance as jest.Mock).mockReturnValue({
                getRepoIndexer: jest.fn().mockImplementation(() => {
                    if (!recoveryAttempted) {
                        recoveryAttempted = true;
                        // 模拟恢复成功
                        mockRepoIndexer.findNodeInFile.mockReturnValue(mockGraphNode);
                        mockRepoIndexer.findRelatedFunctionsByName.mockReturnValue([]);
                        mockRepoIndexer.findSimilarFunctionsByName.mockReturnValue([]);
                    }
                    return mockRepoIndexer;
                })
            });

            // 执行测试
            const result = await getCurrentFunctionContextDynamic();

            // 验证结果
            expect(result).toBeDefined();
            expect(result.currentFunction).not.toBeNull();
            expect(result.currentFunction?.name).toBe('testFunction');
            expect(result.relatedFunctions).toEqual([]);
            expect(result.context).toContain('当前函数: testFunction');

            // 恢复原始mock
            (RepoIndexerManager.getInstance as jest.Mock).mockReturnValue({
                getRepoIndexer: originalGetRepoIndexer
            });
        });
    });

    describe('调试功能', () => {
        it('应该能够获取RepoIndexer状态', () => {
            // 模拟RepoIndexer存在的情况
            const mockGraph = {
                nodes: new Map([
                    ['node1', { name: 'test1', type: 'function' }],
                    ['node2', { name: 'test2', type: 'function' }]
                ])
            };

            const mockRepoIndexer = {
                constructor: { name: 'RepoIndexer' },
                workspacePath: '/test/workspace',
                getGraph: jest.fn().mockReturnValue(mockGraph)
            };

            (RepoIndexerManager.getInstance as jest.Mock).mockReturnValue({
                getRepoIndexer: jest.fn().mockReturnValue(mockRepoIndexer)
            });

            const status = debugRepoIndexerStatus();

            expect(status.hasRepoIndexer).toBe(true);
            expect(status.repoIndexerType).toBe('RepoIndexer');
            expect(status.nodeCount).toBe(2);
            expect(status.workspacePath).toBe('/test/workspace');
        });

        it('应该在RepoIndexer为null时返回正确的状态', () => {
            (RepoIndexerManager.getInstance as jest.Mock).mockReturnValue({
                getRepoIndexer: jest.fn().mockReturnValue(null)
            });

            const status = debugRepoIndexerStatus();

            expect(status.hasRepoIndexer).toBe(false);
            expect(status.repoIndexerType).toBe('null');
            expect(status.nodeCount).toBe(0);
            expect(status.workspacePath).toBe(null);
        });

        it('应该在获取图数据失败时返回错误状态', () => {
            const mockRepoIndexer = {
                constructor: { name: 'RepoIndexer' },
                workspacePath: '/test/workspace',
                getGraph: jest.fn().mockImplementation(() => {
                    throw new Error('获取图数据失败');
                })
            };

            (RepoIndexerManager.getInstance as jest.Mock).mockReturnValue({
                getRepoIndexer: jest.fn().mockReturnValue(mockRepoIndexer)
            });

            const status = debugRepoIndexerStatus();

            expect(status.hasRepoIndexer).toBe(true);
            expect(status.repoIndexerType).toBe('RepoIndexer');
            expect(status.nodeCount).toBe(-1); // 表示获取图数据失败
            expect(status.workspacePath).toBe('/test/workspace');
        });
    });
}); 