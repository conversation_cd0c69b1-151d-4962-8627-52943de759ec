import { RelatedContextBuilder, RelatedContextOptions } from '../../../src/components/code_context/RelatedContextBuilder';
import { RepoIndexer } from '../../../src/components/code_graph/repoIndexer/repoIndexer';
import { ASTNode, ASTNodeKind } from '../../../src/components/code_graph/types/ast';
import { GraphCodeNode } from '../../../src/components/code_graph/types/graph';

// Mock RepoIndexer
jest.mock('../../../src/components/code_graph/repoIndexer/repoIndexer');

describe('RelatedContextBuilder', () => {
  let builder: RelatedContextBuilder;
  let mockRepoIndexer: jest.Mocked<RepoIndexer>;

  beforeEach(() => {
    mockRepoIndexer = {
      findNodeInFile: jest.fn(),
      findCallersByName: jest.fn(),
      findCalleesByName: jest.fn(),
    } as any;

    builder = new RelatedContextBuilder(mockRepoIndexer);
  });

  describe('变量提取粒度控制', () => {
    it('应该只提取顶级变量，避免变量爆炸', async () => {
      const functionNode: ASTNode = {
        name: 'test_function',
        file: 'test.py',
        originalType: 'function',
        kind: ASTNodeKind.FUNCTION_DEFINITION,
        children: []
      };

      const mockCodeNode: GraphCodeNode = {
        id: 'test_function',
        name: 'test_function',
        type: 'function',
        language: 'python',
        kind: 'function',
        definition: `
def test_function(items, config):
    for item in items:
        if item.data.cache.is_valid():
            result = item.data.cache.get_value()
            self.data.cache.store(result)
        else:
            tmp_buf = item.data.cache.get_buffer()
            g_flag = check_global_flag()
            np.array(tmp_buf)
        MAX_LENGTH = 100
        cc5 = get_register()
        temp_var = process_temp()
      `,
        metadata: {
          parameters: [
            { name: 'items', type: 'List[Dict]' },
            { name: 'config', type: 'Config' }
          ]
        }
      };

      mockRepoIndexer.findNodeInFile.mockReturnValue(mockCodeNode);
      mockRepoIndexer.findCallersByName.mockReturnValue([]);
      mockRepoIndexer.findCalleesByName.mockReturnValue([]);

      const options: RelatedContextOptions = {
        mode: 'completion',
        includeHiddenVariables: true
      };

      const result = await builder.buildRelatedContext(
        functionNode,
        'def test_function(items, config):\n    for item in items:\n        if item.data.cache.is_valid():',
        '',
        options
      );

      // 验证结果包含顶级变量
      expect(result).toContain('self.data: class_property');
      expect(result).toContain('g_flag: global');
      expect(result).toContain('np: imported');
      expect(result).toContain('MAX_LENGTH: constant');
      expect(result).toContain('cc5: register');
      expect(result).toContain('tmp_buf: local');
      expect(result).toContain('temp_var: local');

      // 验证不包含子属性（这些应该在函数定义中，但不在隐藏变量中）
      expect(result).not.toContain('config.data.settings');
    });

    it('应该正确识别变量来源类型', async () => {
      const functionNode: ASTNode = {
        name: 'test_function',
        file: 'test.py',
        originalType: 'function',
        kind: ASTNodeKind.FUNCTION_DEFINITION,
        children: []
      };

      const mockCodeNode: GraphCodeNode = {
        id: 'test_function',
        name: 'test_function',
        type: 'function',
        language: 'python',
        kind: 'function',
        definition: `
def test_function():
    self.cache = {}
    g_config = get_config()
    DEFAULT_THRESHOLD = 0.5
    np.array([1, 2, 3])
    cc5 = get_register()
    temp_buffer = create_buffer()
    unknown_var = process()
      `,
        metadata: {
          parameters: []
        }
      };

      mockRepoIndexer.findNodeInFile.mockReturnValue(mockCodeNode);
      mockRepoIndexer.findCallersByName.mockReturnValue([]);
      mockRepoIndexer.findCalleesByName.mockReturnValue([]);

      const options: RelatedContextOptions = {
        mode: 'completion',
        includeHiddenVariables: true
      };

      const result = await builder.buildRelatedContext(
        functionNode,
        'def test_function():',
        '',
        options
      );

      // 验证变量来源分类
      expect(result).toContain('self.cache: class_property');
      expect(result).toContain('g_config: global');
      expect(result).toContain('DEFAULT_THRESHOLD: constant');
      expect(result).toContain('np: imported');
      expect(result).toContain('cc5: register');
      expect(result).toContain('temp_buffer: local');
      expect(result).toContain('unknown_var: unknown');
    });

    it('应该避免重复提取同一个变量', async () => {
      const functionNode: ASTNode = {
        name: 'test_function',
        file: 'test.py',
        originalType: 'function',
        kind: ASTNodeKind.FUNCTION_DEFINITION,
        children: []
      };

      const mockCodeNode: GraphCodeNode = {
        id: 'test_function',
        name: 'test_function',
        type: 'function',
        language: 'python',
        kind: 'function',
        definition: `
def test_function(np, g_flag):
    # np 既是参数又是导入模块
    result = np.array([1, 2, 3])
    # g_flag 既是参数又是全局变量
    if g_flag:
        process(g_flag)
      `,
        metadata: {
          parameters: [
            { name: 'np', type: 'module' },
            { name: 'g_flag', type: 'bool' }
          ]
        }
      };

      mockRepoIndexer.findNodeInFile.mockReturnValue(mockCodeNode);
      mockRepoIndexer.findCallersByName.mockReturnValue([]);
      mockRepoIndexer.findCalleesByName.mockReturnValue([]);

      const options: RelatedContextOptions = {
        mode: 'completion',
        includeHiddenVariables: true
      };

      const result = await builder.buildRelatedContext(
        functionNode,
        'def test_function(np, g_flag):',
        '',
        options
      );

      // 由于参数在可见文本中，它们不会被提取为隐藏变量
      // 但我们应该验证没有重复的变量
      const resultCount = (result.match(/result:/g) || []).length;
      expect(resultCount).toBe(1);
    });

    it('应该根据模式调整上下文粒度', async () => {
      const functionNode: ASTNode = {
        name: 'test_function',
        file: 'test.py',
        originalType: 'function',
        kind: ASTNodeKind.FUNCTION_DEFINITION,
        children: []
      };

      const mockCodeNode: GraphCodeNode = {
        id: 'test_function',
        name: 'test_function',
        type: 'function',
        language: 'python',
        kind: 'function',
        definition: 'def test_function(): pass',
        metadata: {
          parameters: []
        }
      };

      const mockCaller: GraphCodeNode = {
        id: 'caller_function',
        name: 'caller_function',
        type: 'function',
        language: 'python',
        kind: 'function',
        file: 'test.py',
        definition: `
def caller_function():
    # 调用 test_function
    result = test_function()
    return result
        `,
        location: { start: { line: 10, column: 0 }, end: { line: 15, column: 0 } }
      };

      mockRepoIndexer.findNodeInFile.mockReturnValue(mockCodeNode);
      mockRepoIndexer.findCallersByName.mockReturnValue([mockCaller]);
      mockRepoIndexer.findCalleesByName.mockReturnValue([]);

      // 测试 completion 模式
      const completionOptions: RelatedContextOptions = {
        mode: 'completion',
        includeHiddenVariables: true
      };

      const completionResult = await builder.buildRelatedContext(
        functionNode,
        'def test_function():',
        '',
        completionOptions
      );

      // completion 模式应该只显示函数签名
      expect(completionResult).toContain('- test.py:10 -> def caller_function():');
      expect(completionResult).not.toContain('result = test_function()');

      // 测试 check 模式
      const checkOptions: RelatedContextOptions = {
        mode: 'check',
        includeHiddenVariables: true
      };

      const checkResult = await builder.buildRelatedContext(
        functionNode,
        'def test_function():',
        '',
        checkOptions
      );

      // check 模式应该显示更多代码细节
      expect(checkResult).toContain('- test.py:10 -> def caller_function():');
      expect(checkResult).toContain('result = test_function()');
    });
  });

  describe('变量可见性检查', () => {
    it('应该正确识别变量是否在可见文本中', async () => {
      const functionNode: ASTNode = {
        name: 'test_function',
        file: 'test.py',
        originalType: 'function',
        kind: ASTNodeKind.FUNCTION_DEFINITION,
        children: []
      };

      const mockCodeNode: GraphCodeNode = {
        id: 'test_function',
        name: 'test_function',
        type: 'function',
        language: 'python',
        kind: 'function',
        definition: `
def test_function(items, config):
    for item in items:
        process(item)
      `,
        metadata: {
          parameters: [
            { name: 'items', type: 'List' },
            { name: 'config', type: 'Config' }
          ]
        }
      };

      mockRepoIndexer.findNodeInFile.mockReturnValue(mockCodeNode);
      mockRepoIndexer.findCallersByName.mockReturnValue([]);
      mockRepoIndexer.findCalleesByName.mockReturnValue([]);

      const options: RelatedContextOptions = {
        mode: 'completion',
        includeHiddenVariables: true
      };

      // items 在可见文本中，config 也在可见文本中
      const result = await builder.buildRelatedContext(
        functionNode,
        'def test_function(items, config):\n    for item in items:',
        '',
        options
      );

      // items 和 config 都不应该出现在隐藏变量中，因为它们在可见文本中
      expect(result).not.toContain('items: function_param');
      expect(result).not.toContain('config: function_param');
    });
  });
}); 