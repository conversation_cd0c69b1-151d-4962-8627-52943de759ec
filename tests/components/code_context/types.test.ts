import { ContextItem, RetrievalFusionConfig, SourceLocation } from 'src/components/code_context/types';

describe('Context Types', () => {
  describe('SourceLocation', () => {
    it('should have correct structure', () => {
      const location: SourceLocation = {
        file: 'test.ts',
        start: { line: 1, column: 0 },
        end: { line: 10, column: 20 }
      };

      expect(location.file).toBe('test.ts');
      expect(location.start.line).toBe(1);
      expect(location.start.column).toBe(0);
      expect(location.end.line).toBe(10);
      expect(location.end.column).toBe(20);
    });
  });

  describe('ContextItem', () => {
    it('should have correct structure with location', () => {
      const item: ContextItem = {
        source: 'graph',
        contextType: 'related',
        score: 1,
        content: 'test content',
        location: {
          file: 'test.ts',
          start: { line: 1, column: 0 },
          end: { line: 1, column: 10 }
        }
      };

      expect(item.source).toBe('graph');
      expect(item.contextType).toBe('related');
      expect(item.score).toBe(1);
      expect(item.content).toBe('test content');
      expect(item.location?.file).toBe('test.ts');
    });

    it('should allow location to be optional', () => {
      const item: ContextItem = {
        source: 'bm25',
        contextType: 'similar',
        score: 0.6,
        content: 'test content'
      };

      expect(item.location).toBeUndefined();
    });

    it('should accept all source types', () => {
      const graphItem: ContextItem = { source: 'graph', contextType: 'related', score: 1, content: 'graph' };
      const bm25Item: ContextItem = { source: 'bm25', contextType: 'similar', score: 1, content: 'bm25' };
      const embeddingItem: ContextItem = { source: 'embedding', contextType: 'similar', score: 1, content: 'embedding' };

      expect(graphItem.source).toBe('graph');
      expect(bm25Item.source).toBe('bm25');
      expect(embeddingItem.source).toBe('embedding');
    });
  });

  describe('RetrievalFusionConfig', () => {
    it('should have correct structure', () => {
      const config: RetrievalFusionConfig = {
        graphWeight: 0.4,
        bm25Weight: 0.3,
        embeddingWeight: 0.3
      };

      expect(config.graphWeight).toBe(0.4);
      expect(config.bm25Weight).toBe(0.3);
      expect(config.embeddingWeight).toBe(0.3);
    });
  });
}); 