import { ContextFusionEngine } from 'src/components/code_context/ContextFusionEngine';
import { ContextItem, RetrievalFusionConfig } from 'src/components/code_context/types';

describe('ContextFusionEngine', () => {
  let fusionEngine: ContextFusionEngine;
  let config: RetrievalFusionConfig;

  beforeEach(() => {
    config = {
      graphWeight: 0.4,
      bm25Weight: 0.3,
      embeddingWeight: 0.3
    };
    fusionEngine = new ContextFusionEngine(config);
  });

  describe('fuse', () => {
    it('should fuse items from different sources', () => {
      const graphItems: ContextItem[] = [
        { source: 'graph', contextType: 'related', score: 1, content: 'graph item 1' },
        { source: 'graph', contextType: 'related', score: 0.8, content: 'graph item 2' }
      ];

      const bm25Items: ContextItem[] = [
        { source: 'bm25', contextType: 'similar', score: 0.9, content: 'bm25 item 1' },
        { source: 'bm25', contextType: 'similar', score: 0.7, content: 'bm25 item 2' }
      ];

      const embeddingItems: ContextItem[] = [
        { source: 'embedding', contextType: 'similar', score: 0.8, content: 'embedding item 1' },
        { source: 'embedding', contextType: 'similar', score: 0.6, content: 'embedding item 2' }
      ];

      const result = fusionEngine.fuse(graphItems, bm25Items, embeddingItems);

      expect(result).toHaveLength(6);
      expect(result[0].score).toBeGreaterThan(result[1].score);
    });

    it('should handle duplicate content with weighted fusion', () => {
      const graphItems: ContextItem[] = [
        { source: 'graph', contextType: 'related', score: 1, content: 'duplicate item' }
      ];

      const bm25Items: ContextItem[] = [
        { source: 'bm25', contextType: 'similar', score: 0.8, content: 'duplicate item' }
      ];

      const embeddingItems: ContextItem[] = [
        { source: 'embedding', contextType: 'similar', score: 0.6, content: 'unique item' }
      ];

      const result = fusionEngine.fuse(graphItems, bm25Items, embeddingItems);

      expect(result).toHaveLength(2);

      const duplicateItem = result.find(item => item.content === 'duplicate item');
      expect(duplicateItem).toBeDefined();
      expect(duplicateItem!.score).toBe(1 * 0.4 + 0.8 * 0.3); // weighted sum
    });

    it('should sort results by score in descending order', () => {
      const graphItems: ContextItem[] = [
        { source: 'graph', contextType: 'related', score: 0.5, content: 'low score' }
      ];

      const bm25Items: ContextItem[] = [
        { source: 'bm25', contextType: 'similar', score: 0.9, content: 'high score' }
      ];

      const embeddingItems: ContextItem[] = [
        { source: 'embedding', contextType: 'similar', score: 0.7, content: 'medium score' }
      ];

      const result = fusionEngine.fuse(graphItems, bm25Items, embeddingItems);

      expect(result[0].content).toBe('high score');
      expect(result[1].content).toBe('medium score');
      expect(result[2].content).toBe('low score');
    });

    it('should handle empty input arrays', () => {
      const result = fusionEngine.fuse([], [], []);
      expect(result).toHaveLength(0);
    });

    it('should handle partial empty arrays', () => {
      const graphItems: ContextItem[] = [
        { source: 'graph', contextType: 'related', score: 1, content: 'only graph item' }
      ];

      const result = fusionEngine.fuse(graphItems, [], []);
      expect(result).toHaveLength(1);
      expect(result[0].content).toBe('only graph item');
      expect(result[0].score).toBe(1 * 0.4);
    });
  });
}); 