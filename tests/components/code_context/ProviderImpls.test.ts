import { EmbeddingContextProviderImpl } from 'src/components/code_context/EmbeddingContextProviderImpl';
import { GraphContextProviderImpl } from 'src/components/code_context/GraphContextProviderImpl';
import { KeywordContextProviderImpl } from 'src/components/code_context/KeywordContextProviderImpl';
import { RepoIndexer } from 'src/components/code_graph/repoIndexer/repoIndexer';
import { ASTNode, ASTNodeKind } from 'src/components/code_graph/types/ast';
import { GraphCodeNode } from 'src/components/code_graph/types/graph';

describe('Provider Implementations', () => {
  describe('KeywordContextProviderImpl', () => {
    let keywordProvider: KeywordContextProviderImpl;

    beforeEach(() => {
      keywordProvider = new KeywordContextProviderImpl();
    });

    describe('searchByKeywords', () => {
      it('should return mock BM25 results', async () => {
        const code = 'BM25 context mock';
        const result = await keywordProvider.searchByKeywords(code);

        expect(result).toHaveLength(1);
        expect(result[0].source).toBe('bm25');
        expect(result[0].score).toBeGreaterThan(0);
        expect(result[0].content).toBe('BM25 context mock');
        expect(result[0].location).toBeUndefined();
      });

      it('should handle empty code input', async () => {
        const result = await keywordProvider.searchByKeywords('');
        // Empty query returns empty results due to BM25 implementation
        expect(result).toHaveLength(0);
      });
    });
  });

  describe('EmbeddingContextProviderImpl', () => {
    let embeddingProvider: EmbeddingContextProviderImpl;

    beforeEach(() => {
      embeddingProvider = new EmbeddingContextProviderImpl();
    });

    describe('searchByEmbedding', () => {
      it('should return mock embedding results', async () => {
        const code = 'Embedding context mock';
        const result = await embeddingProvider.searchByEmbedding(code);

        // The search method is not implemented yet and returns empty array
        expect(result).toHaveLength(0);
        expect(Array.isArray(result)).toBe(true);
      });

      it('should handle empty code input', async () => {
        const result = await embeddingProvider.searchByEmbedding('');
        expect(result).toHaveLength(0);
      });
    });
  });

  describe('GraphContextProviderImpl', () => {
    let mockRepoIndexer: jest.Mocked<RepoIndexer>;
    let graphProvider: GraphContextProviderImpl;

    beforeEach(() => {
      mockRepoIndexer = {
        findNodeInFile: jest.fn(),
        findCallersByName: jest.fn(),
        findCalleesByName: jest.fn(),
        findSimilarFunctionsByName: jest.fn()
      } as any;

      graphProvider = new GraphContextProviderImpl(mockRepoIndexer);
    });

    describe('getRelatedContext', () => {
      it('should return related context when node is found', async () => {
        const node: ASTNode = {
          originalType: 'function_definition',
          name: 'testFunction',
          kind: ASTNodeKind.FUNCTION,
          language: 'typescript',
          file: 'test.ts',
          children: []
        };

        const graphNode: GraphCodeNode = {
          id: `test.ts::${ASTNodeKind.FUNCTION}::testFunction`,
          name: 'testFunction',
          type: 'function',
          language: 'typescript',
          kind: 'function',
          file: 'test.ts'
        };

        mockRepoIndexer.findNodeInFile.mockReturnValue(graphNode);
        mockRepoIndexer.findCallersByName.mockReturnValue([]);
        mockRepoIndexer.findCalleesByName.mockReturnValue([]);

        const result = await graphProvider.getRelatedContext(node);

        expect(Array.isArray(result)).toBe(true);
      });

      it('should handle case when node is not found', async () => {
        mockRepoIndexer.findNodeInFile.mockReturnValue(undefined);

        const node: ASTNode = {
          originalType: 'function_definition',
          name: 'nonexistentFunction',
          kind: ASTNodeKind.FUNCTION,
          language: 'typescript',
          file: 'test.ts',
          children: []
        };

        const result = await graphProvider.getRelatedContext(node);
        expect(result).toHaveLength(0);
      });
    });
  });
}); 