import { CompletionPromptBuilder, CompletionPromptOptions } from '../../../src/components/code_context/CompletionPromptBuilder';
import { RelatedContextBuilder } from '../../../src/components/code_context/RelatedContextBuilder';
import { RepoIndexer } from '../../../src/components/code_graph/repoIndexer/repoIndexer';
import { ASTNode, ASTNodeKind } from '../../../src/components/code_graph/types/ast';

// Mock RepoIndexer
jest.mock('../../../src/components/code_graph/repoIndexer/repoIndexer');

describe('CompletionPromptBuilder', () => {
  let builder: CompletionPromptBuilder;
  let mockRepoIndexer: jest.Mocked<RepoIndexer>;
  let mockRelatedContextBuilder: jest.Mocked<RelatedContextBuilder>;

  beforeEach(() => {
    mockRepoIndexer = {
      findNodeInFile: jest.fn(),
      findCallersByName: jest.fn(),
      findCalleesByName: jest.fn(),
    } as any;

    mockRelatedContextBuilder = {
      buildRelatedContext: jest.fn(),
    } as any;

    builder = new CompletionPromptBuilder(mockRelatedContextBuilder);
  });

  describe('基础提示词构建', () => {
    it('应该构建基本的代码补全提示词', async () => {
      const functionNode: ASTNode = {
        name: 'test_function',
        file: 'test.py',
        originalType: 'function',
        kind: ASTNodeKind.FUNCTION_DEFINITION,
        children: []
      };

      const textBefore = 'def test_function(items):\n    for item in items:';
      const textAfter = '\n    return result';

      mockRelatedContextBuilder.buildRelatedContext.mockResolvedValue('');

      const result = await builder.buildCompletionPrompt(
        functionNode,
        textBefore,
        textAfter
      );

      // 验证基本结构
      expect(result).toContain('You are a code completion assistant.');
      expect(result).toContain('请结合上下文补全代码');
      expect(result).toContain('## 补全如下代码:');
      expect(result).toContain('def test_function(items):');
      expect(result).toContain('    for item in items:');
      expect(result).toContain('{{complete_code_here}}');
      expect(result).toContain('    return result');
      
      // 验证使用了正确的补全token结构
      const lines = result.split('\n');
      const completeCodeIndex = lines.findIndex(line => line.includes('## 补全如下代码:'));
      const startTokenIndex = lines.findIndex(line => line.includes('{{complete_code_here}}'));
      const fillTokenIndex = lines.findIndex((line, index) => index > startTokenIndex && line.includes('{{complete_code_here}}'));
      const endTokenIndex = lines.findIndex((line, index) => index > fillTokenIndex && line.includes('{{complete_code_here}}'));
      
      expect(startTokenIndex).toBeGreaterThan(completeCodeIndex);
      expect(fillTokenIndex).toBeGreaterThan(startTokenIndex);
      expect(endTokenIndex).toBeGreaterThan(fillTokenIndex);
    });

    it('应该包含相关上下文', async () => {
      const functionNode: ASTNode = {
        name: 'test_function',
        file: 'test.py',
        originalType: 'function',
        kind: ASTNodeKind.FUNCTION_DEFINITION,
        children: []
      };

      const textBefore = 'def test_function():';
      const textAfter = '';

      const mockRelatedContext = `
### Related Context ###

# Current Function:
def test_function():

# Hidden Variables Model Might Need:
- items: function_param, type=List
- result: unknown, type=unknown
      `;

      mockRelatedContextBuilder.buildRelatedContext.mockResolvedValue(mockRelatedContext);

      const result = await builder.buildCompletionPrompt(
        functionNode,
        textBefore,
        textAfter,
        { includeRelatedContext: true }
      );

      expect(result).toContain('<related_context>');
      expect(result).toContain('</related_context>');
      expect(result).toContain('### Related Context ###');
      expect(result).toContain('- items: function_param');
    });

    it('应该支持自定义系统规则', async () => {
      const functionNode: ASTNode = {
        name: 'test_function',
        file: 'test.py',
        originalType: 'function',
        kind: ASTNodeKind.FUNCTION_DEFINITION,
        children: []
      };

      const textBefore = 'def test_function():';
      const textAfter = '';

      mockRelatedContextBuilder.buildRelatedContext.mockResolvedValue('');

      const options: CompletionPromptOptions = {
        systemRules: `
1. 使用Python 3.8+语法
2. 遵循PEP 8代码规范
3. 添加适当的类型注解
        `.trim()
      };

      const result = await builder.buildCompletionPrompt(
        functionNode,
        textBefore,
        textAfter,
        options
      );

      expect(result).toContain('<system_rules>');
      expect(result).toContain('</system_rules>');
      expect(result).toContain('使用Python 3.8+语法');
      expect(result).toContain('遵循PEP 8代码规范');
    });

    it('应该支持环境信息', async () => {
      const functionNode: ASTNode = {
        name: 'test_function',
        file: 'test.py',
        originalType: 'function',
        kind: ASTNodeKind.FUNCTION_DEFINITION,
        children: []
      };

      const textBefore = 'def test_function():';
      const textAfter = '';

      mockRelatedContextBuilder.buildRelatedContext.mockResolvedValue('');

      const options: CompletionPromptOptions = {
        environment: `
- 项目类型: Web应用
- 框架: FastAPI
- 数据库: PostgreSQL
- 缓存: Redis
        `.trim()
      };

      const result = await builder.buildCompletionPrompt(
        functionNode,
        textBefore,
        textAfter,
        options
      );

      expect(result).toContain('<env>');
      expect(result).toContain('</env>');
      expect(result).toContain('项目类型: Web应用');
      expect(result).toContain('框架: FastAPI');
    });

    it('应该支持用户自定义规则', async () => {
      const functionNode: ASTNode = {
        name: 'test_function',
        file: 'test.py',
        originalType: 'function',
        kind: ASTNodeKind.FUNCTION_DEFINITION,
        children: []
      };

      const textBefore = 'def test_function():';
      const textAfter = '';

      mockRelatedContextBuilder.buildRelatedContext.mockResolvedValue('');

      const options: CompletionPromptOptions = {
        userRules: `
- 优先使用异步函数
- 添加详细的错误处理
- 使用日志记录关键操作
        `.trim()
      };

      const result = await builder.buildCompletionPrompt(
        functionNode,
        textBefore,
        textAfter,
        options
      );

      expect(result).toContain('<user_rules>');
      expect(result).toContain('</user_rules>');
      expect(result).toContain('优先使用异步函数');
      expect(result).toContain('添加详细的错误处理');
    });

    it('应该支持相似上下文', async () => {
      const functionNode: ASTNode = {
        name: 'test_function',
        file: 'test.py',
        originalType: 'function',
        kind: ASTNodeKind.FUNCTION_DEFINITION,
        children: []
      };

      const textBefore = 'def test_function():';
      const textAfter = '';

      mockRelatedContextBuilder.buildRelatedContext.mockResolvedValue('');

      const options: CompletionPromptOptions = {
        similarContext: `
def similar_function(data):
    result = []
    for item in data:
        if item.is_valid():
            result.append(item.process())
    return result
        `.trim()
      };

      const result = await builder.buildCompletionPrompt(
        functionNode,
        textBefore,
        textAfter,
        options
      );

      expect(result).toContain('<similar_context>');
      expect(result).toContain('</similar_context>');
      expect(result).toContain('def similar_function(data):');
      expect(result).toContain('result.append(item.process())');
    });
  });

  describe('模型特定提示词', () => {
    it('应该支持DeepSeek模型', async () => {
      const functionNode: ASTNode = {
        name: 'test_function',
        file: 'test.py',
        originalType: 'function',
        kind: ASTNodeKind.FUNCTION_DEFINITION,
        children: []
      };

      const textBefore = 'def test_function():';
      const textAfter = '';

      mockRelatedContextBuilder.buildRelatedContext.mockResolvedValue('');

      const result = await builder.buildModelSpecificPrompt(
        functionNode,
        textBefore,
        textAfter,
        'deepseek'
      );

      expect(result).toContain('DeepSeek Coder model');
      expect(result).toContain('REDACTED_SPECIAL_TOKEN');
      
      // 验证使用了正确的token结构
      const lines = result.split('\n');
      const completeCodeIndex = lines.findIndex(line => line.includes('## 补全如下代码:'));
      const startTokenIndex = lines.findIndex(line => line.includes('REDACTED_SPECIAL_TOKEN'));
      const fillTokenIndex = lines.findIndex((line, index) => index > startTokenIndex && line.includes('REDACTED_SPECIAL_TOKEN'));
      const endTokenIndex = lines.findIndex((line, index) => index > fillTokenIndex && line.includes('REDACTED_SPECIAL_TOKEN'));
      
      expect(startTokenIndex).toBeGreaterThan(completeCodeIndex);
      expect(fillTokenIndex).toBeGreaterThan(startTokenIndex);
      expect(endTokenIndex).toBeGreaterThan(fillTokenIndex);
    });

    it('应该支持Qwen模型', async () => {
      const functionNode: ASTNode = {
        name: 'test_function',
        file: 'test.py',
        originalType: 'function',
        kind: ASTNodeKind.FUNCTION_DEFINITION,
        children: []
      };

      const textBefore = 'def test_function():';
      const textAfter = '';

      mockRelatedContextBuilder.buildRelatedContext.mockResolvedValue('');

      const result = await builder.buildModelSpecificPrompt(
        functionNode,
        textBefore,
        textAfter,
        'qwen'
      );

      expect(result).toContain('<|fim_prefix|>');
      expect(result).toContain('<|fim_suffix|>');
      expect(result).toContain('<|fim_middle|>');
      
      // 验证使用了正确的token结构
      const lines = result.split('\n');
      const completeCodeIndex = lines.findIndex(line => line.includes('## 补全如下代码:'));
      const startTokenIndex = lines.findIndex(line => line.includes('<|fim_prefix|>'));
      const fillTokenIndex = lines.findIndex(line => line.includes('<|fim_suffix|>'));
      const endTokenIndex = lines.findIndex(line => line.includes('<|fim_middle|>'));
      
      expect(startTokenIndex).toBeGreaterThan(completeCodeIndex);
      expect(fillTokenIndex).toBeGreaterThan(startTokenIndex);
      expect(endTokenIndex).toBeGreaterThan(fillTokenIndex);
    });

    it('应该支持自定义标记', async () => {
      const functionNode: ASTNode = {
        name: 'test_function',
        file: 'test.py',
        originalType: 'function',
        kind: ASTNodeKind.FUNCTION_DEFINITION,
        children: []
      };

      const textBefore = 'def test_function():';
      const textAfter = '';

      mockRelatedContextBuilder.buildRelatedContext.mockResolvedValue('');

      const result = await builder.buildModelSpecificPrompt(
        functionNode,
        textBefore,
        textAfter,
        'custom'
      );

      expect(result).toContain('{{complete_code_here}}');
    });
  });

  describe('补全标记', () => {
    it('应该正确放置补全标记', async () => {
      const functionNode: ASTNode = {
        name: 'test_function',
        file: 'test.py',
        originalType: 'function',
        kind: ASTNodeKind.FUNCTION_DEFINITION,
        children: []
      };

      const textBefore = 'def test_function(items):\n    result = []\n    for item in items:';
      const textAfter = '\n    return result';

      mockRelatedContextBuilder.buildRelatedContext.mockResolvedValue('');

      const result = await builder.buildCompletionPrompt(
        functionNode,
        textBefore,
        textAfter
      );

      // 验证补全标记的位置
      const lines = result.split('\n');
      const completeCodeIndex = lines.findIndex(line => line.includes('## 补全如下代码:'));
      const startTokenIndex = lines.findIndex(line => line.includes('{{complete_code_here}}'));
      const fillTokenIndex = lines.findIndex((line, index) => index > startTokenIndex && line.includes('{{complete_code_here}}'));
      const endTokenIndex = lines.findIndex((line, index) => index > fillTokenIndex && line.includes('{{complete_code_here}}'));

      expect(completeCodeIndex).toBeGreaterThan(-1);
      expect(startTokenIndex).toBeGreaterThan(completeCodeIndex);
      expect(fillTokenIndex).toBeGreaterThan(startTokenIndex);
      expect(endTokenIndex).toBeGreaterThan(fillTokenIndex);
      
      // 验证补全标记前后的内容
      const beforeFillToken = lines[fillTokenIndex - 1];
      const afterFillToken = lines[fillTokenIndex + 1];
      expect(beforeFillToken).toContain('for item in items:');
      // 检查afterFillToken是否为空，如果为空则检查下一行
      if (afterFillToken.trim() === '') {
        const nextAfterFillToken = lines[fillTokenIndex + 2];
        expect(nextAfterFillToken).toContain('return result');
      } else {
        expect(afterFillToken).toContain('return result');
      }
    });

    it('应该支持自定义补全标记', async () => {
      const functionNode: ASTNode = {
        name: 'test_function',
        file: 'test.py',
        originalType: 'function',
        kind: ASTNodeKind.FUNCTION_DEFINITION,
        children: []
      };

      const textBefore = 'def test_function():';
      const textAfter = '';

      mockRelatedContextBuilder.buildRelatedContext.mockResolvedValue('');

      const options: CompletionPromptOptions = {
        completionTokens: {
          start: '***START***',
          fill: '***FILL***',
          end: '***END***'
        }
      };

      const result = await builder.buildCompletionPrompt(
        functionNode,
        textBefore,
        textAfter,
        options
      );

      expect(result).toContain('***START***');
      expect(result).toContain('***FILL***');
      expect(result).toContain('***END***');
      expect(result).toContain('请结合上下文补全代码');
      // 指令中应该使用通用描述，而不是具体标记
      expect(result).not.toContain('{{complete_code_here}}');
    });
  });

  describe('提示词结构', () => {
    it('应该按照正确的顺序组织各部分', async () => {
      const functionNode: ASTNode = {
        name: 'test_function',
        file: 'test.py',
        originalType: 'function',
        kind: ASTNodeKind.FUNCTION_DEFINITION,
        children: []
      };

      const textBefore = 'def test_function():';
      const textAfter = '';

      mockRelatedContextBuilder.buildRelatedContext.mockResolvedValue('');

      const options: CompletionPromptOptions = {
        systemRules: 'System rules here',
        environment: 'Environment info here',
        userRules: 'User rules here',
        similarContext: 'Similar context here'
      };

      const result = await builder.buildCompletionPrompt(
        functionNode,
        textBefore,
        textAfter,
        options
      );

      const lines = result.split('\n');
      
      // 验证各部分顺序
      const systemIndex = lines.findIndex(line => line.includes('You are a code completion assistant'));
      const instructionIndex = lines.findIndex(line => line.includes('请结合上下文'));
      const systemRulesIndex = lines.findIndex(line => line.includes('<system_rules>'));
      const envIndex = lines.findIndex(line => line.includes('<env>'));
      const userRulesIndex = lines.findIndex(line => line.includes('<user_rules>'));
      const similarContextIndex = lines.findIndex(line => line.includes('<similar_context>'));
      const completeCodeIndex = lines.findIndex(line => line.includes('## 补全如下代码:'));

      expect(systemIndex).toBeLessThan(instructionIndex);
      expect(instructionIndex).toBeLessThan(systemRulesIndex);
      expect(systemRulesIndex).toBeLessThan(envIndex);
      expect(envIndex).toBeLessThan(userRulesIndex);
      expect(userRulesIndex).toBeLessThan(similarContextIndex);
      expect(similarContextIndex).toBeLessThan(completeCodeIndex);
    });
  });
}); 