import { ContextCollector } from '../../../src/components/code_context/ContextCollector';
import { ContextFusionEngine } from '../../../src/components/code_context/ContextFusionEngine';
import { EmbeddingContextProvider, GraphContextProvider, KeywordContextProvider, UserCustomContextProvider } from '../../../src/components/code_context/types';
import { ASTNode, ASTNodeKind } from '../../../src/components/code_graph/types/ast';

describe('ContextCollector', () => {
  let contextCollector: ContextCollector;
  let mockGraphProvider: jest.Mocked<GraphContextProvider>;
  let mockKeywordProvider: jest.Mocked<KeywordContextProvider>;
  let mockEmbeddingProvider: jest.Mocked<EmbeddingContextProvider>;
  let mockUserCustomProvider: jest.Mocked<UserCustomContextProvider>;
  let mockFusionEngine: jest.Mocked<ContextFusionEngine>;

  beforeEach(() => {
    mockGraphProvider = {
      getRelatedContext: jest.fn(),
      getSimilarContext: jest.fn(),
      search: jest.fn()
    } as any;

    mockKeywordProvider = {
      searchByKeywords: jest.fn(),
      search: jest.fn()
    } as any;

    mockEmbeddingProvider = {
      searchByEmbedding: jest.fn(),
      search: jest.fn()
    } as any;

    mockUserCustomProvider = {
      searchByUserSnippets: jest.fn(),
      search: jest.fn()
    } as any;

    mockFusionEngine = {
      fuse: jest.fn()
    } as any;

    contextCollector = new ContextCollector(
      mockGraphProvider,
      mockKeywordProvider,
      mockEmbeddingProvider,
      mockUserCustomProvider,
      mockFusionEngine
    );
  });

  describe('collectContextForCompletion', () => {
    it('应该收集并融合所有类型的上下文', async () => {
      const mockFunctionNode: ASTNode = {
        children: [],
        originalType: 'function_definition',
        name: 'testFunction',
        kind: ASTNodeKind.FUNCTION,
        language: 'typescript',
        file: 'test.ts',
        location: { start: { line: 1, column: 1 }, end: { line: 10, column: 1 } }
      };

      const mockCode = 'function testFunction() { return "test"; }';

      // Mock provider responses
      mockGraphProvider.getRelatedContext.mockResolvedValue([
        { source: 'graph', contextType: 'related', score: 0.8, content: 'related function 1' },
        { source: 'graph', contextType: 'related', score: 0.6, content: 'related function 2' }
      ]);

      mockGraphProvider.getSimilarContext.mockResolvedValue([
        { source: 'graph', contextType: 'similar', score: 0.7, content: 'similar function 1' },
        { source: 'graph', contextType: 'similar', score: 0.5, content: 'similar function 2' }
      ]);

      mockKeywordProvider.searchByKeywords.mockResolvedValue([
        { source: 'bm25', contextType: 'similar', score: 0.6, content: 'keyword match 1' },
        { source: 'bm25', contextType: 'similar', score: 0.4, content: 'keyword match 2' }
      ]);

      mockEmbeddingProvider.searchByEmbedding.mockResolvedValue([
        { source: 'embedding', contextType: 'similar', score: 0.9, content: 'embedding match 1' },
        { source: 'embedding', contextType: 'similar', score: 0.7, content: 'embedding match 2' }
      ]);

      mockUserCustomProvider.searchByUserSnippets.mockResolvedValue([
        { source: 'user-snippets', contextType: 'similar', score: 0.8, content: 'user snippet 1', userSnippet: { id: '1', name: 'snippet1', priority: 8, similarity: 0.8 } },
        { source: 'user-snippets', contextType: 'similar', score: 0.6, content: 'user snippet 2', userSnippet: { id: '2', name: 'snippet2', priority: 6, similarity: 0.6 } }
      ]);

      mockFusionEngine.fuse.mockReturnValue([
        { source: 'graph', contextType: 'related', score: 0.8, content: 'related function 1' },
        { source: 'embedding', contextType: 'similar', score: 0.9, content: 'embedding match 1' },
        { source: 'user-snippets', contextType: 'similar', score: 0.8, content: 'user snippet 1', userSnippet: { id: '1', name: 'snippet1', priority: 8, similarity: 0.8 } }
      ]);

      const result = await contextCollector.collectContextForCompletion(mockFunctionNode, mockCode);

      // 验证所有provider都被调用
      expect(mockGraphProvider.getRelatedContext).toHaveBeenCalledWith(mockFunctionNode);
      expect(mockGraphProvider.getSimilarContext).toHaveBeenCalledWith(mockFunctionNode);
      expect(mockKeywordProvider.searchByKeywords).toHaveBeenCalledWith(mockCode);
      expect(mockEmbeddingProvider.searchByEmbedding).toHaveBeenCalledWith(mockCode);
      expect(mockUserCustomProvider.searchByUserSnippets).toHaveBeenCalledWith(mockCode, 'typescript');

      // 验证fusion engine被调用
      expect(mockFusionEngine.fuse).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({ source: 'graph', contextType: 'related' }),
          expect.objectContaining({ source: 'graph', contextType: 'similar' })
        ]),
        expect.arrayContaining([
          expect.objectContaining({ source: 'bm25', contextType: 'similar' })
        ]),
        expect.arrayContaining([
          expect.objectContaining({ source: 'embedding', contextType: 'similar' })
        ]),
        expect.arrayContaining([
          expect.objectContaining({ source: 'user-snippets', contextType: 'similar' })
        ])
      );

      // 验证返回结果
      expect(result).toEqual([
        { source: 'graph', contextType: 'related', score: 0.8, content: 'related function 1' },
        { source: 'embedding', contextType: 'similar', score: 0.9, content: 'embedding match 1' },
        { source: 'user-snippets', contextType: 'similar', score: 0.8, content: 'user snippet 1', userSnippet: { id: '1', name: 'snippet1', priority: 8, similarity: 0.8 } }
      ]);
    });
  });

  describe('collectSimilarContext', () => {
    it('应该收集相似上下文', async () => {
      const mockFunctionNode: ASTNode = {
        children: [],
        originalType: 'function_definition',
        name: 'testFunction',
        kind: ASTNodeKind.FUNCTION,
        language: 'typescript',
        file: 'test.ts',
        location: { start: { line: 1, column: 1 }, end: { line: 10, column: 1 } }
      };

      const mockCode = 'function testFunction() { return "test"; }';

      // Mock provider responses
      mockGraphProvider.getSimilarContext.mockResolvedValue([
        { source: 'graph', contextType: 'similar', score: 0.7, content: 'similar function 1' }
      ]);

      mockKeywordProvider.searchByKeywords.mockResolvedValue([
        { source: 'bm25', contextType: 'similar', score: 0.6, content: 'keyword match 1' }
      ]);

      mockEmbeddingProvider.searchByEmbedding.mockResolvedValue([
        { source: 'embedding', contextType: 'similar', score: 0.9, content: 'embedding match 1' }
      ]);

      mockUserCustomProvider.searchByUserSnippets.mockResolvedValue([
        { source: 'user-snippets', contextType: 'similar', score: 0.8, content: 'user snippet 1', userSnippet: { id: '1', name: 'snippet1', priority: 8, similarity: 0.8 } }
      ]);

      mockFusionEngine.fuse.mockReturnValue([
        { source: 'embedding', contextType: 'similar', score: 0.9, content: 'embedding match 1' },
        { source: 'user-snippets', contextType: 'similar', score: 0.8, content: 'user snippet 1', userSnippet: { id: '1', name: 'snippet1', priority: 8, similarity: 0.8 } }
      ]);

      const result = await contextCollector.collectSimilarContext(mockFunctionNode, mockCode, {
        graphWeight: 0.3,
        bm25Weight: 0.3,
        embeddingWeight: 0.4
      });

      // 验证所有provider都被调用
      expect(mockGraphProvider.getSimilarContext).toHaveBeenCalledWith(mockFunctionNode);
      expect(mockKeywordProvider.searchByKeywords).toHaveBeenCalledWith(mockCode);
      expect(mockEmbeddingProvider.searchByEmbedding).toHaveBeenCalledWith(mockCode);
      expect(mockUserCustomProvider.searchByUserSnippets).toHaveBeenCalledWith(mockCode, 'typescript');

      // 验证返回结果
      expect(result).toEqual([
        { source: 'embedding', contextType: 'similar', score: 0.9, content: 'embedding match 1' },
        { source: 'user-snippets', contextType: 'similar', score: 0.8, content: 'user snippet 1', userSnippet: { id: '1', name: 'snippet1', priority: 8, similarity: 0.8 } }
      ]);
    });
  });
});