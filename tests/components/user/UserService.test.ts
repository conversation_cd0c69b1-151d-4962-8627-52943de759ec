
// Mock VSCode API
jest.mock('vscode', () => ({
  window: {
    createOutputChannel: jest.fn().mockReturnValue({
      appendLine: jest.fn(),
      append: jest.fn(),
      clear: jest.fn(),
      show: jest.fn(),
      hide: jest.fn(),
      dispose: jest.fn()
    }),
    showInformationMessage: jest.fn(),
    showErrorMessage: jest.fn(),
    showWarningMessage: jest.fn()
  },
  workspace: {
    workspaceFolders: [
      {
        uri: {
          fsPath: '/test/workspace'
        },
        name: 'test-workspace'
      }
    ],
    createFileSystemWatcher: jest.fn().mockReturnValue({
      onDidCreate: jest.fn().mockReturnValue({
        dispose: jest.fn()
      }),
      onDidChange: jest.fn().mockReturnValue({
        dispose: jest.fn()
      }),
      onDidDelete: jest.fn().mockReturnValue({
        dispose: jest.fn()
      }),
      dispose: jest.fn()
    }),
    fs: {
      readFile: jest.fn(),
      writeFile: jest.fn(),
      stat: jest.fn()
    }
  },
  Uri: {
    file: jest.fn((path) => ({ fsPath: path }))
  },
  EventEmitter: jest.fn().mockImplementation(() => ({
    event: jest.fn(),
    fire: jest.fn(),
    dispose: jest.fn()
  })),
  ExtensionContext: jest.fn().mockImplementation(() => ({
    subscriptions: [],
    globalState: {
      get: jest.fn(),
      update: jest.fn()
    },
    globalStorageUri: {
      fsPath: '/test/storage'
    }
  }))
}));

import { UserService, getCurrentUserInfo, getUserType, globalUserService } from 'src/components/user/UserService';
import { UserType } from 'src/components/user/types/user';

// Mock VSCode API
jest.mock('vscode', () => ({
    commands: {
        executeCommand: jest.fn().mockResolvedValue({
            id: 'test-user',
            name: 'Test User',
            nickName: 'test',
            department: 'TestDept',
            cloudDragonToken: 'test-token',
            accessToken: 'test-access',
            codeCommitToken: 'test-commit',
            codeHubGreenAccessToken: 'test-green',
            codeHubYellowAccessToken: 'test-yellow',
            gitLabGreenAccessToken: 'test-gitlab-green',
            gitLabYellowAccessToken: 'test-gitlab-yellow'
        })
    },
    workspace: {
        onDidChangeLoginStatus: jest.fn()
    }
}));

describe('UserService', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('初始化', () => {
        it('应该正确初始化用户系统', async () => {
            const userService = UserService.getInstance();
            await userService.initialize();
            
            expect(userService.isInitialized()).toBe(true);
        });

        it('应该默认使用 DEBUG 用户类型', () => {
            const userType = getUserType();
            expect(userType).toBe(UserType.DEBUG);
        });
    });

    describe('全局访问函数', () => {
        it('应该能通过全局函数获取用户信息', async () => {
            const userInfo = await getCurrentUserInfo();
            
            expect(userInfo).toBeDefined();
            expect(userInfo?.id).toBe('debug-user');
            expect(userInfo?.name).toBe('Debug User');
            expect(userInfo?.nickname).toBe('debugger');
            expect(userInfo?.department).toBe('DebugDept');
        });

        it('应该能获取用户类型', () => {
            const userType = getUserType();
            expect(userType).toBe(UserType.DEBUG);
        });
    });

    describe('用户服务实例', () => {
        it('应该返回单例实例', () => {
            const instance1 = UserService.getInstance();
            const instance2 = UserService.getInstance();
            
            expect(instance1).toBe(instance2);
        });

        it('应该能获取当前用户', async () => {
            const userService = UserService.getInstance();
            const user = await userService.getCurrentUser();
            
            expect(user).toBeDefined();
            expect(user?.type).toBe(UserType.DEBUG);
            expect(user?.isLoggedIn()).toBe(true);
        });

        it('应该能获取用户信息', async () => {
            const userService = UserService.getInstance();
            const userInfo = await userService.getCurrentUserInfo();
            
            expect(userInfo).toBeDefined();
            expect(userInfo?.id).toBe('debug-user');
        });

        it('应该能获取原始数据', async () => {
            const userService = UserService.getInstance();
            const rawData = await userService.getCurrentUserRaw();
            
            expect(rawData).toBeDefined();
            expect(rawData.id).toBe('debug-user');
        });

        it('应该能检查登录状态', async () => {
            const userService = UserService.getInstance();
            const isLoggedIn = await userService.isCurrentUserLoggedIn();
            
            expect(isLoggedIn).toBe(true);
        });
    });

    describe('用户类型切换', () => {
        it('应该能切换到不同的用户类型', async () => {
            const userService = UserService.getInstance();
            
            // 切换到华为用户
            await userService.switchUserType(UserType.HUAWEI);
            expect(userService.getCurrentUserType()).toBe(UserType.HUAWEI);
            
            // 切换回调试用户
            await userService.switchUserType(UserType.DEBUG);
            expect(userService.getCurrentUserType()).toBe(UserType.DEBUG);
        });
    });

    describe('全局用户服务', () => {
        it('应该提供全局访问实例', () => {
            expect(globalUserService).toBeDefined();
            expect(globalUserService).toBe(UserService.getInstance());
        });
    });
}); 