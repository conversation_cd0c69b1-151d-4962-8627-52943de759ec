
// Mock VSCode API
jest.mock('vscode', () => ({
  window: {
    createOutputChannel: jest.fn().mockReturnValue({
      appendLine: jest.fn(),
      append: jest.fn(),
      clear: jest.fn(),
      show: jest.fn(),
      hide: jest.fn(),
      dispose: jest.fn()
    }),
    showInformationMessage: jest.fn(),
    showErrorMessage: jest.fn(),
    showWarningMessage: jest.fn()
  },
  workspace: {
    workspaceFolders: [
      {
        uri: {
          fsPath: '/test/workspace'
        },
        name: 'test-workspace'
      }
    ],
    createFileSystemWatcher: jest.fn().mockReturnValue({
      onDidCreate: jest.fn().mockReturnValue({
        dispose: jest.fn()
      }),
      onDidChange: jest.fn().mockReturnValue({
        dispose: jest.fn()
      }),
      onDidDelete: jest.fn().mockReturnValue({
        dispose: jest.fn()
      }),
      dispose: jest.fn()
    }),
    fs: {
      readFile: jest.fn(),
      writeFile: jest.fn(),
      stat: jest.fn()
    }
  },
  Uri: {
    file: jest.fn((path) => ({ fsPath: path }))
  },
  EventEmitter: jest.fn().mockImplementation(() => ({
    event: jest.fn(),
    fire: jest.fn(),
    dispose: jest.fn()
  })),
  ExtensionContext: jest.fn().mockImplementation(() => ({
    subscriptions: [],
    globalState: {
      get: jest.fn(),
      update: jest.fn()
    },
    globalStorageUri: {
      fsPath: '/test/storage'
    }
  }))
}));

import { EnvironmentBuilder, buildEnvironmentAndUserInfo } from 'src/components/code_completion/utils/environmentBuilder';
import { IUserInfo } from 'src/components/types/user';

// Mock vscode
jest.mock('vscode', () => ({
  version: '1.80.0',
  env: {
    language: 'en'
  },
  workspace: {
    name: 'test-workspace',
    workspaceFile: { toString: () => 'file:///test/workspace.code-workspace' },
    workspaceFolders: [
      { uri: { fsPath: '/test/folder1' } },
      { uri: { fsPath: '/test/folder2' } }
    ],
    getConfiguration: jest.fn((section) => ({
      get: jest.fn((key) => {
        if (section === 'workbench' && key === 'colorTheme') {return 'dark+';}
        if (section === 'editor' && key === 'fontSize') {return 14;}
        return undefined;
      })
    }))
  },
  extensions: {
    getExtension: jest.fn(() => ({
      packageJSON: { version: '1.0.0' }
    }))
  }
}));

// Mock UserService
jest.mock('src/components/user/UserService', () => ({
  getCurrentUserInfo: jest.fn()
}));

describe('EnvironmentBuilder', () => {
  let builder: EnvironmentBuilder;

  beforeEach(() => {
    builder = EnvironmentBuilder.getInstance();
    builder.clearCache();
  });

  describe('getInstance', () => {
    it('should return the same instance', () => {
      const instance1 = EnvironmentBuilder.getInstance();
      const instance2 = EnvironmentBuilder.getInstance();
      expect(instance1).toBe(instance2);
    });
  });

  describe('buildEnvironmentInfo', () => {
    it('should build environment info correctly', async () => {
      const envInfo = await builder.buildEnvironmentInfo();

      expect(envInfo).toMatchObject({
        platform: expect.any(String),
        deviceInfo: expect.any(String),
        osInfo: expect.any(String),
        vscodeVersion: '1.80.0',
        extensionVersion: '1.0.0',
        workspaceInfo: {
          name: 'test-workspace',
          uri: 'file:///test/workspace.code-workspace',
          folders: ['/test/folder1', '/test/folder2']
        }
      });

      expect(envInfo.platform).toMatch(/^[a-z]+-[a-z0-9]+$/);
      expect(envInfo.deviceInfo).toMatch(/^[a-z]+-[a-z0-9]+-node-v\d+\.\d+\.\d+$/);
      expect(envInfo.osInfo).toMatch(/^[a-z]+-.*-v\d+\.\d+\.\d+$/);
    });

    it('should cache environment info', async () => {
      const envInfo1 = await builder.buildEnvironmentInfo();
      const envInfo2 = await builder.buildEnvironmentInfo();

      expect(envInfo1).toBe(envInfo2);
    });

    it('should refresh environment info when cache is cleared', async () => {
      const envInfo1 = await builder.buildEnvironmentInfo();
      builder.clearCache();
      const envInfo2 = await builder.buildEnvironmentInfo();

      expect(envInfo1).not.toBe(envInfo2);
      expect(envInfo1).toEqual(envInfo2);
    });
  });

  describe('buildUserInfo', () => {
    it('should build user info with valid user data', async () => {
      const mockUserInfo: IUserInfo = {
        id: 'test-user',
        name: 'Test User',
        nickname: 'test',
        department: 'Engineering'
      };

      const { getCurrentUserInfo } = require('src/components/user/UserService');
      getCurrentUserInfo.mockResolvedValue(mockUserInfo);

      const userInfo = await builder.buildUserInfo();

      expect(userInfo).toEqual(mockUserInfo);
    });

    it('should return default user info when user service fails', async () => {
      const { getCurrentUserInfo } = require('src/components/user/UserService');
      getCurrentUserInfo.mockRejectedValue(new Error('Service error'));

      const userInfo = await builder.buildUserInfo();

      expect(userInfo).toEqual({
        id: 'unknown',
        name: 'Unknown User',
        nickname: 'Unknown',
        department: 'Unknown'
      });
    });

    it('should return default user info when user service returns null', async () => {
      const { getCurrentUserInfo } = require('src/components/user/UserService');
      getCurrentUserInfo.mockResolvedValue(null);

      const userInfo = await builder.buildUserInfo();

      expect(userInfo).toEqual({
        id: 'unknown',
        name: 'Unknown User',
        nickname: 'Unknown',
        department: 'Unknown'
      });
    });

    it('should cache user info', async () => {
      const mockUserInfo: IUserInfo = {
        id: 'test-user',
        name: 'Test User',
        nickname: 'test',
        department: 'Engineering'
      };

      const { getCurrentUserInfo } = require('src/components/user/UserService');
      getCurrentUserInfo.mockResolvedValue(mockUserInfo);

      const userInfo1 = await builder.buildUserInfo();
      const userInfo2 = await builder.buildUserInfo();

      expect(userInfo1).toBe(userInfo2);
    });
  });

  describe('buildEnvironmentAndUserInfo', () => {
    it('should build both environment and user info', async () => {
      const mockUserInfo: IUserInfo = {
        id: 'test-user',
        name: 'Test User',
        nickname: 'test',
        department: 'Engineering'
      };

      const { getCurrentUserInfo } = require('src/components/user/UserService');
      getCurrentUserInfo.mockResolvedValue(mockUserInfo);

      const result = await buildEnvironmentAndUserInfo();

      expect(result).toHaveProperty('environment');
      expect(result).toHaveProperty('user');
      expect(result.environment).toBeInstanceOf(Object);
      expect(result.user).toEqual(mockUserInfo);
    });
  });
}); 