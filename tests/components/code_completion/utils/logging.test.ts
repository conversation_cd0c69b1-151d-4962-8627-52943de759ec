import { buildPromptCore, buildPromptFromMeta } from 'src/components/code_completion/prompt/completionPrompt';
import { CompletionMeta } from 'src/components/types/code_completion';

describe('Logging Format Tests', () => {
  describe('buildPromptCore', () => {
    it('应该生成格式化的提示词', () => {
      const flagBefore = 'function test() {\n  console.log("hello");\n  ';
      const flagAfter = '\n}';
      const language = 'javascript';
      const context = '// 这是一个测试函数';

      const prompt = buildPromptCore(flagBefore, flagAfter, language, context);

      // 验证提示词包含必要的元素
      expect(prompt).toContain('{{complete_code_here}}');
      expect(prompt).toContain('javascript');
      expect(prompt).toContain('Context 代码');
      expect(prompt).toContain('要续写的代码');
      expect(prompt).toContain('```');
    });

    it('应该在没有上下文时也能正常工作', () => {
      const flagBefore = 'const x = ';
      const flagAfter = ';';
      const language = 'typescript';
      const context = '';

      const prompt = buildPromptCore(flagBefore, flagAfter, language, context);

      expect(prompt).toContain('{{complete_code_here}}');
      expect(prompt).toContain('typescript');
      expect(prompt).not.toContain('Context 代码');
    });
  });

  describe('buildPromptFromMeta', () => {
    it('应该从CompletionMeta生成格式化的提示词', () => {
      const meta: CompletionMeta = {
        languageId: 'python',
        selectedContext: [
          {
            codeSnippetType: 'function',
            contextType: 'semantic',
            label: 'main',
            fileName: 'test.py',
            startLine: 1,
            endLine: 10,
            startColumn: 0,
            endColumn: 20,
            content: 'def main():\n    print("hello")'
          }
        ],
        scope: {
          scopeType: 'function',
          name: 'main',
          startLine: 1,
          endLine: 10,
          startColumn: 0,
          endColumn: 20
        }
      };

      const textBefore = 'def main():\n    print("hello")\n    ';
      const textAfter = '\n    return 0';

      const prompt = buildPromptFromMeta(meta, textBefore, textAfter);

      expect(prompt).toContain('{{complete_code_here}}');
      expect(prompt).toContain('python');
      expect(prompt).toContain('【function】');
      expect(prompt).toContain('当前续写作用域: function main');
      expect(prompt).toContain('要续写的代码');
    });

    it('应该在没有作用域信息时也能正常工作', () => {
      const meta: CompletionMeta = {
        languageId: 'javascript',
        selectedContext: [],
        scope: undefined
      };

      const textBefore = 'const x = ';
      const textAfter = ';';

      const prompt = buildPromptFromMeta(meta, textBefore, textAfter);

      expect(prompt).toContain('{{complete_code_here}}');
      expect(prompt).toContain('javascript');
      expect(prompt).not.toContain('当前续写作用域');
    });
  });

  describe('日志格式验证', () => {
    it('应该生成可读的提示词格式', () => {
      // 模拟一个复杂的提示词
      const complexPrompt = `请在{{complete_code_here}}标记处补写代码, 只返回代码，不要包含多余解释。请确保代码风格与上下文保持一致。

<important-hints>
1.{{complete_code_here}}为续写标记，请在标记位置进行代码续写
2.当前文件使用的语言为javascript
</important-hints>

<requirements>
1.不要使用模板字符串包裹结果代码。
</requirements>

Context 代码

【function】main test.js (行1-10):
function main() {
    console.log("hello");
    // 这里需要续写
}

---
要续写的代码：
\`\`\`
function main() {
    console.log("hello");
    {{complete_code_here}}
}
\`\`\`
`;

      // 验证提示词的结构
      expect(complexPrompt).toContain('{{complete_code_here}}');
      expect(complexPrompt).toContain('```');
      expect(complexPrompt).toContain('Context 代码');
      expect(complexPrompt).toContain('要续写的代码');
      expect(complexPrompt).toContain('important-hints');
      expect(complexPrompt).toContain('requirements');
    });
  });
}); 