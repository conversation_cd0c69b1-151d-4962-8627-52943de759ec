import {
    buildStructuredPrompt,
    buildStructuredPromptFromMeta,
    DEFAULT_PROMPT_CONFIG,
    getModelTokenConfig,
    MODEL_TOKENS
} from '../../../../src/components/code_completion/prompt/structuredPrompt';
import { ContextItem } from '../../../../src/components/code_context/types';
import { CompletionMeta } from '../../../../src/components/types/code_completion';

describe('structuredPrompt', () => {
  describe('getModelTokenConfig', () => {
    it('should return DeepSeek tokens for deepseek model', () => {
      const config = getModelTokenConfig('deepseek-coder');
      expect(config).toEqual(MODEL_TOKENS.DEEPSEEK_CODER);
    });

    it('should return Qwen tokens for qwen model', () => {
      const config = getModelTokenConfig('qwen-coder');
      expect(config).toEqual(MODEL_TOKENS.QWEN_CODER);
    });

    it('should return DeepSeek tokens as default', () => {
      const config = getModelTokenConfig('unknown-model');
      expect(config).toEqual(MODEL_TOKENS.DEEPSEEK_CODER);
    });
  });

  describe('buildStructuredPrompt', () => {
    const mockRelatedContext: ContextItem[] = [
      {
        content: 'function relatedFunction() { return "test"; }',
        source: 'graph',
        score: 0.9,
        contextType: 'related',
        location: {
          file: '/path/to/file.js',
          start: { line: 10, column: 0 },
          end: { line: 12, column: 1 }
        }
      }
    ];

    const mockSimilarContext: ContextItem[] = [
      {
        content: 'function similarFunction() { console.log("similar"); }',
        source: 'bm25',
        score: 0.8,
        contextType: 'similar',
        location: {
          file: '/path/to/another.js',
          start: { line: 5, column: 0 },
          end: { line: 7, column: 1 }
        }
      }
    ];

    it('should build structured prompt with all sections', () => {
      const prompt = buildStructuredPrompt(
        DEFAULT_PROMPT_CONFIG,
        'const x = ',
        '\nconsole.log(x);',
        mockRelatedContext,
        mockSimilarContext
      );

      // 验证系统提示词
      expect(prompt).toContain('You are a code completion assistant.');
      
      // 验证指令
      expect(prompt).toContain('请结合上下文在<｜fim▁hole｜>处补全代码');
      
      // 验证系统规则
      expect(prompt).toContain('<system_rules>');
      expect(prompt).toContain('请根据上下文信息进行准确的代码补全');
      
      // 验证相关上下文
      expect(prompt).toContain('<related_context>');
      expect(prompt).toContain('// filepath: /path/to/file.js');
      expect(prompt).toContain('function relatedFunction()');
      
      // 验证相似上下文
      expect(prompt).toContain('<similar_context>');
      expect(prompt).toContain('// filepath: /path/to/another.js');
      expect(prompt).toContain('function similarFunction()');
      
      // 验证代码补全部分
      expect(prompt).toContain('<｜fim▁begin｜>');
      expect(prompt).toContain('const x = ');
      expect(prompt).toContain('<｜fim▁hole｜>');
      expect(prompt).toContain('console.log(x);');
      expect(prompt).toContain('<｜fim▁end｜>');
    });

    it('should handle empty contexts', () => {
      const prompt = buildStructuredPrompt(
        DEFAULT_PROMPT_CONFIG,
        'const x = ',
        '\nconsole.log(x);',
        [],
        []
      );

      // 不应包含上下文部分
      expect(prompt).not.toContain('<related_context>');
      expect(prompt).not.toContain('<similar_context>');
      
      // 但应包含其他部分
      expect(prompt).toContain('You are a code completion assistant.');
      expect(prompt).toContain('<｜fim▁begin｜>');
    });

    it('should use Qwen tokens when configured', () => {
      const qwenConfig = {
        ...DEFAULT_PROMPT_CONFIG,
        modelTokens: MODEL_TOKENS.QWEN_CODER
      };

      const prompt = buildStructuredPrompt(
        qwenConfig,
        'const x = ',
        '\nconsole.log(x);',
        [],
        []
      );

      expect(prompt).toContain('<|fim_prefix|>');
      expect(prompt).toContain('<|fim_suffix|>');
      expect(prompt).toContain('<|fim_middle|>');
    });
  });

  describe('buildStructuredPromptFromMeta', () => {
    const mockMeta: CompletionMeta = {
      languageId: 'javascript',
      selectedContext: [],
      environment: {
        platform: 'vscode',
        deviceInfo: 'test-device',
        osInfo: 'test-os',
        vscodeVersion: '1.80.0',
        extensionVersion: '1.0.0'
      }
    };

    const mockRelatedContext: ContextItem[] = [
      {
        content: 'function helper() { return 42; }',
        source: 'graph',
        score: 0.95,
        contextType: 'related',
        location: {
          file: '/src/helper.js',
          start: { line: 1, column: 0 },
          end: { line: 3, column: 1 }
        }
      }
    ];

    it('should build prompt from meta with environment info', () => {
      const prompt = buildStructuredPromptFromMeta(
        mockMeta,
        'function test() {',
        '\n}',
        mockRelatedContext,
        []
      );

      // 验证环境信息
      expect(prompt).toContain('<env>');
      expect(prompt).toContain('平台: vscode');
      expect(prompt).toContain('语言: javascript');
      expect(prompt).toContain('VSCode版本: 1.80.0');
      
      // 验证相关上下文
      expect(prompt).toContain('<related_context>');
      expect(prompt).toContain('// filepath: /src/helper.js');
      expect(prompt).toContain('function helper()');
    });

    it('should adjust instruction for Qwen model', () => {
      const qwenConfig = {
        modelTokens: MODEL_TOKENS.QWEN_CODER
      };

      const prompt = buildStructuredPromptFromMeta(
        mockMeta,
        'const x = ',
        '\nconsole.log(x);',
        [],
        [],
        qwenConfig
      );

      expect(prompt).toContain('请结合上下文在<|fim_suffix|>处补全代码');
      expect(prompt).toContain('<|fim_prefix|>');
      expect(prompt).toContain('<|fim_suffix|>');
      expect(prompt).toContain('<|fim_middle|>');
    });

    it('should handle user snippets in similar context', () => {
      const userSnippetContext: ContextItem[] = [
        {
          content: 'console.log("debug:", value);',
          source: 'user-snippets',
          score: 1.0,
          contextType: 'similar',
          userSnippet: {
            id: 'debug-log-1',
            name: 'debug-log',
            priority: 1,
            similarity: 1.0
          }
        }
      ];

      const prompt = buildStructuredPromptFromMeta(
        mockMeta,
        'const value = 42;',
        '\nreturn value;',
        [],
        userSnippetContext
      );

      expect(prompt).toContain('<similar_context>');
      expect(prompt).toContain('// filepath: debug-log');
      expect(prompt).toContain('console.log("debug:", value);');
    });
  });

  describe('context formatting', () => {
    it('should format context with proper file paths', () => {
      const contextWithFile: ContextItem[] = [
        {
          content: 'const API_URL = "https://api.example.com";',
          source: 'graph',
          score: 0.7,
          contextType: 'related',
          location: {
            file: '/src/config/api.js',
            start: { line: 1, column: 0 },
            end: { line: 1, column: 40 }
          }
        }
      ];

      const prompt = buildStructuredPrompt(
        DEFAULT_PROMPT_CONFIG,
        'const config = {',
        '\n};',
        contextWithFile,
        []
      );

      expect(prompt).toContain('// filepath: /src/config/api.js');
      expect(prompt).toContain('const API_URL = "https://api.example.com";');
    });

    it('should handle unknown file paths', () => {
      const contextWithoutFile: ContextItem[] = [
        {
          content: 'function unknownSource() {}',
          source: 'embedding',
          score: 0.6,
          contextType: 'similar'
        }
      ];

      const prompt = buildStructuredPrompt(
        DEFAULT_PROMPT_CONFIG,
        'function test() {',
        '\n}',
        [],
        contextWithoutFile
      );

      expect(prompt).toContain('// filepath: [unknown]');
      expect(prompt).toContain('function unknownSource() {}');
    });
  });
});
