
// Mock VSCode API
jest.mock('vscode', () => ({
  window: {
    createOutputChannel: jest.fn().mockReturnValue({
      appendLine: jest.fn(),
      append: jest.fn(),
      clear: jest.fn(),
      show: jest.fn(),
      hide: jest.fn(),
      dispose: jest.fn()
    }),
    showInformationMessage: jest.fn(),
    showErrorMessage: jest.fn(),
    showWarningMessage: jest.fn()
  },
  workspace: {
    workspaceFolders: [
      {
        uri: {
          fsPath: '/test/workspace'
        },
        name: 'test-workspace'
      }
    ],
    createFileSystemWatcher: jest.fn().mockReturnValue({
      onDidCreate: jest.fn().mockReturnValue({
        dispose: jest.fn()
      }),
      onDidChange: jest.fn().mockReturnValue({
        dispose: jest.fn()
      }),
      onDidDelete: jest.fn().mockReturnValue({
        dispose: jest.fn()
      }),
      dispose: jest.fn()
    }),
    fs: {
      readFile: jest.fn(),
      writeFile: jest.fn(),
      stat: jest.fn()
    }
  },
  Uri: {
    file: jest.fn((path) => ({ fsPath: path }))
  },
  EventEmitter: jest.fn().mockImplementation(() => ({
    event: jest.fn(),
    fire: jest.fn(),
    dispose: jest.fn()
  })),
  ExtensionContext: jest.fn().mockImplementation(() => ({
    subscriptions: [],
    globalState: {
      get: jest.fn(),
      update: jest.fn()
    },
    globalStorageUri: {
      fsPath: '/test/storage'
    }
  }))
}));

import { CompletionStatus, CompletionStatusBar } from 'src/components/code_completion/status/CompletionStatusBar';

// Mock vscode
jest.mock('vscode', () => {
  const mockStatusBarItem = {
    text: '',
    tooltip: '',
    backgroundColor: undefined,
    show: jest.fn(),
    hide: jest.fn(),
    dispose: jest.fn()
  };

  const mockWindow = {
    createStatusBarItem: jest.fn(() => mockStatusBarItem),
    createOutputChannel: jest.fn(() => ({
      appendLine: jest.fn(),
      show: jest.fn(),
      dispose: jest.fn()
    }))
  };

  return {
    window: mockWindow,
    StatusBarAlignment: {
      Right: 1
    },
    ThemeColor: jest.fn()
  };
});

// 获取mock引用
const mockVscode = require('vscode');
const mockStatusBarItem = mockVscode.window.createStatusBarItem();

describe('CompletionStatusBar', () => {
  let statusBar: CompletionStatusBar;

  beforeEach(() => {
    // 重置单例
    (CompletionStatusBar as any).instance = undefined;

    // 重置 mock
    jest.clearAllMocks();

    statusBar = CompletionStatusBar.getInstance();
  });

  afterEach(() => {
    if (statusBar) {
      statusBar.dispose();
    }
    // 确保重置单例
    (CompletionStatusBar as any).instance = undefined;
  });

  describe('单例模式', () => {
    it('应该返回相同的实例', () => {
      const instance1 = CompletionStatusBar.getInstance();
      const instance2 = CompletionStatusBar.getInstance();
      expect(instance1).toBe(instance2);
    });
  });

  describe('状态更新', () => {
    it('应该更新思考状态', () => {
      statusBar.updateStatus(CompletionStatus.THINKING, '正在分析代码');

      expect(mockStatusBarItem.text).toContain('续写中');
      expect(mockStatusBarItem.tooltip).toContain('正在生成代码');
      expect(mockStatusBarItem.show).toHaveBeenCalled();
    });

    it('应该更新成功状态', () => {
      statusBar.updateStatus(CompletionStatus.SUCCESS, '续写完成');

      expect(mockStatusBarItem.text).toContain('续写完成');
      expect(mockStatusBarItem.tooltip).toContain('续写成功');
      expect(mockStatusBarItem.show).toHaveBeenCalled();
    });

    it('应该更新失败状态', () => {
      statusBar.updateStatus(CompletionStatus.FAILED, '生成失败');

      expect(mockStatusBarItem.text).toContain('续写失败');
      expect(mockStatusBarItem.tooltip).toContain('续写失败');
      expect(mockStatusBarItem.show).toHaveBeenCalled();
    });

    it('应该更新取消状态', () => {
      statusBar.updateStatus(CompletionStatus.CANCELLED, '用户取消');

      expect(mockStatusBarItem.text).toContain('续写取消');
      expect(mockStatusBarItem.tooltip).toContain('续写已取消');
      expect(mockStatusBarItem.show).toHaveBeenCalled();
    });

    it('应该隐藏空闲状态', () => {
      statusBar.updateStatus(CompletionStatus.IDLE);

      expect(mockStatusBarItem.hide).toHaveBeenCalled();
    });
  });

  describe('临时状态显示', () => {
    beforeEach(() => {
      jest.useFakeTimers();
    });

    afterEach(() => {
      jest.useRealTimers();
    });

    it('应该显示临时状态并自动隐藏', () => {
      statusBar.showTemporaryStatus(CompletionStatus.SUCCESS, '临时消息', 2000);

      expect(mockStatusBarItem.text).toContain('续写完成');
      expect(mockStatusBarItem.show).toHaveBeenCalled();

      // 快进时间
      jest.advanceTimersByTime(2000);

      expect(mockStatusBarItem.hide).toHaveBeenCalled();
    });

    it('应该清除之前的定时器', () => {
      // 重置mock调用次数
      jest.clearAllMocks();

      statusBar.showTemporaryStatus(CompletionStatus.SUCCESS, '消息1', 3000);

      // 在定时器到期前再次调用
      statusBar.showTemporaryStatus(CompletionStatus.FAILED, '消息2', 1000);

      expect(mockStatusBarItem.text).toContain('续写失败');

      // 快进时间，应该只隐藏一次
      jest.advanceTimersByTime(1000);

      expect(mockStatusBarItem.hide).toHaveBeenCalledTimes(1);
    });
  });

  describe('进度状态显示', () => {
    it('应该显示进度状态', () => {
      statusBar.showProgressStatus('正在生成', 0.5);

      expect(mockStatusBarItem.text).toContain('正在生成');
      expect(mockStatusBarItem.text).toContain('50%');
      expect(mockStatusBarItem.show).toHaveBeenCalled();
    });

    it('应该显示无进度的状态', () => {
      statusBar.showProgressStatus('正在生成');

      expect(mockStatusBarItem.text).toContain('正在生成');
      expect(mockStatusBarItem.text).not.toContain('%');
      expect(mockStatusBarItem.show).toHaveBeenCalled();
    });
  });

  describe('状态获取', () => {
    it('应该返回当前状态', () => {
      statusBar.updateStatus(CompletionStatus.THINKING);

      const currentStatus = statusBar.getCurrentStatus();
      expect(currentStatus).toBe(CompletionStatus.THINKING);
    });
  });

  describe('默认消息', () => {
    it('应该使用默认消息', () => {
      statusBar.updateStatus(CompletionStatus.THINKING);

      expect(mockStatusBarItem.tooltip).toContain('正在分析代码并生成建议');
    });

    it('应该使用自定义消息', () => {
      const customMessage = '自定义消息';
      statusBar.updateStatus(CompletionStatus.THINKING, customMessage);

      expect(mockStatusBarItem.tooltip).toContain(customMessage);
    });
  });

  describe('资源清理', () => {
    it('应该正确清理资源', () => {
      statusBar.dispose();

      expect(mockStatusBarItem.dispose).toHaveBeenCalled();
    });

    it('应该清理定时器', () => {
      jest.useFakeTimers();

      // 重置mock调用次数
      jest.clearAllMocks();

      statusBar.showTemporaryStatus(CompletionStatus.SUCCESS, '消息', 3000);
      statusBar.dispose();

      // 快进时间，不应该触发隐藏
      jest.advanceTimersByTime(3000);

      expect(mockStatusBarItem.hide).not.toHaveBeenCalled();

      jest.useRealTimers();
    });
  });
}); 