
// Mock VSCode API
jest.mock('vscode', () => ({
  window: {
    createOutputChannel: jest.fn().mockReturnValue({
      appendLine: jest.fn(),
      append: jest.fn(),
      clear: jest.fn(),
      show: jest.fn(),
      hide: jest.fn(),
      dispose: jest.fn()
    }),
    showInformationMessage: jest.fn(),
    showErrorMessage: jest.fn(),
    showWarningMessage: jest.fn()
  },
  workspace: {
    workspaceFolders: [
      {
        uri: {
          fsPath: '/test/workspace'
        },
        name: 'test-workspace'
      }
    ],
    createFileSystemWatcher: jest.fn().mockReturnValue({
      onDidCreate: jest.fn().mockReturnValue({
        dispose: jest.fn()
      }),
      onDidChange: jest.fn().mockReturnValue({
        dispose: jest.fn()
      }),
      onDidDelete: jest.fn().mockReturnValue({
        dispose: jest.fn()
      }),
      dispose: jest.fn()
    }),
    fs: {
      readFile: jest.fn(),
      writeFile: jest.fn(),
      stat: jest.fn()
    }
  },
  Uri: {
    file: jest.fn((path) => ({ fsPath: path }))
  },
  EventEmitter: jest.fn().mockImplementation(() => ({
    event: jest.fn(),
    fire: jest.fn(),
    dispose: jest.fn()
  })),
  ExtensionContext: jest.fn().mockImplementation(() => ({
    subscriptions: [],
    globalState: {
      get: jest.fn(),
      update: jest.fn()
    },
    globalStorageUri: {
      fsPath: '/test/storage'
    }
  }))
}));

import { CompletionStatus } from 'src/components/code_completion/status/CompletionStatusBar';
import { CompletionEventManager, CompletionEventType } from 'src/components/code_completion/status/events/CompletionEventManager';

// Mock vscode
const mockEventEmitter = {
  fire: jest.fn(),
  event: jest.fn((callback: any) => {
    // 存储回调函数，当fire被调用时执行
    mockEventEmitter._callback = callback;
    return { dispose: jest.fn() };
  }),
  dispose: jest.fn(),
  _callback: null as any
};

jest.mock('vscode', () => ({
  EventEmitter: jest.fn(() => mockEventEmitter),
  window: {
    createOutputChannel: jest.fn(() => ({
      appendLine: jest.fn(),
      show: jest.fn(),
      dispose: jest.fn()
    })),
    createStatusBarItem: jest.fn().mockReturnValue({
      show: jest.fn(),
      hide: jest.fn(),
      dispose: jest.fn(),
      text: '',
      tooltip: '',
      command: ''
    })
  }
}));

describe('CompletionEventManager', () => {
  let eventManager: CompletionEventManager;

  beforeEach(() => {
    // 重置单例
    (CompletionEventManager as any).instance = undefined;
    
    // 重置 mock
    jest.clearAllMocks();
    
    eventManager = CompletionEventManager.getInstance();
  });

  afterEach(() => {
    eventManager.dispose();
  });

  describe('单例模式', () => {
    it('应该返回相同的实例', () => {
      const instance1 = CompletionEventManager.getInstance();
      const instance2 = CompletionEventManager.getInstance();
      expect(instance1).toBe(instance2);
    });
  });

  describe('事件订阅', () => {
    it('应该能够订阅单个事件', () => {
      const mockListener = jest.fn();
      const disposable = eventManager.subscribe(CompletionEventType.STARTED, mockListener);
      
      expect(disposable).toBeDefined();
      expect(typeof disposable.dispose).toBe('function');
      
      disposable.dispose();
    });

    it('应该能够订阅多个事件', () => {
      const mockListener = jest.fn();
      const disposable = eventManager.subscribe(
        [CompletionEventType.STARTED, CompletionEventType.SUCCESS],
        mockListener
      );
      
      expect(disposable).toBeDefined();
      
      disposable.dispose();
    });
  });

  describe('事件发布', () => {
    it('应该发布续写开始事件', () => {
      const metadata = { filePath: '/test/file.ts', language: 'typescript' };
      
      eventManager.publishStarted(metadata);
      
      expect(mockEventEmitter.fire).toHaveBeenCalledWith(
        expect.objectContaining({
          eventType: CompletionEventType.STARTED,
          status: CompletionStatus.THINKING,
          message: '续写开始',
          metadata
        })
      );
    });

    it('应该发布续写思考事件', () => {
      const message = '正在分析代码';
      const metadata = { filePath: '/test/file.ts' };
      
      eventManager.publishThinking(message, metadata);
      
      expect(mockEventEmitter.fire).toHaveBeenCalledWith(
        expect.objectContaining({
          eventType: CompletionEventType.THINKING,
          status: CompletionStatus.THINKING,
          message,
          metadata
        })
      );
    });

    it('应该发布进度更新事件', () => {
      const progress = 0.5;
      const message = '生成中...';
      const metadata = { filePath: '/test/file.ts' };
      
      eventManager.publishProgress(progress, message, metadata);
      
      expect(mockEventEmitter.fire).toHaveBeenCalledWith(
        expect.objectContaining({
          eventType: CompletionEventType.PROGRESS,
          status: CompletionStatus.THINKING,
          message,
          progress,
          metadata
        })
      );
    });

    it('应该发布续写成功事件', () => {
      const suggestionText = 'test code';
      const metadata = { filePath: '/test/file.ts' };
      
      eventManager.publishSuccess(suggestionText, metadata);
      
      expect(mockEventEmitter.fire).toHaveBeenCalledWith(
        expect.objectContaining({
          eventType: CompletionEventType.SUCCESS,
          status: CompletionStatus.SUCCESS,
          message: '续写完成',
          metadata: {
            ...metadata,
            suggestionText
          }
        })
      );
    });

    it('应该发布续写失败事件', () => {
      const error = new Error('Test error');
      const metadata = { filePath: '/test/file.ts' };
      
      eventManager.publishFailed(error, metadata);
      
      expect(mockEventEmitter.fire).toHaveBeenCalledWith(
        expect.objectContaining({
          eventType: CompletionEventType.FAILED,
          status: CompletionStatus.FAILED,
          message: 'Test error',
          error,
          metadata
        })
      );
    });

    it('应该发布续写取消事件', () => {
      const metadata = { filePath: '/test/file.ts' };
      
      eventManager.publishCancelled(metadata);
      
      expect(mockEventEmitter.fire).toHaveBeenCalledWith(
        expect.objectContaining({
          eventType: CompletionEventType.CANCELLED,
          status: CompletionStatus.CANCELLED,
          message: '续写已取消',
          metadata
        })
      );
    });

    it('应该发布用户接受事件', () => {
      const suggestionText = 'test code';
      const originalText = 'original';
      const metadata = { filePath: '/test/file.ts' };
      
      eventManager.publishAccepted(suggestionText, originalText, metadata);
      
      expect(mockEventEmitter.fire).toHaveBeenCalledWith(
        expect.objectContaining({
          eventType: CompletionEventType.ACCEPTED,
          status: CompletionStatus.SUCCESS,
          message: '用户接受了续写建议',
          metadata: {
            ...metadata,
            suggestionText,
            originalText
          }
        })
      );
    });

    it('应该发布用户拒绝事件', () => {
      const suggestionText = 'test code';
      const metadata = { filePath: '/test/file.ts' };
      
      eventManager.publishRejected(suggestionText, metadata);
      
      expect(mockEventEmitter.fire).toHaveBeenCalledWith(
        expect.objectContaining({
          eventType: CompletionEventType.REJECTED,
          status: CompletionStatus.IDLE,
          message: '用户拒绝了续写建议',
          metadata: {
            ...metadata,
            suggestionText
          }
        })
      );
    });

    it('应该发布状态变化事件', () => {
      const status = CompletionStatus.THINKING;
      const message = '状态变化';
      const metadata = { filePath: '/test/file.ts' };
      
      eventManager.publishStatusChanged(status, message, metadata);
      
      expect(mockEventEmitter.fire).toHaveBeenCalledWith(
        expect.objectContaining({
          eventType: CompletionEventType.STATUS_CHANGED,
          status,
          message,
          metadata
        })
      );
    });
  });

  describe('自定义事件发布', () => {
    it('应该发布自定义事件', () => {
      const customEvent = {
        eventType: CompletionEventType.STARTED,
        timestamp: Date.now(),
        status: CompletionStatus.THINKING,
        message: '自定义事件',
        metadata: { custom: 'data' }
      };
      
      eventManager.publish(customEvent);
      
      expect(mockEventEmitter.fire).toHaveBeenCalledWith(customEvent);
    });
  });

  describe('事件监听器执行', () => {
    it('应该正确执行同步监听器', () => {
      const mockListener = jest.fn();
      eventManager.subscribe(CompletionEventType.STARTED, mockListener);
      
      const event = {
        eventType: CompletionEventType.STARTED,
        timestamp: Date.now(),
        status: CompletionStatus.THINKING,
        message: '测试事件'
      };
      
      eventManager.publish(event);
      
      // 手动触发回调
      if (mockEventEmitter._callback) {
        mockEventEmitter._callback(event);
      }
      
      expect(mockListener).toHaveBeenCalledWith(event);
    });

    it('应该正确处理异步监听器', async () => {
      const mockListener = jest.fn().mockResolvedValue(undefined);
      eventManager.subscribe(CompletionEventType.STARTED, mockListener);
      
      const event = {
        eventType: CompletionEventType.STARTED,
        timestamp: Date.now(),
        status: CompletionStatus.THINKING,
        message: '测试事件'
      };
      
      eventManager.publish(event);
      
      // 手动触发回调
      if (mockEventEmitter._callback) {
        await mockEventEmitter._callback(event);
      }
      
      expect(mockListener).toHaveBeenCalledWith(event);
    });

    it('应该处理监听器错误', () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
      const mockListener = jest.fn().mockImplementation(() => {
        throw new Error('监听器错误');
      });
      
      eventManager.subscribe(CompletionEventType.STARTED, mockListener);
      
      const event = {
        eventType: CompletionEventType.STARTED,
        timestamp: Date.now(),
        status: CompletionStatus.THINKING,
        message: '测试事件'
      };
      
      eventManager.publish(event);
      
      // 手动触发回调
      if (mockEventEmitter._callback) {
        mockEventEmitter._callback(event);
      }
      
      expect(mockListener).toHaveBeenCalledWith(event);
      
      consoleSpy.mockRestore();
    });
  });

  describe('资源清理', () => {
    it('应该正确清理资源', () => {
      const disposeSpy = jest.spyOn(eventManager, 'dispose');
      
      eventManager.dispose();
      
      expect(disposeSpy).toHaveBeenCalled();
      expect(mockEventEmitter.dispose).toHaveBeenCalled();
    });
  });
}); 