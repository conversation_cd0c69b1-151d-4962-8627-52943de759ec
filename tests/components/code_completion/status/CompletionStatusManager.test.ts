
// Mock VSCode API
jest.mock('vscode', () => ({
  window: {
    createOutputChannel: jest.fn().mockReturnValue({
      appendLine: jest.fn(),
      append: jest.fn(),
      clear: jest.fn(),
      show: jest.fn(),
      hide: jest.fn(),
      dispose: jest.fn()
    }),
    showInformationMessage: jest.fn(),
    showErrorMessage: jest.fn(),
    showWarningMessage: jest.fn()
  },
  workspace: {
    workspaceFolders: [
      {
        uri: {
          fsPath: '/test/workspace'
        },
        name: 'test-workspace'
      }
    ],
    createFileSystemWatcher: jest.fn().mockReturnValue({
      onDidCreate: jest.fn().mockReturnValue({
        dispose: jest.fn()
      }),
      onDidChange: jest.fn().mockReturnValue({
        dispose: jest.fn()
      }),
      onDidDelete: jest.fn().mockReturnValue({
        dispose: jest.fn()
      }),
      dispose: jest.fn()
    }),
    fs: {
      readFile: jest.fn(),
      writeFile: jest.fn(),
      stat: jest.fn()
    }
  },
  Uri: {
    file: jest.fn((path) => ({ fsPath: path }))
  },
  EventEmitter: jest.fn().mockImplementation(() => ({
    event: jest.fn(),
    fire: jest.fn(),
    dispose: jest.fn()
  })),
  ExtensionContext: jest.fn().mockImplementation(() => ({
    subscriptions: [],
    globalState: {
      get: jest.fn(),
      update: jest.fn()
    },
    globalStorageUri: {
      fsPath: '/test/storage'
    }
  }))
}));

import { CompletionStatus, CompletionStatusBar } from 'src/components/code_completion/status/CompletionStatusBar';
import { CompletionStatusManager } from 'src/components/code_completion/status/CompletionStatusManager';
import { CompletionEventManager, CompletionEventType } from 'src/components/code_completion/status/events/CompletionEventManager';

// Mock vscode
jest.mock('vscode', () => {
  const mockStatusBarItem = {
    text: '',
    tooltip: '',
    backgroundColor: undefined,
    show: jest.fn(),
    hide: jest.fn(),
    dispose: jest.fn()
  };

  const mockEventEmitter = {
    event: jest.fn((callback: any) => {
      // 存储回调函数，当fire被调用时执行
      mockEventEmitter._callback = callback;
      return { dispose: jest.fn() };
    }),
    fire: jest.fn((event: any) => {
      // 当fire被调用时，执行存储的回调
      if (mockEventEmitter._callback) {
        mockEventEmitter._callback(event);
      }
    }),
    dispose: jest.fn(),
    _callback: null as any
  };

  const mockWindow = {
    createStatusBarItem: jest.fn(() => mockStatusBarItem),
    createOutputChannel: jest.fn(() => ({
      appendLine: jest.fn(),
      show: jest.fn(),
      dispose: jest.fn()
    })),
    showErrorMessage: jest.fn(),
    showInformationMessage: jest.fn()
  };

  return {
    window: mockWindow,
    StatusBarAlignment: {
      Right: 1
    },
    ThemeColor: jest.fn(),
    EventEmitter: jest.fn(() => mockEventEmitter)
  };
});

// 获取mock引用
const mockVscode = require('vscode');
const mockStatusBarItem = mockVscode.window.createStatusBarItem();
const mockEventEmitter = new mockVscode.EventEmitter();

describe('CompletionStatusManager', () => {
  let statusManager: CompletionStatusManager;
  let eventManager: CompletionEventManager;

  beforeEach(() => {
    // 重置单例
    (CompletionStatusManager as any).instance = undefined;
    (CompletionEventManager as any).instance = undefined;
    (CompletionStatusBar as any).instance = undefined;

    // 重置 mock
    jest.clearAllMocks();

    statusManager = CompletionStatusManager.getInstance();
    eventManager = CompletionEventManager.getInstance();
  });

  afterEach(() => {
    if (statusManager) {
      statusManager.dispose();
    }
    // 确保重置单例
    (CompletionStatusManager as any).instance = undefined;
    (CompletionEventManager as any).instance = undefined;
    (CompletionStatusBar as any).instance = undefined;
  });

  describe('单例模式', () => {
    it('应该返回相同的实例', () => {
      const instance1 = CompletionStatusManager.getInstance();
      const instance2 = CompletionStatusManager.getInstance();
      expect(instance1).toBe(instance2);
    });
  });

  describe('事件处理', () => {
    it('应该处理续写开始事件', () => {
      const payload = {
        prompt: 'test prompt',
        modelConfig: { model: 'gpt-4', max_tokens: 256, temperature: 0.2 },
        meta: {
          fileName: '/test/file.ts',
          languageId: 'typescript',
          environment: {} as any,
          user: {} as any,
          fileContent: 'test content',
          rawContextList: [],
          selectedContext: []
        }
      };

      statusManager.startCompletion(payload);

      expect(mockStatusBarItem.show).toHaveBeenCalled();
      expect(mockStatusBarItem.text).toContain('续写中');
    });

    it('应该处理续写成功事件', () => {
      const payload = {
        prompt: 'test prompt',
        modelConfig: { model: 'gpt-4', max_tokens: 256, temperature: 0.2 },
        meta: {
          fileName: '/test/file.ts',
          languageId: 'typescript',
          environment: {} as any,
          user: {} as any,
          fileContent: 'test content',
          rawContextList: [],
          selectedContext: []
        }
      };

      statusManager.completionSuccess('test code', payload);

      expect(mockStatusBarItem.text).toContain('续写完成');
    });

    it('应该处理续写失败事件', () => {
      const error = new Error('Test error');
      const payload = {
        prompt: 'test prompt',
        modelConfig: { model: 'gpt-4', max_tokens: 256, temperature: 0.2 },
        meta: {
          fileName: '/test/file.ts',
          languageId: 'typescript',
          environment: {} as any,
          user: {} as any,
          fileContent: 'test content',
          rawContextList: [],
          selectedContext: []
        }
      };

      statusManager.completionFailed(error, payload);

      expect(mockStatusBarItem.text).toContain('续写失败');
    });

    it('应该处理用户接受事件', () => {
      const payload = {
        prompt: 'test prompt',
        modelConfig: { model: 'gpt-4', max_tokens: 256, temperature: 0.2 },
        meta: {
          fileName: '/test/file.ts',
          languageId: 'typescript',
          environment: {} as any,
          user: {} as any,
          fileContent: 'test content',
          rawContextList: [],
          selectedContext: []
        }
      };

      statusManager.userAccepted('test code', 'original', payload);

      expect(mockStatusBarItem.text).toContain('续写完成');
    });

    it('应该处理用户拒绝事件', () => {
      const payload = {
        prompt: 'test prompt',
        modelConfig: { model: 'gpt-4', max_tokens: 256, temperature: 0.2 },
        meta: {
          fileName: '/test/file.ts',
          languageId: 'typescript',
          environment: {} as any,
          user: {} as any,
          fileContent: 'test content',
          rawContextList: [],
          selectedContext: []
        }
      };

      statusManager.userRejected('test code', payload);

      expect(mockStatusBarItem.text).toContain('续写取消');
    });
  });

  describe('状态获取', () => {
    it('应该返回当前状态', () => {
      // 确保状态重置为IDLE
      (CompletionStatusBar as any).instance = undefined;
      statusManager = CompletionStatusManager.getInstance();

      const status = statusManager.getCurrentStatus();
      expect(status).toBe(CompletionStatus.IDLE);
    });
  });

  describe('事件订阅', () => {
    it('应该能够订阅事件', () => {
      const mockListener = jest.fn();
      const disposable = statusManager.subscribe(CompletionEventType.STARTED, mockListener);

      expect(disposable).toBeDefined();
      expect(typeof disposable.dispose).toBe('function');

      disposable.dispose();
    });
  });

  describe('资源清理', () => {
    it('应该正确清理资源', () => {
      const disposeSpy = jest.spyOn(statusManager, 'dispose');

      statusManager.dispose();

      expect(disposeSpy).toHaveBeenCalled();
    });
  });
}); 