
// Mock VSCode API
jest.mock('vscode', () => ({
  window: {
    activeTextEditor: {
      document: {
        uri: { fsPath: '/test/workspace/test.ts' },
        languageId: 'typescript',
        fileName: '/test/workspace/test.ts',
        lineAt: jest.fn(() => ({ 
          text: 'test', 
          range: { 
            start: { line: 0, character: 0 }, 
            end: { line: 0, character: 4 } 
          } 
        })),
        getText: jest.fn(() => 'test'),
        lineCount: 1
      },
      selection: {
        active: { line: 0, character: 0 }
      },
      setDecorations: jest.fn()
    },
    createOutputChannel: jest.fn().mockReturnValue({
      appendLine: jest.fn(),
      append: jest.fn(),
      clear: jest.fn(),
      show: jest.fn(),
      hide: jest.fn(),
      dispose: jest.fn()
    }),
    createStatusBarItem: jest.fn().mockReturnValue({
      text: '',
      tooltip: '',
      command: '',
      show: jest.fn(),
      hide: jest.fn(),
      dispose: jest.fn()
    }),
    showInformationMessage: jest.fn(),
    showErrorMessage: jest.fn(),
    showWarningMessage: jest.fn(),
    visibleTextEditors: [],
    onDidChangeActiveTextEditor: jest.fn().mockReturnValue({ dispose: jest.fn() }),
    onDidChangeTextEditorSelection: jest.fn().mockReturnValue({ dispose: jest.fn() })
  },
  workspace: {
    workspaceFolders: [
      {
        uri: {
          fsPath: '/test/workspace'
        },
        name: 'test-workspace'
      }
    ],
    createFileSystemWatcher: jest.fn().mockReturnValue({
      onDidCreate: jest.fn().mockReturnValue({
        dispose: jest.fn()
      }),
      onDidChange: jest.fn().mockReturnValue({
        dispose: jest.fn()
      }),
      onDidDelete: jest.fn().mockReturnValue({
        dispose: jest.fn()
      }),
      dispose: jest.fn()
    }),
    onDidChangeTextDocument: jest.fn().mockReturnValue({ dispose: jest.fn() }),
    onDidChangeWorkspaceFolders: jest.fn().mockReturnValue({ dispose: jest.fn() }),
    onDidOpenTextDocument: jest.fn().mockReturnValue({ dispose: jest.fn() }),
    onDidCloseTextDocument: jest.fn().mockReturnValue({ dispose: jest.fn() }),
    onDidSaveTextDocument: jest.fn().mockReturnValue({ dispose: jest.fn() }),
    onDidCreateFiles: jest.fn().mockReturnValue({ dispose: jest.fn() }),
    onDidDeleteFiles: jest.fn().mockReturnValue({ dispose: jest.fn() }),
    onDidChangeConfiguration: jest.fn().mockReturnValue({ dispose: jest.fn() }),
    fs: {
      readFile: jest.fn(),
      writeFile: jest.fn(),
      stat: jest.fn()
    },
    getConfiguration: jest.fn((section) => ({
      get: jest.fn((key: string) => {
        if (section === 'code-partner') {
          const defaults: Record<string, any> = {
            usePythonService: true,
            pythonEndpoint: 'http://127.0.0.1:5555/api/v1/completion',
            openAIApiKey: '',
            model: 'qwen2.5-coder:3b',
            statisticsEndpoint: 'http://127.0.0.1:5555/api/v1/statistics/events',
            enableStatistics: true,
            maxTokens: 256,
            temperature: 0.2,
            enableInlineCompletion: true,
            enableDiffHighlightCompletion: true,
            enableIndexing: true,
            autoIndexing: true,
            languageExtensions: ['*'],
            graphWeight: 0.4,
            bm25Weight: 0.3,
            embeddingWeight: 0.3,
            logLevel: 'INFO',
            enableDebugLog: false
          };
          return defaults[key];
        }
        return undefined;
      }),
      update: jest.fn()
    }))
  },
  Uri: {
    file: jest.fn((path) => ({ fsPath: path, toString: () => `file://${path}` }))
  },
  Position: jest.fn().mockImplementation(function(line, character) {
    return {
      line,
      character,
      translate: jest.fn(),
      with: jest.fn(),
      compareTo: jest.fn()
    };
  }),
  Range: jest.fn().mockImplementation((start, end) => ({
    start,
    end,
    isEmpty: jest.fn(),
    isSingleLine: jest.fn(),
    contains: jest.fn(),
    isEqual: jest.fn(),
    union: jest.fn(),
    intersection: jest.fn()
  })),
  EventEmitter: jest.fn().mockImplementation(() => ({
    event: jest.fn(),
    fire: jest.fn(),
    dispose: jest.fn()
  })),
  ExtensionContext: jest.fn().mockImplementation(() => ({
    subscriptions: [],
    globalState: {
      get: jest.fn(),
      update: jest.fn()
    },
    globalStorageUri: {
      fsPath: '/test/storage'
    }
  })),
  languages: {
    registerCompletionItemProvider: jest.fn(),
    registerCodeLensProvider: jest.fn(),
    registerInlineCompletionItemProvider: jest.fn(),
  },
  commands: {
    registerCommand: jest.fn(),
    executeCommand: jest.fn(),
    getCommands: jest.fn().mockResolvedValue([
      'code-partner.extractSFTData',
      'code-partner.helloWorld',
      'code-partner.test',
      'code-partner.reinitializeWorkspaceIndex',
      'code-partner.forceReinitializeIndex',
      'code-partner.queryRelatedFunctions',
      'code-partner.updateRepoIndex',
      'code-partner.showRepoIndex'
    ])
  },
  StatusBarAlignment: {
    Left: 1,
    Right: 2
  },
  StatusBarItem: jest.fn().mockImplementation(() => ({
    text: '',
    tooltip: '',
    command: '',
    show: jest.fn(),
    hide: jest.fn(),
    dispose: jest.fn()
  })),
  ConfigurationTarget: {
    Global: 1,
    Workspace: 2
  },
  authentication: {
    getSession: jest.fn().mockResolvedValue(undefined)
  },
  extensions: {
    getExtension: jest.fn().mockReturnValue({ 
      packageJSON: { version: '1.0.0' },
      exports: {
        getAPI: jest.fn().mockReturnValue({
          repositories: [{
            getConfig: jest.fn().mockResolvedValue('test-user')
          }]
        })
      }
    })
  },
  version: '1.80.0',
  env: {
    language: 'en',
    machineId: 'test-machine-id',
    sessionId: 'test-session-id'
  }
}));

import * as vscode from 'vscode';

// 模拟createCompletionItem函数的逻辑
function createCompletionItem(completionText: string, position: vscode.Position) {
    // 统一换行符处理：将\r\n转换为\n，确保跨平台一致性
    const normalizedText = completionText.replace(/\r\n/g, '\n');

    // 计算补全内容的行数（使用标准化后的文本）
    const lines = normalizedText.split('\n');
    let endPosition: vscode.Position;

    if (lines.length === 1) {
        // 单行内容：只需要计算字符偏移
        endPosition = new vscode.Position(position.line, position.character + normalizedText.length);
    } else {
        // 多行内容：计算最终行和列位置
        const lastLineIndex = lines.length - 1;
        const lastLineLength = lines[lastLineIndex].length;
        endPosition = new vscode.Position(
            position.line + lastLineIndex,
            lastLineIndex === 0 ? position.character + lastLineLength : lastLineLength
        );
    }

    return { normalizedText, lines, endPosition };
}

// 模拟isEmptyOrOnlyNewlines函数
function isEmptyOrOnlyNewlines(text: string): boolean {
    // 统一换行符处理，确保跨平台一致性
    const normalizedText = text.replace(/\r\n/g, '\n');
    return !normalizedText || normalizedText.replace(/[\r\n\s]/g, '') === '';
}

describe('Windows换行符处理', () => {
    const startPosition = new vscode.Position(10, 5);

    test('应该正确处理Windows换行符的单行内容', () => {
        const windowsText = 'console.log("Hello World");\r\n';
        const result = createCompletionItem(windowsText, startPosition);

        expect(result.normalizedText).toBe('console.log("Hello World");\n');
        expect(result.lines.length).toBe(2);
        expect(result.endPosition.line).toBe(11);
        expect(result.endPosition.character).toBe(0);
    });

    test('应该正确处理Windows换行符的多行内容', () => {
        const windowsText = 'function test() {\r\n    console.log("Hello");\r\n    return true;\r\n}';
        const result = createCompletionItem(windowsText, startPosition);

        expect(result.normalizedText).toBe('function test() {\n    console.log("Hello");\n    return true;\n}');
        expect(result.lines.length).toBe(4);
        expect(result.endPosition.line).toBe(13);
        expect(result.endPosition.character).toBe(1); // 最后一行的 '}' 字符
    });

    test('应该正确处理Unix换行符', () => {
        const unixText = 'function test() {\n    console.log("Hello");\n    return true;\n}';
        const result = createCompletionItem(unixText, startPosition);

        expect(result.normalizedText).toBe(unixText);
        expect(result.lines.length).toBe(4);
        expect(result.endPosition.line).toBe(13);
        expect(result.endPosition.character).toBe(1);
    });

    test('应该正确处理混合换行符', () => {
        const mixedText = 'function test() {\r\n    console.log("Hello");\n    return true;\r\n}';
        const result = createCompletionItem(mixedText, startPosition);

        expect(result.normalizedText).toBe('function test() {\n    console.log("Hello");\n    return true;\n}');
        expect(result.lines.length).toBe(4);
        expect(result.endPosition.line).toBe(13);
        expect(result.endPosition.character).toBe(1);
    });

    test('isEmptyOrOnlyNewlines应该正确处理Windows换行符', () => {
        expect(isEmptyOrOnlyNewlines('')).toBe(true);
        expect(isEmptyOrOnlyNewlines('   \r\n   \r\n')).toBe(true);
        expect(isEmptyOrOnlyNewlines('   \n   \n')).toBe(true);
        expect(isEmptyOrOnlyNewlines('   \r\n   \n   \r\n')).toBe(true);
        expect(isEmptyOrOnlyNewlines('console.log("Hello");\r\n')).toBe(false);
        expect(isEmptyOrOnlyNewlines('console.log("Hello");\n')).toBe(false);
    });

    test('应该正确处理单行内容', () => {
        const singleLineText = 'console.log("Hello World");';
        const result = createCompletionItem(singleLineText, startPosition);

        expect(result.normalizedText).toBe(singleLineText);
        expect(result.lines.length).toBe(1);
        expect(result.endPosition.line).toBe(10);
        expect(result.endPosition.character).toBe(5 + singleLineText.length);
    });
}); 