import { buildEnhancedPrompt, buildPromptCore } from '../../../src/components/code_completion/prompt/completionPrompt';

describe('CompletionPrompt', () => {
  describe('buildPromptCore', () => {
    it('should include correct prompt structure', () => {
      const textBefore = 'function test() {\n';
      const textAfter = '\n}';
      const language = 'javascript';
      const context = 'test context';
      const scopeInfo = 'test scope';
      const userRules = ['rule1', 'rule2'];

      const prompt = buildPromptCore(textBefore, textAfter, language, context, scopeInfo, userRules);

      expect(prompt).toContain('请根据以下信息进行代码续写：');
      expect(prompt).toContain('<hints>');
      expect(prompt).toContain('<context>');
      expect(prompt).toContain('<code-before>');
      expect(prompt).toContain('<code-after>');
      expect(prompt).toContain('{{complete_code_here}}');
      expect(prompt).toContain('javascript');
      expect(prompt).toContain('test context');
      expect(prompt).toContain('rule1');
      expect(prompt).toContain('rule2');
    });

    it('should include context information', () => {
      const flagBefore = 'function test() {\n';
      const flagAfter = '\n}';
      const language = 'javascript';
      const context = 'test context';

      const prompt = buildPromptCore(flagBefore, flagAfter, language, context);

      expect(prompt).toContain('test context');
      expect(prompt).toContain(context);
    });

    it('should handle empty context', () => {
      const flagBefore = 'function test() {\n';
      const flagAfter = '\n}';
      const language = 'javascript';

      const prompt = buildPromptCore(flagBefore, flagAfter, language);

      expect(prompt).toContain('<context>');
      expect(prompt).toContain('</context>');
    });

    it('should include user rules when provided', () => {
      const flagBefore = 'function test() {\n';
      const flagAfter = '\n}';
      const language = 'javascript';
      const userRules = ['Always use const for variables', 'Use arrow functions'];

      const prompt = buildPromptCore(flagBefore, flagAfter, language, '', '', userRules);

      expect(prompt).toContain('Always use const for variables');
      expect(prompt).toContain('Use arrow functions');
    });

    it('should handle empty user rules', () => {
      const flagBefore = 'function test() {\n';
      const flagAfter = '\n}';
      const language = 'javascript';

      const prompt = buildPromptCore(flagBefore, flagAfter, language);

      expect(prompt).not.toContain('用户规则');
    });
  });

  describe('buildEnhancedPrompt', () => {
    it('should build enhanced prompt with user rules', async () => {
      const textBefore = 'function test() {\n';
      const textAfter = '\n}';
      const language = 'javascript';
      const context = 'test context';

      const prompt = await buildEnhancedPrompt(textBefore, textAfter, language, context);

      expect(prompt).toContain('请根据以下信息进行代码续写：');
      expect(prompt).toContain('<hints>');
      expect(prompt).toContain('<context>');
      expect(prompt).toContain('<code-before>');
      expect(prompt).toContain('<code-after>');
      expect(prompt).toContain('{{complete_code_here}}');
    });

    it('should handle different languages', async () => {
      const textBefore = 'def test():\n';
      const textAfter = '\n    pass';
      const language = 'python';

      const prompt = await buildEnhancedPrompt(textBefore, textAfter, language);

      expect(prompt).toContain('python');
    });
  });
}); 