import { WorkerPool } from '../../../src/components/code_graph/workers/workerPool';

describe('WorkerPool 日志和数量管理测试', () => {
  let workerPool: WorkerPool;

  beforeEach(() => {
    workerPool = new WorkerPool({
      maxWorkers: 3,
      onProgress: () => {}
    });
  });

  afterEach(async () => {
    await workerPool.shutdown();
  });

  it('应该正确管理worker数量', async () => {
    console.log('=== 测试WorkerPool数量管理 ===');
    
    // 初始化
    await workerPool.initialize();
    
    // 检查初始状态
    const initialStatus = workerPool.getStatus();
    console.log('初始状态:', initialStatus);
    
    expect(initialStatus.totalWorkers).toBe(3);
    expect(initialStatus.availableWorkers).toBe(3);
    expect(initialStatus.busyWorkers).toBe(0);
    
    // 关闭并检查日志
    console.log('开始关闭WorkerPool...');
    await workerPool.shutdown();
    
    // 检查最终状态
    const finalStatus = workerPool.getStatus();
    console.log('最终状态:', finalStatus);
    
    expect(finalStatus.totalWorkers).toBe(0);
    expect(finalStatus.availableWorkers).toBe(0);
    expect(finalStatus.busyWorkers).toBe(0);
  });

  it('应该在关闭时正确处理worker消息', async () => {
    console.log('=== 测试关闭时的消息处理 ===');
    
    await workerPool.initialize();
    
    // 模拟关闭过程中的消息处理
    const status = workerPool.getStatus();
    console.log('关闭前状态:', status);
    
    // 关闭
    await workerPool.shutdown();
    
    console.log('关闭完成');
  });

  it('应该验证worker数量一致性', async () => {
    console.log('=== 测试worker数量一致性验证 ===');
    
    await workerPool.initialize();
    
    // 多次获取状态来触发验证
    for (let i = 0; i < 3; i++) {
      const status = workerPool.getStatus();
      console.log(`第${i + 1}次状态检查:`, status);
      
      // 验证数量关系
      expect(status.totalWorkers).toBeGreaterThanOrEqual(0);
      expect(status.availableWorkers).toBeGreaterThanOrEqual(0);
      expect(status.busyWorkers).toBeGreaterThanOrEqual(0);
      expect(status.totalWorkers).toBe(status.availableWorkers + status.busyWorkers);
    }
    
    await workerPool.shutdown();
  });
}); 