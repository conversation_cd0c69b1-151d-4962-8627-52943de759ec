import * as path from 'path';
import { CodeGraphBuilder } from '../../../src/components/code_graph/builder/CodeGraphBuilder';
import { ParserManager } from '../../../src/components/code_graph/parser/ParserManager';
import { RepoIndexer } from '../../../src/components/code_graph/repoIndexer/repoIndexer';
import { ASTNode, ASTNodeKind } from '../../../src/components/code_graph/types/ast';

describe('语言支持测试', () => {
  let graphBuilder: CodeGraphBuilder;
  let parserManager: ParserManager;

  beforeAll(() => {
    graphBuilder = new CodeGraphBuilder();
    parserManager = new ParserManager();
  });

  describe('文件扩展名语言识别', () => {
    const testCases = [
      { file: 'test.asm', expectedLanguage: 'np' },
      { file: 'test.asm', expectedLanguage: 'np' },
      { file: 'test.c', expectedLanguage: 'c' },
      { file: 'test.h', expectedLanguage: 'c' },
      { file: 'test.cpp', expectedLanguage: 'c' },
      { file: 'test.hpp', expectedLanguage: 'c' }
    ];

    testCases.forEach(({ file, expectedLanguage }) => {
      it(`应该正确识别${file}文件为${expectedLanguage}语言`, () => {
        const language = parserManager.getLanguageFromFile(file);
        expect(language).toBe(expectedLanguage);
      });
    });
  });

  describe('C语言AST节点类型测试', () => {
    const cAstSamples: Array<{ node: ASTNode; expectedKind: string; description: string }> = [
      // 函数定义
      {
        node: {
          name: 'main_function',
          originalType: 'function_definition',
          kind: ASTNodeKind.FUNCTION,
          language: 'c',
          text: 'int main_function() { return 0; }',
          children: [],
          location: { start: { line: 1, column: 0 }, end: { line: 1, column: 25 } }
        },
        expectedKind: 'function',
        description: 'C语言函数定义'
      },
      // 函数调用
      {
        node: {
          name: 'helper_function',
          originalType: 'call_expression',
          kind: ASTNodeKind.FUNCTION,
          language: 'c',
          text: 'helper_function();',
          children: [],
          location: { start: { line: 1, column: 0 }, end: { line: 1, column: 18 } }
        },
        expectedKind: 'function',
        description: 'C语言函数调用'
      },
      // 变量声明
      {
        node: {
          name: 'variable_name',
          originalType: 'declaration',
          kind: ASTNodeKind.VARIABLE,
          language: 'c',
          text: 'int variable_name = 0;',
          children: [],
          location: { start: { line: 1, column: 0 }, end: { line: 1, column: 20 } }
        },
        expectedKind: 'variable',
        description: 'C语言变量声明'
      },
      // 宏定义
      {
        node: {
          name: 'MACRO_NAME',
          originalType: 'preprocessor_function_def',
          kind: ASTNodeKind.FUNCTION, // 根据项目规范，宏统一为function
          language: 'c',
          text: '#define MACRO_NAME value',
          children: [],
          location: { start: { line: 1, column: 0 }, end: { line: 1, column: 25 } },
          metadata: {
            type: 'macro' // 在元数据中标记实际类型
          }
        },
        expectedKind: 'function', // 根据项目规范，宏的kind应该是function
        description: 'C语言宏定义'
      },
      // 头文件包含
      {
        node: {
          name: 'stdio.h',
          originalType: 'preprocessor_include',
          kind: ASTNodeKind.MODULE,
          language: 'c',
          text: '#include <stdio.h>',
          children: [],
          location: { start: { line: 1, column: 0 }, end: { line: 1, column: 18 } }
        },
        expectedKind: 'module',
        description: 'C语言头文件包含'
      },
      // 结构体定义
      {
        node: {
          name: 'StructName',
          originalType: 'struct_declaration',
          kind: ASTNodeKind.TYPE,
          language: 'c',
          text: 'struct StructName { int field; };',
          children: [],
          location: { start: { line: 1, column: 0 }, end: { line: 1, column: 30 } }
        },
        expectedKind: 'type',
        description: 'C语言结构体定义'
      },
      // 类型定义
      {
        node: {
          name: 'TypeName',
          originalType: 'typedef_declaration',
          kind: ASTNodeKind.TYPE,
          language: 'c',
          text: 'typedef int TypeName;',
          children: [],
          location: { start: { line: 1, column: 0 }, end: { line: 1, column: 20 } }
        },
        expectedKind: 'type',
        description: 'C语言类型定义'
      }
    ];

    cAstSamples.forEach(({ node, expectedKind, description }) => {
      it(`${description} - 应该正确赋值kind=${expectedKind}`, () => {
        expect(node.kind).toBe(expectedKind);
      });

      it(`${description} - 应该被CodeGraphBuilder正确识别`, () => {
        graphBuilder.buildFromAst(node, 'test.c');
        const graph = graphBuilder.getGraph();
        const nodes = Array.from(graph.nodes.values());
        
        const foundNode = nodes.find((n: any) => n.name === node.name);
        
        expect(foundNode).toBeDefined();
        expect(foundNode?.type).toBe(expectedKind);
      });
    });
  });

  describe('混合语言支持', () => {
    it('应该正确处理C语言和NP语言的混合AST', () => {
      const cNode: ASTNode = {
        name: 'c_function',
        originalType: 'function_definition',
        kind: ASTNodeKind.FUNCTION,
        language: 'c',
        text: 'int c_function() { return 0; }',
        children: [],
        location: { start: { line: 1, column: 0 }, end: { line: 1, column: 25 } }
      };

      const npNode: ASTNode = {
        name: 'np_function',
        originalType: 'statement_function_def',
        kind: ASTNodeKind.FUNCTION,
        language: 'np',
        text: 'function np_function { statement; }',
        children: [],
        location: { start: { line: 1, column: 0 }, end: { line: 1, column: 30 } }
      };

      graphBuilder.buildFromAst(cNode, 'test.c');
      graphBuilder.buildFromAst(npNode, 'test.asm');

      const graph = graphBuilder.getGraph();
      const nodes = Array.from(graph.nodes.values());

      const cFunction = nodes.find((n: any) => n.name === 'c_function');
      const npFunction = nodes.find((n: any) => n.name === 'np_function');

      expect(cFunction).toBeDefined();
      expect(cFunction?.language).toBe('c');
      expect(npFunction).toBeDefined();
      expect(npFunction?.language).toBe('np');
    });
  });

  describe('统一抽象层测试', () => {
    it('所有语言的函数节点都应该被统一识别为kind=function', () => {
      const cFunction: ASTNode = {
        name: 'c_func',
        originalType: 'function_definition',
        kind: ASTNodeKind.FUNCTION,
        language: 'c',
        text: 'int c_func() { return 0; }',
        children: [],
        location: { start: { line: 1, column: 0 }, end: { line: 1, column: 20 } }
      };

      const npFunction: ASTNode = {
        name: 'np_func',
        originalType: 'statement_function_def',
        kind: ASTNodeKind.FUNCTION,
        language: 'np',
        text: 'function np_func { statement; }',
        children: [],
        location: { start: { line: 1, column: 0 }, end: { line: 1, column: 25 } }
      };

      graphBuilder.buildFromAst(cFunction, 'test.c');
      graphBuilder.buildFromAst(npFunction, 'test.asm');

      const graph = graphBuilder.getGraph();
      const nodes = Array.from(graph.nodes.values());
      const functionNodes = nodes.filter((n: any) => n.type === 'function');

      functionNodes.forEach((node: any) => {
        expect(node.type).toBe('function');
      });
    });

    it('所有语言的变量节点都应该被统一识别为kind=variable', () => {
      const cVariable: ASTNode = {
        name: 'c_var',
        originalType: 'declaration',
        kind: ASTNodeKind.VARIABLE,
        language: 'c',
        text: 'int c_var = 0;',
        children: [],
        location: { start: { line: 1, column: 0 }, end: { line: 1, column: 15 } }
      };

      const npVariable: ASTNode = {
        name: 'np_var',
        originalType: 'variable_declaration',
        kind: ASTNodeKind.VARIABLE,
        language: 'np',
        text: 'declare np_var;',
        children: [],
        location: { start: { line: 1, column: 0 }, end: { line: 1, column: 15 } }
      };

      graphBuilder.buildFromAst(cVariable, 'test.c');
      graphBuilder.buildFromAst(npVariable, 'test.asm');

      const graph = graphBuilder.getGraph();
      const nodes = Array.from(graph.nodes.values());
      const variableNodes = nodes.filter((n: any) => n.type === 'variable');

      variableNodes.forEach((node: any) => {
        expect(node.type).toBe('variable');
      });
    });

    it('所有语言的宏节点都应该被统一识别为kind=function', () => {
      const cMacro: ASTNode = {
        name: 'C_MACRO',
        originalType: 'preprocessor_function_def',
        kind: ASTNodeKind.FUNCTION, // 根据项目规范，宏统一为function
        language: 'c',
        text: '#define C_MACRO value',
        children: [],
        location: { start: { line: 1, column: 0 }, end: { line: 1, column: 20 } },
        metadata: {
          type: 'macro' // 在元数据中标记实际类型
        }
      };

      const npMacro: ASTNode = {
        name: 'NP_MACRO',
        originalType: 'define_statement',
        kind: ASTNodeKind.FUNCTION, // 根据项目规范，宏统一为function
        language: 'np',
        text: '#define NP_MACRO value',
        children: [],
        location: { start: { line: 1, column: 0 }, end: { line: 1, column: 20 } },
        metadata: {
          type: 'macro' // 在元数据中标记实际类型
        }
      };

      graphBuilder.buildFromAst(cMacro, 'test.c');
      graphBuilder.buildFromAst(npMacro, 'test.asm');

      const graph = graphBuilder.getGraph();
      const nodes = Array.from(graph.nodes.values());
      const macroNodes = nodes.filter((n: any) => n.type === 'function' && n.metadata?.type === 'macro');

      macroNodes.forEach((node: any) => {
        expect(node.type).toBe('function');
        expect(node.metadata?.type).toBe('macro');
      });
    });
  });

  describe('跨语言项目实际测试', () => {
    const testProjectPath = path.resolve(__dirname, '../../../test_projects/np');
    let repoIndexer: RepoIndexer;

    beforeAll(async () => {
      // 创建 RepoIndexer 实例，只包含 .h 文件以避免 NP parser 兼容性问题
      repoIndexer = new RepoIndexer(testProjectPath, ['.h']);

      // 执行全量索引
      await repoIndexer.analyze();
    });

    it('应该成功索引 test_projects/np 项目中的 C 文件', () => {
      const graph = repoIndexer.getGraph();

      console.log('\n=== test_projects/np 项目 C 文件索引结果 ===');
      console.log(`图节点数量: ${graph.nodes.size}`);
      console.log(`图边数量: ${graph.edges.length}`);

      expect(graph.nodes.size).toBeGreaterThan(0);
    });

    it('应该找到 SQUARE 宏定义', async () => {
      const graph = repoIndexer.getGraph();
      const nodes = Array.from(graph.nodes.values());

      // 查找 SQUARE 宏
      const squareNodes = nodes.filter((node: any) =>
        node.name === 'SQUARE' && node.type === 'function' && node.metadata?.type === 'macro'
      );

      console.log(`找到 ${squareNodes.length} 个 SQUARE 宏`);

      expect(squareNodes.length).toBeGreaterThan(0);
    });

    it('应该验证 C 解析器的真实 tree-sitter 功能', () => {
      const graph = repoIndexer.getGraph();
      const nodes = Array.from(graph.nodes.values());
      const edges = graph.edges;

      console.log(`\n=== C 解析器验证 ===`);
      console.log(`总节点数: ${nodes.length}`);
      console.log(`总边数: ${edges.length}`);
      
      // 打印所有节点
      console.log('\n所有节点:');
      nodes.forEach((node: any, index: number) => {
        console.log(`  ${index + 1}. ${node.name} (${node.type}) - ${node.id}`);
      });

      // 验证找到了宏定义
      const macroNodes = nodes.filter((node: any) => node.type === 'function' && node.metadata?.type === 'macro');
      expect(macroNodes.length).toBeGreaterThan(0);
      
      // 验证找到了 SQUARE 宏
      const squareNode = nodes.find((node: any) => node.name === 'SQUARE');
      expect(squareNode).toBeDefined();
      expect(squareNode?.type).toBe('function');
      expect(squareNode?.metadata?.type).toBe('macro');
    });
  });
}); 