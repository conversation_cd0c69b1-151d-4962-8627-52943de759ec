import * as fs from 'fs';
import { RepoIndexer } from 'src/components/code_graph/repoIndexer/repoIndexer';

// Mock fs
jest.mock('fs', () => {
  const actual = jest.requireActual('fs');
  return {
    ...actual,
    promises: {
      readFile: jest.fn(),
      stat: jest.fn(),
      readdir: jest.fn()
    }
  };
});

// Mock ParserManager
const mockParser = {
  parse: jest.fn()
};

const mockParserManager = {
  getParser: jest.fn().mockResolvedValue(mockParser)
};

jest.mock('src/components/code_graph/parser/ParserManager', () => ({
  ParserManager: jest.fn().mockImplementation(() => mockParserManager)
}));

describe('RepoIndexer', () => {
  let repoIndexer: RepoIndexer;
  const workspacePath = '/test/workspace';

  beforeEach(() => {
    repoIndexer = new RepoIndexer(workspacePath);
    jest.clearAllMocks();
  });

  describe('indexFile', () => {
    it('应该正确索引单个文件', async () => {
      // Arrange
      const filePath = '/test/workspace/test.c';
      const mockContent = 'int main() { return 0; }';
      const mockStats = { mtimeMs: 123456789 };
      const mockAst = { type: 'program', body: [] };

      (fs.promises.readFile as jest.Mock).mockResolvedValue(mockContent);
      (fs.promises.stat as jest.Mock).mockResolvedValue(mockStats);
      mockParser.parse.mockResolvedValue(mockAst);

      // Act
      await repoIndexer.indexFile(filePath);

      // Assert
      expect(fs.promises.readFile).toHaveBeenCalledWith(filePath, 'utf-8');
      expect(mockParser.parse).toHaveBeenCalledWith(mockContent);
      expect(fs.promises.stat).toHaveBeenCalledWith(filePath);
    });

    it('应该跳过不支持的语言文件', async () => {
      // Arrange
      const filePath = '/test/workspace/test.txt';

      // Act
      await repoIndexer.indexFile(filePath);

      // Assert
      expect(fs.promises.readFile).not.toHaveBeenCalled();
      expect(mockParser.parse).not.toHaveBeenCalled();
    });

    it('应该处理文件读取错误', async () => {
      // Arrange
      const filePath = '/test/workspace/test.c';
      (fs.promises.readFile as jest.Mock).mockRejectedValue(new Error('File not found'));

      // Act & Assert
      await expect(repoIndexer.indexFile(filePath)).resolves.not.toThrow();
    });

    it('应该处理解析错误', async () => {
      // Arrange
      const filePath = '/test/workspace/test.c';
      const mockContent = 'invalid code';
      (fs.promises.readFile as jest.Mock).mockResolvedValue(mockContent);
      (fs.promises.stat as jest.Mock).mockResolvedValue({ mtimeMs: 123456789 });
      mockParser.parse.mockRejectedValue(new Error('Parse error'));

      // Act & Assert
      await expect(repoIndexer.indexFile(filePath)).resolves.not.toThrow();
    });
  });

  describe('removeFileFromIndex', () => {
    it('应该正确移除文件的所有节点', () => {
      // Arrange
      const filePath = '/test/workspace/test.c';
      const relativePath = 'test.c';

      // 添加一些测试节点
      const mockGraph = {
        nodes: new Map([
          ['node1', { id: 'node1', file: relativePath, name: 'func1' }],
          ['node2', { id: 'node2', file: 'other.c', name: 'func2' }],
          ['node3', { id: 'node3', file: relativePath, name: 'func3' }]
        ])
      };

      // 替换graph属性
      (repoIndexer as any).graph = mockGraph;

      // Act
      repoIndexer.removeFileFromIndex(filePath);

      // Assert
      expect(mockGraph.nodes.has('node1')).toBe(false);
      expect(mockGraph.nodes.has('node2')).toBe(true); // 其他文件的不应该被删除
      expect(mockGraph.nodes.has('node3')).toBe(false);
    });

    it('应该处理不存在的文件', () => {
      // Arrange
      const filePath = '/test/workspace/nonexistent.c';
      const mockGraph = {
        nodes: new Map([
          ['node1', { id: 'node1', file: 'other.c', name: 'func1' }]
        ])
      };
      (repoIndexer as any).graph = mockGraph;

      // Act
      repoIndexer.removeFileFromIndex(filePath);

      // Assert
      expect(mockGraph.nodes.has('node1')).toBe(true); // 不应该删除任何节点
    });
  });

  describe('getLanguageFromFile', () => {
    it('应该正确识别C文件', () => {
      const result = (repoIndexer as any).getLanguageFromFile('/test/workspace/test.c');
      expect(result).toBe('c');
    });

    it('应该正确识别NP文件', () => {
      const result = (repoIndexer as any).getLanguageFromFile('/test/workspace/test.asm');
      expect(result).toBe('np');
    });

    it('应该返回null对于不支持的文件类型', () => {
      const result = (repoIndexer as any).getLanguageFromFile('/test/workspace/test.txt');
      expect(result).toBeNull();
    });
  });
}); 