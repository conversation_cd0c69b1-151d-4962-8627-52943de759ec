
// Mock VSCode API
jest.mock('vscode', () => ({
  window: {
    createOutputChannel: jest.fn().mockReturnValue({
      appendLine: jest.fn(),
      append: jest.fn(),
      clear: jest.fn(),
      show: jest.fn(),
      hide: jest.fn(),
      dispose: jest.fn()
    }),
    showInformationMessage: jest.fn(),
    showErrorMessage: jest.fn(),
    showWarningMessage: jest.fn()
  },
  workspace: {
    workspaceFolders: [
      {
        uri: {
          fsPath: '/test/workspace'
        },
        name: 'test-workspace'
      }
    ],
    createFileSystemWatcher: jest.fn().mockReturnValue({
      onDidCreate: jest.fn().mockReturnValue({
        dispose: jest.fn()
      }),
      onDidChange: jest.fn().mockReturnValue({
        dispose: jest.fn()
      }),
      onDidDelete: jest.fn().mockReturnValue({
        dispose: jest.fn()
      }),
      dispose: jest.fn()
    }),
    fs: {
      readFile: jest.fn(),
      writeFile: jest.fn(),
      stat: jest.fn()
    }
  },
  Uri: {
    file: jest.fn((path) => ({ fsPath: path }))
  },
  EventEmitter: jest.fn().mockImplementation(() => ({
    event: jest.fn(),
    fire: jest.fn(),
    dispose: jest.fn()
  })),
  ExtensionContext: jest.fn().mockImplementation(() => ({
    subscriptions: [],
    globalState: {
      get: jest.fn(),
      update: jest.fn()
    },
    globalStorageUri: {
      fsPath: '/test/storage'
    }
  }))
}));

import * as fs from 'fs/promises';
import { RepoIndexerManager } from 'src/components/code_graph/repoIndexer/repoIndexerManager';
import * as vscode from 'vscode';

// Mock vscode
jest.mock('vscode', () => ({
    workspace: {
        workspaceFolders: [
            { uri: { fsPath: '/test/workspace' } }
        ],
        fs: {
            readFile: jest.fn(),
            writeFile: jest.fn()
        },
        createFileSystemWatcher: jest.fn(() => ({
            onDidCreate: jest.fn(),
            onDidChange: jest.fn(),
            onDidDelete: jest.fn()
        }))
    },
    window: {
        showInformationMessage: jest.fn(),
        showErrorMessage: jest.fn(),
        createStatusBarItem: jest.fn(() => ({
            show: jest.fn(),
            hide: jest.fn(),
            dispose: jest.fn()
        })),
        createOutputChannel: jest.fn(() => ({
            appendLine: jest.fn(),
            show: jest.fn(),
            clear: jest.fn(),
            dispose: jest.fn()
        }))
    },
    Uri: {
        file: jest.fn((fsPath) => ({ fsPath }))
    },
    EventEmitter: jest.fn().mockImplementation(() => ({
        fire: jest.fn(),
        event: jest.fn()
    })),
    StatusBarAlignment: {
        Left: 1,
        Right: 2
    }
}));

// Mock fs
jest.mock('fs/promises', () => ({
    mkdir: jest.fn(),
    writeFile: jest.fn(),
    readFile: jest.fn(),
    unlink: jest.fn() // Added unlink for cleanExistingIndexData
}));

describe('RepoIndexerManager', () => {
    let manager: RepoIndexerManager;
    let mockContext: any;

    beforeEach(() => {
        // 重置所有mock
        jest.clearAllMocks();

        // 确保每个测试都有正确的workspaceFolders
        (vscode.workspace as any).workspaceFolders = [
            { uri: { fsPath: '/test/workspace' } }
        ];

        // 创建mock context
        mockContext = {
            globalState: {
                get: jest.fn(),
                update: jest.fn()
            },
            globalStorageUri: {
                fsPath: '/test/storage'
            },
            subscriptions: []
        };

        // 获取单例实例
        manager = RepoIndexerManager.getInstance();
    });

    describe('手动初始化工作区索引', () => {
        it('应该创建配置文件并执行索引', async () => {
            // Mock 没有现有索引
            manager['repoIndexer'] = null;

            // Mock 配置文件不存在
            (vscode.workspace.fs.readFile as jest.Mock).mockRejectedValue(new Error('文件不存在'));

            // Mock fs.mkdir 成功
            (fs.mkdir as jest.Mock).mockResolvedValue(undefined);
            (fs.writeFile as jest.Mock).mockResolvedValue(undefined);

            // Mock RepoIndexer 相关方法
            const mockRepoIndexer = {
                analyze: jest.fn().mockResolvedValue(undefined),
                exportData: jest.fn().mockReturnValue({ test: 'data' }),
                setProgressCallback: jest.fn()
            };

            // Mock createRepoIndexer 方法
            jest.spyOn(manager as any, 'createRepoIndexer').mockResolvedValue(mockRepoIndexer as any);
            jest.spyOn(manager as any, 'saveToFile').mockResolvedValue(undefined);

            // 执行测试
            await manager.initializeWorkspaceIndex(mockContext);

            // 验证创建了.vscode目录
            expect(fs.mkdir).toHaveBeenCalledWith('/test/workspace/.vscode', { recursive: true });

            // 验证创建了配置文件
            expect(fs.writeFile).toHaveBeenCalledWith(
                '/test/workspace/.vscode/code-partner.json',
                expect.stringContaining('"enableIndexing": true'),
                'utf-8'
            );

            // 验证配置文件内容格式正确
            const writeFileCall = (fs.writeFile as jest.Mock).mock.calls[0];
            const configContent = JSON.parse(writeFileCall[1]);
            expect(configContent).toEqual({
                enableIndexing: true,
                autoIndexing: true,
                languageExts: ['*'],
                rules: [
                    {
                        type: 'include',
                        pattern: '**/*.{*}'
                    }
                ]
            });
        });

        it('应该跳过已有索引的情况', async () => {
            // Mock 已有索引
            const mockRepoIndexer = { test: 'existing' };
            manager['repoIndexer'] = mockRepoIndexer as any;

            // Mock createRepoIndexer 方法（不应该被调用）
            const createRepoIndexerSpy = jest.spyOn(manager as any, 'createRepoIndexer').mockResolvedValue(mockRepoIndexer as any);

            // 执行测试
            await manager.initializeWorkspaceIndex(mockContext);

            // 验证没有创建新的索引器
            expect(createRepoIndexerSpy).not.toHaveBeenCalled();
        });

        it('应该处理工作区不存在的情况', async () => {
            // Mock 没有工作区
            (vscode.workspace as any).workspaceFolders = undefined;

            await expect(manager.initializeWorkspaceIndex(mockContext))
                .rejects.toThrow('未找到工作区');
        });

        it('应该处理配置文件创建失败的情况', async () => {
            // Mock 没有现有索引
            manager['repoIndexer'] = null;

            // Mock 配置文件不存在
            (vscode.workspace.fs.readFile as jest.Mock).mockRejectedValue(new Error('文件不存在'));

            // Mock fs.writeFile 失败
            (fs.writeFile as jest.Mock).mockRejectedValue(new Error('权限不足'));

            await expect(manager.initializeWorkspaceIndex(mockContext))
                .rejects.toThrow('权限不足');
        });
    });

    describe('自动初始化索引', () => {
        it('应该在有配置文件且enableIndexing=true且有索引数据时加载索引', async () => {
            // Mock 配置文件存在且enableIndexing=true
            const mockConfig = {
                enableIndexing: true,
                languageExts: ['*']
            };
            (vscode.workspace.fs.readFile as jest.Mock).mockResolvedValue(
                Buffer.from(JSON.stringify(mockConfig))
            );

            // Mock 有保存的索引数据
            mockContext.globalState.get.mockReturnValue('/test/storage/repoIndexerData.json');

            // Mock loadFromFile 方法
            const mockRepoIndexer = { test: 'indexer' };
            jest.spyOn(manager as any, 'loadFromFile').mockResolvedValue(mockRepoIndexer as any);

            // 设置context
            manager['context'] = mockContext;

            // 执行测试
            await (manager as any).autoInitializeIndex();

            // 验证读取了配置文件
            expect(vscode.workspace.fs.readFile).toHaveBeenCalledWith(
                expect.objectContaining({ fsPath: '/test/workspace/.vscode/code-partner.json' })
            );

            // 验证尝试加载索引数据
            expect(manager['loadFromFile']).toHaveBeenCalledWith('/test/storage/repoIndexerData.json');
        });

        it('应该在有配置文件且enableIndexing=true但无索引数据时执行全量索引', async () => {
            // Mock 配置文件存在且enableIndexing=true
            const mockConfig = {
                enableIndexing: true,
                autoIndexing: true,
                languageExts: ['ts', 'js']
            };
            (vscode.workspace.fs.readFile as jest.Mock).mockResolvedValue(
                Buffer.from(JSON.stringify(mockConfig))
            );

            // Mock 没有保存的索引数据
            mockContext.globalState.get.mockReturnValue(undefined);

            // Mock initializeAndAnalyze 方法
            jest.spyOn(manager as any, 'initializeAndAnalyze').mockResolvedValue(undefined);

            // 设置context
            manager['context'] = mockContext;

            // 执行测试
            await (manager as any).autoInitializeIndex();

            // 验证执行了全量索引
            expect(manager['initializeAndAnalyze']).toHaveBeenCalledWith('/test/workspace', ['ts', 'js']);
        });

        it('应该在enableIndexing=false时跳过索引', async () => {
            // Mock 配置文件存在但enableIndexing=false
            const mockConfig = {
                enableIndexing: false,
                languageExts: ['*']
            };
            (vscode.workspace.fs.readFile as jest.Mock).mockResolvedValue(
                Buffer.from(JSON.stringify(mockConfig))
            );

            // Mock initializeAndAnalyze 方法（不应该被调用）
            const initializeAndAnalyzeSpy = jest.spyOn(manager as any, 'initializeAndAnalyze').mockResolvedValue(undefined);

            // 执行测试
            await (manager as any).autoInitializeIndex();

            // 验证没有执行索引
            expect(initializeAndAnalyzeSpy).not.toHaveBeenCalled();
        });

        it('应该在配置文件不存在时创建配置文件并执行全量索引', async () => {
            // Mock 配置文件不存在
            (vscode.workspace.fs.readFile as jest.Mock).mockRejectedValue(new Error('文件不存在'));

            // Mock createDefaultConfig 和 initializeAndAnalyze 方法
            jest.spyOn(manager as any, 'createDefaultConfig').mockResolvedValue(undefined);
            jest.spyOn(manager as any, 'initializeAndAnalyze').mockResolvedValue(undefined);

            // 设置context
            manager['context'] = mockContext;

            // 执行测试
            await (manager as any).autoInitializeIndex();

            // 验证创建了配置文件
            expect(manager['createDefaultConfig']).toHaveBeenCalledWith('/test/workspace', ['*']);

            // 验证执行了全量索引
            expect(manager['initializeAndAnalyze']).toHaveBeenCalledWith('/test/workspace', ['*']);
        });

        it('应该在无工作区时跳过自动初始化', async () => {
            // Mock 没有工作区
            (vscode.workspace as any).workspaceFolders = undefined;

            // Mock initializeAndAnalyze 方法（不应该被调用）
            const initializeAndAnalyzeSpy = jest.spyOn(manager as any, 'initializeAndAnalyze').mockResolvedValue(undefined);

            // 执行测试
            await (manager as any).autoInitializeIndex();

            // 验证没有执行索引
            expect(initializeAndAnalyzeSpy).not.toHaveBeenCalled();
        });
    });

    describe('配置文件创建', () => {
        it('应该创建正确格式的配置文件', async () => {
            // Mock fs 方法
            (fs.mkdir as jest.Mock).mockResolvedValue(undefined);
            (fs.writeFile as jest.Mock).mockResolvedValue(undefined);

            // 执行测试
            await (manager as any).createDefaultConfig('/test/workspace', ['ts', 'js']);

            // 验证配置文件内容
            const writeFileCall = (fs.writeFile as jest.Mock).mock.calls[0];
            const configContent = JSON.parse(writeFileCall[1]);

            expect(configContent).toEqual({
                enableIndexing: true,
                autoIndexing: true,
                languageExts: ['ts', 'js'],
                rules: [
                    {
                        type: 'include',
                        pattern: '**/*.{ts,js}'
                    }
                ]
            });
        });

        it('应该处理目录创建失败的情况', async () => {
            // Mock fs.mkdir 失败
            (fs.mkdir as jest.Mock).mockRejectedValue(new Error('权限不足'));

            // 执行测试 - 应该继续执行，只是记录警告
            await (manager as any).createDefaultConfig('/test/workspace', ['*']);

            // 验证仍然尝试写入文件
            expect(fs.writeFile).toHaveBeenCalled();
        });
    });

    describe('重置索引', () => {
        it('应该在有索引时清理现有数据并重新初始化', async () => {
            // 设置context和现有索引
            manager['context'] = mockContext;
            manager['repoIndexer'] = { test: 'existing' } as any;
            mockContext.globalState.get.mockReturnValue('/test/storage/repoIndexerData.json');

            // Mock 清理方法
            jest.spyOn(manager as any, 'cleanExistingIndexData').mockResolvedValue(undefined);

            // Mock 配置文件存在
            const mockConfig = {
                enableIndexing: true,
                languageExts: ['*']
            };
            (vscode.workspace.fs.readFile as jest.Mock).mockResolvedValue(
                Buffer.from(JSON.stringify(mockConfig))
            );

            // Mock initializeAndAnalyze 方法
            jest.spyOn(manager as any, 'initializeAndAnalyze').mockResolvedValue(undefined);

            // 执行测试
            await manager.resetIndex();

            // 验证清理了现有数据
            expect(manager['cleanExistingIndexData']).toHaveBeenCalled();

            // 验证使用配置文件重新初始化
            expect(manager['initializeAndAnalyze']).toHaveBeenCalledWith('/test/workspace', ['*']);
        });

        it('应该在无索引时直接创建配置并初始化', async () => {
            // 设置context但无现有索引
            manager['context'] = mockContext;
            manager['repoIndexer'] = null;
            mockContext.globalState.get.mockReturnValue(undefined);

            // Mock 清理方法（不应该被调用）
            const cleanSpy = jest.spyOn(manager as any, 'cleanExistingIndexData').mockResolvedValue(undefined);

            // Mock 配置文件不存在
            (vscode.workspace.fs.readFile as jest.Mock).mockRejectedValue(new Error('文件不存在'));

            // Mock createDefaultConfig 和 initializeAndAnalyze 方法
            jest.spyOn(manager as any, 'createDefaultConfig').mockResolvedValue(undefined);
            jest.spyOn(manager as any, 'initializeAndAnalyze').mockResolvedValue(undefined);

            // 执行测试
            await manager.resetIndex();

            // 验证没有清理现有数据
            expect(cleanSpy).not.toHaveBeenCalled();

            // 验证创建了配置文件并初始化
            expect(manager['createDefaultConfig']).toHaveBeenCalledWith('/test/workspace', ['*']);
            expect(manager['initializeAndAnalyze']).toHaveBeenCalledWith('/test/workspace', ['*']);
        });

        it('应该在配置文件禁用索引时启用索引并重新构建', async () => {
            // 设置context
            manager['context'] = mockContext;
            manager['repoIndexer'] = { test: 'existing' } as any;

            // Mock 清理方法
            jest.spyOn(manager as any, 'cleanExistingIndexData').mockResolvedValue(undefined);

            // Mock 配置文件存在但enableIndexing=false
            const mockConfig = {
                enableIndexing: false,
                languageExts: ['ts', 'js']
            };
            (vscode.workspace.fs.readFile as jest.Mock).mockResolvedValue(
                Buffer.from(JSON.stringify(mockConfig))
            );

            // Mock initializeAndAnalyze 方法
            jest.spyOn(manager as any, 'initializeAndAnalyze').mockResolvedValue(undefined);

            // 执行测试
            await manager.resetIndex();

            // 验证清理了现有数据
            expect(manager['cleanExistingIndexData']).toHaveBeenCalled();

            // 验证启用了索引并重新初始化
            expect(manager['initializeAndAnalyze']).toHaveBeenCalledWith('/test/workspace', ['ts', 'js']);
        });
    });

    describe('初始化流程', () => {
        it('应该正确初始化RepoIndexerManager', () => {
            // Mock registerFileWatcher 方法
            jest.spyOn(manager as any, 'registerFileWatcher').mockImplementation(() => { });
            jest.spyOn(manager as any, 'autoInitializeIndex').mockResolvedValue(undefined);

            // 执行测试
            manager.initialize(mockContext);

            // 验证设置了context
            expect(manager['context']).toBe(mockContext);

            // 验证注册了文件监听器
            expect(manager['registerFileWatcher']).toHaveBeenCalledWith(mockContext);

            // 验证尝试自动加载索引
            expect(manager['autoInitializeIndex']).toHaveBeenCalled();
        });
    });

    describe('清理索引数据', () => {
        it('应该完整清理索引数据', async () => {
            // 设置context和现有索引
            manager['context'] = mockContext;
            const mockRepoIndexer = { test: 'existing' };
            manager['repoIndexer'] = mockRepoIndexer as any;
            mockContext.globalState.get.mockReturnValue('/test/storage/repoIndexerData.json');

            // Mock fs.unlink 方法
            (fs.unlink as jest.Mock).mockResolvedValue(undefined);

            // 执行测试
            await manager.cleanExistingIndexData();

            // 验证清理了内存中的索引器
            expect(manager['repoIndexer']).toBeNull();

            // 验证清理了globalState中的存储键
            expect(mockContext.globalState.update).toHaveBeenCalledWith('repoIndexerData', undefined);

            // 验证尝试删除索引文件
            expect(fs.unlink).toHaveBeenCalledWith('/test/storage/repoIndexerData.json');

            // 验证重置了状态
            expect(manager['lastIndexTime']).toBe(0);
            expect(manager['_currentStatus']).toBe('idle');
        });

        it('应该处理索引文件删除失败的情况', async () => {
            // 设置context和现有索引
            manager['context'] = mockContext;
            manager['repoIndexer'] = { test: 'existing' } as any;
            mockContext.globalState.get.mockReturnValue('/test/storage/repoIndexerData.json');

            // Mock fs.unlink 失败
            (fs.unlink as jest.Mock).mockRejectedValue(new Error('文件不存在'));

            // 执行测试 - 应该继续执行，只是记录警告
            await manager.cleanExistingIndexData();

            // 验证仍然清理了内存中的索引器
            expect(manager['repoIndexer']).toBeNull();

            // 验证仍然清理了globalState
            expect(mockContext.globalState.update).toHaveBeenCalledWith('repoIndexerData', undefined);
        });

        it('应该在没有索引文件时正常清理', async () => {
            // 设置context但没有索引文件
            manager['context'] = mockContext;
            manager['repoIndexer'] = { test: 'existing' } as any;
            mockContext.globalState.get.mockReturnValue(undefined);

            // 执行测试
            await manager.cleanExistingIndexData();

            // 验证清理了内存中的索引器
            expect(manager['repoIndexer']).toBeNull();

            // 验证清理了globalState
            expect(mockContext.globalState.update).toHaveBeenCalledWith('repoIndexerData', undefined);

            // 验证没有尝试删除文件（因为没有文件路径）
            expect(fs.unlink).not.toHaveBeenCalled();
        });
    });

    describe('清理索引后查看索引', () => {
        it('应该在清理索引后显示索引为空', async () => {
            // 设置context
            manager['context'] = mockContext;

            // 先设置一个模拟的索引器
            const mockRepoIndexer = {
                exportData: jest.fn().mockReturnValue({
                    workspacePath: '/test/workspace',
                    languageExts: ['*'],
                    graph: {
                        nodes: new Map(),
                        edges: []
                    },
                    lastModifiedTime: []
                })
            };
            manager['repoIndexer'] = mockRepoIndexer as any;

            // 验证清理前有索引数据
            expect(manager.getRepoIndexer()).toBe(mockRepoIndexer);

            // 执行清理
            await manager.cleanExistingIndexData();

            // 验证清理后索引器为null
            expect(manager.getRepoIndexer()).toBeNull();

            // 验证hasRepoIndexer返回false
            expect(manager.hasRepoIndexer()).toBe(false);
        });

        it('应该在清理索引后getIndexStatus显示正确状态', async () => {
            // 设置context和初始状态
            manager['context'] = mockContext;
            manager['repoIndexer'] = { test: 'existing' } as any;
            manager['lastIndexTime'] = Date.now();

            // 验证清理前的状态
            const beforeStatus = manager.getIndexStatus();
            expect(beforeStatus.hasRepoIndexer).toBe(true);
            expect(beforeStatus.lastIndexTime).toBeGreaterThan(0);

            // 执行清理
            await manager.cleanExistingIndexData();

            // 验证清理后的状态
            const afterStatus = manager.getIndexStatus();
            expect(afterStatus.hasRepoIndexer).toBe(false);
            expect(afterStatus.lastIndexTime).toBe(0);
            expect(afterStatus.isIndexing).toBe(false);
        });

        it('应该在清理索引后能正确重新初始化工作区索引', async () => {
            // 设置context和初始索引
            manager['context'] = mockContext;
            manager['repoIndexer'] = { test: 'existing' } as any;
            mockContext.globalState.get.mockReturnValue('/test/storage/repoIndexerData.json');

            // Mock fs.unlink 成功
            (fs.unlink as jest.Mock).mockResolvedValue(undefined);

            // 执行清理
            await manager.cleanExistingIndexData();

            // 验证清理后索引器为null且globalState已清理
            expect(manager.getRepoIndexer()).toBeNull();
            expect(mockContext.globalState.update).toHaveBeenCalledWith('repoIndexerData', undefined);

            // 重置mock，模拟清理后的状态
            mockContext.globalState.get.mockReturnValue(undefined);

            // Mock 配置文件不存在，需要创建
            (vscode.workspace.fs.readFile as jest.Mock).mockRejectedValue(new Error('文件不存在'));

            // Mock createDefaultConfig 和 initializeAndAnalyze 方法
            jest.spyOn(manager as any, 'createDefaultConfig').mockResolvedValue(undefined);
            jest.spyOn(manager as any, 'initializeAndAnalyze').mockResolvedValue(undefined);

            // 执行重新初始化工作区索引
            await manager.initializeWorkspaceIndex(mockContext);

            // 验证创建了配置文件并执行了索引
            expect(manager['createDefaultConfig']).toHaveBeenCalledWith('/test/workspace', ['*']);
            expect(manager['initializeAndAnalyze']).toHaveBeenCalledWith('/test/workspace', ['*']);
        });
    });
}); 