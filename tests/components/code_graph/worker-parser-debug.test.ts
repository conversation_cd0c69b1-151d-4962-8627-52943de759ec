import * as fs from 'fs';
import * as path from 'path';
import { Worker } from 'worker_threads';

describe('Worker Parser Debug', () => {
  let worker: Worker;

  beforeAll(async () => {
    // 确保 worker 文件存在
    const workerPath = path.join(__dirname, '../../../dist/components/code_graph/workers/fileParserWorker.js');
    expect(fs.existsSync(workerPath)).toBe(true);
  });

  afterEach(() => {
    if (worker) {
      worker.terminate();
    }
  });

  test('应该能够正确初始化 worker 并解析 NP 文件', async () => {
    console.log('🔍 开始测试 worker 解析器...');
    
    return new Promise<void>((resolve, reject) => {
      try {
        // 创建 worker
        const workerPath = path.join(__dirname, '../../../dist/components/code_graph/workers/fileParserWorker.js');
        console.log(`📁 Worker 路径: ${workerPath}`);
        
        worker = new Worker(workerPath);

        // 设置超时
        const timeout = setTimeout(() => {
          worker.terminate();
          reject(new Error('Worker 超时'));
        }, 30000); // 30秒超时

        // 监听 worker 消息
        worker.on('message', (result) => {
          clearTimeout(timeout);
          worker.terminate();
          
          console.log('📋 Worker 解析结果:');
          console.log(`   成功: ${result.success}`);
          console.log(`   文件: ${result.filePath}`);
          console.log(`   有AST: ${!!result.ast}`);
          if (result.ast) {
            console.log(`   AST类型: ${result.ast.type || 'N/A'}`);
            console.log(`   语言: ${result.ast.language || 'N/A'}`);
            console.log(`   根节点类型: ${result.ast.children?.[0]?.type || 'N/A'}`);
            console.log(`   函数数量: ${result.ast.children?.filter((c: any) => c.kind === 'function').length || 0}`);
          }
          if (result.error) {
            console.log(`   错误: ${result.error}`);
          }
          
          // 验证结果
          expect(result.success).toBe(true);
          expect(result.ast).toBeDefined();
          expect(result.ast.type).toBe('program');
          expect(result.ast.language).toBe('np');
          
          resolve();
        });

        // 监听 worker 错误
        worker.on('error', (error) => {
          clearTimeout(timeout);
          worker.terminate();
          console.error('❌ Worker 错误:', error.message);
          console.error('错误堆栈:', error.stack);
          reject(error);
        });

        // 测试文件路径
        const testFile = path.join(__dirname, '../../../test_projects/np/main.asm');
        const workspacePath = path.join(__dirname, '../../../test_projects/np');

        console.log(`📁 测试文件: ${testFile}`);
        console.log(`📁 工作空间: ${workspacePath}`);
        console.log(`📁 测试文件存在: ${fs.existsSync(testFile)}`);

        // 读取测试文件内容
        const fileContent = fs.readFileSync(testFile, 'utf-8');
        console.log(`📄 文件内容 (前200字符): ${fileContent.substring(0, 200)}...`);

        // 发送解析任务
        worker.postMessage({
          type: 'parse_file',
          filePath: testFile,
          workspacePath: workspacePath
        });

      } catch (error) {
        reject(error);
      }
    });
  }, 30000);

  test('应该能够正确初始化 worker 并解析 C 文件', async () => {
    console.log('🔍 开始测试 worker 解析 C 文件...');
    
    return new Promise<void>((resolve, reject) => {
      try {
        // 创建 worker
        const workerPath = path.join(__dirname, '../../../dist/components/code_graph/workers/fileParserWorker.js');
        worker = new Worker(workerPath);

        // 设置超时
        const timeout = setTimeout(() => {
          worker.terminate();
          reject(new Error('Worker 超时'));
        }, 30000);

        // 监听 worker 消息
        worker.on('message', (result) => {
          clearTimeout(timeout);
          worker.terminate();
          
          console.log('📋 Worker C 文件解析结果:');
          console.log(`   成功: ${result.success}`);
          console.log(`   文件: ${result.filePath}`);
          console.log(`   有AST: ${!!result.ast}`);
          if (result.ast) {
            console.log(`   AST类型: ${result.ast.type || 'N/A'}`);
            console.log(`   语言: ${result.ast.language || 'N/A'}`);
            console.log(`   根节点类型: ${result.ast.children?.[0]?.type || 'N/A'}`);
            console.log(`   函数数量: ${result.ast.children?.filter((c: any) => c.kind === 'function').length || 0}`);
          }
          if (result.error) {
            console.log(`   错误: ${result.error}`);
          }
          
          // 验证结果
          expect(result.success).toBe(true);
          expect(result.ast).toBeDefined();
          expect(result.ast.type).toBe('program');
          expect(result.ast.language).toBe('c');
          
          resolve();
        });

        // 监听 worker 错误
        worker.on('error', (error) => {
          clearTimeout(timeout);
          worker.terminate();
          console.error('❌ Worker 错误:', error.message);
          console.error('错误堆栈:', error.stack);
          reject(error);
        });

        // 测试文件路径
        const testFile = path.join(__dirname, '../../../test_projects/np/math.h');
        const workspacePath = path.join(__dirname, '../../../test_projects/np');

        console.log(`📁 测试文件: ${testFile}`);
        console.log(`📁 工作空间: ${workspacePath}`);
        console.log(`📁 测试文件存在: ${fs.existsSync(testFile)}`);

        // 读取测试文件内容
        const fileContent = fs.readFileSync(testFile, 'utf-8');
        console.log(`📄 文件内容 (前200字符): ${fileContent.substring(0, 200)}...`);

        // 发送解析任务
        worker.postMessage({
          type: 'parse_file',
          filePath: testFile,
          workspacePath: workspacePath
        });

      } catch (error) {
        reject(error);
      }
    });
  }, 30000);

  test('应该能够处理模块路径测试', async () => {
    console.log('🔍 开始测试 worker 模块路径...');
    
    return new Promise<void>((resolve, reject) => {
      try {
        // 创建 worker
        const workerPath = path.join(__dirname, '../../../dist/components/code_graph/workers/fileParserWorker.js');
        worker = new Worker(workerPath);

        // 设置超时
        const timeout = setTimeout(() => {
          worker.terminate();
          reject(new Error('Worker 超时'));
        }, 30000);

        // 监听 worker 消息
        worker.on('message', (result) => {
          clearTimeout(timeout);
          worker.terminate();
          
          if (result.type === 'module_path_test') {
            console.log('📋 Worker 模块路径测试结果:');
            console.log(`   当前目录: ${result.currentDir}`);
            console.log(`   __dirname: ${result.dirname}`);
            console.log(`   process.cwd(): ${result.cwd}`);
            console.log(`   web-tree-sitter 路径: ${result.webTreeSitterPath}`);
            console.log(`   tree-sitter-np 路径: ${result.treeSitterNpPath}`);
            console.log(`   WASM 文件存在: ${result.wasmExists}`);
            
            // 验证结果
            expect(result.wasmExists).toBe(true);
            expect(result.webTreeSitterPath).not.toBe('not found');
            expect(result.treeSitterNpPath).not.toBe('not found');
          }
          
          resolve();
        });

        // 监听 worker 错误
        worker.on('error', (error) => {
          clearTimeout(timeout);
          worker.terminate();
          console.error('❌ Worker 错误:', error.message);
          console.error('错误堆栈:', error.stack);
          reject(error);
        });

        // 发送测试消息
        worker.postMessage({
          type: 'test_module_path',
          test: true
        });

      } catch (error) {
        reject(error);
      }
    });
  }, 30000);
}); 