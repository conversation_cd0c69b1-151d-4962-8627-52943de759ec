import * as path from 'path';
import { WorkerPool } from '../../../src/components/code_graph/workers/workerPool';

describe('WorkerPool 批量解析修复测试', () => {
  let workerPool: WorkerPool;

  beforeEach(() => {
    workerPool = new WorkerPool({
      maxWorkers: 3,
      onProgress: (progress) => {
        console.log(`📊 进度: ${progress.percentage}% (${progress.completed}/${progress.total})`);
        if (progress.currentFile) {
          console.log(`📁 当前文件: ${progress.currentFile}`);
        }
      }
    });
  });

  afterEach(async () => {
    await workerPool.shutdown();
  });

  it('应该正确处理14个文件的批量解析，避免"没有可用的worker"错误', async () => {
    console.log('=== 测试 WorkerPool 批量解析修复 ===');
    
    await workerPool.initialize();
    console.log('✅ Worker 池初始化完成');

    // 创建14个测试文件路径（模拟原始问题）
    const testFiles: string[] = [];
    const workspacePath = path.join(__dirname, '../../../test_projects/np');
    
    // 添加一些真实存在的文件
    const realFiles = [
      'hello.asm',
      'main.asm', 
      'math.h',
      'math.asm',
      'test.asm',
      'switch1.asm',
      'ipc.asm',
      'ipu_usertrace.asm',
      'demo.asm'
    ];
    
    // 添加一些不存在的文件来模拟完整场景
    const fakeFiles = [
      'ipu_res_def.h',
      'ipu_tbl_access.asm',
      'ipu_tbl_def.h',
      'ipu_tbl_key_def.h',
      'ipu_translb.asm',
      'pkt_ipv6_hdr.h'
    ];
    
    // 组合所有文件
    [...realFiles, ...fakeFiles].forEach(file => {
      testFiles.push(path.join(workspacePath, file));
    });

    const tasks = testFiles.map(filePath => ({
      filePath,
      workspacePath
    }));

    console.log(`🚀 开始解析 ${tasks.length} 个文件...`);
    console.log(`📋 文件列表:`);
    tasks.forEach((task, index) => {
      console.log(`  ${index + 1}. ${path.basename(task.filePath)}`);
    });

    const startTime = Date.now();
    const results = await workerPool.parseFiles(tasks);
    const endTime = Date.now();

    console.log(`✅ 解析完成，耗时: ${endTime - startTime}ms`);
    
    const successCount = results.filter(r => r.success).length;
    const failedCount = results.filter(r => !r.success).length;
    
    console.log(`📈 结果统计:`);
    console.log(`  成功: ${successCount} 个`);
    console.log(`  失败: ${failedCount} 个`);
    console.log(`  总计: ${tasks.length} 个`);
    
    // 显示失败的文件
    if (failedCount > 0) {
      console.log(`❌ 失败的文件:`);
      results.filter(r => !r.success).forEach(result => {
        console.log(`  - ${path.basename(result.filePath)}: ${result.error}`);
      });
    }
    
    // 显示成功的文件
    if (successCount > 0) {
      console.log(`✅ 成功的文件:`);
      results.filter(r => r.success).forEach(result => {
        console.log(`  - ${path.basename(result.filePath)}`);
      });
    }

    // 验证修复效果
    console.log(`\n🔍 修复验证:`);
    
    // 验证：存在的文件应该成功解析
    const realFileResults = results.filter(r => 
      realFiles.some(realFile => r.filePath.includes(realFile))
    );
    const realFileSuccessCount = realFileResults.filter(r => r.success).length;
    
    console.log(`真实文件解析结果: ${realFileSuccessCount}/${realFiles.length} 成功`);
    
    // 验证：不存在的文件应该失败
    const fakeFileResults = results.filter(r => 
      fakeFiles.some(fakeFile => r.filePath.includes(fakeFile))
    );
    const fakeFileSuccessCount = fakeFileResults.filter(r => r.success).length;
    
    console.log(`不存在文件解析结果: ${fakeFileSuccessCount}/${fakeFiles.length} 成功`);
    
    // 断言：真实文件应该大部分成功解析
    expect(realFileSuccessCount).toBeGreaterThan(0);
    
    // 断言：不存在文件应该失败
    expect(fakeFileSuccessCount).toBe(0);
    
    // 断言：不应该有"没有可用的worker"错误
    const noWorkerErrors = results.filter(r => 
      !r.success && r.error && r.error.includes('没有可用的 worker')
    );
    expect(noWorkerErrors.length).toBe(0);
    
    if (realFileSuccessCount > 0) {
      console.log(`✅ 修复成功！存在的文件成功解析，不存在文件正确失败，没有worker池错误`);
    } else {
      console.log(`❌ 修复失败！存在文件也失败了，说明还有问题`);
    }
  }, 30000); // 30秒超时

  it('应该按批次处理文件，每批最多3个并发', async () => {
    await workerPool.initialize();
    
    const testFiles = [
      'hello.asm',
      'main.asm', 
      'math.h',
      'math.asm',
      'test.asm',
      'switch1.asm',
      'ipc.asm',
      'ipu_usertrace.asm',
      'demo.asm'
    ];
    
    const workspacePath = path.join(__dirname, '../../../test_projects/np');
    const tasks = testFiles.map(file => ({
      filePath: path.join(workspacePath, file),
      workspacePath
    }));

    console.log(`🚀 开始解析 ${tasks.length} 个文件，验证批次处理...`);
    
    const startTime = Date.now();
    const results = await workerPool.parseFiles(tasks);
    const endTime = Date.now();

    console.log(`✅ 解析完成，耗时: ${endTime - startTime}ms`);
    
    const successCount = results.filter(r => r.success).length;
    console.log(`📈 成功解析: ${successCount}/${tasks.length} 个文件`);
    
    // 验证所有真实文件都应该成功解析
    expect(successCount).toBeGreaterThan(0);
    
    // 验证没有worker池错误
    const workerErrors = results.filter(r => 
      !r.success && r.error && r.error.includes('没有可用的 worker')
    );
    expect(workerErrors.length).toBe(0);
    
    console.log(`✅ 批次处理验证成功！`);
  }, 30000);
}); 