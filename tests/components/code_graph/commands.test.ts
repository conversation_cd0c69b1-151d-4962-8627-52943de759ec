
// Mock VSCode API
jest.mock('vscode', () => ({
  window: {
    activeTextEditor: {
      document: {
        uri: { fsPath: '/test/workspace/test.ts' },
        languageId: 'typescript',
        fileName: '/test/workspace/test.ts',
        lineAt: jest.fn(() => ({ 
          text: 'test', 
          range: { 
            start: { line: 0, character: 0 }, 
            end: { line: 0, character: 4 } 
          } 
        })),
        getText: jest.fn(() => 'test'),
        lineCount: 1
      },
      selection: {
        active: { line: 0, character: 0 }
      },
      setDecorations: jest.fn()
    },
    createOutputChannel: jest.fn().mockReturnValue({
      appendLine: jest.fn(),
      append: jest.fn(),
      clear: jest.fn(),
      show: jest.fn(),
      hide: jest.fn(),
      dispose: jest.fn()
    }),
    createStatusBarItem: jest.fn().mockReturnValue({
      text: '',
      tooltip: '',
      command: '',
      show: jest.fn(),
      hide: jest.fn(),
      dispose: jest.fn()
    }),
    showInformationMessage: jest.fn(),
    showErrorMessage: jest.fn(),
    showWarningMessage: jest.fn(),
    visibleTextEditors: [],
    onDidChangeActiveTextEditor: jest.fn().mockReturnValue({ dispose: jest.fn() }),
    onDidChangeTextEditorSelection: jest.fn().mockReturnValue({ dispose: jest.fn() })
  },
  workspace: {
    workspaceFolders: [
      {
        uri: {
          fsPath: '/test/workspace'
        },
        name: 'test-workspace'
      }
    ],
    createFileSystemWatcher: jest.fn().mockReturnValue({
      onDidCreate: jest.fn().mockReturnValue({
        dispose: jest.fn()
      }),
      onDidChange: jest.fn().mockReturnValue({
        dispose: jest.fn()
      }),
      onDidDelete: jest.fn().mockReturnValue({
        dispose: jest.fn()
      }),
      dispose: jest.fn()
    }),
    onDidChangeTextDocument: jest.fn().mockReturnValue({ dispose: jest.fn() }),
    onDidChangeWorkspaceFolders: jest.fn().mockReturnValue({ dispose: jest.fn() }),
    onDidOpenTextDocument: jest.fn().mockReturnValue({ dispose: jest.fn() }),
    onDidCloseTextDocument: jest.fn().mockReturnValue({ dispose: jest.fn() }),
    onDidSaveTextDocument: jest.fn().mockReturnValue({ dispose: jest.fn() }),
    onDidCreateFiles: jest.fn().mockReturnValue({ dispose: jest.fn() }),
    onDidDeleteFiles: jest.fn().mockReturnValue({ dispose: jest.fn() }),
    onDidChangeConfiguration: jest.fn().mockReturnValue({ dispose: jest.fn() }),
    fs: {
      readFile: jest.fn(),
      writeFile: jest.fn(),
      stat: jest.fn()
    },
    getConfiguration: jest.fn((section) => ({
      get: jest.fn((key: string) => {
        if (section === 'code-partner') {
          const defaults: Record<string, any> = {
            usePythonService: true,
            pythonEndpoint: 'http://127.0.0.1:5555/api/v1/completion',
            openAIApiKey: '',
            model: 'qwen2.5-coder:3b',
            statisticsEndpoint: 'http://127.0.0.1:5555/api/v1/statistics/events',
            enableStatistics: true,
            maxTokens: 256,
            temperature: 0.2,
            enableInlineCompletion: true,
            enableDiffHighlightCompletion: true,
            enableIndexing: true,
            autoIndexing: true,
            languageExtensions: ['*'],
            graphWeight: 0.4,
            bm25Weight: 0.3,
            embeddingWeight: 0.3,
            logLevel: 'INFO',
            enableDebugLog: false
          };
          return defaults[key];
        }
        return undefined;
      }),
      update: jest.fn()
    }))
  },
  Uri: {
    file: jest.fn((path) => ({ fsPath: path, toString: () => `file://${path}` }))
  },
  Position: jest.fn().mockImplementation(function(line, character) {
    return {
      line,
      character,
      translate: jest.fn(),
      with: jest.fn(),
      compareTo: jest.fn()
    };
  }),
  Range: jest.fn().mockImplementation((start, end) => ({
    start,
    end,
    isEmpty: jest.fn(),
    isSingleLine: jest.fn(),
    contains: jest.fn(),
    isEqual: jest.fn(),
    union: jest.fn(),
    intersection: jest.fn()
  })),
  EventEmitter: jest.fn().mockImplementation(() => ({
    event: jest.fn(),
    fire: jest.fn(),
    dispose: jest.fn()
  })),
  ExtensionContext: jest.fn().mockImplementation(() => ({
    subscriptions: [],
    globalState: {
      get: jest.fn(),
      update: jest.fn()
    },
    globalStorageUri: {
      fsPath: '/test/storage'
    }
  })),
  languages: {
    registerCompletionItemProvider: jest.fn(),
    registerCodeLensProvider: jest.fn(),
    registerInlineCompletionItemProvider: jest.fn(),
  },
  commands: {
    registerCommand: jest.fn(),
    executeCommand: jest.fn(),
    getCommands: jest.fn().mockResolvedValue([
      'code-partner.extractSFTData',
      'code-partner.helloWorld',
      'code-partner.test',
      'code-partner.reinitializeWorkspaceIndex',
      'code-partner.forceReinitializeIndex',
      'code-partner.queryRelatedFunctions',
      'code-partner.updateRepoIndex',
      'code-partner.showRepoIndex'
    ])
  },
  StatusBarAlignment: {
    Left: 1,
    Right: 2
  },
  StatusBarItem: jest.fn().mockImplementation(() => ({
    text: '',
    tooltip: '',
    command: '',
    show: jest.fn(),
    hide: jest.fn(),
    dispose: jest.fn()
  })),
  ConfigurationTarget: {
    Global: 1,
    Workspace: 2
  },
  ProgressLocation: {
    Notification: 1,
    Window: 10,
    SourceControl: 1
  },
  authentication: {
    getSession: jest.fn().mockResolvedValue(undefined)
  },
  extensions: {
    getExtension: jest.fn().mockReturnValue({ 
      packageJSON: { version: '1.0.0' },
      exports: {
        getAPI: jest.fn().mockReturnValue({
          repositories: [{
            getConfig: jest.fn().mockResolvedValue('test-user')
          }]
        })
      }
    })
  },
  version: '1.80.0',
  env: {
    language: 'en',
    machineId: 'test-machine-id',
    sessionId: 'test-session-id'
  }
}));

import { CodeGraphCommands } from 'src/components/code_graph/commands';
import { RepoIndexerManager } from 'src/components/code_graph/repoIndexer/repoIndexerManager';
import * as vscode from 'vscode';

describe('CodeGraphCommands', () => {
    let commands: CodeGraphCommands;
    let mockContext: vscode.ExtensionContext;
    let mockOutputChannel: any;
    let repoIndexerManager: RepoIndexerManager;

    beforeEach(() => {
        // Mock context
        mockContext = {
            globalState: {
                get: jest.fn(),
                update: jest.fn()
            },
            subscriptions: []
        } as any;

        // Mock output channel
        mockOutputChannel = {
            clear: jest.fn(),
            show: jest.fn(),
            appendLine: jest.fn()
        };
        (vscode.window.createOutputChannel as jest.Mock).mockReturnValue(mockOutputChannel);



        // 创建命令实例
        commands = new CodeGraphCommands(mockContext);
        repoIndexerManager = RepoIndexerManager.getInstance();
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    describe('showRepoIndex', () => {
        it('应该在索引为空时显示提示信息', async () => {
            // Mock 没有索引器
            jest.spyOn(repoIndexerManager, 'getRepoIndexer').mockReturnValue(null);

            // 执行测试
            await commands.showRepoIndex();

            // 验证显示提示信息（根据实际代码行为）
            expect(vscode.window.showInformationMessage).toHaveBeenCalledWith('索引数据为空，请先执行"初始化工作区索引"命令');
        });

        it('应该在索引数据为空时显示提示信息', async () => {
            // Mock 有索引器但数据为空
            const mockRepoIndexer = {
                exportData: jest.fn().mockReturnValue({
                    workspacePath: '/test/workspace',
                    languageExts: ['*'],
                    graph: {
                        nodes: [],
                        edges: []
                    },
                    lastModifiedTime: []
                })
            };
            jest.spyOn(repoIndexerManager, 'getRepoIndexer').mockReturnValue(mockRepoIndexer as any);

            // 执行测试
            await commands.showRepoIndex();

            // 验证显示提示信息
            expect(vscode.window.showInformationMessage).toHaveBeenCalledWith('索引数据为空，请先执行索引');
        });

        it('应该在索引数据有效时显示索引内容', async () => {
            // Mock 有有效的索引数据
            const mockRepoIndexer = {
                exportData: jest.fn().mockReturnValue({
                    workspacePath: '/test/workspace',
                    languageExts: ['*'],
                    graph: {
                        nodes: [
                            { name: 'test1', type: 'function' },
                            { name: 'test2', type: 'function' }
                        ],
                        edges: [{ from: 'node1', to: 'node2', type: 'calls' }]
                    },
                    lastModifiedTime: []
                })
            };
            jest.spyOn(repoIndexerManager, 'getRepoIndexer').mockReturnValue(mockRepoIndexer as any);

            // 执行测试
            await commands.showRepoIndex();

            // 验证显示成功（不验证具体的输出通道调用，因为Logger可能没有正确配置）
            expect(vscode.window.showInformationMessage).not.toHaveBeenCalled();
            expect(vscode.window.showErrorMessage).not.toHaveBeenCalled();
        });

        it('应该在导出数据失败时显示错误信息', async () => {
            // Mock 索引器抛出异常
            const mockRepoIndexer = {
                exportData: jest.fn().mockImplementation(() => {
                    throw new Error('导出失败');
                })
            };
            jest.spyOn(repoIndexerManager, 'getRepoIndexer').mockReturnValue(mockRepoIndexer as any);

            // 执行测试
            await commands.showRepoIndex();

            // 验证显示错误信息
            expect(vscode.window.showErrorMessage).toHaveBeenCalledWith('获取索引数据失败，请重新初始化索引');
        });

        it('应该在清理索引后显示索引为空', async () => {
            // 先设置一个有效的索引器
            const mockRepoIndexer = {
                exportData: jest.fn().mockReturnValue({
                    workspacePath: '/test/workspace',
                    languageExts: ['*'],
                    graph: {
                        nodes: [{ name: 'test1', type: 'function' }],
                        edges: []
                    },
                    lastModifiedTime: []
                })
            };
            jest.spyOn(repoIndexerManager, 'getRepoIndexer').mockReturnValue(mockRepoIndexer as any);

            // 验证清理前可以正常显示
            await commands.showRepoIndex();
            expect(vscode.window.showInformationMessage).not.toHaveBeenCalled();
            expect(vscode.window.showErrorMessage).not.toHaveBeenCalled();

            // 清理索引（模拟清理后的状态）
            jest.spyOn(repoIndexerManager, 'getRepoIndexer').mockReturnValue(null);

            // 验证清理后显示提示信息
            await commands.showRepoIndex();
            expect(vscode.window.showInformationMessage).toHaveBeenCalledWith('索引数据为空，请先执行"初始化工作区索引"命令');
        });
    });

    describe('queryRelatedFunctions', () => {
        it('应该在索引为空时显示错误信息', async () => {
            // Mock 没有索引器
            jest.spyOn(repoIndexerManager, 'getRepoIndexer').mockReturnValue(null);

            // 执行测试
            await commands.queryRelatedFunctions();

            // 验证显示错误信息
            expect(vscode.window.showErrorMessage).toHaveBeenCalledWith('请先初始化代码仓库索引');
        });

        it('应该在清理索引后显示错误信息', async () => {
            // 先设置一个有效的索引器
            const mockRepoIndexer = {
                getModuleRelatedNodesByName: jest.fn().mockReturnValue([])
            };
            jest.spyOn(repoIndexerManager, 'getRepoIndexer').mockReturnValue(mockRepoIndexer as any);

            // 清理索引（模拟清理后的状态）
            jest.spyOn(repoIndexerManager, 'getRepoIndexer').mockReturnValue(null);

            // 执行测试
            await commands.queryRelatedFunctions();

            // 验证显示错误信息
            expect(vscode.window.showErrorMessage).toHaveBeenCalledWith('请先初始化代码仓库索引');
        });
    });
}); 