// Mock VSCode API
jest.mock('vscode', () => ({
  window: {
    createOutputChannel: jest.fn().mockReturnValue({
      appendLine: jest.fn(),
      append: jest.fn(),
      clear: jest.fn(),
      show: jest.fn(),
      hide: jest.fn(),
      dispose: jest.fn()
    }),
    showInformationMessage: jest.fn(),
    showErrorMessage: jest.fn(),
    showWarningMessage: jest.fn()
  },
  workspace: {
    workspaceFolders: [
      {
        uri: {
          fsPath: '/test/workspace'
        },
        name: 'test-workspace'
      }
    ],
    createFileSystemWatcher: jest.fn().mockReturnValue({
      onDidCreate: jest.fn().mockReturnValue({
        dispose: jest.fn()
      }),
      onDidChange: jest.fn().mockReturnValue({
        dispose: jest.fn()
      }),
      onDidDelete: jest.fn().mockReturnValue({
        dispose: jest.fn()
      }),
      dispose: jest.fn()
    }),
    fs: {
      readFile: jest.fn(),
      writeFile: jest.fn(),
      stat: jest.fn()
    }
  },
  Uri: {
    file: jest.fn((path) => ({ fsPath: path }))
  },
  Position: jest.fn().mockImplementation(function(line, character) {
    return {
      line,
      character,
      translate: jest.fn(),
      with: jest.fn(),
      compareTo: jest.fn()
    };
  }),
  EventEmitter: jest.fn().mockImplementation(() => ({
    event: jest.fn(),
    fire: jest.fn(),
    dispose: jest.fn()
  })),
  ExtensionContext: jest.fn().mockImplementation(() => ({
    subscriptions: [],
    globalState: {
      get: jest.fn(),
      update: jest.fn()
    },
    globalStorageUri: {
      fsPath: '/test/storage'
    }
  }))
}));

import * as assert from 'assert';
import { ParserManager } from 'src/components/code_graph/parser/ParserManager';
import { SupportedLanguage } from 'src/components/code_graph/types/language';
import * as vscode from 'vscode';

function createMockTextDocument(content = 'void foo() {}', filePath = 'test.c') {
  return {
    uri: { 
      fsPath: filePath,
      scheme: 'file',
      authority: '',
      path: filePath,
      query: '',
      fragment: '',
      toString: () => filePath,
      with: jest.fn(),
      toJSON: () => ({ fsPath: filePath, scheme: 'file', authority: '', path: filePath, query: '', fragment: '' })
    },
    fileName: filePath,
    isUntitled: false,
    languageId: 'c',
    version: 1,
    isDirty: false,
    isClosed: false,
    save: jest.fn(),
    lineCount: content.split('\n').length,
    getText: () => content,
    getWordRangeAtPosition: jest.fn(),
    validateRange: jest.fn(),
    validatePosition: jest.fn(),
    eol: 1,
    encoding: 'utf8',
    lineAt: ((lineOrPosition: number | any) => {
      const line = typeof lineOrPosition === 'number' ? lineOrPosition : lineOrPosition.line;
      return {
        lineNumber: line,
        text: content.split('\n')[line] || '',
        range: {
          start: { 
            line, 
            character: 0,
            isBefore: jest.fn(),
            isBeforeOrEqual: jest.fn(),
            isAfter: jest.fn(),
            isAfterOrEqual: jest.fn(),
            isEqual: jest.fn(),
            compareTo: jest.fn(),
            translate: jest.fn(),
            with: jest.fn()
          },
          end: { 
            line, 
            character: (content.split('\n')[line] || '').length,
            isBefore: jest.fn(),
            isBeforeOrEqual: jest.fn(),
            isAfter: jest.fn(),
            isAfterOrEqual: jest.fn(),
            isEqual: jest.fn(),
            compareTo: jest.fn(),
            translate: jest.fn(),
            with: jest.fn()
          },
          isEmpty: false,
          isSingleLine: true,
          contains: jest.fn(),
          intersection: jest.fn(),
          union: jest.fn(),
          with: jest.fn(),
          isEqual: jest.fn()
        },
        rangeIncludingLineBreak: {
          start: { 
            line, 
            character: 0,
            isBefore: jest.fn(),
            isBeforeOrEqual: jest.fn(),
            isAfter: jest.fn(),
            isAfterOrEqual: jest.fn(),
            isEqual: jest.fn(),
            compareTo: jest.fn(),
            translate: jest.fn(),
            with: jest.fn()
          },
          end: { 
            line: line + 1, 
            character: 0,
            isBefore: jest.fn(),
            isBeforeOrEqual: jest.fn(),
            isAfter: jest.fn(),
            isAfterOrEqual: jest.fn(),
            isEqual: jest.fn(),
            compareTo: jest.fn(),
            translate: jest.fn(),
            with: jest.fn()
          },
          isEmpty: false,
          isSingleLine: false,
          contains: jest.fn(),
          intersection: jest.fn(),
          union: jest.fn(),
          with: jest.fn(),
          isEqual: jest.fn()
        },
        firstNonWhitespaceCharacterIndex: 0,
        isEmptyOrWhitespace: false
      };
    }) as any,
    offsetAt: jest.fn(),
    positionAt: jest.fn(),
  };
}

describe('ParserManager', () => {
  let parserManager: ParserManager;

  beforeEach(() => {
    parserManager = new ParserManager();
  });

  describe('getLanguageFromFile', () => {
    test('should return c for C files', () => {
      const language = parserManager.getLanguageFromFile('test.c');
      expect(language).toBe('c' as SupportedLanguage);
    });

    test('should return np for NP files', () => {
      const language = parserManager.getLanguageFromFile('test.asm');
      expect(language).toBe('np' as SupportedLanguage);
    });

    test('should return default language for unknown extensions', () => {
      const language = parserManager.getLanguageFromFile('test.unknown');
      expect(language).toBe('c' as SupportedLanguage);
    });
  });

  describe('getParser', () => {
    test('should return CParser for C language', async () => {
      const parser = await parserManager.getParser('c' as SupportedLanguage);
      expect(parser).toBeDefined();
      expect(parser.parse).toBeDefined();
    });

    test('should return NPParser for NP language', async () => {
      const parser = await parserManager.getParser('np' as SupportedLanguage);
      expect(parser).toBeDefined();
      expect(parser.parse).toBeDefined();
    });

    test('should return null for unsupported language', async () => {
      const parser = await parserManager.getParser('unsupported' as SupportedLanguage);
      expect(parser).toBeNull();
    });
  });

  describe('findFunctionAtPosition', () => {
    test('should find function at position', async () => {
      const mockContent = 'int main() {\n  return 0;\n}';
      const mockFs = vscode.workspace.fs as any;
      const readFileSpy = jest.spyOn(mockFs, 'readFile');
      readFileSpy.mockResolvedValue(new Uint8Array(Buffer.from(mockContent)));

      const position = new vscode.Position(1, 0);
      const mockDoc = createMockTextDocument('void foo() {}', 'test.c');
      mockDoc.lineAt = () => ({
        lineNumber: 0,
        text: 'int main() { return 0; }',
        range: {
          start: { 
            line: 0, 
            character: 0,
            isBefore: jest.fn(),
            isBeforeOrEqual: jest.fn(),
            isAfter: jest.fn(),
            isAfterOrEqual: jest.fn(),
            isEqual: jest.fn(),
            compareTo: jest.fn(),
            translate: jest.fn(),
            with: jest.fn()
          },
          end: { 
            line: 0, 
            character: 22,
            isBefore: jest.fn(),
            isBeforeOrEqual: jest.fn(),
            isAfter: jest.fn(),
            isAfterOrEqual: jest.fn(),
            isEqual: jest.fn(),
            compareTo: jest.fn(),
            translate: jest.fn(),
            with: jest.fn()
          },
          isEmpty: false,
          isSingleLine: true,
          contains: jest.fn(),
          intersection: jest.fn(),
          union: jest.fn(),
          with: jest.fn(),
          isEqual: jest.fn()
        },
        rangeIncludingLineBreak: {
          start: { 
            line: 0, 
            character: 0,
            isBefore: jest.fn(),
            isBeforeOrEqual: jest.fn(),
            isAfter: jest.fn(),
            isAfterOrEqual: jest.fn(),
            isEqual: jest.fn(),
            compareTo: jest.fn(),
            translate: jest.fn(),
            with: jest.fn()
          },
          end: { 
            line: 1, 
            character: 0,
            isBefore: jest.fn(),
            isBeforeOrEqual: jest.fn(),
            isAfter: jest.fn(),
            isAfterOrEqual: jest.fn(),
            isEqual: jest.fn(),
            compareTo: jest.fn(),
            translate: jest.fn(),
            with: jest.fn()
          },
          isEmpty: false,
          isSingleLine: false,
          contains: jest.fn(),
          intersection: jest.fn(),
          union: jest.fn(),
          with: jest.fn(),
          isEqual: jest.fn()
        },
        firstNonWhitespaceCharacterIndex: 0,
        isEmptyOrWhitespace: false
      });
      const result = await parserManager.findFunctionAtPosition(mockDoc, position);
      
      // 由于解析器可能初始化失败，结果可能是 null 或 undefined
      // 我们主要测试方法能够正常执行而不抛出错误
      expect(result).toBeDefined();
    });

    test('should handle parser errors', async () => {
      const position = new vscode.Position(1, 0);
      const result = await parserManager.findFunctionAtPosition(createMockTextDocument('void foo() {}', 'test.c'), position);
      
      // 由于解析器可能初始化失败，结果可能是 null
      expect(result).toBeDefined();
    });
  });

  describe('静态方法', () => {
    test('静态getLanguageFromFile应该工作', () => {
      const language = ParserManager.getLanguageFromFile('test.c');
      assert.strictEqual(language, 'c');
    });

    test('静态方法和实例方法应该返回相同结果', () => {
      const filePath = 'test.asm';
      const staticResult = ParserManager.getLanguageFromFile(filePath);
      const instanceResult = parserManager.getLanguageFromFile(filePath);
      
      assert.strictEqual(staticResult, instanceResult);
    });
  });

  describe('错误处理', () => {
    test('应该处理解析器获取失败', async () => {
      // 模拟解析器获取失败的情况
      const originalGetParser = parserManager.getParser.bind(parserManager);
      parserManager.getParser = async () => {
        throw new Error('Parser get failed');
      };

      try {
        const result = await parserManager.findFunctionAtPosition(createMockTextDocument('void foo() {}', 'test.c'), new vscode.Position(1, 0));
        expect(result).toBeNull();
      } catch (error) {
        assert.fail('不应该抛出错误，应该返回null');
      } finally {
        parserManager.getParser = originalGetParser;
      }
    });

    test('应该处理解析失败', async () => {
      const functionNode = await parserManager.findFunctionAtPosition(createMockTextDocument('invalid syntax {', 'test.c'), new vscode.Position(1, 0));
      // 应该返回null或undefined而不是抛出错误
      expect(functionNode).toBeDefined();
    });
  });

  describe('边界情况', () => {
    test('应该处理空文件', async () => {
      const functionNode = await parserManager.findFunctionAtPosition(createMockTextDocument('', 'test.c'), new vscode.Position(0, 0));
      // 空文件应该返回 null 或 undefined
      expect(functionNode).toBeDefined();
    });

    test('应该处理大文件', async () => {
      // 生成大文件
      const largeCode = Array(1000).fill('int func() { return 0; }').join('\n');
      const mockDoc = createMockTextDocument(largeCode, 'large.c');

      const startTime = Date.now();
      const functionNode = await parserManager.findFunctionAtPosition(mockDoc, new vscode.Position(500, 0));
      const endTime = Date.now();
      const duration = endTime - startTime;

      // 大文件应该在5秒内处理完成
      expect(duration).toBeLessThan(5000);
      // 结果可能是 null 或 undefined，取决于解析器状态
      expect(functionNode).toBeDefined();
    });
  });
}); 