import * as fs from 'fs';
import * as path from 'path';
import { WorkerPool } from '../../../src/components/code_graph/workers/workerPool';

describe('WorkerPool Worker路径测试', () => {
  let workerPool: WorkerPool;

  beforeEach(() => {
    workerPool = new WorkerPool({
      maxWorkers: 1,
      onProgress: () => {}
    });
  });

  it('应该能正确找到worker脚本文件', () => {
    // 通过反射获取私有属性
    const workerScriptPath = (workerPool as any).workerScriptPath;
    
    console.log('Worker脚本路径:', workerScriptPath);
    
    // 检查文件是否存在
    expect(fs.existsSync(workerScriptPath)).toBe(true);
    
    // 检查路径是否指向正确的文件
    const fileName = path.basename(workerScriptPath);
    expect(fileName).toBe('fileParserWorker.js');
    
    // 检查路径是否在dist目录中
    expect(workerScriptPath).toContain('dist');
    expect(workerScriptPath).toContain('components/code_graph/workers');
  });

  it('worker脚本文件应该包含必要的代码', () => {
    const workerScriptPath = (workerPool as any).workerScriptPath;
    const fileContent = fs.readFileSync(workerScriptPath, 'utf-8');
    
    // 检查文件是否包含worker_threads相关代码
    expect(fileContent).toContain('worker_threads');
    expect(fileContent).toContain('parentPort');
    
    // 检查文件是否包含消息处理逻辑
    expect(fileContent).toContain('message');
    expect(fileContent).toContain('postMessage');
  });
}); 