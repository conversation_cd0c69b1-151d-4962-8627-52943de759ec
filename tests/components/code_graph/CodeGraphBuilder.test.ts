import path from 'path';
import { CodeGraphBuilder } from '../../../src/components/code_graph/builder/CodeGraphBuilder';
import { NPParser } from '../../../src/components/code_graph/parser/NPParser';
import { ASTNode, ASTNodeKind } from '../../../src/components/code_graph/types/ast';

describe('CodeGraphBuilder - 跨模块函数调用', () => {
  let builder: CodeGraphBuilder;
  let npParser: NPParser;

  beforeAll(async () => {
    builder = new CodeGraphBuilder();
    npParser = await NPParser.create();
  });

  beforeEach(() => {
    builder = new CodeGraphBuilder();
  });

  describe('函数定义收集', () => {
    it('应该正确收集所有模块的函数定义到全局索引', () => {
      // 创建两个模块的AST
      const module1Ast: ASTNode = {
        name: 'module1',
        kind: ASTNodeKind.MODULE,
        originalType: 'module',
        language: 'c',
        children: [
          {
            name: 'func1',
            kind: ASTNodeKind.FUNCTION,
        originalType: 'function_definition',
            language: 'c',
            text: 'void func1() {}',
            children: []
          },
          {
            name: 'func2',
            kind: ASTNodeKind.FUNCTION,
        originalType: 'function_definition',
            language: 'c',
            text: 'void func2() {}',
            children: []
          }
        ]
      };

      const module2Ast: ASTNode = {
        name: 'module2',
        kind: ASTNodeKind.MODULE,
        originalType: 'module',
        language: 'c',
        children: [
          {
            name: 'func3',
            kind: ASTNodeKind.FUNCTION,
            originalType: 'function_definition',
            language: 'c',
            text: 'void func3() {}',
            children: []
          }
        ]
      };

      // 构建代码图
      builder.buildFromMultipleAsts([
        { ast: module1Ast, filePath: 'module1.c' },
        { ast: module2Ast, filePath: 'module2.c' }
      ]);

      // 验证全局函数索引
      const functionIndex = builder.getGlobalFunctionIndex();
      expect(functionIndex.get('func1')).toHaveLength(1);
      expect(functionIndex.get('func1')![0].modulePath).toBe('module1.c');
      expect(functionIndex.get('func2')).toHaveLength(1);
      expect(functionIndex.get('func2')![0].modulePath).toBe('module1.c');
      expect(functionIndex.get('func3')).toHaveLength(1);
      expect(functionIndex.get('func3')![0].modulePath).toBe('module2.c');
    });
  });

  describe('跨模块函数调用', () => {
    it('应该正确构建跨模块的函数调用关系', () => {
      // 创建包含导入关系的AST - 基于实际的 main.asm 文件
      const module1Ast: ASTNode = {
        name: 'main.asm',
        kind: ASTNodeKind.MODULE,
        originalType: 'module',
        language: 'np',
        children: [
          // 头文件包含
          {
            name: 'math.h',
            kind: ASTNodeKind.MODULE_IMPORT,
            originalType: 'include',
            language: 'np',
            children: []
          },
          {
            name: 'stdio.np',
            kind: ASTNodeKind.MODULE_IMPORT,
            originalType: 'include',
            language: 'np',
            children: []
          },
          // 宏定义
          {
            name: 'MSG',
            kind: ASTNodeKind.FUNCTION,
            originalType: 'define_statement',
            language: 'np',
            text: '#define MSG "NP Project Start"',
            children: [],
            metadata: { type: 'macro', value: '"NP Project Start"' }
          },
          // main 函数
          {
            name: 'main',
            kind: ASTNodeKind.FUNCTION,
            originalType: 'function_definition',
            language: 'np',
            text: 'void main() { ... }',
            children: [
              // 函数调用
              {
                name: 'print',
                kind: ASTNodeKind.FUNCTION,
                originalType: 'call_expression',
                language: 'np',
                children: [],
                metadata: { isCall: true }
              },
              {
                name: 'add',
                kind: ASTNodeKind.FUNCTION,
                originalType: 'call_expression',
                language: 'np',
                children: [],
                metadata: { isCall: true }
              },
              {
                name: 'print_point',
                kind: ASTNodeKind.FUNCTION,
                originalType: 'call_expression',
                language: 'np',
                children: [],
                metadata: { isCall: true }
              },
              {
                name: 'SQUARE',
                kind: ASTNodeKind.FUNCTION,
                originalType: 'call_expression',
                language: 'np',
                children: [],
                metadata: { isCall: true, type: 'macro' }
              }
            ]
          }
        ]
      };

      const module2Ast: ASTNode = {
        name: 'math.h',
        kind: ASTNodeKind.MODULE,
        originalType: 'module',
        language: 'c',
        children: [
          // 宏定义
          {
            name: 'E',
            kind: ASTNodeKind.FUNCTION,
            originalType: 'define_statement',
            language: 'c',
            text: '#define E 2.718',
            children: [],
            metadata: { type: 'macro', value: '2.718' }
          },
          {
            name: 'SQUARE',
            kind: ASTNodeKind.FUNCTION,
            originalType: 'define_statement',
            language: 'c',
            text: '#define SQUARE(x) ((x)*(x))',
            children: [],
            metadata: { type: 'macro', value: '((x)*(x))' }
          },
          // bundle 声明
          {
            name: 'add',
            kind: ASTNodeKind.FUNCTION,
            originalType: 'extern_bundle_declaration',
            language: 'c',
            text: 'extern bundle add(a, b)',
            children: [],
            metadata: { type: 'bundle' }
          },
          {
            name: 'print_point',
            kind: ASTNodeKind.FUNCTION,
            originalType: 'function_declaration',
            language: 'c',
            text: 'extern void print_point(p)',
            children: []
          }
        ]
      };

      // 构建代码图
      builder.buildFromMultipleAsts([
        { ast: module1Ast, filePath: 'main.asm' },
        { ast: module2Ast, filePath: 'math.h' }
      ]);

      // 验证调用关系
      const graph = builder.getGraph();
      const mainNodeId = `main.asm::${ASTNodeKind.FUNCTION}::main`;
      const addNodeId = `math.h::${ASTNodeKind.FUNCTION}::add`;
      const printPointNodeId = `math.h::${ASTNodeKind.FUNCTION}::print_point`;
              const squareDefNodeId = `math.h::${ASTNodeKind.FUNCTION}::SQUARE`; // 宏定义节点在导入模块

      // 检查main函数是否调用了add、print_point和SQUARE
      const outgoingEdges = graph.getOutgoingEdges(mainNodeId);
      const callEdges = outgoingEdges.filter(edge => edge.type === 'calls');
      
      expect(callEdges).toHaveLength(3);
      expect(callEdges.some(edge => edge.to === addNodeId)).toBe(true);
      expect(callEdges.some(edge => edge.to === printPointNodeId)).toBe(true);
      expect(callEdges.some(edge => edge.to === squareDefNodeId)).toBe(true);
    });

    it('应该优先在当前模块查找函数', () => {
      // 创建同名函数在不同模块的情况
      const module1Ast: ASTNode = {
        name: 'module1',
        kind: ASTNodeKind.MODULE,
        originalType: 'module',
        language: 'c',
        children: [
          {
            name: 'header.h',
            kind: ASTNodeKind.MODULE,
            originalType: 'include',
            language: 'c',
            children: []
          },
          {
            name: 'func1',
            kind: ASTNodeKind.FUNCTION,
        originalType: 'function_definition',
            language: 'c',
            text: 'void func1() { helper(); }',
                        children: [
              {
                name: 'helper',
                kind: ASTNodeKind.FUNCTION_CALL,
                originalType: 'call_expression',
                language: 'c',
                children: []
              }
            ]
          },
          {
            name: 'helper',
            kind: ASTNodeKind.FUNCTION,
            originalType: 'function_definition',
            language: 'c',
            text: 'void helper() {}',
            children: []
          }
        ]
      };

      const module2Ast: ASTNode = {
        name: 'module2',
        kind: ASTNodeKind.MODULE,
        originalType: 'module',
        language: 'c',
        children: [
          {
            name: 'helper',
            kind: ASTNodeKind.FUNCTION,
        originalType: 'function_definition',
            language: 'c',
            text: 'void helper() {}',
        children: []
          }
        ]
      };

      // 构建代码图
      builder.buildFromMultipleAsts([
        { ast: module1Ast, filePath: 'module1.c' },
        { ast: module2Ast, filePath: 'header.h' }
      ]);

      // 验证func1调用的helper是当前模块的helper
      const graph = builder.getGraph();
      const func1NodeId = `module1.c::${ASTNodeKind.FUNCTION}::func1`;
      const localHelperNodeId = `module1.c::${ASTNodeKind.FUNCTION}::helper`;
      const importedHelperNodeId = `header.h::${ASTNodeKind.FUNCTION}::helper`;

      const outgoingEdges = graph.getOutgoingEdges(func1NodeId);
      const callEdges = outgoingEdges.filter(edge => edge.type === 'calls');
      
      expect(callEdges).toHaveLength(1);
      expect(callEdges[0].to).toBe(localHelperNodeId); // 应该调用当前模块的helper
      expect(callEdges[0].to).not.toBe(importedHelperNodeId); // 不应该调用导入模块的helper
    });
  });

  describe('模块导入关系', () => {
    it('应该正确收集模块导入关系', () => {
      const module1Ast: ASTNode = {
        name: 'module1',
        kind: ASTNodeKind.MODULE,
        originalType: 'module',
        language: 'c',
        children: [
          {
            name: 'header1.h',
            kind: ASTNodeKind.MODULE,
            originalType: 'include',
            language: 'c',
            children: []
          },
          {
            name: 'header2.h',
            kind: ASTNodeKind.MODULE,
            originalType: 'include',
            language: 'c',
            children: []
          }
        ]
      };

      builder.buildFromAst(module1Ast, 'module1.c');

      // 验证模块导入关系
      const moduleImports = builder.getModuleImports();
      expect(moduleImports.get('module1.c')).toBeDefined();
      expect(moduleImports.get('module1.c')!.has('header1.h')).toBe(true);
      expect(moduleImports.get('module1.c')!.has('header2.h')).toBe(true);
    });
  });

  describe('函数查找策略', () => {
    it('应该按照优先级查找函数：当前模块 > 导入模块 > 全局', () => {
      // 创建测试场景：同名函数在多个地方定义
      const module1Ast: ASTNode = {
        name: 'module1',
        kind: ASTNodeKind.MODULE,
        originalType: 'module',
        language: 'c',
        children: [
          {
            name: 'header.h',
            kind: ASTNodeKind.MODULE,
            originalType: 'include',
            language: 'c',
            children: []
          },
          {
        name: 'test',
            kind: ASTNodeKind.FUNCTION,
            originalType: 'function_definition',
        language: 'c',
            text: 'void test() { common(); }',
            children: [
              {
                name: 'common',
                kind: ASTNodeKind.FUNCTION_CALL,
                originalType: 'call_expression',
                language: 'c',
                children: []
              }
            ]
          },
          {
            name: 'common',
            kind: ASTNodeKind.FUNCTION,
            originalType: 'function_definition',
            language: 'c',
            text: 'void common() {}',
            children: []
          }
        ]
      };

      const module2Ast: ASTNode = {
        name: 'module2',
        kind: ASTNodeKind.MODULE,
        originalType: 'module',
        language: 'c',
        children: [
          {
            name: 'common',
            kind: ASTNodeKind.FUNCTION,
            originalType: 'function_definition',
            language: 'c',
            text: 'void common() {}',
            children: []
          }
        ]
      };

      const module3Ast: ASTNode = {
        name: 'module3',
        kind: ASTNodeKind.MODULE,
        originalType: 'module',
        language: 'c',
        children: [
          {
            name: 'common',
            kind: ASTNodeKind.FUNCTION,
            originalType: 'function_definition',
            language: 'c',
            text: 'void common() {}',
            children: []
          }
        ]
      };

      // 构建代码图
      builder.buildFromMultipleAsts([
        { ast: module1Ast, filePath: 'module1.c' },
        { ast: module2Ast, filePath: 'header.h' },
        { ast: module3Ast, filePath: 'global.h' }
      ]);

      // 验证test函数调用的common是当前模块的common
      const graph = builder.getGraph();
      const testNodeId = `module1.c::${ASTNodeKind.FUNCTION}::test`;
      const localCommonNodeId = `module1.c::${ASTNodeKind.FUNCTION}::common`;

      const outgoingEdges = graph.getOutgoingEdges(testNodeId);
      const callEdges = outgoingEdges.filter(edge => edge.type === 'calls');
      
      expect(callEdges).toHaveLength(1);
      expect(callEdges[0].to).toBe(localCommonNodeId); // 应该调用当前模块的common
    });
  });

  describe('基于main.asm的调试测试', () => {
    it('应该正确解析main.asm中的函数调用关系', () => {
      // 基于main.asm创建AST结构
      const mainAst: ASTNode = {
        name: 'main.asm',
        kind: ASTNodeKind.MODULE,
        originalType: 'module',
        language: 'np',
        children: [
          // 头文件包含
          {
            name: 'math.h',
            kind: ASTNodeKind.MODULE,
            originalType: 'include',
            language: 'np',
            children: []
          },
          {
            name: 'stdio.np',
            kind: ASTNodeKind.MODULE,
            originalType: 'include',
            language: 'np',
            children: []
          },
          // main函数
          {
        name: 'main',
            kind: ASTNodeKind.FUNCTION,
            originalType: 'function_definition',
            language: 'np',
            text: 'void main() { ... }',
            children: [
              // print(MSG)
              {
                name: 'print',
                kind: ASTNodeKind.FUNCTION_CALL,
                originalType: 'call_expression',
                language: 'np',
                children: []
              },
              // add(var1, var2)
              {
                name: 'add',
                kind: ASTNodeKind.FUNCTION_CALL,
                originalType: 'call_expression',
                language: 'np',
                children: []
              },
              // print(sum)
              {
                name: 'print',
                kind: ASTNodeKind.FUNCTION_CALL,
                originalType: 'call_expression',
                language: 'np',
                children: []
              },
              // print_point(p)
              {
                name: 'print_point',
                kind: ASTNodeKind.FUNCTION_CALL,
                originalType: 'call_expression',
                language: 'np',
                children: []
              },
              // SQUARE(va1,var2) - 注意这里有拼写错误va1
              {
                name: 'SQUARE',
                kind: ASTNodeKind.FUNCTION_CALL,
                originalType: 'call_expression',
                language: 'np',
                children: []
              },
              // print(sq)
              {
                name: 'print',
                kind: ASTNodeKind.FUNCTION_CALL,
                originalType: 'call_expression',
                language: 'np',
                children: []
              },
              // print("square > 40")
              {
                name: 'print',
                kind: ASTNodeKind.FUNCTION_CALL,
                originalType: 'call_expression',
                language: 'np',
                children: []
              },
              // print("square <= 40")
              {
                name: 'print',
                kind: ASTNodeKind.FUNCTION_CALL,
                originalType: 'call_expression',
                language: 'np',
                children: []
              }
            ]
          }
        ]
      };

      // 创建头文件AST
      const mathAst: ASTNode = {
        name: 'math.h',
        kind: ASTNodeKind.MODULE,
        originalType: 'module',
        language: 'np',
        children: [
          {
            name: 'add',
            kind: ASTNodeKind.FUNCTION,
            originalType: 'function_definition',
            language: 'np',
            text: 'int add(int a, int b) { return a + b; }',
            children: []
          },
          {
            name: 'SQUARE',
            kind: ASTNodeKind.FUNCTION,
            originalType: 'function_definition',
            language: 'np',
            text: 'int SQUARE(int a, int b) { return a * b; }',
            children: []
          }
        ]
      };

      const stdioAst: ASTNode = {
        name: 'stdio.np',
        kind: ASTNodeKind.MODULE,
        originalType: 'module',
        language: 'np',
        children: [
          {
            name: 'print',
            kind: ASTNodeKind.FUNCTION,
            originalType: 'function_definition',
            language: 'np',
            text: 'void print(const char* msg) { ... }',
            children: []
          },
          {
            name: 'print_point',
            kind: ASTNodeKind.FUNCTION,
            originalType: 'function_definition',
            language: 'np',
            text: 'void print_point(Point p) { ... }',
            children: []
          }
        ]
      };

      // 构建代码图
      builder.buildFromMultipleAsts([
        { ast: mainAst, filePath: 'main.asm' },
        { ast: mathAst, filePath: 'math.h' },
        { ast: stdioAst, filePath: 'stdio.np' }
      ]);

      // 调试信息
      console.log('=== main.asm 调试信息 ===');
      console.log('全局函数索引:', builder.getGlobalFunctionIndex());
      console.log('模块导入关系:', builder.getModuleImports());

      const graph = builder.getGraph();
      const mainNodeId = `main.asm::${ASTNodeKind.FUNCTION}::main`;
      
      console.log('main函数节点ID:', mainNodeId);
      console.log('main函数是否存在:', !!graph.node(mainNodeId));
      
      const outgoingEdges = graph.getOutgoingEdges(mainNodeId);
      console.log('main函数的出边:', outgoingEdges);
      
      const callEdges = outgoingEdges.filter(edge => edge.type === 'calls');
      console.log('main函数的调用边:', callEdges);
      
      // 验证关键函数调用
      const expectedCalls = ['print', 'add', 'print_point', 'SQUARE'];
      const actualCalls = callEdges.map(edge => {
        const node = graph.node(edge.to);
        return node ? node.name : 'unknown';
      });
      
      console.log('期望的调用函数:', expectedCalls);
      console.log('实际的调用函数:', actualCalls);
      
      // 验证print函数调用（应该有多个）
      const printCalls = callEdges.filter(edge => {
        const node = graph.node(edge.to);
        return node && node.name === 'print';
      });
      console.log('print函数调用次数:', printCalls.length);
      
      // 验证add函数调用
      const addCalls = callEdges.filter(edge => {
        const node = graph.node(edge.to);
        return node && node.name === 'add';
      });
      console.log('add函数调用次数:', addCalls.length);
      
      // 验证SQUARE函数调用
      const squareCalls = callEdges.filter(edge => {
        const node = graph.node(edge.to);
        return node && node.name === 'SQUARE';
      });
      console.log('SQUARE函数调用次数:', squareCalls.length);
      
      // 基本验证
      expect(graph.node(mainNodeId)).toBeDefined();
      expect(callEdges.length).toBeGreaterThan(0);
      expect(printCalls.length).toBeGreaterThan(0);
      expect(addCalls.length).toBe(1);
      expect(squareCalls.length).toBe(1);
    });

    it('应该能正确找到main函数的相关函数', () => {
      // 基于main.asm创建AST结构
      const mainAst: ASTNode = {
        name: 'main.asm',
        kind: ASTNodeKind.MODULE,
        originalType: 'module',
        language: 'np',
        children: [
          // 头文件包含
          {
            name: 'math.h',
            kind: ASTNodeKind.MODULE,
            originalType: 'include',
            language: 'np',
            children: []
          },
          {
            name: 'stdio.np',
            kind: ASTNodeKind.MODULE,
            originalType: 'include',
            language: 'np',
            children: []
          },
          // main函数
          {
            name: 'main',
            kind: ASTNodeKind.FUNCTION,
            originalType: 'function_definition',
            language: 'np',
            text: 'void main() { ... }',
            children: [
              // print(MSG)
              {
                name: 'print',
                kind: ASTNodeKind.FUNCTION_CALL,
                originalType: 'call_expression',
                language: 'np',
                children: []
              },
              // add(var1, var2)
              {
                name: 'add',
                kind: ASTNodeKind.FUNCTION_CALL,
                originalType: 'call_expression',
                language: 'np',
                children: []
              },
              // print(sum)
              {
                name: 'print',
                kind: ASTNodeKind.FUNCTION_CALL,
                originalType: 'call_expression',
                language: 'np',
                children: []
              },
              // print_point(p)
              {
                name: 'print_point',
                kind: ASTNodeKind.FUNCTION_CALL,
                originalType: 'call_expression',
                language: 'np',
                children: []
              },
              // SQUARE(va1,var2)
              {
                name: 'SQUARE',
                kind: ASTNodeKind.FUNCTION_CALL,
                originalType: 'call_expression',
                language: 'np',
                children: []
              },
              // print(sq)
              {
                name: 'print',
                kind: ASTNodeKind.FUNCTION_CALL,
                originalType: 'call_expression',
                language: 'np',
                children: []
              },
              // print("square > 40")
              {
                name: 'print',
                kind: ASTNodeKind.FUNCTION_CALL,
                originalType: 'call_expression',
                language: 'np',
                children: []
              },
              // print("square <= 40")
              {
                name: 'print',
                kind: ASTNodeKind.FUNCTION_CALL,
                originalType: 'call_expression',
                language: 'np',
                children: []
              }
            ]
          }
        ]
      };

      // 创建头文件AST
      const mathAst: ASTNode = {
        name: 'math.h',
        kind: ASTNodeKind.MODULE,
        originalType: 'module',
        language: 'np',
        children: [
          {
            name: 'add',
            kind: ASTNodeKind.FUNCTION,
            originalType: 'function_definition',
            language: 'np',
            text: 'int add(int a, int b) { return a + b; }',
            children: []
          },
          {
            name: 'SQUARE',
            kind: ASTNodeKind.FUNCTION,
            originalType: 'function_definition',
            language: 'np',
            text: 'int SQUARE(int a, int b) { return a * b; }',
            children: []
          }
        ]
      };

      const stdioAst: ASTNode = {
        name: 'stdio.np',
        kind: ASTNodeKind.MODULE,
        originalType: 'module',
        language: 'np',
        children: [
          {
            name: 'print',
            kind: ASTNodeKind.FUNCTION,
            originalType: 'function_definition',
            language: 'np',
            text: 'void print(const char* msg) { ... }',
            children: []
          },
          {
            name: 'print_point',
            kind: ASTNodeKind.FUNCTION,
            originalType: 'function_definition',
            language: 'np',
            text: 'void print_point(Point p) { ... }',
            children: []
          }
        ]
      };

      // 构建代码图
      builder.buildFromMultipleAsts([
        { ast: mainAst, filePath: 'main.asm' },
        { ast: mathAst, filePath: 'math.h' },
        { ast: stdioAst, filePath: 'stdio.np' }
      ]);

      const graph = builder.getGraph();
      const mainNodeId = `main.asm::${ASTNodeKind.FUNCTION}::main`;

      // 测试getRelatedNodes方法
      console.log('=== 测试getRelatedNodes方法 ===');
      
      // 获取深度为1的相关节点
      const relatedNodesDepth1 = graph.getRelatedNodes(mainNodeId, 1);
      console.log('深度1的相关节点数量:', relatedNodesDepth1.length);
      console.log('深度1的相关节点:', relatedNodesDepth1.map(node => node.name));
      
      // 获取深度为2的相关节点
      const relatedNodesDepth2 = graph.getRelatedNodes(mainNodeId, 2);
      console.log('深度2的相关节点数量:', relatedNodesDepth2.length);
      console.log('深度2的相关节点:', relatedNodesDepth2.map(node => node.name));
      
      // 获取main.asm模块内的相关节点
      const moduleRelatedNodes = graph.getModuleRelatedNodes(mainNodeId, 'main.asm');
      console.log('main.asm模块内相关节点数量:', moduleRelatedNodes.length);
      console.log('main.asm模块内相关节点:', moduleRelatedNodes.map(node => node.name));
      
      // 验证相关节点包含预期的函数
      const relatedFunctionNames = relatedNodesDepth1.map(node => node.name);
      expect(relatedFunctionNames).toContain('print');
      expect(relatedFunctionNames).toContain('add');
      expect(relatedFunctionNames).toContain('print_point');
      expect(relatedFunctionNames).toContain('SQUARE');
      
      // 验证深度1应该包含main函数本身和直接调用的函数
      expect(relatedNodesDepth1.length).toBeGreaterThanOrEqual(5); // main + 4个不同的被调用函数
      
      // 验证模块内相关节点应该只包含main函数本身
      expect(moduleRelatedNodes.length).toBe(1);
      expect(moduleRelatedNodes[0].name).toBe('main');
    });
  });

  describe('RepoIndexer 相关函数查找测试', () => {
    it('应该能通过RepoIndexer找到main函数的相关函数', () => {
      // 导入RepoIndexer
      const { RepoIndexer } = require('src/components/code_graph/repoIndexer/repoIndexer');
      
      // 创建临时工作区
      const tempWorkspacePath = '/tmp/test_workspace';
      
      // 创建RepoIndexer实例
      const repoIndexer = new RepoIndexer(tempWorkspacePath, ['*']);
      
      // 基于main.asm创建AST结构
      const mainAst: ASTNode = {
        name: 'main.asm',
        kind: ASTNodeKind.MODULE,
        originalType: 'module',
        language: 'np',
        children: [
          // 头文件包含
          {
            name: 'math.h',
            kind: ASTNodeKind.MODULE,
            originalType: 'include',
            language: 'np',
            children: []
          },
          {
            name: 'stdio.np',
            kind: ASTNodeKind.MODULE,
            originalType: 'include',
            language: 'np',
            children: []
          },
          // main函数
          {
            name: 'main',
            kind: ASTNodeKind.FUNCTION,
        originalType: 'function_definition',
            language: 'np',
            text: 'void main() { ... }',
            children: [
              // print(MSG)
              {
                name: 'print',
                kind: ASTNodeKind.FUNCTION_CALL,
                originalType: 'call_expression',
                language: 'np',
                children: []
              },
              // add(var1, var2)
              {
                name: 'add',
                kind: ASTNodeKind.FUNCTION_CALL,
                originalType: 'call_expression',
                language: 'np',
                children: []
              },
              // print(sum)
              {
                name: 'print',
                kind: ASTNodeKind.FUNCTION_CALL,
                originalType: 'call_expression',
                language: 'np',
                children: []
              },
              // print_point(p)
              {
                name: 'print_point',
                kind: ASTNodeKind.FUNCTION_CALL,
                originalType: 'call_expression',
                language: 'np',
                children: []
              },
              // SQUARE(va1,var2)
              {
                name: 'SQUARE',
                kind: ASTNodeKind.FUNCTION_CALL,
                originalType: 'call_expression',
                language: 'np',
                children: []
              },
              // print(sq)
              {
                name: 'print',
                kind: ASTNodeKind.FUNCTION_CALL,
                originalType: 'call_expression',
                language: 'np',
                children: []
              },
              // print("square > 40")
              {
                name: 'print',
                kind: ASTNodeKind.FUNCTION_CALL,
                originalType: 'call_expression',
                language: 'np',
                children: []
              },
              // print("square <= 40")
              {
                name: 'print',
                kind: ASTNodeKind.FUNCTION_CALL,
                originalType: 'call_expression',
                language: 'np',
                children: []
              }
            ]
          }
        ]
      };

      // 创建头文件AST
      const mathAst: ASTNode = {
        name: 'math.h',
        kind: ASTNodeKind.MODULE,
        originalType: 'module',
        language: 'np',
        children: [
          {
            name: 'add',
            kind: ASTNodeKind.FUNCTION,
            originalType: 'function_definition',
            language: 'np',
            text: 'int add(int a, int b) { return a + b; }',
            children: []
          },
          {
            name: 'SQUARE',
            kind: ASTNodeKind.FUNCTION,
            originalType: 'function_definition',
            language: 'np',
            text: 'int SQUARE(int a, int b) { return a * b; }',
            children: []
          }
        ]
      };

      const stdioAst: ASTNode = {
        name: 'stdio.np',
        kind: ASTNodeKind.MODULE,
        originalType: 'module',
        language: 'np',
        children: [
          {
            name: 'print',
            kind: ASTNodeKind.FUNCTION,
            originalType: 'function_definition',
            language: 'np',
            text: 'void print(const char* msg) { ... }',
            children: []
          },
          {
            name: 'print_point',
            kind: ASTNodeKind.FUNCTION,
            originalType: 'function_definition',
            language: 'np',
            text: 'void print_point(Point p) { ... }',
            children: []
          }
        ]
      };

      // 手动构建图谱（模拟RepoIndexer的行为）
      const graphBuilder = new CodeGraphBuilder();
      graphBuilder.buildFromMultipleAsts([
        { ast: mainAst, filePath: 'main.asm' },
        { ast: mathAst, filePath: 'math.h' },
        { ast: stdioAst, filePath: 'stdio.np' }
      ]);

      // 获取构建的图谱
      const graph = graphBuilder.getGraph();
      
      // 手动设置RepoIndexer的图谱
      (repoIndexer as any).graph = graph;

      console.log('=== RepoIndexer 调试信息 ===');
      
      // 测试findNodeByName
      const mainNode = repoIndexer.findNodeByName('main', 'function');
      console.log('findNodeByName("main"):', mainNode);
      
      // 测试findRelatedFunctionsByName
      const relatedFunctions = repoIndexer.findRelatedFunctionsByName('main', 1, 'function');
      console.log('findRelatedFunctionsByName("main", 1):', relatedFunctions);
      console.log('相关函数数量:', relatedFunctions.length);
             console.log('相关函数名称:', relatedFunctions.map((f: any) => f.name));
      
      // 测试直接通过nodeId查找
      if (mainNode) {
        const relatedByNodeId = (repoIndexer as any).findRelatedFunctions(mainNode.id, 1);
        console.log('findRelatedFunctions by nodeId:', relatedByNodeId);
        console.log('通过nodeId找到的相关函数数量:', relatedByNodeId.length);
                 console.log('通过nodeId找到的相关函数名称:', relatedByNodeId.map((f: any) => f.name));
        
        // 测试getRelatedNodes
        const allRelatedNodes = graph.getRelatedNodes(mainNode.id, 1);
        console.log('graph.getRelatedNodes 结果:', allRelatedNodes.map(n => n.name));
        console.log('graph.getRelatedNodes 数量:', allRelatedNodes.length);
        
        // 测试getOutgoingEdges
        const outgoingEdges = graph.getOutgoingEdges(mainNode.id);
        console.log('main函数的出边:', outgoingEdges);
        
        const callEdges = outgoingEdges.filter(edge => edge.type === 'calls');
        console.log('main函数的调用边:', callEdges);
      }
      
      // 验证结果
      expect(mainNode).toBeDefined();
      expect(relatedFunctions.length).toBeGreaterThan(0);
      
      // 验证相关函数包含预期的函数
             const relatedFunctionNames = relatedFunctions.map((f: any) => f.name);
      expect(relatedFunctionNames).toContain('print');
      expect(relatedFunctionNames).toContain('add');
      expect(relatedFunctionNames).toContain('print_point');
      expect(relatedFunctionNames).toContain('SQUARE');
    });
  });

  describe('RepoIndexer 实际文件路径测试', () => {
    it('应该能通过实际文件路径找到main函数的相关函数', () => {
      // 导入RepoIndexer
      const { RepoIndexer } = require('src/components/code_graph/repoIndexer/repoIndexer');
      
      // 使用实际的test_projects/np目录
      const actualWorkspacePath = path.resolve(__dirname, '../../../test_projects/np');
      
      console.log('实际工作区路径:', actualWorkspacePath);
      
      // 创建RepoIndexer实例
      const repoIndexer = new RepoIndexer(actualWorkspacePath, ['*']);
      
      // 手动构建图谱（模拟实际的文件解析过程）
      const mainAst: ASTNode = {
        name: 'main.asm',
        kind: ASTNodeKind.MODULE,
        originalType: 'module',
        language: 'np',
        children: [
          // 头文件包含
          {
            name: 'math.h',
            kind: ASTNodeKind.MODULE,
            originalType: 'include',
            language: 'np',
            children: []
          },
          {
            name: 'stdio.np',
            kind: ASTNodeKind.MODULE,
            originalType: 'include',
            language: 'np',
            children: []
          },
          // main函数
          {
        name: 'main',
            kind: ASTNodeKind.FUNCTION,
        originalType: 'function_definition',
            language: 'np',
            text: 'void main() { ... }',
            children: [
              // print(MSG)
              {
                name: 'print',
                kind: ASTNodeKind.FUNCTION_CALL,
                originalType: 'call_expression',
                language: 'np',
                children: []
              },
              // add(var1, var2)
              {
                name: 'add',
                kind: ASTNodeKind.FUNCTION_CALL,
                originalType: 'call_expression',
                language: 'np',
                children: []
              },
              // print(sum)
              {
                name: 'print',
                kind: ASTNodeKind.FUNCTION_CALL,
                originalType: 'call_expression',
                language: 'np',
                children: []
              },
              // print_point(p)
              {
                name: 'print_point',
                kind: ASTNodeKind.FUNCTION_CALL,
                originalType: 'call_expression',
                language: 'np',
                children: []
              },
              // SQUARE(va1,var2)
              {
                name: 'SQUARE',
                kind: ASTNodeKind.FUNCTION_CALL,
                originalType: 'call_expression',
                language: 'np',
                children: []
              },
              // print(sq)
              {
                name: 'print',
                kind: ASTNodeKind.FUNCTION_CALL,
                originalType: 'call_expression',
                language: 'np',
                children: []
              },
              // print("square > 40")
              {
                name: 'print',
                kind: ASTNodeKind.FUNCTION_CALL,
                originalType: 'call_expression',
                language: 'np',
                children: []
              },
              // print("square <= 40")
              {
                name: 'print',
                kind: ASTNodeKind.FUNCTION_CALL,
                originalType: 'call_expression',
                language: 'np',
                children: []
              }
            ]
          }
        ]
      };

      // 创建头文件AST
      const mathAst: ASTNode = {
        name: 'math.h',
        kind: ASTNodeKind.MODULE,
        originalType: 'module',
        language: 'np',
        children: [
          {
            name: 'add',
            kind: ASTNodeKind.FUNCTION,
            originalType: 'function_definition',
            language: 'np',
            text: 'int add(int a, int b) { return a + b; }',
            children: []
          },
          {
            name: 'SQUARE',
            kind: ASTNodeKind.FUNCTION,
            originalType: 'function_definition',
            language: 'np',
            text: 'int SQUARE(int a, int b) { return a * b; }',
            children: []
          }
        ]
      };

      const stdioAst: ASTNode = {
        name: 'stdio.np',
        kind: ASTNodeKind.MODULE,
        originalType: 'module',
        language: 'np',
        children: [
          {
            name: 'print',
            kind: ASTNodeKind.FUNCTION,
            originalType: 'function_definition',
            language: 'np',
            text: 'void print(const char* msg) { ... }',
            children: []
          },
          {
            name: 'print_point',
            kind: ASTNodeKind.FUNCTION,
            originalType: 'function_definition',
            language: 'np',
            text: 'void print_point(Point p) { ... }',
          children: []
          }
        ]
      };

      // 手动构建图谱（模拟RepoIndexer的行为）
      const graphBuilder = new CodeGraphBuilder();
      graphBuilder.buildFromMultipleAsts([
        { ast: mainAst, filePath: 'main.asm' },
        { ast: mathAst, filePath: 'math.h' },
        { ast: stdioAst, filePath: 'stdio.np' }
      ]);

      // 获取构建的图谱
      const graph = graphBuilder.getGraph();
      
      // 手动设置RepoIndexer的图谱
      (repoIndexer as any).graph = graph;

      console.log('=== 实际文件路径测试 ===');
      
      // 测试findNodeByName
      const mainNode = repoIndexer.findNodeByName('main', 'function');
      console.log('findNodeByName("main"):', mainNode);
      
      // 测试findRelatedFunctionsByName
      const relatedFunctions = repoIndexer.findRelatedFunctionsByName('main', 1, 'function');
      console.log('findRelatedFunctionsByName("main", 1):', relatedFunctions);
      console.log('相关函数数量:', relatedFunctions.length);
      console.log('相关函数名称:', relatedFunctions.map((f: any) => f.name));
      
      // 测试通过文件路径查找
      const mainNodeInFile = repoIndexer.findNodeInFile('main.asm', 'main', 'function');
      console.log('findNodeInFile("main.asm", "main"):', mainNodeInFile);
      
      // 调试文件路径问题
      console.log('=== 文件路径调试 ===');
      console.log('workspacePath:', (repoIndexer as any).workspacePath);
      console.log('filePath:', 'main.asm');
      const relativePath = path.relative((repoIndexer as any).workspacePath, 'main.asm');
      console.log('relativePath:', relativePath);
      
             // 查看图谱中所有节点的文件路径
       console.log('图谱中所有节点的文件路径:');
       for (const [nodeId, node] of Array.from((repoIndexer as any).graph.nodes as Map<string, any>)) {
         console.log(`  ${nodeId}: file=${node.file}, name=${node.name}`);
       }
      
      // 尝试使用绝对路径
      const absolutePath = path.resolve((repoIndexer as any).workspacePath, 'main.asm');
      console.log('absolutePath:', absolutePath);
      const mainNodeInFileAbsolute = repoIndexer.findNodeInFile(absolutePath, 'main', 'function');
      console.log('findNodeInFile(absolutePath, "main"):', mainNodeInFileAbsolute);
      
      if (mainNodeInFile) {
        const moduleRelatedNodes = repoIndexer.getModuleRelatedNodesByName('main', 'main.asm', 'function');
        console.log('getModuleRelatedNodesByName("main", "main.asm"):', moduleRelatedNodes);
        console.log('模块内相关节点数量:', moduleRelatedNodes.length);
        console.log('模块内相关节点名称:', moduleRelatedNodes.map((f: any) => f.name));
      }
      
      // 验证结果
      expect(mainNode).toBeDefined();
      expect(relatedFunctions.length).toBeGreaterThan(0);
      
      // 验证相关函数包含预期的函数
      const relatedFunctionNames = relatedFunctions.map((f: any) => f.name);
      expect(relatedFunctionNames).toContain('print');
      expect(relatedFunctionNames).toContain('add');
      expect(relatedFunctionNames).toContain('print_point');
      expect(relatedFunctionNames).toContain('SQUARE');
    });
  });

  describe('重新初始化索引问题分析', () => {
    it('应该分析重新初始化索引后内容为空的问题', () => {
      console.log('=== 重新初始化索引问题分析 ===');
      
      // 分析问题可能的原因
      console.log('1. 问题描述: 重新初始化索引后，查看索引提示内容为空');
      console.log('2. 可能的原因分析:');
      
      console.log('   a) RepoIndexer.analyze() 方法可能没有正确解析文件');
      console.log('   b) 文件解析器可能没有找到支持的文件');
      console.log('   c) AST解析可能失败');
      console.log('   d) 图谱构建可能有问题');
      console.log('   e) 数据保存和恢复可能有问题');
      
      console.log('3. 关键代码路径:');
      console.log('   - RepoIndexerManager.resetIndex()');
      console.log('   - RepoIndexerManager.initializeAndAnalyze()');
      console.log('   - RepoIndexer.analyze()');
      console.log('   - RepoIndexer.fullAnalyze()');
      console.log('   - RepoIndexer.parseFilesByLanguage()');
      console.log('   - CodeGraphBuilder.buildFromAst()');
      
      console.log('4. 建议的调试步骤:');
      console.log('   a) 检查工作区是否有支持的文件类型');
      console.log('   b) 检查文件解析器是否正确初始化');
      console.log('   c) 检查AST解析是否成功');
      console.log('   d) 检查图谱构建过程');
      console.log('   e) 检查数据保存和恢复');
      
      console.log('5. 关键检查点:');
      console.log('   - 文件扫描: getAllFiles() 是否找到文件');
      console.log('   - 语言识别: getLanguageFromFile() 是否正确');
      console.log('   - 解析器: ParserManager.getParser() 是否返回解析器');
      console.log('   - AST解析: parser.parse() 是否成功');
      console.log('   - 图谱构建: CodeGraphBuilder.buildFromAst() 是否成功');
      console.log('   - 数据保存: saveCurrentIndexData() 是否成功');
      
      // 验证分析逻辑
      expect(true).toBe(true);
    });
  });

  describe('文件过滤逻辑修复测试', () => {
    it('应该能正确处理languageExts为["*"]的情况', () => {
      console.log('=== 文件过滤逻辑修复测试 ===');
      
      // 模拟文件扩展名
      const testExtensions = ['.c', '.h', '.py', '.go', '.asm', '.np', '.txt', '.md'];
      
      // 测试 languageExts = ['*'] 的情况
      const languageExtsWildcard = ['*'];
      console.log('1. 测试 languageExts = ["*"]:');
      
      for (const ext of testExtensions) {
        const shouldInclude = languageExtsWildcard.includes('*') || languageExtsWildcard.includes(ext);
        console.log(`   ${ext}: ${shouldInclude ? '包含' : '排除'}`);
      }
      
      // 测试 languageExts = ['.c', '.py'] 的情况
      const languageExtsSpecific = ['.c', '.py'];
      console.log('2. 测试 languageExts = [".c", ".py"]:');
      
      for (const ext of testExtensions) {
        const shouldInclude = languageExtsSpecific.includes('*') || languageExtsSpecific.includes(ext);
        console.log(`   ${ext}: ${shouldInclude ? '包含' : '排除'}`);
      }
      
      // 验证修复逻辑
      expect(languageExtsWildcard.includes('*') || languageExtsWildcard.includes('.c')).toBe(true);
      expect(languageExtsWildcard.includes('*') || languageExtsWildcard.includes('.py')).toBe(true);
      expect(languageExtsWildcard.includes('*') || languageExtsWildcard.includes('.txt')).toBe(true);
      
      expect(languageExtsSpecific.includes('*') || languageExtsSpecific.includes('.c')).toBe(true);
      expect(languageExtsSpecific.includes('*') || languageExtsSpecific.includes('.py')).toBe(true);
      expect(languageExtsSpecific.includes('*') || languageExtsSpecific.includes('.txt')).toBe(false);
    });
  });

  describe('完整索引流程测试', () => {
    it('应该能正确执行完整的索引流程', async () => {
      console.log('=== 完整索引流程测试 ===');
      
      // 导入RepoIndexer
      const { RepoIndexer } = require('src/components/code_graph/repoIndexer/repoIndexer');
      
      // 创建临时工作区路径
      const tempWorkspacePath = '/tmp/test_workspace_complete';
      
      console.log('1. 创建RepoIndexer实例...');
      const repoIndexer = new RepoIndexer(tempWorkspacePath, ['*']);
      console.log('RepoIndexer创建成功，workspacePath:', repoIndexer.workspacePath);
      console.log('RepoIndexer创建成功，languageExts:', (repoIndexer as any).languageExts);
      
      // 模拟文件系统
      const mockFiles = [
        '/tmp/test_workspace_complete/main.asm',
        '/tmp/test_workspace_complete/math.h',
        '/tmp/test_workspace_complete/stdio.np',
        '/tmp/test_workspace_complete/test.txt', // 不支持的文件类型
        '/tmp/test_workspace_complete/README.md' // 不支持的文件类型
      ];
      
      // Mock fs.promises.readdir
      const originalReaddir = require('fs').promises.readdir;
      require('fs').promises.readdir = jest.fn().mockImplementation((dir) => {
        if (dir === tempWorkspacePath) {
          return Promise.resolve([
            { name: 'main.asm', isDirectory: () => false, isFile: () => true },
            { name: 'math.h', isDirectory: () => false, isFile: () => true },
            { name: 'stdio.np', isDirectory: () => false, isFile: () => true },
            { name: 'test.txt', isDirectory: () => false, isFile: () => true },
            { name: 'README.md', isDirectory: () => false, isFile: () => true },
            { name: 'node_modules', isDirectory: () => true, isFile: () => false },
            { name: '.git', isDirectory: () => true, isFile: () => false }
          ]);
        }
        return Promise.resolve([]);
      });
      
      // Mock fs.promises.readFile
      const originalReadFile = require('fs').promises.readFile;
      require('fs').promises.readFile = jest.fn().mockImplementation((filePath) => {
        if (filePath.includes('main.asm')) {
          return Promise.resolve('void main() { print("Hello"); }');
        } else if (filePath.includes('math.h')) {
          return Promise.resolve('int add(int a, int b) { return a + b; }');
        } else if (filePath.includes('stdio.np')) {
          return Promise.resolve('void print(const char* msg) { }');
        }
        return Promise.resolve('');
      });
      
      // Mock fs.promises.stat
      const originalStat = require('fs').promises.stat;
      require('fs').promises.stat = jest.fn().mockResolvedValue({ mtimeMs: Date.now() });
      
      console.log('2. 执行索引分析...');
      try {
        await repoIndexer.analyze(false); // 全量分析
        console.log('索引分析完成');
      } catch (error) {
        console.error('索引分析失败:', error);
      }
      
      console.log('3. 检查索引结果...');
      const graph = repoIndexer.getGraph();
      console.log('图谱节点数量:', graph.nodes.size);
      console.log('图谱边数量:', graph.edges.length);
      
      // 列出所有节点
      console.log('图谱节点详情:');
      for (const [nodeId, node] of Array.from(graph.nodes as Map<string, any>)) {
        console.log(`  ${nodeId}: ${node.name} (${node.type}) - ${node.file}`);
      }
      
      console.log('4. 导出索引数据...');
      const data = repoIndexer.exportData();
      console.log('导出数据:', {
        hasData: !!data,
        hasGraph: !!data?.graph,
        nodesSize: data?.graph?.nodes?.size || 0,
        edgesLength: data?.graph?.edges?.length || 0,
        workspacePath: data?.workspacePath || 'undefined'
      });
      
      // 恢复原始方法
      require('fs').promises.readdir = originalReaddir;
      require('fs').promises.readFile = originalReadFile;
      require('fs').promises.stat = originalStat;
      
      // 验证结果
      expect(graph.nodes.size).toBeGreaterThan(0);
      expect(data).toBeDefined();
      expect(data.graph).toBeDefined();
      expect(data.workspacePath).toBe(tempWorkspacePath);
      
      console.log('=== 完整索引流程测试完成 ===');
    });
  });

  describe('自动初始化索引逻辑测试', () => {
    it('应该按照正确的逻辑处理不同的配置组合', () => {
      console.log('=== 自动初始化索引逻辑测试 ===');
      
      // 测试不同的配置组合
      const testCases = [
        {
          name: 'enableIndexing=true, autoIndexing=true, 有持久化索引',
          config: { enableIndexing: true, autoIndexing: true },
          hasSavedData: true,
          expectedAction: '加载持久化索引'
        },
        {
          name: 'enableIndexing=true, autoIndexing=true, 无持久化索引',
          config: { enableIndexing: true, autoIndexing: true },
          hasSavedData: false,
          expectedAction: '执行全量索引构建'
        },
        {
          name: 'enableIndexing=true, autoIndexing=false, 有持久化索引',
          config: { enableIndexing: true, autoIndexing: false },
          hasSavedData: true,
          expectedAction: '加载持久化索引'
        },
        {
          name: 'enableIndexing=true, autoIndexing=false, 无持久化索引',
          config: { enableIndexing: true, autoIndexing: false },
          hasSavedData: false,
          expectedAction: '跳过自动索引构建（等待用户手动操作）'
        },
        {
          name: 'enableIndexing=false',
          config: { enableIndexing: false },
          hasSavedData: false,
          expectedAction: '索引功能已禁用'
        }
      ];
      
      console.log('测试用例分析:');
      for (const testCase of testCases) {
        console.log(`\n${testCase.name}:`);
        console.log(`  配置: ${JSON.stringify(testCase.config)}`);
        console.log(`  持久化索引: ${testCase.hasSavedData ? '存在' : '不存在'}`);
        console.log(`  预期行为: ${testCase.expectedAction}`);
        
        // 模拟逻辑判断
        let actualAction = '';
        if (!testCase.config.enableIndexing) {
          actualAction = '索引功能已禁用';
        } else if (testCase.hasSavedData) {
          actualAction = '加载持久化索引';
        } else if (testCase.config.autoIndexing) {
          actualAction = '执行全量索引构建';
        } else {
          actualAction = '跳过自动索引构建（等待用户手动操作）';
        }
        
        console.log(`  实际行为: ${actualAction}`);
        console.log(`  结果: ${actualAction === testCase.expectedAction ? '✅ 正确' : '❌ 错误'}`);
        
        // 验证逻辑
        expect(actualAction).toBe(testCase.expectedAction);
      }
      
      console.log('\n=== 逻辑验证完成 ===');
    });
  });

  describe('Bundle和宏函数测试', () => {
    it('应该正确收集bundle函数定义', async () => {
      const content = `
void add(a, b) {
    result = a + b;
    return result;
}
      `;

      const ast = npParser.parse(content);
      builder.buildFromAst(ast, 'math.asm');
      
      const graph = builder.getGraph();
      const globalIndex = builder.getGlobalFunctionIndex();
      
      // 检查全局函数索引
      expect(globalIndex.has('add')).toBe(true);
      const addEntries = globalIndex.get('add');
      expect(addEntries).toHaveLength(1);
      expect(addEntries![0].modulePath).toBe('math.asm');
      
      // 检查图谱节点
      const addNode = graph.node(addEntries![0].nodeId);
      expect(addNode).toBeDefined();
      expect(addNode?.name).toBe('add');
      expect(addNode?.type).toBe('function');
      expect(addNode?.file).toBe('math.asm');
      expect(addNode?.metadata?.isBundle).toBeUndefined(); // 普通函数，不是 bundle
    });

    it('应该正确收集宏定义', async () => {
      const content = `
#define SQUARE(x) ((x)*(x))
      `;

      const ast = npParser.parse(content);
      builder.buildFromAst(ast, 'math.h');
      
      const graph = builder.getGraph();
      const globalIndex = builder.getGlobalFunctionIndex();
      
      // 检查全局函数索引
      expect(globalIndex.has('SQUARE')).toBe(true);
      const squareEntries = globalIndex.get('SQUARE');
      expect(squareEntries).toHaveLength(1);
      expect(squareEntries![0].modulePath).toBe('math.h');
      
      // 检查图谱节点
      const squareNode = graph.node(squareEntries![0].nodeId);
      expect(squareNode).toBeDefined();
      expect(squareNode?.name).toBe('SQUARE');
      expect(squareNode?.type).toBe('function');
      expect(squareNode?.file).toBe('math.h');
      expect(squareNode?.metadata?.type).toBe('macro');
    });

    it('应该正确构建bundle函数调用关系', async () => {
      // 构建bundle定义
      const bundleContent = `
bundle add(a, b): {
    result = a + b;
    return result;
}
      `;
      const bundleAst = npParser.parse(bundleContent);
      
      // 构建调用bundle的代码
      const callContent = `
void main() {
    sum = add(var1, var2);
}
      `;
      const callAst = npParser.parse(callContent);
      
      // 调试信息：检查 AST 中是否包含模块导入节点
      console.log('Call AST children:', callAst.children.map(child => ({
        name: child.name,
        kind: child.kind,
        originalType: child.originalType,
        metadata: child.metadata
      })));
      
      // 使用buildFromMultipleAsts同时构建两个文件的AST
      builder.buildFromMultipleAsts([
        { ast: bundleAst, filePath: 'math.asm' },
        { ast: callAst, filePath: 'main.asm' }
      ]);
      
      const graph = builder.getGraph();
      const globalIndex = builder.getGlobalFunctionIndex();
      const moduleImports = builder.getModuleImports();
      
      // 调试信息
      console.log('模块导入关系:', moduleImports);
      console.log('全局函数索引中的 add:', globalIndex.get('add'));
      
      // 检查函数定义
      expect(globalIndex.has('add')).toBe(true);
      expect(globalIndex.has('main')).toBe(true);
      
      // 查找main函数节点
      const mainEntries = globalIndex.get('main');
      const mainNodeId = mainEntries![0].nodeId;
      
      // 查找add函数节点
      const addEntries = globalIndex.get('add');
      const addNodeId = addEntries![0].nodeId;
      
      // 调试信息
      console.log('Main node ID:', mainNodeId);
      console.log('Add node ID:', addNodeId);
      console.log('Main outgoing edges:', graph.getOutgoingEdges(mainNodeId));
      
      // 检查调用边
      const outgoingEdges = graph.getOutgoingEdges(mainNodeId);
      const addCallEdge = outgoingEdges.find(edge => edge.to === addNodeId);
      expect(addCallEdge).toBeDefined();
      expect(addCallEdge?.type).toBe('calls');
    });

    it('应该正确构建宏调用关系', async () => {
      // 构建宏定义
      const macroContent = `
#define SQUARE(x) ((x)*(x))
      `;
      const macroAst = npParser.parse(macroContent);
      
      // 构建调用宏的代码，包含头文件包含
      const callContent = `
#include "math.h"

void main() {
    var1 = 5;
    var2 = 7;
    SQUARE(var1);
}
      `;
      const callAst = npParser.parse(callContent);
      
      // 调试信息：检查 AST 中是否包含模块导入节点
      console.log('Call AST children:', callAst.children.map(child => ({
        name: child.name,
        kind: child.kind,
        originalType: child.originalType,
        metadata: child.metadata
      })));
      
      // 使用buildFromMultipleAsts同时构建两个文件的AST
      builder.buildFromMultipleAsts([
        { ast: macroAst, filePath: 'math.h' },
        { ast: callAst, filePath: 'main.asm' }
      ]);
      
      const graph = builder.getGraph();
      const globalIndex = builder.getGlobalFunctionIndex();
      const moduleImports = builder.getModuleImports();
      
      // 调试信息
      console.log('模块导入关系:', moduleImports);
      console.log('全局函数索引中的 SQUARE:', globalIndex.get('SQUARE'));
      
      // 检查函数定义
      expect(globalIndex.has('SQUARE')).toBe(true);
      expect(globalIndex.has('main')).toBe(true);
      
      // 验证模块导入关系正确建立
      expect(moduleImports.has('main.asm')).toBe(true);
      const mainImports = moduleImports.get('main.asm');
      expect(mainImports).toBeDefined();
      expect(mainImports!.has('math.h')).toBe(true);
      
      // 验证宏定义在 math.h 中正确识别
      const squareEntries = globalIndex.get('SQUARE');
      expect(squareEntries).toHaveLength(1); // 只有一个定义节点在 math.h
      
      const mathHSquare = squareEntries!.find(entry => entry.modulePath === 'math.h');
      expect(mathHSquare).toBeDefined();
      expect(mathHSquare?.nodeId).toBe('math.h::function::SQUARE'); // 宏统一为function类型
      
      // 查找main函数节点
      const mainEntries = globalIndex.get('main');
      const mainNodeId = mainEntries![0].nodeId;
      
      // 验证调用关系 - main.asm 调用 math.h 中的 SQUARE 宏
      const outgoingEdges = graph.getOutgoingEdges(mainNodeId);
      expect(outgoingEdges.length).toBeGreaterThan(0);
      
      // 验证存在从 main 到 SQUARE 的调用边
      const squareCallEdge = outgoingEdges.find(edge => edge.to === mathHSquare?.nodeId);
      expect(squareCallEdge).toBeDefined();
      expect(squareCallEdge?.type).toBe('calls');
      
      // 验证跨模块查找功能 - 如果 main.asm 中没有 SQUARE 定义，
      // findFunctionNode 应该能找到 math.h 中的定义
      console.log('验证跨模块查找功能...');
    });
  });
}); 