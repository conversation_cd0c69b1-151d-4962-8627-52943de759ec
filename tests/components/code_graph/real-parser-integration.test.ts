import * as fs from 'fs';
import * as path from 'path';
import { Language, Parser } from 'web-tree-sitter';
import { CodeGraphBuilder } from '../../../src/components/code_graph/builder/CodeGraphBuilder';
import { ASTNode, ASTNodeKind } from '../../../src/components/code_graph/types/ast';

describe('真实 Tree-sitter 解析器集成测试', () => {
  let cParser: Parser;
  let npParser: Parser | null;

  beforeAll(async () => {
    // 初始化 C 语言解析器
    await Parser.init();
    cParser = new Parser();
    const cWasmPath = path.resolve(__dirname, '../../../node_modules/tree-sitter-c/tree-sitter-c.wasm');
    const cWasmBuffer = fs.readFileSync(cWasmPath);
    const C = await Language.load(cWasmBuffer);
    cParser.setLanguage(C);

    // 初始化 NP 语言解析器（如果可用）
    try {
      npParser = new Parser();
      const npWasmPath = path.resolve(__dirname, '../../../node_modules/tree-sitter-np/tree-sitter-np.wasm');
      const npWasmBuffer = fs.readFileSync(npWasmPath);
      const NP = await Language.load(npWasmBuffer);
      npParser.setLanguage(NP);
    } catch (error) {
      console.warn('NP 语言解析器不可用，跳过 NP 相关测试');
      npParser = null;
    }
  });

  describe('C 语言解析测试', () => {
    it('应该正确解析 C 语言函数定义', () => {
      const sourceCode = `#include <stdio.h>

int add(int a, int b) {
    return a + b;
}

int main() {
    int result = add(5, 10);
    printf("Result: %d\\n", result);
    return 0;
}`;

      const tree = cParser.parse(sourceCode);
      expect(tree).toBeDefined();
      if (tree) {
        const rootNode = tree.rootNode;
        expect(rootNode.type).toBe('translation_unit');

        // 查找函数定义
        const functionDefs = findNodesByType(rootNode, 'function_definition');
        expect(functionDefs.length).toBe(2); // add 和 main

        // 查找函数调用
        const functionCalls = findNodesByType(rootNode, 'call_expression');
        expect(functionCalls.length).toBe(2); // add(5, 10) 和 printf

        console.log('C 语言解析结果:');
        console.log('- 函数定义:', functionDefs.length, '个');
        console.log('- 函数调用:', functionCalls.length, '个');
      }
    });

    it('应该正确解析 C 语言宏定义', () => {
      const sourceCode = `#define MAX(a, b) ((a) > (b) ? (a) : (b))
#define PI 3.14159

int main() {
    int max_val = MAX(10, 20);
    double area = PI * 5 * 5;
    return 0;
}`;

      const tree = cParser.parse(sourceCode);
      expect(tree).toBeDefined();
      if (tree) {
        const rootNode = tree.rootNode;

        // 查找宏定义
        const macroDefs = findNodesByType(rootNode, 'preproc_function_def');
        const macroConsts = findNodesByType(rootNode, 'preproc_def');

        expect(macroDefs.length + macroConsts.length).toBeGreaterThan(0);

        console.log('C 语言宏解析结果:');
        console.log('- 函数宏:', macroDefs.length, '个');
        console.log('- 常量宏:', macroConsts.length, '个');
      }
    });
  });

  describe('NP 语言解析测试', () => {
    it('应该正确解析 NP 语言代码（如果解析器可用）', () => {
      if (!npParser) {
        console.log('跳过 NP 语言测试，解析器不可用');
        return;
      }

      const sourceCode = `#include "math.h"

#define MSG "Hello NP"

void main() {
    print(MSG);
    var1 = 5;
    sum = add(var1, 10);
    sq = SQUARE(var1);
}`;

      const tree = npParser.parse(sourceCode);
      expect(tree).toBeDefined();
      if (tree) {
        const rootNode = tree.rootNode;
        console.log('NP 语言解析结果:');
        console.log('- 根节点类型:', rootNode.type);
        console.log('- 子节点数量:', rootNode.namedChildren.length);
      }
    });
  });

  describe('CodeGraph 构建测试', () => {
    it('应该从真实解析结果构建代码图', () => {
      const sourceCode = `#include <stdio.h>

int add(int a, int b) {
    return a + b;
}

int multiply(int x, int y) {
    return x * y;
}

int main() {
    int sum = add(5, 10);
    int product = multiply(3, 4);
    printf("Sum: %d, Product: %d\\n", sum, product);
    return 0;
}`;

      const tree = cParser.parse(sourceCode);
      expect(tree).toBeDefined();
      if (tree) {
        const rootNode = tree.rootNode;
        
        // 模拟我们的解析器处理逻辑
        const builder = new CodeGraphBuilder();
        
        // 创建模拟的 AST 节点
        const mockAst: ASTNode = {
          kind: ASTNodeKind.PROGRAM,
          originalType: 'translation_unit',
          text: sourceCode,
          children: [],
          language: 'c',
          location: {
            start: { line: 0, column: 0 },
            end: { line: sourceCode.split('\n').length - 1, column: 0 }
          }
        };

        // 查找所有函数定义并添加到 AST
        const functionDefs = findNodesByType(rootNode, 'function_definition');
        functionDefs.forEach(funcDef => {
          const funcName = findFunctionName(funcDef);
          if (funcName) {
            mockAst.children.push({
              kind: ASTNodeKind.FUNCTION_DEFINITION,
              originalType: 'function_definition',
              name: funcName,
              text: funcDef.text,
              children: [],
              language: 'c',
              location: {
                start: { line: funcDef.startPosition.row, column: funcDef.startPosition.column },
                end: { line: funcDef.endPosition.row, column: funcDef.endPosition.column }
              }
            });
          }
        });

        // 查找所有函数调用并添加到 AST
        const functionCalls = findNodesByType(rootNode, 'call_expression');
        functionCalls.forEach(callExpr => {
          const funcName = findFunctionName(callExpr);
          if (funcName) {
            mockAst.children.push({
              kind: ASTNodeKind.FUNCTION,
              originalType: 'call_expression',
              name: funcName,
              text: callExpr.text,
              children: [],
              language: 'c',
              location: {
                start: { line: callExpr.startPosition.row, column: callExpr.startPosition.column },
                end: { line: callExpr.endPosition.row, column: callExpr.endPosition.column }
              }
            });
          }
        });

        // 使用真实的构建方法
        builder.buildFromAst(mockAst, 'test.c');
        const graph = builder.getGraph();
        
        console.log('代码图构建结果:');
        console.log('- 节点数量:', graph.nodes.size);
        console.log('- 边数量:', graph.edges.length);
        
        expect(graph.nodes.size).toBeGreaterThan(0);
      }
    });
  });
});

// 辅助方法：递归查找指定类型的节点
function findNodesByType(node: any, type: string): any[] {
  const results: any[] = [];
  
  if (node.type === type) {
    results.push(node);
  }
  
  for (const child of node.namedChildren) {
    if (child) {
      results.push(...findNodesByType(child, type));
    }
  }
  
  return results;
}

// 辅助方法：从函数定义或调用中提取函数名
function findFunctionName(node: any): string | null {
  if (node.type === 'function_definition') {
    // 函数定义：查找 identifier
    const identifier = node.namedChildren.find((child: any) => child.type === 'identifier');
    return identifier ? identifier.text : null;
  } else if (node.type === 'call_expression') {
    // 函数调用：查找 function
    const functionNode = node.namedChildren.find((child: any) => child.type === 'identifier');
    return functionNode ? functionNode.text : null;
  }
  return null;
} 