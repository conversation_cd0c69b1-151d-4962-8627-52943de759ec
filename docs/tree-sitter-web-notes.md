# Tree-sitter WebAssembly 使用方法总结

Tree-sitter 的 WebAssembly 版本 (`web-tree-sitter`) 适用于浏览器、Electron WebView、Node.js 无法使用 native 插件的场景。相比原生版本，它更具可移植性，支持跨平台运行。

---

## 一、官方说明文档

参考地址：
[https://github.com/tree-sitter/tree-sitter/blob/master/lib/binding_web/README.md](https://github.com/tree-sitter/tree-sitter/blob/master/lib/binding_web/README.md)

---

## 二、安装依赖

```bash
npm install web-tree-sitter
```

---

## 三、获取语言的 `.wasm` 文件

你需要获取各语言的 `.wasm` 文件（例如 `tree-sitter-c.wasm`），以下是四种常见方式：

### 方法一：从 npm 安装语言包并构建 `.wasm`

```bash
npm install tree-sitter-c --save-dev
npx tree-sitter build --wasm node_modules/tree-sitter-c
```

构建成功后会生成 `.wasm` 文件，位于当前目录。

注意事项：

* 在 macOS 上构建时会自动拉取 emscripten 镜像（如 `emscripten/emsdk`）
* 构建需依赖 `tree-sitter-cli` 和 `docker`
* 如果使用自定义语法，建议参考其语法包结构构建 wasm

### 方法二：从 GitHub Releases 下载

直接访问语言仓库的 Releases 页面，下载官方预构建好的 `.wasm` 文件。例如：

[https://github.com/tree-sitter/tree-sitter-c/releases](https://github.com/tree-sitter/tree-sitter-c/releases)

### 方法三：直接使用语言包自带 `.wasm`（推荐）

大部分 npm 上的 `tree-sitter-xxx` 包都内置了 `.wasm` 文件，例如：

```ts
const wasmPath = require.resolve('tree-sitter-c/tree-sitter-c.wasm');
```

无需手动复制，自动适配开发和打包路径。

### 方法四：本地包链接 🆕

**对于自定义的本地语言包，使用 `npm link` 进行链接：**

```bash
# 在本地语言包目录中
cd /path/to/your/tree-sitter-custom
npm link

# 在项目目录中
cd /path/to/your/project  
npm link tree-sitter-custom
```

**优势：**
- ✅ 支持本地开发和调试
- ✅ 自动同步本地包的更新
- ✅ 与 `require.resolve()` 完美兼容
- ✅ 构建脚本可正常处理符号链接

**注意事项：**
- 本地包必须有正确的 `package.json`
- 确保 `.wasm` 文件在根目录
- 链接后的包在 `npm list` 中会显示为 `extraneous`

---

## 四、加载和使用 Parser

### 1. 初始化与加载示例

```ts
import path from 'path';
import { Parser, Language } from 'web-tree-sitter';

export async function loadCLanguageParser() {
  await Parser.init();
  const parser = new Parser();

  // 推荐使用固定的相对路径访问 wasm 文件，确保开发和生产环境一致
  const wasmPath = path.join(__dirname, 'languages/tree-sitter-c.wasm');

  const lang = await Language.load(wasmPath);
  parser.setLanguage(lang);

  const tree = parser.parse('int main() { return 0; }');
  console.log(tree.rootNode.toString());

  return parser;
}
```

说明：

* `Parser.init()` 必须先执行，用于初始化 WASM 支持
* `.wasm` 文件通过 `Language.load()` 异步加载
* `.wasm` 文件不能用 `import` 或 `require` 加载，只能异步读取
* 这里改为统一从 `languages/` 目录加载 wasm，避免 `require.resolve()` 依赖 node\_modules 路径问题

---

## 五、导入方式与兼容性说明

`web-tree-sitter` 是 CommonJS 模块，TypeScript 使用时需注意导入方式。

| 写法                                            | 可用性 | 说明                    |
| --------------------------------------------- | --- | --------------------- |
| `const { Parser } = require(...)`             | ✅   | 推荐写法                  |
| `import WebTreeSitter from 'web-tree-sitter'` | ✅   | 需开启 `esModuleInterop` |
| `import { Parser } from 'web-tree-sitter'`    | ❌   | 报错：无 `init` 方法        |
| `import * as WebTreeSitter from ...`          | ❌   | 报错：命名空间对象中无 `init`    |

如使用 `import` 写法，请在 `tsconfig.json` 中开启：

```json
{
  "compilerOptions": {
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true
  }
}
```

---

## 六、与原生 tree-sitter 对比

| 项目   | node-tree-sitter (native) | web-tree-sitter (WASM) |
| ---- | ------------------------- | ---------------------- |
| 环境   | Node.js（C++ 插件）           | 浏览器 / Web / Node.js    |
| 语言语法 | `.node` 插件文件              | `.wasm` 模块文件           |
| 初始化  | 同步构造、同步设置                 | 异步初始化和加载语言             |
| 性能   | 高                         | 略低于 native             |
| 可移植性 | 低                         | 高（支持浏览器等场景）            |
| 打包配置 | 需 native 插件支持             | 易于外部化打包处理              |

---

## 七、使用封装示例（类方式）

```ts
const { Parser, Language, Query } = require('web-tree-sitter');

class ParserManager {
  private parser;
  private language;

  async init() {
    await Parser.init();
  }

  async loadWasm(wasmPath: string) {
    this.parser = new Parser();
    this.language = await Language.load(wasmPath);
    this.parser.setLanguage(this.language);
  }

  parse(code: string) {
    return this.parser.parse(code);
  }

  query(queryText: string) {
    return new Query(this.language, queryText);
  }
}
```

使用方式：

```ts
const manager = new ParserManager();
await manager.init();
await manager.loadWasm(path.join(__dirname, 'languages/tree-sitter-c.wasm'));
const tree = manager.parse('int add(int a, int b);');
console.log(tree.rootNode.toString());
```

---

## 八、打包注意事项与 `.wasm` 加载策略

### 1. 两种 `.wasm` 加载方式对比

| 加载方式                     | 是否依赖 `node_modules` | 是否需 `projectRoot()` | 是否支持打包运行 | 优点         | 缺点                                             |
| ------------------------ | ------------------- | ------------------- | -------- | ---------- | ---------------------------------------------- |
| 方案一：`require.resolve()`  | 是                   | 否                   | 否        | 使用简单，开发体验好 | 构建后 `.wasm` 无法直接解析；需 `dist/node_modules` 结构    |
| 方案二：统一 `languages/` 目录加载 | 否                   | 是                   | 是        | 路径清晰、打包可控  | 需要手动拷贝 `.wasm`；每次加载需通过 `getProjectRoot()` 定位路径 |
| 组合方案（构建时 resolve + 拷贝）   | 是（源路径依赖）            | 是                   | 是        | 产物稳定，路径清晰  | 构建逻辑较复杂，需脚本辅助                                  |

---

### 2. 加载方式一：使用 `require.resolve()` 加载 `.wasm`

```ts
const wasmPath = require.resolve('tree-sitter-c/tree-sitter-c.wasm');
const lang = await Language.load(wasmPath);
```

**说明：**

* 依赖语言包目录结构规范
* 适用于开发环境
* 打包后无法工作，需复制 `node_modules` 至 `dist` 中

---

### 3. 加载方式二：统一 `languages/` 目录加载 `.wasm`

```ts
import { getProjectRoot } from './utils/path';
const wasmPath = path.join(getProjectRoot(), 'languages/tree-sitter-c.wasm');
const lang = await Language.load(wasmPath);
```

**依赖点：**

* 语言文件需预先复制至 `languages/` 目录
* 需要工具函数定位项目根目录

```ts
export function getProjectRoot(): string {
  let dir = __dirname;
  while (dir !== path.parse(dir).root) {
    if (require('fs').existsSync(path.join(dir, 'package.json'))) return dir;
    dir = path.dirname(dir);
  }
  throw new Error('Project root not found.');
}
```

---

### 4. 构建阶段组合策略（推荐）

构建阶段使用 `require.resolve()` 查找 `.wasm` 路径，然后将其复制至 `dist/languages/`：

```ts
const src = require.resolve('tree-sitter-c/tree-sitter-c.wasm');
const dst = path.join(__dirname, '..', 'dist', 'languages', 'tree-sitter-c.wasm');
fs.copyFileSync(src, dst);
```

运行时统一通过相对路径加载：

```ts
const wasmPath = path.join(__dirname, 'languages/tree-sitter-c.wasm');
```

---

### 5. 构建产物结构对比

**方案一产物结构：**

```
dist/
├── main.js
└── node_modules/
    └── tree-sitter-c/
        ├── tree-sitter-c.wasm
        └── bindings/node/index.js
```

**方案二产物结构：**

```
dist/
├── main.js
└── languages/
    └── tree-sitter-c.wasm
```

---

### 6. 🆕 精简复制策略（强烈推荐）

**问题：** 完整复制 tree-sitter 包会导致产物过大（可能40MB+）

**解决方案：** 只复制运行时必需的文件

#### 精简复制脚本示例

```js
// copy-modules.js - 精简版
const fs = require('fs-extra');
const path = require('path');

const moduleConfigs = {
  'web-tree-sitter': {
    files: [
      'tree-sitter.wasm',           // 核心WASM (204K)
      'tree-sitter.cjs',            // CommonJS运行时 (160K) 
      'tree-sitter.js',             // ES模块运行时 (148K)
      'web-tree-sitter.d.ts',       // TypeScript类型定义 (40K)
      'package.json',               // 模块解析必需 (4K)
      'LICENSE'                     // 许可证文件
    ]
  },
  'tree-sitter-c': {
    files: [
      'tree-sitter-c.wasm',         // 语言WASM (612K)
      'package.json',               // 模块解析必需 (4K)
      'bindings/',                  // Node.js绑定 (16K)
      'LICENSE'                     // 许可证文件
    ]
  },
  'tree-sitter-custom': {
    files: [
      'tree-sitter-custom.wasm',    // 自定义语言WASM
      'package.json',               // 模块解析必需
      'bindings/',                  // Node.js绑定
      // 注意：不复制 node_modules/, src/, build/ 等大文件夹
    ]
  }
};

function copyModuleFiles(moduleName, config) {
  const symlinkPath = path.resolve(__dirname, 'node_modules', moduleName);
  const realPath = fs.realpathSync(symlinkPath); // 处理符号链接
  const distModulePath = path.resolve(__dirname, 'dist', 'node_modules', moduleName);
  
  // 清理旧目录
  if (fs.existsSync(distModulePath)) {
    fs.removeSync(distModulePath);
  }
  
  fs.ensureDirSync(distModulePath);
  
  // 只复制指定文件
  for (const file of config.files) {
    const srcPath = path.join(realPath, file);
    const dstPath = path.join(distModulePath, file);
    
    if (fs.existsSync(srcPath)) {
      fs.ensureDirSync(path.dirname(dstPath));
      fs.copySync(srcPath, dstPath, { overwrite: true });
    }
  }
}
```

#### 精简效果对比

| 包名 | 完整复制 | 精简复制 | 精简比例 |
|------|----------|----------|----------|
| web-tree-sitter | 5.7MB | 545KB | 10.4倍 |
| tree-sitter-c | 8.7MB | 615KB | 14.1倍 |
| tree-sitter-np | 43MB | 151KB | 285倍 |
| **总计** | **57.4MB** | **1.3MB** | **44倍** |

#### 运行时必需文件列表

**web-tree-sitter:**
- ✅ `tree-sitter.wasm` - 核心WASM运行时
- ✅ `tree-sitter.cjs` - CommonJS模块
- ✅ `tree-sitter.js` - ES模块  
- ✅ `web-tree-sitter.d.ts` - TypeScript类型
- ✅ `package.json` - 模块解析
- ❌ `debug/`, `lib/`, `src/` - 开发文件
- ❌ `*.map` - Source maps

**language packages:**
- ✅ `tree-sitter-{lang}.wasm` - 语言解析器
- ✅ `package.json` - 模块解析
- ✅ `bindings/node/` - Node.js绑定
- ❌ `src/`, `prebuilds/`, `node_modules/` - 构建文件
- ❌ `grammar.js`, `build/` - 源码和构建产物

---

### 7. 使用推荐总结

| 场景      | 推荐方案                                                                      |
| ------- | ------------------------------------------------------------------------- |
| 开发调试    | 使用 `require.resolve()` + `npm link`（本地包） 加载 `.wasm`                         |
| 构建产物部署  | 构建阶段用 `require.resolve()` 定位 `.wasm` 并拷贝至 `dist/node_modules/`；运行时统一使用相对路径加载 |
| 浏览器运行场景 | 必须使用明确路径加载 `.wasm`，不能使用 `require.resolve()`                               |
| 本地包开发 | 使用 `npm link` 建立符号链接，配合精简复制策略 |

### 8. 本地包管理最佳实践 🆕

#### 创建本地包链接

```bash
# 步骤1：在本地语言包目录
cd /path/to/your/tree-sitter-custom
npm link

# 步骤2：在项目目录
cd /path/to/your/project
npm link tree-sitter-custom
```

#### 验证链接状态

```bash
npm list tree-sitter-custom
# 输出：tree-sitter-custom@x.x.x extraneous -> /path/to/local/package
```

#### 解除链接

```bash
# 解除项目中的链接
npm unlink tree-sitter-custom

# 解除全局链接
cd /path/to/your/tree-sitter-custom
npm unlink
```

#### 本地包构建配置

确保本地包的 `package.json` 包含正确配置：

```json
{
  "name": "tree-sitter-custom",
  "version": "1.0.0",
  "main": "bindings/node/index.js",
  "files": [
    "tree-sitter-custom.wasm",
    "bindings/",
    "package.json"
  ]
}
```

---

### 9. 两种构建方式总结

#### 方案一：纯 `require.resolve()` 方式（开发友好）

1. 通过 `require.resolve('tree-sitter-c/tree-sitter-c.wasm')` 直接定位 `.wasm` 文件路径
2. 代码中直接使用该路径加载 `.wasm`
3. 使用 esbuild 打包时需将 `web-tree-sitter` 及语言包标记为 `external`
4. 构建产物需手动拷贝相关 `node_modules` 目录（包括语言包及依赖）到 `dist/node_modules`
5. 运行时依赖 `dist/node_modules` 完整结构，路径解析依赖此结构存在

#### 方案二：统一 `languages/` 目录加载（构建产物友好）

1. 在项目根目录创建 `languages/` 文件夹，并将所有 `.wasm` 文件放入
2. 代码中通过工具函数定位项目根目录，统一从 `languages/` 目录相对路径加载 `.wasm`
3. 使用 esbuild 打包时依然标记 `web-tree-sitter` 为 `external`，语言包作为资源手动管理
4. 构建时运行脚本复制 `.wasm` 文件（以及语言包需要的部分文件）到 `dist/languages/`
5. 运行时只需保证 `dist/languages` 中有对应 `.wasm` 文件，加载路径稳定，不依赖 `node_modules`

**🆕 方案一+ (推荐)：require.resolve() + 精简复制**

1. 开发时使用 `require.resolve()` + `npm link` 获得最佳开发体验
2. 构建时使用精简复制策略，只复制运行时必需文件
3. 产物大小从数十MB精简到1-2MB，提升部署效率
4. 保持路径解析的稳定性和兼容性

---

你可以根据项目需求选择合适的方案，也可以结合使用：开发时用 `require.resolve()` + `npm link` 方便调试，构建时使用精简复制保证部署效率。

---

## 九、典型错误与排查

| 错误信息                            | 原因                  | 解决方案                                 |
| ------------------------------- | ------------------- | ------------------------------------ |
| `Parser.init is not a function` | 错误导入方式              | 改用 `require()` 或开启 `esModuleInterop` |
| `Bad node name`                 | 查询语法错误              | 核对 grammar.js 中的节点名称                 |
| `memory access out of bounds`   | 大文件解析、未正确加载 wasm    | 检查 `.wasm` 是否加载成功，限制输入大小             |
| `Language is not initialized`   | 未调用 `Parser.init()` | 补充初始化逻辑                              |
| `Module not found: tree-sitter-xxx` | 本地包未正确链接 | 检查 `npm link` 状态，确认符号链接有效 | 