# 代码图构建逻辑与顺序文档

## 概述

代码图构建是代码分析和上下文检索的核心功能，通过解析源代码的AST（抽象语法树）构建表示代码结构和关系的图数据结构。本文档详细描述了代码图构建的逻辑、顺序和关键算法。

## 架构设计

### 核心组件

1. **CodeGraphBuilder**: 代码图构建器，负责从AST构建代码图
2. **GraphlibCodeGraph**: 图数据结构实现，基于graphlib库
3. **ParserManager**: 解析器管理器，支持多种语言的AST解析
4. **RepoIndexer**: 仓库索引器，协调整个索引流程

### 数据结构

#### ASTNode (抽象语法树节点)
```typescript
interface ASTNode {
  type: string;           // 原始语法类型
  kind?: 'function' | 'variable' | 'class' | 'interface' | 'type' | 'macro' | 'module';
  name?: string;          // 节点名称
  text?: string;          // 源代码文本
  children: ASTNode[];    // 子节点
  location?: Location;    // 位置信息
  language?: string;      // 编程语言
  file?: string;          // 文件路径
  module?: string;        // 模块路径
  metadata?: Metadata;    // 元数据
}
```

#### GraphCodeNode (图节点)
```typescript
interface GraphCodeNode {
  readonly id: string;           // 唯一标识符
  readonly name: string;         // 节点名称
  readonly type: CodeNodeType;   // 节点类型
  readonly language: Language;   // 编程语言
  readonly kind: NodeKind;       // 节点种类
  readonly file?: string;        // 文件路径
  readonly module?: string;      // 模块路径
  readonly definition?: string;  // 定义文本
  readonly location?: Location;  // 位置信息
  readonly metadata?: Metadata;  // 元数据
}
```

#### CodeModule (代码模块)
```typescript
interface CodeModule {
  path: string;           // 模块路径
  language: Language;     // 编程语言
  nodeIds: Set<string>;   // 包含的节点ID集合
  imports: Set<string>;   // 导入的模块路径集合
  exports?: Set<string>;  // 导出的节点ID集合
  metadata?: Metadata;    // 元数据
}
```

## 构建流程

### 1. 单文件构建流程 (buildFromAst)

```mermaid
graph TD
    A[输入AST和文件路径] --> B[创建CodeModule]
    B --> C[构建节点映射]
    C --> D[添加模块入口节点]
    D --> E[第一阶段：收集函数定义]
    E --> F[第一阶段：收集模块导入]
    F --> G[添加模块到图]
    G --> H[第二阶段：构建调用关系]
    H --> I[输出代码图]
```

#### 详细步骤

1. **初始化模块**
   ```typescript
   const module: CodeModule = {
     path: filePath,
     language: getLanguageFromFile(filePath),
     nodeIds: new Set(),
     imports: new Set()
   };
   ```

2. **构建节点映射**
   - 遍历AST，建立节点名称到ASTNode的映射
   - 用于后续的节点查找和关系建立

3. **添加模块入口节点**
   ```typescript
   const moduleEntryId = generateNodeId('__module_entry__', moduleName, 'entry');
   addNode({
     id: moduleEntryId,
     name: '__module_entry__',
     type: 'entry',
     language: language,
     kind: 'module',
     file: moduleName,
     module: moduleName
   });
   ```

4. **第一阶段：收集函数定义**
   - 遍历AST，识别函数定义、宏定义
   - 添加到全局函数索引
   - 记录到模块的nodeIds集合

5. **第一阶段：收集模块导入**
   - 识别头文件包含、模块导入
   - 记录到模块的imports集合

6. **添加模块到图**
   - 创建模块节点
   - 添加模块包含关系边
   - 添加模块导入关系边

7. **第二阶段：构建调用关系**
   - 遍历AST，识别函数调用
   - 建立调用者和被调用者之间的边

### 2. 多文件构建流程 (buildFromMultipleAsts)

```mermaid
graph TD
    A[输入多个AST] --> B[第一阶段：收集所有函数定义]
    B --> C[第一阶段：收集所有模块导入]
    C --> D[添加所有模块到图]
    D --> E[第二阶段：构建所有调用关系]
    E --> F[输出统一代码图]
```

#### 关键设计原则

1. **两阶段构建**：确保所有函数定义收集完毕后再构建调用关系
2. **全局索引**：维护函数名到模块路径和节点ID的映射
3. **模块隔离**：每个文件作为一个独立的模块处理

## 核心算法

### 1. 函数定义收集算法

```typescript
private collectFunctionDefinitions(node: ASTNode, module: CodeModule): void {
  // 处理函数定义
  if (node.kind === 'function' && node.name && isFunctionDefinition(node.type)) {
    const nodeId = generateNodeId(node.name, module.path, 'function');
    const functionNode = createFunctionNode(node, module);
    addNode(functionNode);
    module.nodeIds.add(nodeId);
    
    // 添加到全局函数索引
    addToGlobalFunctionIndex(node.name, module.path, nodeId);
  }
  
  // 处理宏定义
  else if (node.kind === 'macro' && node.name) {
    // 类似处理...
  }
  
  // 递归处理子节点
  if (node.children) {
    for (const child of node.children) {
      collectFunctionDefinitions(child, module);
    }
  }
}
```

### 2. 模块导入收集算法

```typescript
private collectModuleImports(node: ASTNode, module: CodeModule): void {
  // 处理头文件包含/模块导入
  if (node.kind === 'module' && node.name) {
    module.imports.add(node.name);
    
    // 记录模块导入关系
    if (!moduleImports.has(module.path)) {
      moduleImports.set(module.path, new Set());
    }
    moduleImports.get(module.path)!.add(node.name);
  }
  
  // 递归处理子节点
  if (node.children) {
    for (const child of node.children) {
      collectModuleImports(child, module);
    }
  }
}
```

### 3. 调用关系构建算法

```typescript
private buildCallRelationships(node: ASTNode, module: CodeModule, moduleEntryId: string): void {
  // 处理模块顶层的函数调用
  if (node.kind === 'function' && isFunctionCall(node.type) && node.name) {
    const calleeId = findFunctionNode(node.name, module.path);
    if (calleeId) {
      addEdge(moduleEntryId, calleeId, 'calls');
    }
  }
  
  // 处理函数定义内的调用
  if (node.kind === 'function' && node.name && !isFunctionCall(node.type)) {
    const callerId = generateNodeId(node.name, module.path, 'function');
    
    // 处理函数体内的调用
    if (node.children) {
      for (const child of node.children) {
        processFunctionBody(child, callerId, module);
      }
    }
  }
  
  // 递归处理子节点
  if (node.children) {
    for (const child of node.children) {
      buildCallRelationships(child, module, moduleEntryId);
    }
  }
}
```

### 4. 跨模块函数查找算法

```typescript
private findFunctionNode(functionName: string, currentModulePath: string): string | null {
  const functionEntries = globalFunctionIndex.get(functionName);
  if (!functionEntries || functionEntries.length === 0) {
    return null;
  }
  
  // 1. 优先在当前模块查找
  const currentModuleEntry = functionEntries.find(entry => entry.modulePath === currentModulePath);
  if (currentModuleEntry) {
    return currentModuleEntry.nodeId;
  }
  
  // 2. 在导入的模块中查找（包括递归查找）
  const visitedModules = new Set<string>();
  const result = findFunctionInImportedModules(functionName, currentModulePath, visitedModules);
  if (result) {
    return result;
  }
  
  // 3. 如果还是找不到，返回第一个匹配的函数（可能是全局函数）
  return functionEntries[0].nodeId;
}

private findFunctionInImportedModules(functionName: string, modulePath: string, visitedModules: Set<string>): string | null {
  // 防止循环依赖
  if (visitedModules.has(modulePath)) {
    return null;
  }
  visitedModules.add(modulePath);
  
  const functionEntries = globalFunctionIndex.get(functionName);
  if (!functionEntries) {
    return null;
  }
  
  const importedModules = moduleImports.get(modulePath);
  if (!importedModules) {
    return null;
  }
  
  for (const importedModule of importedModules) {
    // 在当前导入模块中查找
    const importedEntry = functionEntries.find(entry => entry.modulePath === importedModule);
    if (importedEntry) {
      return importedEntry.nodeId;
    }
    
    // 递归查找导入模块的导入模块
    const recursiveResult = findFunctionInImportedModules(functionName, importedModule, visitedModules);
    if (recursiveResult) {
      return recursiveResult;
    }
  }
  
  return null;
}
```

## 数据流

### 1. 输入数据流

```
源代码文件 → Parser → AST → CodeGraphBuilder
```

### 2. 内部数据流

```
AST → 节点映射 → 函数定义收集 → 模块导入收集 → 模块添加 → 调用关系构建 → 代码图
```

### 3. 输出数据流

```
代码图 → GraphlibCodeGraph → 图查询接口 → 上下文检索
```

## 关键数据结构

### 1. 全局函数索引

```typescript
// 函数名 -> {模块路径, 节点ID}[]
private globalFunctionIndex = new Map<string, Array<{modulePath: string, nodeId: string}>>();
```

**用途**：
- 快速查找函数定义
- 支持跨模块函数调用
- 处理函数重载和同名函数

### 2. 模块导入映射

```typescript
// 模块路径 -> 导入的模块路径集合
private moduleImports = new Map<string, Set<string>>();
```

**用途**：
- 记录模块间的依赖关系
- 支持递归查找导入模块
- 构建模块依赖图

### 3. 模块集合

```typescript
// 模块路径 -> CodeModule
private modules = new Map<string, CodeModule>();
```

**用途**：
- 管理所有模块信息
- 维护模块内的节点集合
- 支持模块级别的查询

## 边类型定义

### 1. 调用关系边 (calls)
- **含义**：函数A调用函数B
- **方向**：A → B
- **用途**：构建调用链，支持调用者/被调用者查询

### 2. 包含关系边 (contains)
- **含义**：模块A包含节点B
- **方向**：A → B
- **用途**：建立模块与节点的从属关系

### 3. 导入关系边 (imports)
- **含义**：模块A导入模块B
- **方向**：A → B
- **用途**：建立模块间的依赖关系

### 4. 继承关系边 (inherits)
- **含义**：类A继承类B
- **方向**：A → B
- **用途**：构建类层次结构

### 5. 使用关系边 (uses)
- **含义**：节点A使用节点B
- **方向**：A → B
- **用途**：建立变量、类型的使用关系

## 性能优化

### 1. 缓存机制
- 相关节点查询缓存
- 函数查找结果缓存
- 模块导入关系缓存

### 2. 索引优化
- 全局函数索引：O(1) 函数查找
- 模块导入映射：O(1) 模块依赖查询
- 节点ID映射：O(1) 节点查找

### 3. 算法优化
- 两阶段构建：避免重复遍历
- 递归深度限制：防止无限递归
- 循环依赖检测：避免死循环

## 错误处理

### 1. 数据完整性检查
- 节点存在性验证
- 边有效性检查
- 模块依赖完整性验证

### 2. 异常处理
- AST解析失败处理
- 文件读取错误处理
- 内存不足处理

### 3. 日志记录
- 构建过程日志
- 错误详情记录
- 性能指标统计

## 扩展性设计

### 1. 语言扩展
- 插件化的解析器接口
- 语言特定的节点类型
- 可配置的语法规则

### 2. 关系类型扩展
- 可扩展的边类型系统
- 自定义关系定义
- 关系权重支持

### 3. 查询接口扩展
- 图查询语言支持
- 复杂查询优化
- 实时查询更新

## 总结

代码图构建是一个复杂的数据处理流程，需要仔细考虑：

1. **数据依赖关系**：确保数据收集和处理的正确顺序
2. **跨模块关系**：正确处理模块间的依赖和调用关系
3. **性能优化**：使用合适的索引和缓存机制
4. **扩展性**：设计灵活的接口支持未来扩展
5. **错误处理**：提供完善的错误处理和日志记录

通过这种系统性的设计，代码图构建器能够准确、高效地构建表示代码结构和关系的图数据，为代码分析和上下文检索提供坚实的基础。 