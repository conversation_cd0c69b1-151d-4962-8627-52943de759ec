# NP 解析器设计理念

## 概述

NP 解析器采用简化的设计理念，在解析阶段不区分宏定义和函数定义，而是将它们统一处理为函数，在元数据中保留类型信息。这种设计支持模块间关系连接时的二义性处理。

## 设计原则

### 1. 统一处理原则
- **解析阶段：** 不区分宏定义、函数定义、bundle 定义，统一处理为 `kind: 'function'`
- **类型标记：** 在 `metadata.type` 字段中标记实际类型（`'function'`、`'macro'`、`'bundle'`）
- **二义性支持：** 支持 `kind` 为 `function` 但 `type` 为 `macro` 或 `function` 的情况

### 2. 元数据结构

```typescript
interface ASTNode {
  name: string;
  kind: ASTNodeKind.FUNCTION;  // 统一为 function
  originalType: string;
  metadata: {
    type: 'function' | 'macro' | 'bundle';  // 实际类型
    value?: string;  // 宏的值（仅宏定义）
    isCall?: boolean;  // 是否为调用
    caller?: string;  // 调用者
    // ... 其他元数据
  };
}
```

### 3. 解析示例

#### Bundle 定义
```np
bundle add(a, b): {
    result = a + b;
    return result;
}
```

解析结果：
```json
{
  "name": "add",
  "kind": "function",
  "originalType": "function",
  "metadata": {
    "type": "bundle"
  }
}
```

#### 宏定义
```np
#define SQUARE(x) ((x)*(x))
```

解析结果：
```json
{
  "name": "SQUARE",
  "kind": "function",
  "originalType": "function",
  "metadata": {
    "type": "macro",
    "value": "((x)*(x))"
  }
}
```

#### 函数调用
```np
print(MSG);
SQUARE(va1, var2);
```

解析结果：
```json
[
  {
    "name": "print",
    "kind": "function",
    "originalType": "call_expression",
    "metadata": {
      "type": "function",
      "isCall": true
    }
  },
  {
    "name": "SQUARE",
    "kind": "function",
    "originalType": "call_expression",
    "metadata": {
      "type": "macro",
      "isCall": true
    }
  }
]
```

## 优势

### 1. 简化解析逻辑
- 解析阶段不需要复杂的类型判断
- 统一的 AST 节点结构
- 减少解析错误的可能性

### 2. 支持二义性
- 在模块间关系连接时可以处理类型二义性
- 支持 `kind` 为 `function` 但实际类型不同的情况
- 便于后续的类型推断和关系分析

### 3. 扩展性
- 容易添加新的类型（如 `'inline'`、`'template'` 等）
- 元数据字段可以灵活扩展
- 支持复杂的类型组合

## 使用场景

### 1. 模块间关系分析
```typescript
// 在关系分析阶段处理二义性
function analyzeCallRelation(caller: ASTNode, callee: ASTNode) {
  if (callee.metadata.type === 'macro') {
    // 宏调用：展开宏定义
    return expandMacroCall(caller, callee);
  } else if (callee.metadata.type === 'bundle') {
    // Bundle 调用：特殊处理
    return handleBundleCall(caller, callee);
  } else {
    // 普通函数调用
    return handleFunctionCall(caller, callee);
  }
}
```

### 2. 代码补全
```typescript
// 在代码补全时提供类型信息
function getCompletionItems(node: ASTNode) {
  if (node.metadata.type === 'macro') {
    return getMacroCompletionItems(node);
  } else if (node.metadata.type === 'bundle') {
    return getBundleCompletionItems(node);
  } else {
    return getFunctionCompletionItems(node);
  }
}
```

## 总结

这种设计理念将复杂的类型判断从解析阶段推迟到使用阶段，简化了解析逻辑，同时保持了足够的类型信息来支持后续的分析和处理。通过元数据中的 `type` 字段，我们可以在需要时区分不同的类型，而在不需要区分时统一处理为函数。 