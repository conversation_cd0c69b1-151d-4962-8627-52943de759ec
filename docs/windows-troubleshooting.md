# Windows 环境问题排查指南

## 问题描述

在Windows环境下使用VSCode插件时，可能会遇到以下问题：

1. **Worker文件加载失败**：`Cannot find module 'd:\coding\code-partner\dist\fileParserWorker.js'`
2. **路径重复错误**：`Cannot find module '/Users/<USER>/dist/dist/components/code_graph/workers/fileParserWorker.js'`
3. **Worker异常退出**：`[WARN] [WorkerPool] Worker 异常退出，退出码: 1`
4. **保存文件触发全量索引**：保存文件时意外触发全量索引，影响性能

## 问题原因

### 1. Worker文件加载失败
- **原因**：`esbuild`只编译主入口文件，不会自动复制Worker文件到`dist`目录
- **影响**：Worker线程无法找到执行脚本，导致插件功能异常

### 2. 路径重复错误
- **原因**：WorkerPool的路径计算逻辑在不同运行时环境下产生错误
- **影响**：路径中出现重复的`dist`目录，导致文件找不到

### 3. Worker异常退出
- **原因**：Worker在关闭时没有正确处理`terminate()`调用和`SIGTERM`信号
- **影响**：Worker以退出码1退出，产生错误日志，但功能基本正常

### 4. 保存文件触发全量索引
- **原因**：文件监听器过于宽泛，监听所有文件变化，包括保存文件时
- **影响**：用户保存文件时意外触发全量索引，影响开发体验

## 解决方案

### 1. 修复构建配置

更新`esbuild.js`，添加Worker文件复制插件：

```javascript
// 添加Worker文件复制插件
const copyWorkerFilesPlugin = {
  name: 'copy-worker-files',
  setup(build) {
    build.onEnd(async () => {
      const workerFiles = [
        'src/components/code_graph/workers/fileParserWorker.js',
        'src/components/code_graph/workers/workerDemo.js',
        'src/components/code_graph/workers/testProgressDemo.js',
        'src/components/code_graph/workers/workerDemo.ts'
      ];
      
      for (const file of workerFiles) {
        const src = path.resolve(__dirname, file);
        const dest = path.resolve(__dirname, 'dist', file);
        
        // 确保目标目录存在
        await fs.mkdir(path.dirname(dest), { recursive: true });
        
        // 复制文件
        await fs.copyFile(src, dest);
        console.log(`[copy] Copied ${file} to dist/`);
      }
    });
  }
};

// 在esbuild配置中添加插件
esbuild.build({
  // ... 其他配置
  plugins: [copyWorkerFilesPlugin]
});
```

### 2. 修复路径处理逻辑

更新`WorkerPool.ts`中的路径计算逻辑：

```typescript
constructor(options: WorkerPoolOptions = {}) {
  this.maxWorkers = options.maxWorkers || Math.max(1, os.cpus().length - 1);
  
  // 改进路径处理逻辑
  if (options.workerScriptPath) {
    this.workerScriptPath = options.workerScriptPath;
  } else {
    // 根据当前运行环境计算正确的Worker脚本路径
    const currentDir = __dirname;
    
    if (currentDir.includes('src/components/code_graph/workers')) {
      // 从src目录运行
      this.workerScriptPath = path.resolve(currentDir, 'fileParserWorker.js');
    } else if (currentDir.includes('dist/components/code_graph/workers')) {
      // 从dist目录运行
      this.workerScriptPath = path.resolve(currentDir, 'fileParserWorker.js');
    } else if (currentDir.includes('dist/components/code_graph')) {
      // 从dist/components/code_graph目录运行
      this.workerScriptPath = path.resolve(currentDir, 'workers/fileParserWorker.js');
    } else if (currentDir.includes('dist/components')) {
      // 从dist/components目录运行
      this.workerScriptPath = path.resolve(currentDir, 'code_graph/workers/fileParserWorker.js');
    } else if (currentDir.includes('dist')) {
      // 从dist目录运行
      this.workerScriptPath = path.resolve(currentDir, 'components/code_graph/workers/fileParserWorker.js');
    } else {
      // 从项目根目录运行
      this.workerScriptPath = path.resolve(currentDir, 'dist/components/code_graph/workers/fileParserWorker.js');
    }
  }
  
  logger.info(`Worker 脚本路径: ${this.workerScriptPath}`);
}
```

### 3. 修复Worker关闭逻辑

更新`fileParserWorker.js`，改进关闭处理：

```javascript
// 优雅关闭处理
process.on('SIGTERM', () => {
  log('收到 SIGTERM 信号，开始优雅关闭...');
  // 确保在SIGTERM时也能优雅退出
  if (parentPort) {
    try {
      parentPort.postMessage({ type: 'shutdown_ack' });
    } catch (error) {
      log(`发送关闭确认失败: ${error.message}`);
    }
  }
  process.exit(0);
});

// 改进shutdown消息处理
parentPort.on('message', async (message) => {
  try {
    if (message.type === 'shutdown') {
      log('收到关闭消息，开始优雅关闭...');
      // 发送关闭确认
      try {
        parentPort.postMessage({ type: 'shutdown_ack' });
        log('已发送关闭确认');
      } catch (error) {
        log(`发送关闭确认失败: ${error.message}`);
      }
      // 延迟一点时间确保消息发送完成，然后退出
      setTimeout(() => {
        log('Worker 优雅关闭完成');
        process.exit(0);
      }, 200); // 增加延迟时间
    }
    // ... 其他消息处理
  } catch (error) {
    log(`处理消息时出错: ${error.message}`);
  }
});
```

更新`WorkerPool.ts`，改进关闭逻辑：

```typescript
async shutdown(): Promise<void> {
  logger.info('开始关闭 worker 池...');
  this.isShuttingDown = true;

  // 等待所有任务完成
  let waitCount = 0;
  while (this.taskQueue.length > 0 && waitCount < 50) {
    await new Promise(resolve => setTimeout(resolve, 100));
    waitCount++;
  }

  // 先发送关闭消息给所有worker
  logger.info(`发送关闭消息给 ${this.workers.length} 个 worker...`);
  const shutdownPromises = this.workers.map((worker, index) => {
    return new Promise<void>((resolve) => {
      const timeout = setTimeout(() => {
        logger.warn(`Worker ${index} 关闭消息超时，强制终止`);
        resolve();
      }, 2000);

      const messageHandler = (message: any) => {
        if (message.type === 'shutdown_ack') {
          clearTimeout(timeout);
          logger.info(`Worker ${index} 确认关闭`);
          resolve();
        }
      };

      worker.on('message', messageHandler);
      worker.postMessage({ type: 'shutdown' });
    });
  });

  await Promise.all(shutdownPromises);

  // 给Worker一些额外时间来处理关闭
  logger.info('等待Worker完成关闭处理...');
  await new Promise(resolve => setTimeout(resolve, 500));

  // 终止所有 worker
  logger.info(`开始终止 ${this.workers.length} 个 worker...`);
  const terminationPromises = this.workers.map((worker, index) => {
    return new Promise<void>((resolve) => {
      const timeout = setTimeout(() => {
        logger.warn(`Worker ${index} 终止超时，强制终止`);
        resolve();
      }, 3000);

      worker.terminate()
        .then(() => {
          clearTimeout(timeout);
          logger.info(`Worker ${index} 正常终止`);
          resolve();
        })
        .catch((error) => {
          clearTimeout(timeout);
          logger.warn(`Worker ${index} 终止时出错:`, error);
          resolve();
        });
    });
  });

  await Promise.all(terminationPromises);
  
  this.workers = [];
  this.availableWorkers = [];
  this.taskQueue = [];
  
  logger.info('Worker 池已关闭');
}
```

### 4. 修复文件监听器

更新`RepoIndexerManager.ts`，优化文件监听器：

```typescript
/**
 * 注册文件变更监听器
 */
private registerFileWatcher(context: vscode.ExtensionContext): void {
  // 只监听支持的文件类型，避免监听所有文件
  const supportedPatterns = [
    '**/*.c',
    '**/*.h', 
    '**/*.asm',
    '**/*.cpp',
    '**/*.cc',
    '**/*.cxx',
    '**/*.hpp',
    '**/*.hxx'
  ];
  
  const fileWatcher = vscode.workspace.createFileSystemWatcher(`{${supportedPatterns.join(',')}}`);

  // 监听文件创建
  fileWatcher.onDidCreate(async uri => {
    try {
      logger.debug(`检测到文件创建: ${uri.fsPath}`);
      await this.handleFileChange(uri);
    } catch (error) {
      logger.error(`处理文件创建事件失败: ${uri.fsPath}`, error);
    }
  });

  // 监听文件变更
  fileWatcher.onDidChange(async uri => {
    try {
      logger.debug(`检测到文件变更: ${uri.fsPath}`);
      await this.handleFileChange(uri);
    } catch (error) {
      logger.error(`处理文件变更事件失败: ${uri.fsPath}`, error);
    }
  });

  // 监听文件删除
  fileWatcher.onDidDelete(async uri => {
    try {
      logger.debug(`检测到文件删除: ${uri.fsPath}`);
      await this.handleFileChange(uri);
    } catch (error) {
      logger.error(`处理文件删除事件失败: ${uri.fsPath}`, error);
    }
  });

  context.subscriptions.push(fileWatcher);
}

/**
 * 处理文件变更
 */
private async handleFileChange(uri: vscode.Uri): Promise<void> {
  // 检查自动索引配置
  if (!this.context) {
    return;
  }

  const workspaceFolders = vscode.workspace.workspaceFolders;
  if (!workspaceFolders) {
    return;
  }

  const workspacePath = workspaceFolders[0].uri.fsPath;
  const configPath = vscode.Uri.file(path.join(workspacePath, this.CONFIG_FILE));

  try {
    const configContent = await vscode.workspace.fs.readFile(configPath);
    const config = JSON.parse(configContent.toString());

    // 只有在启用索引且启用自动索引时才处理文件变更
    if (!config.enableIndexing || !config.autoIndexing) {
      logger.debug('自动索引已禁用，跳过文件变更处理');
      return;
    }
  } catch (error) {
    logger.debug('无法读取配置文件，跳过文件变更处理');
    return;
  }

  // 检查文件是否在支持的文件类型中
  const supportedExtensions = ['.c', '.h', '.asm', '.cpp', '.cc', '.cxx', '.hpp', '.hxx'];
  const fileExtension = path.extname(uri.fsPath).toLowerCase();
  if (!supportedExtensions.includes(fileExtension)) {
    logger.debug(`文件类型不支持: ${fileExtension}，跳过索引`);
    return;
  }

  // 如果文件变更间隔小于增量更新间隔，则延迟触发增量更新
  const currentTime = Date.now();
  if (currentTime - this.lastIndexTime < this.INCREMENTAL_UPDATE_INTERVAL) {
    const remainingTime = Math.round((this.INCREMENTAL_UPDATE_INTERVAL - (currentTime - this.lastIndexTime)) / 1000);
    logger.debug(`📝 文件变更检测: ${path.basename(uri.fsPath)}, 距离下次索引还有 ${remainingTime} 秒`);
    return;
  }

  // 检查是否有索引器，如果没有则跳过（避免在初始化前触发索引）
  if (!this.repoIndexer) {
    logger.debug('索引器未初始化，跳过文件变更处理');
    return;
  }

  logger.info(`📝 检测到文件变更: ${path.basename(uri.fsPath)}`);
  logger.info(`🔄 准备触发增量索引...`);

  try {
    // 强制使用增量索引，避免全量索引
    await this.triggerIndexing(false);
  } catch (error) {
    logger.error(`❌ 处理文件变更失败: ${uri.fsPath}`, error);
  }
}
```

### 5. 更新测试文件路径

更新所有Worker相关测试文件，使用`path.resolve`：

```typescript
// 在测试文件中
const workerPath = path.resolve(__dirname, '../../../../src/components/code_graph/workers/fileParserWorker.js');
```

## 验证步骤

1. **重新构建项目**：
   ```bash
   npm run compile
   ```

2. **检查dist目录**：
   ```bash
   ls -la dist/components/code_graph/workers/
   ```
   确保`fileParserWorker.js`文件存在

3. **运行测试**：
   ```bash
   npm test -- tests/components/code_graph/workers/basic.test.ts
   ```

4. **检查插件日志**：
   在VSCode中打开插件，查看输出面板中的日志，确认没有Worker异常退出的错误

5. **测试文件保存**：
   保存一个`.md`或`.txt`文件，确认不会触发索引
   保存一个`.c`或`.asm`文件，确认只触发增量索引

## 预防措施

1. **使用path.resolve**：在所有路径处理中使用`path.resolve`而不是`path.join`
2. **构建时验证**：在构建过程中验证关键文件的存在
3. **跨平台测试**：在Windows、macOS、Linux环境下都进行测试
4. **详细日志**：添加详细的日志记录，便于问题排查
5. **文件类型过滤**：只监听支持的文件类型，避免不必要的索引触发

## 常见问题

### Q: 为什么Worker仍然以退出码1退出？
A: 这通常是因为Worker在关闭时遇到了未捕获的错误。通过改进关闭逻辑和错误处理，这个问题已经解决。

### Q: 如何调试Worker路径问题？
A: 查看WorkerPool初始化时的日志输出，确认`Worker 脚本路径`是否正确。如果路径不正确，检查`__dirname`的值和路径计算逻辑。

### Q: WorkerPool的路径计算逻辑为什么这么复杂？
A: 因为插件可能在不同的环境下运行（开发环境、生产环境、测试环境），每种环境的`__dirname`都不同，需要根据实际情况计算正确的路径。

### Q: 为什么需要给Worker额外的关闭时间？
A: Worker在收到关闭消息后需要时间来处理清理工作，如果立即调用`terminate()`可能会导致Worker异常退出。额外的延迟时间确保Worker能够优雅关闭。

### Q: 为什么保存文件会触发全量索引？
A: 这是因为之前的文件监听器监听所有文件（`**/*`），包括不支持的文件类型。现在修复为只监听支持的文件类型，并且改进了索引判断逻辑。

### Q: 如何避免保存文件时触发索引？
A: 通过以下方式避免：
1. 文件监听器只监听支持的文件类型（`.c`, `.h`, `.asm`等）
2. 在文件变更处理中添加文件类型检查
3. 检查索引器是否已初始化
4. 改进增量索引的判断逻辑

### Q: 文件监听器的监听模式是什么？
A: 现在的监听模式是：`{**/*.c,**/*.h,**/*.asm,**/*.cpp,**/*.cc,**/*.cxx,**/*.hpp,**/*.hxx}`，只会监听支持的文件类型，不会监听其他文件。 