# NP Parser 总结

## 概述

NP Parser 是项目中专门用于解析 NP (Neural Processing) 语言的代码解析器，它是基于 Tree-sitter 构建的语法分析器，能够将 NP 语言代码解析为抽象语法树 (AST)。

## 核心特性

### 1. 语言支持
- **目标语言**：NP (Neural Processing) 语言
- **文件扩展名**：`.asm`, `.np`
- **语法特性**：支持函数定义、bundle、宏定义、结构体等

### 2. 解析能力
- **函数定义**：`void function_name()`
- **Bundle 定义**：`bundle function_name()`
- **宏定义**：`#define MACRO_NAME value`
- **宏调用**：`MACRO_NAME(args)`
- **函数调用**：`function_name(args)`
- **变量声明**：`declare_variable`
- **结构体定义**：`struct StructName`
- **头文件包含**：`#include "header.h"`

## 架构设计

### 1. 类结构
```typescript
export class NPParser implements ICodeParser {
  private parser: any;
  private currentFunction: string | null = null;
  
  static async create(includePaths: string[] = [])
  private async init()
  public parse(content: string): ASTNode
  private createMockAST(content: string): ASTNode
  private processNode(node: Node): ASTNode
}
```

### 2. 初始化流程
1. **异步创建**：使用静态工厂方法 `create()`
2. **Tree-sitter 初始化**：加载 NP 语言的 WASM 模块
3. **错误处理**：初始化失败时自动降级到 Mock 模式
4. **解析器设置**：配置语言特定的解析规则

### 3. 解析策略
- **主要模式**：基于 Tree-sitter 的精确语法解析
- **降级模式**：基于正则表达式的启发式解析
- **错误恢复**：解析失败时自动切换到 Mock 模式

## 功能详解

### 1. 函数定义解析

#### 普通函数
```np
void main() {
    // 函数体
}
```

**解析结果**：
```typescript
{
  name: "main",
  type: "statement_function_def",
  kind: "function",
  language: "np",
  metadata: {
    parameters: [],
    linkage: "void"
  }
}
```

#### Bundle 函数
```np
bundle add(a, b): {
    result = a + b;
    return result;
}
```

**解析结果**：
```typescript
{
  name: "add",
  type: "statement_bundle_definition",
  kind: "function",
  language: "np",
  metadata: {
    parameters: [
      { name: "a", type: "unknown" },
      { name: "b", type: "unknown" }
    ],
    isBundle: true
  }
}
```

### 2. 宏定义解析

#### 宏定义
```np
#define SQUARE(x) ((x)*(x))
```

**解析结果**：
```typescript
{
  name: "SQUARE",
  type: "define_statement",
  kind: "function",
  language: "np",
  metadata: {
    value: "((x)*(x))",
    isMacro: true
  }
}
```

#### 宏调用
```np
SQUARE(var1, var2)
```

**解析结果**：
```typescript
{
  name: "SQUARE",
  type: "call_expression",
  kind: "function",
  language: "np",
  metadata: {
    isCall: true,
    isMacroCall: true,
    arguments: ["var1", "var2"]
  }
}
```

### 3. 函数调用解析

```np
print(MSG);
add(var1, var2);
```

**解析结果**：
```typescript
{
  name: "print",
  type: "call_expression",
  kind: "function",
  language: "np",
  metadata: {
    isCall: true,
    caller: "main",
    arguments: ["MSG"]
  }
}
```

### 4. 变量声明解析

```np
declare_variable int var1;
```

**解析结果**：
```typescript
{
  name: "var1",
  type: "declare_variable",
  kind: "variable",
  language: "np",
  metadata: {
    type: "int"
  }
}
```

### 5. 结构体定义解析

```np
struct Point {
    x;
    y;
};
```

**解析结果**：
```typescript
{
  name: "Point",
  type: "struct_specifier",
  kind: "type",
  language: "np",
  metadata: {
    isStruct: true
  }
}
```

### 6. 头文件包含解析

```np
#include "math.h"
```

**解析结果**：
```typescript
{
  name: "math.h",
  type: "include_statement",
  kind: "module",
  language: "np",
  metadata: {
    isInclude: true,
    includePath: "math.h"
  }
}
```

## Mock 模式

### 1. 触发条件
- Tree-sitter 初始化失败
- 解析过程中发生错误
- WASM 模块加载失败

### 2. 实现策略
```typescript
private createMockAST(content: string): ASTNode {
  const lines = content.split('\n');
  const children: ASTNode[] = [];
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();
    
    // 检测函数定义
    if (line.match(/^(void|bundle)\s+(\w+)\s*\(/)) {
      // 创建函数节点
    }
    
    // 检测宏定义
    else if (line.match(/^#define\s+(\w+)/)) {
      // 创建宏节点
    }
    
    // 检测函数调用
    else if (line.match(/(\w+)\s*\([^)]*\)/)) {
      // 创建调用节点
    }
  }
  
  return {
    name: '',
    type: 'program',
    text: content,
    children: children,
    location: { start: { line: 0, column: 0 }, end: { line: lines.length - 1, column: 0 } }
  };
}
```

### 3. 正则表达式规则
- **函数定义**：`/^(void|bundle)\s+(\w+)\s*\(/`
- **宏定义**：`/^#define\s+(\w+)/`
- **函数调用**：`/(\w+)\s*\([^)]*\)/`
- **宏识别**：大写字母且长度大于1的函数名

## 上下文管理

### 1. 当前函数跟踪
```typescript
private currentFunction: string | null = null;
```

### 2. 上下文设置
- 在函数定义时设置 `currentFunction`
- 在 Bundle 定义时设置 `currentFunction`
- 在宏定义时设置 `currentFunction`

### 3. 上下文使用
- 函数调用时记录调用者信息
- 宏调用时记录调用者信息
- 提供调用链分析的基础数据

## 集成与使用

### 1. ParserManager 集成
```typescript
// 在 ParserManager 中注册
this.parserPromises.set('np', NPParser.create());

// 文件类型映射
FILE_TYPE_MAP['.asm'] = 'np';
FILE_TYPE_MAP['.np'] = 'np';
```

### 2. 使用示例
```typescript
// 创建解析器
const parser = await NPParser.create();

// 解析代码
const ast = parser.parse(npCode);

// 查找函数
const functionNode = findNodeByName(ast, 'main');
```

### 3. 错误处理
```typescript
try {
  const ast = parser.parse(content);
  return ast;
} catch (error) {
  console.warn('NPParser解析失败，使用mock模式:', error);
  return this.createMockAST(content);
}
```

## 测试覆盖

### 1. 测试用例
- **Bundle 函数识别**：测试 bundle 定义和声明
- **宏定义识别**：测试宏定义和宏调用
- **函数调用识别**：测试各种函数调用场景
- **上下文管理**：测试当前函数上下文设置

### 2. 测试数据
```np
// Bundle 函数测试
bundle add(a, b): {
    result = a + b;
    return result;
}

// 宏定义测试
#define SQUARE(x) ((x)*(x))

// 函数调用测试
void main() {
    print(MSG);
    add(var1, var2);
    SQUARE(va1,var2)
}
```

### 3. 测试结果
- ✅ Bundle 函数正确识别
- ✅ 宏定义和调用正确识别
- ✅ 函数调用正确识别
- ✅ 上下文管理正常工作

## 性能优化

### 1. 异步初始化
- 使用 Promise 进行异步初始化
- 避免阻塞主线程
- 支持并发创建多个解析器实例

### 2. 缓存机制
- 解析器实例缓存
- 避免重复初始化
- 提高响应速度

### 3. 错误恢复
- 自动降级到 Mock 模式
- 保证解析功能的可用性
- 提供基本的语法分析能力

## 扩展性

### 1. 语言特性扩展
- 支持新的语法结构
- 添加新的节点类型
- 扩展元数据信息

### 2. 解析策略扩展
- 支持不同的解析模式
- 添加自定义解析规则
- 支持语言特定的优化

### 3. 集成扩展
- 支持更多开发工具
- 添加代码分析功能
- 支持代码生成功能

## 最佳实践

### 1. 错误处理
- 始终检查解析器初始化结果
- 提供降级方案
- 记录详细的错误信息

### 2. 性能考虑
- 使用异步初始化
- 实现缓存机制
- 避免重复解析

### 3. 代码质量
- 提供完整的测试覆盖
- 使用类型安全的接口
- 遵循统一的代码风格

## 总结

NP Parser 是一个功能完整的 NP 语言解析器，具有以下特点：

### 优势
- ✅ **完整的语言支持**：支持 NP 语言的所有主要语法特性
- ✅ **健壮的错误处理**：自动降级到 Mock 模式
- ✅ **高性能**：基于 Tree-sitter 的高效解析
- ✅ **易于扩展**：模块化设计，易于添加新功能
- ✅ **良好的测试覆盖**：完整的单元测试

### 应用场景
- **代码分析**：分析 NP 代码结构和依赖关系
- **智能补全**：提供基于语法的代码补全
- **错误检测**：检测语法错误和潜在问题
- **代码重构**：支持代码重构和优化

### 技术栈
- **Tree-sitter**：语法解析引擎
- **TypeScript**：类型安全的实现
- **WASM**：高性能的语法分析
- **正则表达式**：降级模式的文本分析

NP Parser 为 NP 语言的开发工具链提供了强大的语法分析能力，是项目中代码理解和分析的重要组成部分。 