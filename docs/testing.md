# 测试规范

## 目录结构

本项目采用独立的 `tests` 目录来组织所有测试文件，与源码目录 `src` 保持对应的结构关系。

### 目录映射

```
tests/
├── commands/                    # 对应 src/commands/
│   ├── register.coreflow.test.ts
│   └── register.extractSFTData.test.ts
├── common/                      # 对应 src/common/
│   ├── config/                  # 对应 src/common/config/
│   │   └── ConfigurationManager.test.ts
│   └── log/                     # 对应 src/common/log/
│       └── logger.test.ts
├── components/                  # 对应 src/components/
│   ├── code_completion/         # 对应 src/components/code_completion/
│   │   ├── completionPrompt.test.ts
│   │   ├── providers/           # 对应 src/components/code_completion/providers/
│   │   │   ├── inlineCompletionProvider.test.ts
│   │   │   └── windows-line-endings.test.ts
│   │   ├── status/              # 对应 src/components/code_completion/status/
│   │   │   ├── CompletionEventManager.test.ts
│   │   │   ├── CompletionStatusBar.test.ts
│   │   │   └── CompletionStatusManager.test.ts
│   │   └── utils/               # 对应 src/components/code_completion/utils/
│   │       ├── environmentBuilder.test.ts
│   │       └── logging.test.ts
│   ├── code_context/            # 对应 src/components/code_context/
│   │   ├── collect.test.ts
│   │   ├── ContextCollector.test.ts
│   │   ├── ContextFusionEngine.test.ts
│   │   ├── EmbeddingContextProviderImpl.test.ts
│   │   ├── GraphContextProviderImpl.test.ts
│   │   ├── integration.test.ts
│   │   ├── KeywordContextProviderImpl.test.ts
│   │   ├── ProviderImpls.test.ts
│   │   └── types.test.ts
│   ├── code_graph/              # 对应 src/components/code_graph/
│   │   ├── CodeGraphBuilder.test.ts
│   │   ├── commands.test.ts
│   │   ├── indexToCompletion.test.ts
│   │   ├── languageSupport.test.ts
│   │   ├── ParserManager.test.ts
│   │   ├── parser/              # 对应 src/components/code_graph/parser/
│   │   │   └── NPParser.test.ts
│   │   └── repoIndexer/         # 对应 src/components/code_graph/repoIndexer/
│   │       ├── repoIndexer.test.ts
│   │       └── repoIndexerManager.test.ts
│   ├── data_extraction/         # 对应 src/components/data_extraction/
│   │   ├── ASTStatementLevelExtractionStrategy.test.ts
│   │   ├── DataExtractor.test.ts
│   │   ├── FunctionLevelExtractionStrategy.test.ts
│   │   ├── FunctionWithContextExtractionStrategy.test.ts
│   │   ├── integration.test.ts

│   ├── statistics/              # 对应 src/components/statistics/
│   │   └── StatisticsService.test.ts
│   ├── types/                   # 对应 src/components/types/
│   │   ├── configuration.test.ts
│   │   ├── import-export.test.ts
│   │   ├── integration.test.ts
│   │   └── types.test.ts
│   └── user/                    # 对应 src/components/user/
│       └── UserService.test.ts
├── extension.coreflow.test.ts   # 对应 src/extension.ts
└── extension.test.ts            # 对应 src/extension.ts
```

## 命名规范

### 测试文件命名

- 测试文件必须以 `.test.ts` 或 `.test.js` 结尾
- 测试文件名应该与被测试的源文件对应
- 例如：`src/components/logger.ts` → `tests/components/logger.test.ts`

### 测试用例命名

- 使用中文描述测试用例的功能
- 使用 `describe` 块组织相关的测试用例
- 使用 `test` 或 `it` 定义具体的测试用例

```typescript
describe('Logger', () => {
  describe('log', () => {
    test('应该记录日志消息', () => {
      // 测试实现
    });
    
    test('应该处理空消息', () => {
      // 测试实现
    });
  });
});
```

## 导入路径规范

### 从测试文件导入源码

由于测试文件现在位于独立的 `tests` 目录中，需要使用绝对路径导入源码：

```typescript
// ✅ 正确：使用绝对路径导入
import { Logger } from '../../../src/common/log/logger';
import { ConfigurationManager } from '../../../src/common/config/ConfigurationManager';

// ❌ 错误：使用相对路径导入
import { Logger } from '../logger';
import { ConfigurationManager } from '../ConfigurationManager';
```

### 导入路径计算

根据测试文件在 `tests` 目录中的位置，计算到 `src` 目录的相对路径：

- `tests/common/log/logger.test.ts` → `../../../src/common/log/logger`
- `tests/components/code_completion/completionPrompt.test.ts` → `../../../src/components/code_completion/completionPrompt`
- `tests/extension.test.ts` → `../src/extension`

## 测试工具和配置

### Jest 配置

Jest 配置文件 `jest.config.js` 已更新为：

```javascript
module.exports = {
  testMatch: [
    '**/tests/**/*.test.ts',
    '**/tests/**/*.test.js'
  ],
  // ... 其他配置
};
```

### VSCode 测试配置

VSCode 的测试资源管理器会自动识别 `tests` 目录中的测试文件，无需额外配置。

### 运行测试

```bash
# 运行所有测试
npm test

# 运行特定测试文件
npx jest tests/common/log/logger.test.ts

# 运行特定目录的测试
npx jest tests/components/code_completion/

# 监听模式
npm run test:watch
```

## 测试最佳实践

### 1. 测试隔离

- 每个测试用例应该是独立的
- 使用 `beforeEach` 和 `afterEach` 清理测试状态
- 避免测试用例之间的依赖

### 2. Mock 使用

- 对于外部依赖（如 VSCode API），使用 Jest 的 mock 功能
- 在 `jest.setup.js` 中配置全局 mock

### 3. 测试覆盖率

- 确保核心功能有足够的测试覆盖率
- 使用 `npm run test:coverage` 查看覆盖率报告



### 5. 集成测试

- 对于复杂的组件交互，编写集成测试
- 使用 `integration.test.ts` 命名集成测试文件

## 迁移指南

### 从旧目录结构迁移

如果要将测试文件从 `src/**/__tests__/` 迁移到 `tests/` 目录：

1. 创建对应的 `tests` 目录结构
2. 移动测试文件到新位置
3. 更新导入路径为绝对路径
4. 更新 Jest 配置
5. 验证测试正常运行

### 自动化迁移脚本

项目提供了自动化迁移脚本：

```bash
# 修复导入路径
node scripts/fix-all-test-imports.js
```

## 注意事项

1. **导入路径**：确保所有测试文件都使用正确的绝对路径导入源码
2. **文件同步**：当添加新的源码文件时，记得在对应的 `tests` 目录中创建测试文件
3. **目录结构**：保持 `tests` 目录结构与 `src` 目录结构的一致性
4. **命名一致性**：测试文件名应该与源码文件名对应

## 常见问题

### Q: 为什么测试文件无法找到源码模块？

A: 检查导入路径是否正确。测试文件现在位于独立的 `tests` 目录中，需要使用绝对路径导入源码。

### Q: 如何添加新的测试文件？

A: 在 `tests` 目录中创建与源码对应的目录结构，然后添加测试文件。记得使用正确的导入路径。

### Q: VSCode 测试资源管理器没有显示测试文件？

A: 确保 Jest 配置正确，并且测试文件位于 `tests` 目录中。重启 VSCode 或重新加载窗口。 