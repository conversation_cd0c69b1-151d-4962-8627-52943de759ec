# 代码图构建流程图

## 构建顺序概览

```mermaid
graph TD
    A[源代码文件] --> B[AST解析]
    B --> C[CodeGraphBuilder]
    C --> D[第一阶段：数据收集]
    D --> E[第二阶段：关系构建]
    E --> F[代码图输出]
    
    D --> D1[收集函数定义]
    D --> D2[收集模块导入]
    D --> D3[创建模块节点]
    
    E --> E1[构建调用关系]
    E --> E2[建立模块关系]
    E --> E3[处理跨模块调用]
```

## 增量索引架构

### 统一文件监听系统

```mermaid
graph TD
    A[文件变更事件] --> B[RepoIndexerManager.handleFileChange]
    B --> C{检查配置}
    C -->|enableIndexing=false| D[跳过处理]
    C -->|autoIndexing=false| D
    C -->|配置有效| E[triggerIndexing]
    E --> F[RepoIndexer.analyze]
    F --> G{incremental=true?}
    G -->|是| H[incrementalAnalyze]
    G -->|否| I[fullAnalyze]
    
    H --> H1[getFileChanges]
    H1 --> H2[handleFileRemoved]
    H1 --> H3[handleFileModified]
    H1 --> H4[handleFileAdded]
    
    I --> I1[parseAllFiles]
    I1 --> I2[buildUnifiedGraph]
```

### 增量索引处理流程

```mermaid
graph TD
    A[文件变更检测] --> B[获取文件变更列表]
    B --> C{变更类型}
    C -->|新增文件| D[handleFileAdded]
    C -->|删除文件| E[handleFileRemoved]
    C -->|修改文件| F[handleFileModified]
    
    D --> D1[解析新文件AST]
    D1 --> D2[构建文件节点和边]
    D2 --> D3[建立跨文件引用]
    
    E --> E1[删除文件相关节点]
    E1 --> E2[删除文件相关边]
    
    F --> F1[删除旧节点和边]
    F1 --> F2[重新解析文件]
    F2 --> F3[重建节点和边]
    F3 --> F4[重建跨文件引用]
```

## 详细构建流程

### 第一阶段：数据收集阶段

```mermaid
graph TD
    A[输入AST] --> B[创建CodeModule]
    B --> C[构建节点映射]
    C --> D[添加模块入口节点]
    D --> E[遍历AST收集函数定义]
    E --> F[遍历AST收集模块导入]
    F --> G[添加到全局函数索引]
    G --> H[记录到模块导入映射]
    H --> I[第一阶段完成]
    
    E --> E1[识别函数定义]
    E --> E2[识别宏定义]
    E --> E3[识别bundle定义]
    
    F --> F1[识别头文件包含]
    F --> F2[识别模块导入]
    F --> F3[识别import语句]
```

**关键决策点**：
- 函数定义 vs 函数调用：通过 `node.metadata?.isCall` 区分
- 宏定义 vs 普通函数：通过 `node.metadata?.type === 'macro'` 识别
- 模块导入 vs 其他节点：通过 `node.kind === 'module_import'` 识别
- 递归处理：对所有子节点递归执行收集逻辑

### 第二阶段：关系构建阶段

```mermaid
graph TD
    A[第一阶段完成] --> B[遍历所有AST构建调用关系]
    B --> C[处理函数定义内的调用]
    C --> D[处理模块顶层的调用]
    D --> E[通过全局图判断宏调用]
    E --> F[查找被调用函数]
    F --> G[建立调用边]
    G --> H[更新节点元数据]
    H --> I[第二阶段完成]
    
    F --> F1[当前模块查找]
    F --> F2[导入模块查找]
    F --> F3[递归查找]
    
    E --> E1[isMacroCallByGlobalIndex]
    E --> E2[检查全局函数索引]
    E --> E3[更新type和isMacroCall]
```

**关键决策点**：
- 调用关系建立：通过 `findFunctionNode` 查找目标函数
- 跨模块查找：优先当前模块 → 直接导入模块 → 递归导入模块
- 宏调用识别：通过 `isMacroCallByGlobalIndex` 基于全局图判断
- 循环依赖处理：使用 `visitedModules` 防止无限递归

## 宏调用识别机制

### 改进后的宏识别流程

```mermaid
graph TD
    A[Parser阶段] --> B[识别call_expression]
    B --> C[标记为普通函数调用]
    C --> D[CodeGraphBuilder第一阶段]
    D --> E[收集宏定义到全局索引]
    E --> F[CodeGraphBuilder第二阶段]
    F --> G[isMacroCallByGlobalIndex]
    G --> H{全局索引中有宏定义?}
    H -->|是| I[标记为宏调用]
    H -->|否| J[保持为函数调用]
    I --> K[更新节点元数据]
    J --> L[建立调用关系]
    K --> L
```

**关键改进**：
- **移除正则表达式**：不再使用命名模式判断宏调用
- **基于全局图**：通过全局函数索引判断是否为宏调用
- **延迟识别**：在关系构建阶段判断，支持宏定义在调用之后
- **准确识别**：基于实际的宏定义而不是命名约定

### 宏识别代码实现

```typescript
// Parser阶段：不判断宏调用
private processFunctionCall(node: Node): ASTNode {
  const astNode = this.nodeFactory.createFunctionCall(
    functionName,
    args,
    {
      caller: this.currentFunction,
      type: 'function',  // 默认为function
      isCall: true,
      isMacroCall: false  // 默认为false
    }
  );
}

// CodeGraphBuilder阶段：通过全局图判断
private isMacroCallByGlobalIndex(functionName: string): boolean {
  const functionEntries = this.globalFunctionIndex.get(functionName);
  if (!functionEntries || functionEntries.length === 0) {
    return false;
  }
  
  // 检查全局索引中是否有对应的宏定义
  for (const entry of functionEntries) {
    const node = this.graph.nodes.get(entry.nodeId);
    if (node && node.metadata && node.metadata.type === 'macro') {
      return true;
    }
  }
  
  return false;
}
```

## 架构优化：消除重复逻辑

### 重构前的重复问题

```mermaid
graph TD
    A[文件变更] --> B[RepoIndexerManager.handleFileChange]
    A --> C[CommandRegister文件监听器]
    
    B --> D[triggerIndexing]
    C --> E[CodeGraphCommands.updateIndexForFiles]
    
    D --> F[RepoIndexer.analyze]
    E --> G[RepoIndexerManager.updateIndexForFiles]
    
    F --> H[incrementalAnalyze]
    G --> I[手动文件处理]
    
    H --> J[getFileChanges + 处理]
    I --> K[indexFile + removeFileFromIndex]
    
    J --> L[重复的文件处理逻辑]
    K --> L
```

### 重构后的统一架构

```mermaid
graph TD
    A[文件变更] --> B[RepoIndexerManager.handleFileChange]
    B --> C[triggerIndexing]
    C --> D[RepoIndexer.analyze]
    D --> E[incrementalAnalyze]
    E --> F[统一文件处理逻辑]
    
    G[手动触发] --> H[CodeGraphCommands.updateIndexForFiles]
    H --> I[RepoIndexerManager.updateIndexForFiles]
    I --> D
```

**优化效果**：
- ✅ 消除重复的文件监听器
- ✅ 统一增量索引逻辑
- ✅ 简化调用链路
- ✅ 提升性能和可维护性

## 数据依赖关系

### 构建顺序的重要性

```mermaid
graph LR
    A[函数定义收集] --> B[全局函数索引]
    C[模块导入收集] --> D[模块导入映射]
    B --> E[调用关系构建]
    D --> E
    E --> F[跨模块函数查找]
    B --> G[宏调用识别]
    G --> E
```

**为什么顺序很重要**：

1. **函数定义必须在调用关系构建之前收集**
   - 原因：调用关系构建需要查找被调用函数
   - 如果函数定义未收集，无法找到目标函数
   - 结果：调用关系丢失

2. **模块导入必须在模块添加之前收集**
   - 原因：模块添加时需要创建导入边
   - 如果导入关系未收集，无法建立模块间的边
   - 结果：模块依赖关系丢失

3. **宏定义必须在宏调用识别之前收集**
   - 原因：宏调用识别需要检查全局函数索引
   - 如果宏定义未收集，无法正确识别宏调用
   - 结果：宏调用被误判为普通函数调用

4. **所有数据收集必须在关系构建之前完成**
   - 原因：关系构建依赖所有前置数据
   - 如果数据不完整，关系构建会失败
   - 结果：图结构不完整

## 增量索引性能优化

### 索引间隔控制

```mermaid
graph TD
    A[文件变更事件] --> B[检查时间间隔]
    B --> C{距离上次索引 < 5分钟?}
    C -->|是| D[跳过本次索引]
    C -->|否| E[执行增量索引]
    E --> F[更新lastIndexTime]
```

**性能优化策略**：
- **默认间隔**：5分钟（`INCREMENTAL_UPDATE_INTERVAL = 5 * 60 * 1000`）
- **防抖动**：避免频繁文件变更导致的重复索引
- **配置化**：可通过配置文件调整索引行为

### 索引状态管理

```mermaid
graph TD
    A[索引开始] --> B[设置状态为indexing]
    B --> C[执行索引逻辑]
    C --> D{索引成功?}
    D -->|是| E[设置状态为success]
    D -->|否| F[设置状态为error]
    E --> G[保存索引数据]
    F --> H[记录错误日志]
```

## 关键数据结构的时间线

```mermaid
gantt
    title 数据结构构建时间线
    dateFormat X
    axisFormat %s
    
    section 全局函数索引
    函数定义收集    :done, index1, 0, 10
    宏定义收集      :done, macro1, 5, 15
    索引建立        :done, index2, 10, 20
    
    section 模块导入映射
    导入关系收集    :done, import1, 0, 10
    映射建立        :done, import2, 5, 15
    
    section 模块集合
    模块创建        :done, module1, 0, 5
    模块添加        :done, module2, 15, 20
    
    section 调用关系
    关系构建        :done, relation1, 20, 30
    宏调用识别      :done, macro2, 25, 35
    跨模块处理      :done, relation2, 30, 40
    
    section 增量索引
    文件变更检测    :active, incremental1, 40, 45
    增量处理        :active, incremental2, 45, 50
```

## 错误处理检查点

### 1. 数据完整性检查

```mermaid
graph TD
    A[构建开始] --> B{函数定义收集完成?}
    B -->|否| C[错误：函数定义缺失]
    B -->|是| D{宏定义收集完成?}
    D -->|否| E[错误：宏定义缺失]
    D -->|是| F{模块导入收集完成?}
    F -->|否| G[错误：导入关系缺失]
    F -->|是| H{全局索引建立完成?}
    H -->|否| I[错误：索引不完整]
    H -->|是| J[进入第二阶段]
    
    J --> K{调用关系构建完成?}
    K -->|否| L[错误：调用关系缺失]
    K -->|是| M{宏调用识别完成?}
    M -->|否| N[错误：宏调用识别失败]
    M -->|是| O{跨模块关系处理完成?}
    O -->|否| P[错误：跨模块关系不完整]
    O -->|是| Q[构建完成]
```

### 2. 增量索引错误处理

```mermaid
graph TD
    A[增量索引开始] --> B{RepoIndexer已初始化?}
    B -->|否| C[自动执行全量索引]
    B -->|是| D[获取文件变更]
    D --> E{有文件变更?}
    E -->|否| F[跳过索引]
    E -->|是| G[处理文件变更]
    G --> H{处理成功?}
    H -->|否| I[记录错误，继续处理其他文件]
    H -->|是| J[更新索引数据]
    I --> K{还有其他文件?}
    K -->|是| G
    K -->|否| L[索引完成]
    J --> L
```

### 3. 关键验证点

| 检查点 | 验证内容 | 失败后果 |
|--------|----------|----------|
| 函数定义收集 | 所有函数是否已添加到全局索引 | 调用关系无法建立 |
| 宏定义收集 | 所有宏定义是否已添加到全局索引 | 宏调用识别失败 |
| 模块导入收集 | 所有导入关系是否已记录 | 跨模块调用失败 |
| 模块节点创建 | 所有模块节点是否已创建 | 模块关系边无法添加 |
| 调用关系构建 | 所有调用边是否已建立 | 调用链查询失败 |
| 宏调用识别 | 宏调用是否正确识别 | 宏调用被误判为函数调用 |
| 跨模块处理 | 递归查找是否正常工作 | 复杂依赖关系丢失 |
| 增量索引 | 文件变更是否正确处理 | 索引数据过期 |
| 索引间隔 | 时间间隔控制是否生效 | 性能问题 |

## 配置管理

### 索引配置选项

```json
{
  "enableIndexing": true,      // 是否启用索引功能
  "autoIndexing": true,        // 是否启用自动索引
  "languageExts": ["*"],       // 支持的语言扩展名
  "rules": [                   // 索引规则
    {
      "type": "include",
      "pattern": "**/*.{asm,c,h}"
    }
  ]
}
```

### 配置影响

| 配置项 | 全量索引 | 增量索引 | 文件监听 |
|--------|----------|----------|----------|
| `enableIndexing: false` | ❌ 禁用 | ❌ 禁用 | ❌ 禁用 |
| `enableIndexing: true, autoIndexing: false` | ✅ 手动触发 | ✅ 手动触发 | ❌ 禁用 |
| `enableIndexing: true, autoIndexing: true` | ✅ 自动执行 | ✅ 自动执行 | ✅ 启用 |

## 最新实现特性

### 1. 状态重置机制

```typescript
reset(): void {
  console.log('[CodeGraphBuilder] 重置状态...');
  this.graph = new GraphlibCodeGraph();
  this.modules.clear();
  this.nodeMap.clear();
  this.globalFunctionIndex.clear();
  this.moduleImports.clear();
  console.log('[CodeGraphBuilder] 状态重置完成');
}
```

**作用**：确保每次构建前状态干净，避免异步并发导致的数据混乱

### 2. 两阶段分离设计

```typescript
// 第一阶段：收集所有数据
for (const {ast, filePath} of asts) {
  this.collectFunctionDefinitions(ast, module);
  this.collectModuleImports(ast, module);
}

// 第二阶段：构建关系
for (const {ast, filePath} of asts) {
  this.buildCallRelationships(ast, module, moduleEntryId);
}
```

**优势**：数据收集与关系构建分离，确保所有数据都收集完毕后再构建关系

### 3. 并发处理设计

```typescript
// WorkerPool并发解析
const workerResults = await this.workerPool!.parseFiles(workerTasks);

// 统一构建图谱
this.buildUnifiedGraph(allAsts);
```

**优势**：文件解析阶段支持并发，提高构建效率

### 4. 宏调用识别改进

```typescript
// 通过全局图判断宏调用
const isMacroCall = this.isMacroCallByGlobalIndex(node.name);

// 更新节点元数据
if (isMacroCall) {
  node.metadata = {
    ...node.metadata,
    type: 'macro',
    isMacroCall: true
  };
}
```

**优势**：基于实际宏定义而不是命名模式，提高识别准确性 