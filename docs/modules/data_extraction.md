# 数据提取模块设计与用法

## 1. 设计目标
- 支持多种数据提取策略，便于从代码仓库抽取结构化样本用于SFT训练
- 输出统一的数据结构，便于下游训练和评估
- 支持多种编程语言：C、NP、ASM等
- 提供工作区级别的批量数据提取功能

## 2. 核心数据结构

```ts
export interface SFTSample {
  id: string; // 唯一标识
  file: string; // 文件路径
  functionName?: string; // 函数名（可选）
  language: string; // 编程语言
  expected: string; // 目标代码片段（如当前语句或函数）
  above_cursor: string; // 光标上方代码
  below_cursor: string; // 光标下方代码
  contexts?: string[]; // 检索到的上下文（可选）
  meta?: Record<string, any>; // 其他元信息
}
```

## 3. 数据提取器实现

### 3.1 DataExtractor 主类

```ts
export class DataExtractor {
  private statementStrategy: ASTStatementLevelExtractionStrategy;
  private functionStrategy: FunctionLevelExtractionStrategy;
  private functionWithContextStrategy: FunctionWithContextExtractionStrategy;

  constructor() {
    // 初始化所有策略实例
    this.statementStrategy = new ASTStatementLevelExtractionStrategy();
    this.functionStrategy = new FunctionLevelExtractionStrategy();
    this.functionWithContextStrategy = new FunctionWithContextExtractionStrategy();
  }
}
```

### 3.2 支持的提取策略

#### 语句级提取 (`statement`)
- **实现类**: `ASTStatementLevelExtractionStrategy`
- **功能**: 解析函数AST，遍历每条语句，按光标位置切分代码
- **适用场景**: 细粒度SFT训练
- **支持语言**: C、NP、ASM等
- **输出**: 每个语句作为一个样本，包含语句类型、位置等元信息

#### 函数级提取 (`function`)
- **实现类**: `FunctionLevelExtractionStrategy`
- **功能**: 以函数为单位提取完整函数体
- **适用场景**: 函数级SFT训练或代码生成
- **输出**: 每个函数作为一个样本

#### 函数+上下文提取 (`function_with_context`)
- **实现类**: `FunctionWithContextExtractionStrategy`
- **功能**: 以函数为单位，结合上下文检索系统获取相关代码片段
- **适用场景**: 上下文增强型SFT训练
- **上下文类型**:
  - `above_cursor`: 函数上方的代码（最多10行）
  - `below_cursor`: 函数下方的代码（最多10行）
  - `contexts`: 通过图分析、关键词、嵌入等方式检索的相关代码

## 4. 核心方法

### 4.1 单文件提取

```ts
async extractSFTData(
  filePath: string, 
  strategy: 'statement' | 'function' | 'function_with_context' = 'statement'
): Promise<SFTSample[]>
```

### 4.2 全策略提取

```ts
async extractAllStrategies(filePath: string): Promise<{
  statement: SFTSample[];
  function: SFTSample[];
  function_with_context: SFTSample[];
}>
```

### 4.3 工作区批量提取

```ts
async extractSFTDataFromWorkspace(
  strategy: 'statement' | 'function' | 'function_with_context' = 'statement'
): Promise<SFTSample[]>
```
- **功能**: 扫描整个工作区，提取所有支持文件的数据
- **文件类型**: `**/*.{c,np,asm}`
- **排除**: `**/node_modules/**`

## 5. 典型用法

### 5.1 单文件提取示例

```ts
const extractor = new DataExtractor();

// 语句级提取
const statementSamples = await extractor.extractSFTData('example.c', 'statement');

// 函数级提取
const functionSamples = await extractor.extractSFTData('example.c', 'function');

// 函数+上下文提取
const contextSamples = await extractor.extractSFTData('example.c', 'function_with_context');
```

### 5.2 全策略对比提取

```ts
const extractor = new DataExtractor();
const allResults = await extractor.extractAllStrategies('example.c');

console.log('语句级样本数:', allResults.statement.length);
console.log('函数级样本数:', allResults.function.length);
console.log('上下文增强样本数:', allResults.function_with_context.length);
```

### 5.3 工作区批量提取

```ts
const extractor = new DataExtractor();

// 提取整个工作区的语句级数据
const workspaceSamples = await extractor.extractSFTDataFromWorkspace('statement');
console.log(`从工作区提取了 ${workspaceSamples.length} 个样本`);
```

## 6. 语言支持

### 6.1 支持的文件扩展名
- `.c` - C语言
- `.np` - NP语言  
- `.asm` - 汇编语言

### 6.2 语言特定的语句类型

#### C语言
- `expression_statement`, `declaration`, `if_statement`
- `for_statement`, `while_statement`, `do_statement`
- `switch_statement`, `case_statement`, `return_statement`
- `break_statement`, `continue_statement`, `goto_statement`
- `labeled_statement`, `compound_statement`

#### NP语言
- `statement`, `statement_function_def`, `statement_function_call`
- `statement_if`, `statement_for`, `statement_while`
- `statement_return`, `statement_break`, `statement_continue`

## 7. 错误处理与健壮性

- **文件读取失败**: 跳过该文件并记录警告
- **语言不支持**: 返回空数组并记录警告
- **AST解析失败**: 返回空数组并记录错误
- **工作区扫描**: 单个文件失败不影响整体提取过程

## 8. 性能优化

- **并发提取**: `extractAllStrategies` 使用 `Promise.all` 并发执行
- **内存管理**: 大文件分块处理，避免内存溢出
- **缓存机制**: AST解析结果可复用于多种策略

## 9. 扩展建议

- **新增语言支持**: 在 `ParserManager` 中添加新的解析器
- **新增策略**: 实现类似的 `extractSamples(filePath: string)` 方法
- **上下文优化**: 调整 `FunctionWithContextExtractionStrategy` 的权重配置
- **批量优化**: 支持更细粒度的文件过滤和并发控制

## 10. 测试覆盖

每种策略都有对应的单元测试和集成测试：
- `ASTStatementLevelExtractionStrategy.test.ts`
- `FunctionLevelExtractionStrategy.test.ts` 
- `FunctionWithContextExtractionStrategy.test.ts`
- `DataExtractor.test.ts`
- `integration.test.ts`
 