# 用户自定义片段集成重构总结

## 重构目标

根据用户需求，将用户自定义的 snippets 从直接添加到提示词的方式，改为集成到 `similar_context` 的统一流程管理中，通过配置系数来调整其优先级。

## 重构内容

### 1. 类型定义扩展

#### 扩展 ContextItem 类型
```typescript
export type ContextItem = {
  source: 'graph' | 'bm25' | 'embedding' | 'user-snippets';  // 新增 user-snippets 源
  contextType: 'related' | 'similar';
  score: number;
  content: string;
  location?: SourceLocation;
  // 新增：用户自定义内容相关字段
  userSnippet?: {
    id: string;
    name: string;
    priority: number;
    similarity: number;
  };
};
```

#### 新增 UserCustomContextProvider 接口
```typescript
export interface UserCustomContextProvider extends ContextProvider {
  /**
   * 获取匹配的用户自定义代码片段
   */
  searchByUserSnippets(code: string, language: string): Promise<ContextItem[]>;
}
```

### 2. 新增组件实现

#### UserCustomContextProviderImpl
- 实现用户自定义片段的搜索和匹配
- 将匹配结果转换为标准的 ContextItem 格式
- 集成到统一的上下文收集流程中

#### 扩展 ContextFusionEngine
```typescript
export interface ExtendedRetrievalFusionConfig extends RetrievalFusionConfig {
  userSnippetsWeight?: number;  // 用户自定义片段权重
  userSnippetsPriorityMultiplier?: number;  // 用户片段优先级倍数
}
```

- 支持用户自定义片段的权重配置
- 应用优先级倍数来调整用户片段的得分
- 在融合过程中与其他上下文源进行统一处理

### 3. 上下文收集流程重构

#### ContextCollector 更新
- 集成 UserCustomContextProvider
- 在 `collectSimilarContext` 方法中包含用户自定义片段
- 支持配置化的权重调整

#### 配置管理
```typescript
export const DEFAULT_CONTEXT_FUSION_CONFIG: ExtendedRetrievalFusionConfig = {
  graphWeight: 0.4,           // 图谱相似度权重
  bm25Weight: 0.3,            // BM25关键词搜索权重
  embeddingWeight: 0.3,       // 向量搜索权重
  userSnippetsWeight: 0.2,    // 用户片段基础权重
  userSnippetsPriorityMultiplier: 1.5  // 用户片段优先级倍数
};
```

### 4. 代码补全流程更新

#### 提示词构建重构
- 移除直接添加用户片段到提示词的逻辑
- 用户片段现在通过 `similar_context` 统一管理
- 保留用户规则的直接添加（规则仍然直接添加到提示词中）

#### 上下文构建更新
```typescript
// 获取用户自定义片段（通过统一流程管理）
const userSnippetsItems = await collectContextForCompletion(currentCode, position);

// 将用户自定义片段添加到similar_context中
if (userSnippetsItems && userSnippetsItems.length > 0) {
    for (const userSnippetItem of userSnippetsItems) {
        if (userSnippetItem.source === 'user-snippets') {
            const userSnippet = {
                label: `[用户片段] ${userSnippetItem.userSnippet?.name || '未命名'}`,
                content: userSnippetItem.content,
                contextType: 'similar',
                fileName: '用户自定义片段',
                score: userSnippetItem.score,
                userSnippet: userSnippetItem.userSnippet
            };
            contextSnippets.push(userSnippet);
        }
    }
}
```

## 架构优势

### 1. 统一管理
- 用户自定义片段与其他相似上下文（图谱相似函数、BM25搜索结果、向量搜索结果）统一管理
- 通过相同的权重计算和排序机制进行处理
- 避免重复和冲突

### 2. 配置化优先级
- 通过 `userSnippetsWeight` 控制用户片段的基础权重
- 通过 `userSnippetsPriorityMultiplier` 控制优先级倍数
- 支持不同场景下的优先级调整

### 3. 可扩展性
- 新增的 Provider 接口便于扩展其他类型的用户自定义内容
- 配置化的权重系统便于调整和优化
- 统一的 ContextItem 格式便于处理

### 4. 性能优化
- 用户片段搜索与其他上下文搜索并行进行
- 统一的去重和排序机制
- 避免重复计算和内存浪费

## 配置选项

### 默认配置
```typescript
{
  userSnippetsWeight: 0.2,           // 基础权重 20%
  userSnippetsPriorityMultiplier: 1.5  // 优先级倍数 1.5x
}
```

### 高优先级配置
```typescript
{
  userSnippetsWeight: 0.4,           // 基础权重 40%
  userSnippetsPriorityMultiplier: 2.0  // 优先级倍数 2.0x
}
```

### 低优先级配置
```typescript
{
  userSnippetsWeight: 0.1,           // 基础权重 10%
  userSnippetsPriorityMultiplier: 1.2  // 优先级倍数 1.2x
}
```

## 使用流程

### 1. 用户创建自定义片段
- 通过管理面板创建代码片段
- 设置优先级、标签等信息

### 2. 代码补全触发
- 系统检测到代码补全需求
- 并行收集多种上下文（图谱、BM25、向量、用户片段）

### 3. 权重计算和融合
- 根据配置计算各上下文源的权重
- 用户片段应用优先级倍数
- 统一排序和去重

### 4. 上下文展示
- 用户片段以 `[用户片段]` 标签显示
- 与其他相似上下文一起展示
- 按得分排序

## 总结

这次重构成功实现了用户需求：

1. ✅ **统一管理**：用户自定义片段集成到 `similar_context` 统一流程
2. ✅ **配置化优先级**：通过权重和倍数配置调整优先级
3. ✅ **保持灵活性**：支持不同场景下的优先级调整
4. ✅ **架构优化**：更好的可扩展性和性能

用户自定义片段现在不再是独立的提示词部分，而是作为相似上下文的一部分，与其他自动搜索的结果一起进行权重计算和排序，实现了真正的统一管理。 