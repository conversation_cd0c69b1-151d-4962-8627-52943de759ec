# 项目设计文档（design.md）

> 本文档用于记录本项目的整体架构、模块划分、接口设计等内容，持续更新，保持与需求和实现同步。

## 总体架构

- 采用多Provider分层架构，分为：上下文收集、检索Provider（关键字/BM25、调用链、向量）、融合引擎、补全建议生成等层次。
- Provider接口定义于 `types.ts`，实现类分别在 `KeywordContextProviderImpl.ts`、`GraphContextProviderImpl.ts`、`EmbeddingContextProviderImpl.ts`。
- 所有Provider均为异步接口，便于扩展和集成。

## 模块设计

| 模块名称 | 功能描述 | 主要接口 | 依赖关系 | 备注 |
| -------- | -------- | -------- | -------- | ---- |

## 设计说明

### 1. 总体架构
- 采用多Provider分层架构，分为：上下文收集、检索Provider（关键字/BM25、调用链、向量）、融合引擎、补全建议生成等层次。
- Provider接口定义于 `types.ts`，实现类分别在 `KeywordContextProviderImpl.ts`、`GraphContextProviderImpl.ts`、`EmbeddingContextProviderImpl.ts`。
- 所有Provider均为异步接口，便于扩展和集成。

### 2. 代码图构建
- 详细的代码图构建逻辑和顺序请参考：[代码图构建逻辑文档](code-graph-construction.md)
- 构建流程图和关键决策点请参考：[代码图构建流程图](code-graph-construction-flow.md)
- 核心特性：两阶段构建、跨模块关系处理、递归函数查找、性能优化

### 2. Provider接口与实现

#### 2.1 关键字检索Provider
- 接口：`KeywordContextProvider`，方法：`searchByKeywords(code: string): Promise<ContextItem[]>`
- 实现：`KeywordContextProviderImpl`，BM25算法实现
- 单元测试：`__tests__/KeywordContextProviderImpl.test.ts`

#### 2.2 调用链检索Provider
- 接口：`GraphContextProvider`，方法：`getContextFromGraph(functionNode: ASTNode): Promise<ContextItem[]>`
- 实现：`GraphContextProviderImpl`，调用链分析实现
- 单元测试：`__tests__/GraphContextProviderImpl.test.ts`

#### 2.3 向量检索Provider
- 接口：`EmbeddingContextProvider`，方法：`searchByEmbedding(code: string): Promise<ContextItem[]>`
- 实现：`EmbeddingContextProviderImpl`，向量相似度检索实现（预留）
- 单元测试：`__tests__/EmbeddingContextProviderImpl.test.ts`

### 3. 主流程与融合
- `ContextCollector` 统一负责调用各Provider并融合结果，主入口为 `collectContext(astNode, code, config)`。
- 融合引擎 `ContextFusionEngine` 支持多策略加权融合，权重可配置。
- 相关类型定义于 `types.ts`，如 `ContextItem`、`RetrievalFusionConfig`。

### 3.1 增强的代码补全上下文检索（2024年更新）
- **核心函数**：`getCurrentFunctionContextDynamic()` 已升级为多维度上下文检索
- **检索能力**：
  - 图谱检索：基于调用链的相关函数分析
  - BM25关键词搜索：基于文本相似性的代码片段检索
  - 向量搜索：基于语义相似性的深度检索
  - 用户自定义片段：用户配置的代码模板和片段
- **智能融合**：使用 `ContextFusionEngine` 进行加权融合、去重和排序
- **配置化**：支持不同场景的融合配置，可调整各检索方式权重
- **返回格式**：提供丰富的上下文信息，包括来源标识、评分和位置信息

### 3.2 结构化提示词系统（2024年新增）
- **设计原则**：系统化、结构化、可迭代的提示词模版
- **多模型支持**：
  - DeepSeek Coder: `<｜fim▁begin｜>`, `<｜fim▁hole｜>`, `<｜fim▁end｜>`
  - Qwen Coder: `<|fim_prefix|>`, `<|fim_suffix|>`, `<|fim_middle|>`
- **提示词结构**：
  - 系统提示词：定义AI助手角色
  - 指令：明确补全任务和特殊token说明
  - 系统规则：结构化的补全规则
  - 环境信息：平台、语言、版本等上下文
  - 用户规则：个性化编码偏好
  - 动态上下文：相关上下文和相似上下文
  - 用户输入：使用模型原生支持的补全token
- **核心模块**：`src/components/code_completion/prompt/structuredPrompt.ts`
- **配置系统**：支持自定义系统规则、用户规则和模型token配置

### 4. 测试与可追溯性
- 每个Provider均有独立单元测试，覆盖正常、边界、异常场景。
- 集成测试覆盖主流程（见 `__tests__/integration.test.ts`）。
- 需求-设计-实现-测试全链路可追溯，详见下表：

| 需求编号 | 主要接口/实现 | 主要测试用例 |
|----------|----------------|-------------|
| 4.1      | KeywordContextProviderImpl.ts | KeywordContextProviderImpl.test.ts |
| 4.2      | GraphContextProviderImpl.ts   | GraphContextProviderImpl.test.ts   |
| 4.3      | EmbeddingContextProviderImpl.ts | EmbeddingContextProviderImpl.test.ts |
| 4.x      | types.ts (接口定义) | __tests__/ |

### 5. 规范与扩展性
- 所有接口、实现、测试均遵循AI Agent开发规范，便于团队协作和自动化追溯。
- 支持后续扩展更多检索Provider或融合策略。

### 4. 上下文融合检索模块设计

#### 4.1 关键字检索（BM25）
- 主要流程：文本分词、倒排索引、BM25打分
- 相关需求：4.1
- 接口：`KeywordContextProvider`，实现：`KeywordContextProviderImpl`，方法：`searchByKeywords(code: string): Promise<ContextItem[]>`
- 单元测试：`__tests__/KeywordContextProviderImpl.test.ts`

#### 4.2 调用链检索
- 主要流程：代码解析、调用关系图构建、链路遍历
- 相关需求：4.2
- 接口：`GraphContextProvider`，实现：`GraphContextProviderImpl`，方法：`getContextFromGraph(functionNode: ASTNode): Promise<ContextItem[]>`
- 单元测试：`__tests__/GraphContextProviderImpl.test.ts`

#### 4.3 向量检索
- 主要流程：代码片段向量化、向量索引、相似度检索
- 相关需求：4.3
- 接口：`EmbeddingContextProvider`，实现：`EmbeddingContextProviderImpl`，方法：`searchByEmbedding(code: string): Promise<ContextItem[]>`
- 单元测试：`__tests__/EmbeddingContextProviderImpl.test.ts`

##### 需求-实现映射表

| 需求编号 | 主要接口/实现 | 主要测试用例 |
|----------|----------------|-------------|
| 4.1      | KeywordContextProviderImpl.ts | KeywordContextProviderImpl.test.ts |
| 4.2      | GraphContextProviderImpl.ts   | GraphContextProviderImpl.test.ts   |
| 4.3      | EmbeddingContextProviderImpl.ts | EmbeddingContextProviderImpl.test.ts |
| 4.x      | types.ts (接口定义) | __tests__/ |

### 6. 数据提取与SFT训练

#### 6.1 目标
- 支持多种数据提取策略，便于从现有代码仓库抽取结构化样本用于SFT训练。
- 输出统一的数据结构，便于下游训练和评估。
- 具备良好的可扩展性和可维护性。

#### 6.2 核心数据结构

```ts
export interface SFTSample {
  id: string; // 唯一标识
  file: string;
  functionName?: string;
  language: string;
  expected: string; // 目标代码片段（如当前语句）
  above_cursor: string; // 光标上方代码
  below_cursor: string; // 光标下方代码
  contexts?: string[]; // 检索到的上下文（可选）
  meta?: Record<string, any>; // 其他元信息
}
```

#### 6.3 数据提取器架构

- 采用集成式架构，`DataExtractor` 类内部包含所有策略实例：

```ts
export class DataExtractor {
  private statementStrategy: ASTStatementLevelExtractionStrategy;
  private functionStrategy: FunctionLevelExtractionStrategy;
  private functionWithContextStrategy: FunctionWithContextExtractionStrategy;

  constructor() {
    this.statementStrategy = new ASTStatementLevelExtractionStrategy();
    this.functionStrategy = new FunctionLevelExtractionStrategy();
    this.functionWithContextStrategy = new FunctionWithContextExtractionStrategy();
  }

  async extractSFTData(filePath: string, strategy: 'statement' | 'function' | 'function_with_context'): Promise<SFTSample[]>;
  async extractAllStrategies(filePath: string): Promise<{statement: SFTSample[]; function: SFTSample[]; function_with_context: SFTSample[]}>;
  async extractSFTDataFromWorkspace(strategy: 'statement' | 'function' | 'function_with_context'): Promise<SFTSample[]>;
  }
```

- 具体策略实现：
  - `ASTStatementLevelExtractionStrategy`：AST语句级别提取，支持C、NP、ASM等语言
  - `FunctionLevelExtractionStrategy`：函数级别提取
  - `FunctionWithContextExtractionStrategy`：函数+检索上下文提取，集成上下文融合引擎

#### 6.4 典型策略说明

##### AST语句级别提取 (`statement`)
- 解析函数AST，遍历每条语句，生成独立的训练样本
- 支持语言特定的语句类型识别（C、NP、ASM等）
- 包含语句类型、位置等元信息
- 适合细粒度SFT训练

##### 函数级别提取 (`function`)
- 以函数为单位提取完整函数体
- 生成以函数为粒度的训练样本
- 适合函数级SFT训练或代码生成

##### 函数+检索上下文提取 (`function_with_context`)
- 以函数为单位，集成上下文融合引擎获取相关代码片段
- 包含函数上下文（上方10行、下方10行）
- 通过图分析、关键词、嵌入检索获取相关上下文
- 适合上下文增强型SFT训练

#### 6.5 主要功能
- **单文件提取**：`extractSFTData(filePath, strategy)`
- **全策略对比**：`extractAllStrategies(filePath)` - 并发执行所有策略
- **工作区批量提取**：`extractSFTDataFromWorkspace(strategy)` - 扫描 `**/*.{c,np,asm}` 文件

#### 6.6 扩展点
- 新增语言支持：在 `ParserManager` 中添加新的解析器
- 新增策略：实现 `extractSamples(filePath: string)` 方法
- 支持批量/并发提取和性能优化

#### 6.7 相关文档
- 详细设计与用法：docs/modules/data_extraction.md
- 任务追溯：docs/tasks/task-20240611-data-extraction.md

### 6.8 命令集成与任务追溯
- 通过VSCode命令 `code-partner.extractSFTData` 触发数据提取，支持交互式提取与扩展。
- 每次数据提取任务均应在 docs/tasks/ 下建立独立任务文档，记录需求、设计、实现、测试与变更。
- 相关任务文档：docs/tasks/task-20240611-data-extraction.md

## 7. 用户系统架构

### 7.1 概述
用户系统采用抽象接口和工厂模式设计，支持多种用户类型的统一管理。系统提供通用的用户信息接口，同时保留获取原始数据的能力。插件激活时自动初始化，默认使用 DEBUG 用户类型。

### 7.2 核心接口

#### IUserInfo - 通用用户信息
```typescript
interface IUserInfo {
    id: string;        // 用户ID
    name: string;      // 用户姓名
    nickname: string;  // 用户昵称
    department: string; // 用户部门
}
```

#### IUser - 用户接口
```typescript
interface IUser {
    readonly type: UserType;           // 用户类型
    readonly info: IUserInfo | undefined; // 通用用户信息
    getRaw(): any;                     // 获取原始数据
    init(): Promise<void>;             // 初始化
    isLoggedIn(): boolean;             // 检查登录状态
}
```

#### IUserFactory - 用户工厂接口
```typescript
interface IUserFactory {
    createUser(type: UserType): IUser;     // 创建用户实例
    getCurrentUser(): IUser | undefined;   // 获取当前用户
    setCurrentUserType(type: UserType): void; // 设置当前用户类型
}
```

### 7.3 支持的用户类型

- `UserType.HUAWEI` - 华为用户
- `UserType.DEBUG` - 调试用户（默认）
- `UserType.GITHUB` - GitHub用户（待实现）
- `UserType.MICROSOFT` - Microsoft用户（待实现）
- `UserType.GITLAB` - GitLab用户（待实现）

### 7.4 使用方式

#### 插件初始化
用户系统会在插件激活时自动初始化，默认使用 DEBUG 用户类型。

#### 全局访问方式（推荐）
```typescript
import { 
    getCurrentUser, 
    getCurrentUserInfo, 
    getCurrentUserRaw, 
    isUserLoggedIn, 
    getUserType 
} from './components/user/UserService';

// 获取当前用户信息
const userInfo = await getCurrentUserInfo();

// 检查登录状态
const isLoggedIn = await isUserLoggedIn();

// 获取原始数据
const rawData = await getCurrentUserRaw();

// 获取用户类型
const userType = getUserType();
```

#### 服务实例方式
```typescript
import { UserService } from './components/user/UserService';

const userService = UserService.getInstance();

// 获取当前用户信息
const userInfo = await userService.getCurrentUserInfo();

// 检查登录状态
const isLoggedIn = await userService.isCurrentUserLoggedIn();

// 获取原始数据
const rawData = await userService.getCurrentUserRaw();
```

### 7.5 实际业务集成示例

#### 在代码补全功能中使用用户信息
```typescript
import { getCurrentUserInfo, getUserType } from './components/user/UserService';
import { UserType } from './components/user/types/user';

export async function getCompletionContext() {
    const userInfo = await getCurrentUserInfo();
    const userType = getUserType();
    
    return {
        user: userInfo,
        userType: userType,
        // 根据用户类型提供不同的上下文
        context: userType === UserType.DEBUG ? 'debug' : 'production'
    };
}
```

#### 在代码分析功能中使用用户信息
```typescript
import { getCurrentUser } from './components/user/UserService';
import { UserType } from './components/user/types/user';

export async function analyzeCode(code: string) {
    const user = await getCurrentUser();
    
    // 根据用户类型调整分析策略
    if (user?.type === UserType.DEBUG) {
        console.log('使用调试模式进行代码分析');
        // 调试模式：更详细的日志，更宽松的规则
    } else {
        console.log('使用生产模式进行代码分析');
        // 生产模式：更严格的规则
    }
    
    // 返回分析结果
    return {
        code,
        analyzedBy: user?.info?.name || 'Unknown',
        timestamp: new Date().toISOString()
    };
}
```

#### 在错误处理中使用用户信息
```typescript
import { getCurrentUserInfo, getUserType } from './components/user/UserService';
import { UserType } from './components/user/types/user';

export async function handleError(error: Error) {
    const userInfo = await getCurrentUserInfo();
    const userType = getUserType();
    
    // 根据用户类型决定错误处理策略
    if (userType === UserType.DEBUG) {
        // 调试模式：显示详细错误信息
        console.error('调试模式错误详情:', {
            error: error.message,
            stack: error.stack,
            user: userInfo
        });
    } else {
        // 生产模式：只记录基本信息
        console.error('生产模式错误:', {
            error: error.message,
            user: userInfo?.name
        });
    }
}
```

### 7.6 扩展新用户类型

要添加新的用户类型，需要：

1. 在 `UserType` 枚举中添加新类型
2. 在 `UserFactory.createUser()` 方法中添加对应的 case
3. 创建对应的用户适配器类，实现 `IUser` 接口
4. 在适配器中实现 `getRaw()` 方法返回原始数据

#### 示例：添加 GitHub 用户
```typescript
// 1. 在 UserType 中添加
enum UserType {
    // ... 现有类型
    GITHUB = 'github'
}

// 2. 在 UserFactory 中添加
case UserType.GITHUB:
    return new GitHubUserAdapter();

// 3. 创建 GitHub 用户适配器
class GitHubUserAdapter implements IUser {
    // 实现 IUser 接口的所有方法
    public getRaw(): any {
        // 返回 GitHub 用户的原始数据
    }
}
```

### 7.7 设计优势

1. **统一接口** - 所有用户类型都实现相同的接口
2. **类型安全** - 使用 TypeScript 提供类型检查
3. **扩展性** - 易于添加新的用户类型
4. **原始数据访问** - 通过 `getRaw()` 方法可以访问特定用户类型的完整数据
5. **工厂模式** - 统一的用户创建和管理
6. **单例模式** - 确保全局只有一个用户服务实例
7. **模块化** - 每个用户类型有独立的适配器文件
8. **自动初始化** - 插件激活时自动初始化，无需手动调用

### 7.8 文件结构

```
src/components/user/
├── types/
│   └── user.ts              # 核心接口定义
├── huawei/
│   ├── user.ts              # 华为用户实现
│   └── adapter.ts           # 华为用户适配器
├── debug/
│   └── adapter.ts           # 调试用户适配器
├── UserFactory.ts           # 用户工厂实现
├── UserService.ts           # 用户服务类
└── __tests__/
    └── UserService.test.ts  # 用户服务测试
```

### 7.9 向后兼容

系统保持了向后兼容性，原有的 VSCode 用户信息获取函数仍然可用：

- `getVSCodeUserInfo()`
- `getGitHubUserInfo()`
- `getGitUserInfo()`
- `getWorkspaceInfo()`
- `getActiveEditorInfo()`

## 8. 统计组件架构

### 8.1 概述
统计组件负责收集和上报续写相关的统计信息，包括续写触发、接受、拒绝等事件。采用异步上报机制，不影响用户体验，支持错误处理和重试机制。

### 8.2 核心接口

#### StatisticsEventType - 事件类型枚举
```typescript
enum StatisticsEventType {
    COMPLETION_TRIGGERED = 'completion_triggered',    // 续写触发
    COMPLETION_ACCEPTED = 'completion_accepted',      // 续写接受
    COMPLETION_REJECTED = 'completion_rejected'       // 续写拒绝
}
```

#### IStatisticsService - 统计服务接口
```typescript
interface IStatisticsService {
    // 上报续写触发事件
    reportCompletionTriggered(
        scope: CompletionScope,
        context: CompletionContext,
        position: CompletionPosition
    ): Promise<void>;

    // 上报续写反馈事件
    reportCompletionFeedback(
        eventType: StatisticsEventType.COMPLETION_ACCEPTED | StatisticsEventType.COMPLETION_REJECTED,
        scope: CompletionScope,
        context: CompletionContext,
        position: CompletionPosition,
        suggestionText: string,
        originalText?: string,
        feedback?: string
    ): Promise<void>;
}
```

### 8.3 数据结构

#### CompletionScope - 续写作用域信息
```typescript
interface CompletionScope {
    filePath: string;           // 文件路径
    language: string;           // 编程语言
    functionName?: string;      // 函数名（如果有）
    className?: string;         // 类名（如果有）
    moduleName?: string;        // 模块名（如果有）
}
```

#### CompletionContext - 续写上下文信息
```typescript
interface CompletionContext {
    beforeCursor: string;       // 光标前的内容
    afterCursor?: string;       // 光标后的内容
    surroundingLines: number;   // 周围行数
    contextSnippets?: string[]; // 相关的代码片段
}
```

#### CompletionPosition - 续写位置信息
```typescript
interface CompletionPosition {
    line: number;               // 行号
    character: number;          // 字符位置
    offset: number;             // 文件偏移量
}
```

### 8.4 使用方式

#### 在续写触发时上报
```typescript
import { StatisticsService } from './components/statistics/StatisticsService';

// 在 server.ts 的 getCompletion 方法中
async getCompletion(promptOrPayload: string | CompletionRequestPayload, abortSig?:AbortSignal, stream?: boolean, params?: Partial<ModelParams>): Promise<string | ReadableStream> {
    // 上报续写触发事件
    await this.statisticsService.reportCurrentCompletionTriggered();
    
    // 原有的续写逻辑...
}
```

#### 在续写接受时上报
```typescript
import { StatisticsService, StatisticsEventType } from './components/statistics';

export async function acceptSuggestion() {
    const editor = vscode.window.activeTextEditor;
    if (!editor) return;

    const docUri = editor.document.uri.toString();
    const suggestion = suggestionStore.get(docUri);
    if (!suggestion || !suggestion.text || !suggestion.range) return;

    // 获取原始文本
    const originalText = editor.document.getText(suggestion.range);

    // 执行接受操作
    await editor.edit(editBuilder => {
        editBuilder.replace(suggestion.range!, suggestion.text as string);
    });

    // 上报接受事件
    const statisticsService = StatisticsService.getInstance();
    await statisticsService.reportCurrentCompletionFeedback(
        StatisticsEventType.COMPLETION_ACCEPTED,
        suggestion.text as string,
        originalText
    );

    // 清理状态
    suggestionStore.delete(docUri);
    vscode.commands.executeCommand('editor.action.inlineSuggest.hide');
}
```

#### 在续写拒绝时上报
```typescript
export async function rejectSuggestion() {
    const editor = vscode.window.activeTextEditor;
    if (!editor) return;

    const docUri = editor.document.uri.toString();
    const suggestion = suggestionStore.get(docUri);
    
    // 上报拒绝事件（如果有建议）
    if (suggestion && suggestion.text) {
        const statisticsService = StatisticsService.getInstance();
        await statisticsService.reportCurrentCompletionFeedback(
            StatisticsEventType.COMPLETION_REJECTED,
            suggestion.text as string
        );
    }

    // 清理状态
    suggestionStore.delete(docUri);
    fireCodeLensChange();
    await vscode.commands.executeCommand('editor.action.inlineSuggest.hide');
}
```

### 8.5 错误处理

统计服务采用静默错误处理策略，确保统计失败不会影响用户体验：

```typescript
private async reportEvent(event: CompletionTriggeredEvent | CompletionFeedbackEvent): Promise<void> {
    try {
        this.logger.info(`上报事件: ${event.eventType}`, {
            filePath: event.scope.filePath,
            language: event.scope.language,
            user: event.userInfo.name
        });

        const response = await fetch(this.endpoint, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(event)
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        this.logger.info('事件上报成功');
    } catch (error) {
        this.logger.error('事件上报失败:', error);
        // 不抛出错误，避免影响用户体验
    }
}
```

### 8.6 配置与扩展

#### 端点配置
目前使用 mock 端点，实际使用时需要替换为真实的后端API：

```typescript
private constructor() {
    this.logger = new Logger('StatisticsService');
    this.sessionId = this.generateSessionId();
    // TODO: 从配置中获取实际的统计上报端点
    this.endpoint = 'https://api.example.com/api/v1/statistics/code_completion';
}
```

#### 扩展新事件类型
要添加新的事件类型：

1. 在 `StatisticsEventType` 枚举中添加新类型
2. 在 `IStatisticsService` 接口中添加对应方法
3. 在 `StatisticsService` 中实现对应方法
4. 在相应的业务逻辑中调用上报方法

### 8.7 文件结构

```
src/components/statistics/
├── types.ts                           # 类型定义
├── StatisticsService.ts               # 统计服务实现
├── index.ts                           # 导出文件
└── __tests__/
    └── StatisticsService.test.ts      # 统计服务测试
```

### 8.8 设计优势

1. **异步上报** - 不阻塞用户操作
2. **错误隔离** - 统计失败不影响核心功能
3. **类型安全** - 完整的 TypeScript 类型定义
4. **单例模式** - 全局统一的统计服务实例
5. **会话管理** - 自动生成会话ID，便于数据关联
6. **用户信息集成** - 自动获取当前用户信息
7. **上下文收集** - 自动收集文件、位置、上下文等信息
8. **可扩展性** - 易于添加新的事件类型和字段

## 9. 前端开发规范
- 遵循项目前端开发规范，详见：docs/conventions/frontend.md
- 页面设计要考虑全局布局、稳定性、响应式设计
- 组件设计要合理拆分、逻辑分离、便于测试
- 确保代码组织、样式、性能、测试、文档、版本控制等各方面符合规范要求
