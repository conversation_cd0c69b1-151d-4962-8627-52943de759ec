# 数据提取功能开发任务

## 任务目标
实现可扩展的数据提取功能，支持多种策略（AST语句级、函数级、函数+检索上下文），用于SFT训练数据生成。

## 相关需求
- docs/requirements.md#6

## 相关设计
- docs/design.md#6
- docs/modules/data_extraction.md

## 主要实现文件
- src/components/data_extraction/types.ts
- src/components/data_extraction/DataExtractor.ts
- src/components/data_extraction/ASTStatementLevelExtractionStrategy.ts
- src/components/data_extraction/FunctionLevelExtractionStrategy.ts
- src/components/data_extraction/FunctionWithContextExtractionStrategy.ts

## 主要测试用例
- src/components/data_extraction/__tests__/
- src/commands/__tests__/register.extractSFTData.test.ts

## 变更说明
- 新增数据提取核心类型和集成式架构，支持三种提取策略
- 实现 DataExtractor 主类，集成所有策略实例
- 支持单文件提取、全策略对比提取、工作区批量提取
- 集成主命令注册，支持VSCode命令触发
- 完善单元测试、集成测试与详细文档

## 实现特性
- 支持 C、NP、ASM 等多种编程语言
- AST语句级、函数级、函数+上下文三种提取策略
- 工作区级别的批量数据提取功能
- 并发执行和性能优化
- 完善的错误处理和健壮性保障 