# RepoIndexer 组件关系图

## 组件架构图

```mermaid
graph TB
    subgraph "VSCode Extension Layer"
        A[RepoIndexerManager<br/>单例管理器<br/>管理索引生命周期]
        B[VSCode Commands<br/>用户命令接口]
        C[File Watcher<br/>文件变化监听]
    end
    
    subgraph "Core Indexing Layer"
        D[RepoIndexer<br/>核心索引器<br/>协调整个索引过程]
        E[WorkerPool<br/>Worker线程池<br/>并发文件解析]
        F[ParserManager<br/>解析器管理器<br/>管理Tree-sitter解析器]
    end
    
    subgraph "Data Processing Layer"
        G[CodeGraphBuilder<br/>图谱构建器<br/>构建代码关系图]
        H[GraphlibCodeGraph<br/>图谱存储<br/>存储节点和边]
        I[Data Extractor<br/>数据提取器<br/>从AST提取信息]
    end
    
    subgraph "Worker Threads"
        J[FileParserWorker<br/>文件解析Worker<br/>解析单个文件]
        K[Tree-sitter Parser<br/>语法解析器<br/>生成AST]
    end
    
    subgraph "Storage Layer"
        L[VSCode GlobalState<br/>VSCode全局存储]
        M[File System<br/>文件系统<br/>源代码文件]
        N[Configuration<br/>配置文件<br/>.vscode/code-partner.json]
    end
    
    %% 连接关系
    A --> D
    B --> A
    C --> A
    D --> E
    D --> F
    D --> G
    D --> H
    E --> J
    J --> K
    F --> K
    G --> I
    I --> H
    A --> L
    A --> M
    A --> N
```

## 数据流图

```mermaid
flowchart LR
    subgraph "Input"
        A[源代码文件]
        B[配置文件]
        C[用户命令]
    end
    
    subgraph "Processing"
        D[文件扫描]
        E[并发解析]
        F[AST构建]
        G[数据提取]
        H[图谱构建]
    end
    
    subgraph "Output"
        I[代码图谱]
        J[查询结果]
        K[持久化数据]
    end
    
    A --> D
    B --> D
    C --> D
    D --> E
    E --> F
    F --> G
    G --> H
    H --> I
    I --> J
    I --> K
```

## 状态转换图

```mermaid
stateDiagram-v2
    [*] --> Idle: 初始化完成
    
    Idle --> Initializing: 开始初始化
    Initializing --> Indexing: 开始索引
    Indexing --> Success: 索引成功
    Indexing --> Error: 索引失败
    
    Success --> Idle: 等待下次更新
    Error --> Idle: 错误恢复
    
    Idle --> IncrementalIndexing: 文件变化
    IncrementalIndexing --> Success: 增量更新成功
    IncrementalIndexing --> Error: 增量更新失败
    
    Success --> Success: 查询请求
    Error --> Error: 错误处理
```

## 类关系图

```mermaid
classDiagram
    class RepoIndexerManager {
        -instance: RepoIndexerManager
        -repoIndexer: RepoIndexer
        -context: ExtensionContext
        +getInstance(): RepoIndexerManager
        +initialize(context, autoInitIndex): Promise
        +triggerIndexing(force): Promise
        +getCurrentIndexingStatus(): IndexingStatus
    }
    
    class RepoIndexer {
        -parserManager: ParserManager
        -graphBuilder: CodeGraphBuilder
        -graph: GraphlibCodeGraph
        -workerPool: WorkerPool
        +analyze(incremental): Promise
        +getGraph(): ICodeGraph
        +findNodeByName(name, type): CodeNode
        +findCallersByName(name): CodeNode[]
    }
    
    class WorkerPool {
        -workers: Worker[]
        -taskQueue: Task[]
        -maxWorkers: number
        +initialize(): Promise
        +parseFiles(tasks): Promise
        +shutdown(): Promise
    }
    
    class ParserManager {
        -parsers: Map
        +getParser(language): Parser
        +initParser(language): Promise
    }
    
    class CodeGraphBuilder {
        +buildGraph(asts): ICodeGraph
        +extractNodes(ast): CodeNode[]
        +extractEdges(ast): Edge[]
    }
    
    class GraphlibCodeGraph {
        -nodes: Map
        -edges: Edge[]
        +addNode(node): void
        +addEdge(edge): void
        +getNode(id): CodeNode
    }
    
    class FileParserWorker {
        +parseFile(filePath): Promise
        +getAST(content, language): AST
    }
    
    RepoIndexerManager --> RepoIndexer : manages
    RepoIndexer --> WorkerPool : uses
    RepoIndexer --> ParserManager : uses
    RepoIndexer --> CodeGraphBuilder : uses
    RepoIndexer --> GraphlibCodeGraph : contains
    WorkerPool --> FileParserWorker : creates
    FileParserWorker --> ParserManager : uses
    CodeGraphBuilder --> GraphlibCodeGraph : builds
```

## 异步处理流程图

```mermaid
sequenceDiagram
    participant User as 用户
    participant Manager as RepoIndexerManager
    participant Indexer as RepoIndexer
    participant WorkerPool as WorkerPool
    participant Worker as FileParserWorker
    participant Graph as GraphlibCodeGraph
    
    User->>Manager: 触发索引
    Manager->>Indexer: analyze(false)
    Indexer->>WorkerPool: 初始化Worker池
    
    par 并发文件解析
        WorkerPool->>Worker: 解析文件1
        WorkerPool->>Worker: 解析文件2
        WorkerPool->>Worker: 解析文件N
    end
    
    Worker-->>WorkerPool: 文件1结果
    Worker-->>WorkerPool: 文件2结果
    Worker-->>WorkerPool: 文件N结果
    
    WorkerPool-->>Indexer: 所有解析结果
    Indexer->>Graph: 构建图谱
    Graph-->>Indexer: 图谱构建完成
    Indexer-->>Manager: 索引完成
    Manager-->>User: 索引状态更新
```

## 内存管理图

```mermaid
flowchart TD
    A[内存分配] --> B{内存类型}
    
    B -->|Worker线程| C[Worker内存池]
    B -->|AST对象| D[AST缓存]
    B -->|图谱数据| E[图谱内存]
    B -->|解析器| F[解析器内存]
    
    C --> G[Worker完成]
    D --> H[AST处理完成]
    E --> I[图谱查询]
    F --> J[解析完成]
    
    G --> K[释放Worker内存]
    H --> L[释放AST内存]
    I --> M[保持图谱内存]
    J --> N[保持解析器内存]
    
    K --> O[垃圾回收]
    L --> O
    M --> P[持久化存储]
    N --> Q[复用解析器]
```

## 性能监控图

```mermaid
graph LR
    subgraph "性能指标"
        A1[解析时间]
        A2[内存使用]
        A3[并发度]
        A4[成功率]
    end
    
    subgraph "监控点"
        B1[文件扫描]
        B2[Worker解析]
        B3[图谱构建]
        B4[查询响应]
    end
    
    subgraph "优化策略"
        C1[并发优化]
        C2[内存优化]
        C3[缓存优化]
        C4[算法优化]
    end
    
    A1 --> B1
    A2 --> B2
    A3 --> B3
    A4 --> B4
    
    B1 --> C1
    B2 --> C2
    B3 --> C3
    B4 --> C4
```

## 错误处理架构

```mermaid
flowchart TD
    A[错误发生] --> B{错误级别}
    
    B -->|致命错误| C[停止索引]
    B -->|严重错误| D[降级处理]
    B -->|一般错误| E[跳过文件]
    B -->|轻微错误| F[记录日志]
    
    C --> G[清理资源]
    D --> H[重试机制]
    E --> I[继续处理]
    F --> J[监控告警]
    
    G --> K[错误恢复]
    H --> L[部分成功]
    I --> M[完成索引]
    J --> N[性能优化]
    
    K --> O[重新初始化]
    L --> P[增量更新]
    M --> Q[状态更新]
    N --> R[配置调整]
```

## 总结

RepoIndexer 的组件关系体现了以下设计原则：

1. **分层架构**：清晰的层次分离，便于维护和扩展
2. **异步处理**：Worker线程池实现并发解析，提升性能
3. **状态管理**：完善的状态转换和错误处理机制
4. **数据流**：清晰的数据流向和处理流程
5. **资源管理**：合理的内存分配和释放策略
6. **监控优化**：全面的性能监控和优化策略

这种架构设计确保了 RepoIndexer 能够高效、稳定地处理大型代码仓库的索引任务。 