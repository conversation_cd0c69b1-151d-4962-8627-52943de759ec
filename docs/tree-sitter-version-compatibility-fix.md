# Tree-sitter 版本兼容性问题修复

## 问题描述

在修改 worker 构建系统后，出现了 Tree-sitter 语言版本不兼容的错误：

```
Error: Incompatible language version 0. Compatibility range 13 through 15.
```

## 问题分析

### 错误原因

1. **版本不匹配**：
   - `web-tree-sitter`: `^0.25.8`
   - `tree-sitter-cli` (在 tree-sitter-np 中): `^0.24.5`
   - 语言版本不兼容

2. **依赖关系**：
   - tree-sitter-np 是本地开发的包
   - 使用了较旧的 tree-sitter-cli 版本
   - 生成的语法文件版本与 web-tree-sitter 不兼容

### 影响范围

- NP 解析器初始化失败
- C 解析器初始化失败
- 解析器回退到 mock 模式
- 索引功能无法正常工作

## 解决方案

### 1. 更新 tree-sitter-cli 版本

```bash
cd ./../../AI/ai-common/code/tree_sitters/languages/tree-sitter-np
npm install tree-sitter-cli@^0.25.0 --save-dev
```

### 2. 重新构建语法文件

```bash
# 生成语法文件
npx tree-sitter generate

# 构建原生绑定
npx tree-sitter build

# 安装绑定
npm run install
```

### 3. 验证修复

```bash
# 重新编译项目
npm run compile

# 运行测试
npm test -- --testNamePattern="应该正确构建bundle函数调用关系"
```

## 技术细节

### 版本兼容性

Tree-sitter 的版本兼容性规则：

- **语言版本 0-12**：兼容 tree-sitter-cli 0.24.x
- **语言版本 13-15**：兼容 tree-sitter-cli 0.25.x
- **web-tree-sitter 0.25.8**：需要语言版本 13-15

### 构建流程

1. **语法生成**：`tree-sitter generate`
   - 从 grammar.js 生成 C 代码
   - 创建语法解析器

2. **原生构建**：`tree-sitter build`
   - 编译 C 代码为原生模块
   - 生成 .node 文件

3. **安装绑定**：`node-gyp-build`
   - 安装原生模块
   - 配置 Node.js 绑定

## 预防措施

### 1. 版本同步

确保所有 Tree-sitter 相关包的版本同步：

```json
{
  "web-tree-sitter": "^0.25.8",
  "tree-sitter-cli": "^0.25.0",
  "tree-sitter-c": "^0.24.1"
}
```

### 2. 构建脚本

在 tree-sitter-np 的 package.json 中添加构建脚本：

```json
{
  "scripts": {
    "build": "tree-sitter generate && tree-sitter build && node-gyp build",
    "rebuild": "npm run build && npm run install"
  }
}
```

### 3. 版本检查

在解析器初始化时添加版本检查：

```typescript
async init() {
  try {
    // 检查版本兼容性
    const version = this.parser.getLanguage().version;
    if (version < 13) {
      throw new Error(`Incompatible language version ${version}. Need version 13+`);
    }
    
    this.parser.setLanguage(NP);
    console.log('NPParser初始化成功，使用tree-sitter-np解析器');
  } catch (error) {
    console.error('NPParser初始化失败，使用mock模式:', error);
    this.parser = null;
  }
}
```

## 测试验证

### 测试结果

修复后的测试结果：

```
✅ NPParser初始化成功，使用tree-sitter-np解析器
✅ 多文件图谱构建完成，最终节点数: 5, 边数: 2
✅ 测试通过
```

### 功能验证

1. **解析器初始化**：NP 和 C 解析器都能正常初始化
2. **语法解析**：能够正确解析 NP 和 C 文件
3. **节点提取**：能够提取函数、模块等节点
4. **关系构建**：能够构建函数调用关系

## 总结

通过更新 tree-sitter-cli 版本并重新构建语法文件，成功解决了 Tree-sitter 版本兼容性问题。现在解析器能够正常工作，索引功能也能正确提取函数和模块节点。

### 关键要点

1. **版本同步很重要**：确保所有 Tree-sitter 相关包的版本兼容
2. **本地包需要重建**：修改依赖后需要重新构建本地包
3. **错误处理要完善**：在解析器初始化时添加版本检查
4. **测试验证必要**：通过测试确保修复效果

### 后续维护

1. 定期检查 Tree-sitter 相关包的版本更新
2. 在更新依赖时同步更新本地包
3. 保持构建脚本的可用性
4. 完善错误处理和日志记录 