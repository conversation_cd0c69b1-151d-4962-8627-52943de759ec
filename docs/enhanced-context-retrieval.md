# 增强的代码补全上下文检索系统

## 概述

本文档描述了 `getCurrentFunctionContextDynamic` 函数的增强版本，该函数集成了多维度上下文检索能力，为代码补全提供更准确、更丰富的上下文信息。

## 架构设计

### 核心组件

1. **ContextCollector**: 统一的上下文收集器
2. **多种Provider**: 不同维度的检索提供器
3. **ContextFusionEngine**: 智能融合引擎
4. **配置系统**: 灵活的权重配置

### 检索维度

#### 1. 图谱检索 (GraphContextProvider)
- **功能**: 基于代码调用链的相关函数分析
- **优势**: 提供语义相关的函数上下文
- **实现**: 使用 `RelatedContextBuilder` 构建结构化相关上下文

#### 2. BM25关键词搜索 (KeywordContextProvider)
- **功能**: 基于文本相似性的代码片段检索
- **优势**: 快速匹配相似的代码模式
- **实现**: 使用BM25算法进行文本相似度计算

#### 3. 向量搜索 (EmbeddingContextProvider)
- **功能**: 基于语义相似性的深度检索
- **优势**: 理解代码的语义含义
- **实现**: 使用向量嵌入进行语义相似度匹配

#### 4. 用户自定义片段 (UserCustomContextProvider)
- **功能**: 用户配置的代码模板和片段
- **优势**: 个性化的代码补全建议
- **实现**: 支持用户自定义代码片段的检索和匹配

## 函数接口

### getCurrentFunctionContextDynamic

```typescript
export async function getCurrentFunctionContextDynamic(
  config?: ExtendedRetrievalFusionConfig
): Promise<{
  currentFunction: GraphCodeNode | null;
  relatedContext: ContextItem[];
  similarContext: ContextItem[];
  allContext: ContextItem[];
  contextSummary: string;
  // 向后兼容性字段
  relatedFunctions: GraphCodeNode[];
  similarFunctions: GraphCodeNode[];
  context: string;
}>
```

### 返回结果说明

- **currentFunction**: 当前编辑的函数节点
- **relatedContext**: 相关上下文（调用链分析结果）
- **similarContext**: 相似上下文（多维度相似性检索结果）
- **allContext**: 所有上下文的合并结果
- **contextSummary**: 上下文摘要信息
- **向后兼容字段**: 保持与旧版本的兼容性

## 配置系统

### ExtendedRetrievalFusionConfig

```typescript
interface ExtendedRetrievalFusionConfig {
  graphWeight: number;                    // 图谱检索权重
  bm25Weight: number;                     // BM25搜索权重
  embeddingWeight: number;                // 向量搜索权重
  userSnippetsWeight?: number;            // 用户片段权重
  userSnippetsPriorityMultiplier?: number; // 用户片段优先级倍数
}
```

### 预设配置

- **DEFAULT_CONTEXT_FUSION_CONFIG**: 默认平衡配置
- **HIGH_PRIORITY_USER_SNIPPETS_CONFIG**: 高优先级用户片段配置
- **LOW_PRIORITY_USER_SNIPPETS_CONFIG**: 低优先级用户片段配置

## 智能融合机制

### ContextFusionEngine

融合引擎负责：

1. **加权计算**: 根据配置对不同来源的结果进行加权
2. **去重处理**: 移除重复的上下文项目
3. **智能排序**: 按照综合评分进行排序
4. **长度控制**: 控制最终结果的数量

### 融合算法

```typescript
// 简化的融合逻辑
for (const item of graphItems) {
  score = item.score * config.graphWeight;
}

for (const item of bm25Items) {
  score = item.score * config.bm25Weight;
}

for (const item of embeddingItems) {
  score = item.score * config.embeddingWeight;
}

for (const item of userSnippetsItems) {
  adjustedScore = item.score * config.userSnippetsPriorityMultiplier;
  score = adjustedScore * config.userSnippetsWeight;
}
```

## 使用示例

### 基本使用

```typescript
// 使用默认配置
const result = await getCurrentFunctionContextDynamic();

console.log('当前函数:', result.currentFunction?.name);
console.log('相关上下文数量:', result.relatedContext.length);
console.log('相似上下文数量:', result.similarContext.length);
```

### 自定义配置

```typescript
// 使用自定义配置
const customConfig = {
  graphWeight: 0.5,
  bm25Weight: 0.2,
  embeddingWeight: 0.2,
  userSnippetsWeight: 0.3,
  userSnippetsPriorityMultiplier: 2.0
};

const result = await getCurrentFunctionContextDynamic(customConfig);
```

### 在代码补全中的应用

```typescript
// 在 inlineCompletionProvider.ts 中的使用
const functionContextResult = await getCurrentFunctionContextDynamic();
const { relatedContext, similarContext } = functionContextResult;

// 构建上下文片段
const contextSnippets = [];

// 添加相关上下文
for (const contextItem of relatedContext.slice(0, 2)) {
  const snippet = contextItemToContextSnippet(contextItem, 'semantic');
  contextSnippets.push(snippet);
}

// 添加相似上下文
for (const contextItem of similarContext.slice(0, 3)) {
  const snippet = contextItemToContextSnippet(contextItem, 'similar');
  contextSnippets.push(snippet);
}
```

## 性能优化

### 缓存机制
- 结果缓存：缓存频繁查询的结果
- 增量更新：只更新变化的部分

### 并行处理
- 多Provider并行执行
- 异步处理减少阻塞

### 资源控制
- 限制上下文数量
- 控制检索深度

## 测试覆盖

### 单元测试
- `getCurrentFunctionContextDynamic.test.ts`: 核心函数测试
- 各Provider的独立测试
- 融合引擎测试

### 集成测试
- 端到端的上下文检索测试
- 不同配置下的行为验证

## 向后兼容性

新版本保持了与旧版本的完全兼容：

- 保留了原有的返回字段
- 支持原有的调用方式
- 渐进式升级路径

## 未来扩展

### 计划中的功能
- 更多检索维度的支持
- 机器学习优化的权重调整
- 实时学习用户偏好
- 更智能的上下文选择算法

### 扩展点
- 新的Provider接口
- 自定义融合策略
- 动态配置调整
