# 项目文档索引

## 核心设计文档

### 1. [项目设计文档](design.md)
- 总体架构设计
- 模块划分和接口设计
- Provider分层架构说明

### 2. [代码图构建逻辑文档](code-graph-construction.md)
- 详细的代码图构建逻辑和顺序
- 核心算法和数据结构
- 性能优化和错误处理

### 3. [代码图构建流程图](code-graph-construction-flow.md)
- 构建顺序概览和详细流程
- 数据依赖关系和时间线
- 关键决策点和验证检查点

## 功能模块文档

### 4. [需求文档](requirements.md)
- 项目功能需求
- 用户场景和用例
- 技术要求和约束

### 5. [测试规范](testing.md)
- 测试目录结构
- 测试文件命名规范
- 导入路径规范

### 6. [自动索引配置](auto-indexing-config.md)
- 索引配置说明
- 自动索引逻辑
- 配置参数详解

## 技术文档

### 7. [Tree-sitter Web 使用笔记](tree-sitter-web-notes.md)
- Web Tree-sitter 集成说明
- WASM 模块加载
- 解析器初始化和使用

### 8. [控制字符修复](control-character-fix.md)
- 控制字符处理
- 文本清理和修复

### 9. [Windows VSCode 补全问题](windows-vscode-completion-issue.md)
- Windows 环境下的补全问题
- 解决方案和修复

### 10. [代码重构总结](code-refactoring-summary.md)
- 重复代码消除和重构
- 代码优化和最佳实践

### 11. [代码清理总结](code-cleanup-summary.md)
- 废弃方法调用处理
- 调试代码清理
- 向后兼容代码优化

### 12. [废弃API清理总结](deprecated-api-cleanup-summary.md)
- 废弃API彻底移除
- 接口设计优化
- 测试同步更新

### 13. [NP Parser总结](np-parser-summary.md)
- NP语言解析器架构
- 语法分析能力
- Mock模式实现

### 14. [测试修复总结](test-fixes-summary.md)
- 废弃API测试更新
- Jest配置优化
- 模块导入路径修复

## 任务文档

### 10. [任务文档目录](tasks/)
- [上下文融合任务](tasks/task-20240611-context-fusion.md)
- [数据提取任务](tasks/task-20240611-data-extraction.md)

## 模块文档

### 11. [模块文档目录](modules/)
- [数据提取模块](modules/data_extraction.md)

## 快速导航

### 新开发者入门
1. 阅读 [需求文档](requirements.md) 了解项目目标
2. 查看 [项目设计文档](design.md) 了解整体架构
3. 学习 [代码图构建逻辑文档](code-graph-construction.md) 了解核心功能
4. 参考 [测试规范](testing.md) 了解开发规范

### 代码图功能开发
1. [代码图构建流程图](code-graph-construction-flow.md) - 构建顺序和关键决策点
2. [代码图构建逻辑文档](code-graph-construction.md) - 详细算法和数据结构
3. [Tree-sitter Web 使用笔记](tree-sitter-web-notes.md) - AST解析技术

### 测试和调试
1. [测试规范](testing.md) - 测试编写规范
2. [自动索引配置](auto-indexing-config.md) - 索引功能配置
3. [控制字符修复](control-character-fix.md) - 常见问题处理

## 文档维护

- 所有文档使用 Markdown 格式
- 包含流程图时使用 Mermaid 语法
- 代码示例使用 TypeScript
- 定期更新以保持与代码同步

## 贡献指南

1. 修改代码时同步更新相关文档
2. 新增功能时创建或更新对应文档
3. 发现文档问题时及时修复
4. 保持文档的准确性和可读性 