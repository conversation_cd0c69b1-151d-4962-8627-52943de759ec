# Worker 构建系统

## 概述

本项目采用了独立的 worker 构建系统，将 worker 代码与主进程代码分离，避免 worker 代码被打包进主进程，提高构建效率和代码组织性。

## 架构设计

### 构建流程

```
src/
├── extension.ts                    # 主进程入口
├── components/
│   └── code_graph/
│       └── workers/
│           ├── fileParserWorker.ts    # Worker 代码
│           ├── workerDemo.ts          # Worker 演示
│           └── testProgressDemo.js    # 测试进度演示
└── scripts/
    └── build-workers.js              # Worker 构建脚本

dist/
├── extension.js                     # 主进程代码（不包含 worker）
└── components/
    └── code_graph/
        └── workers/
            ├── fileParserWorker.js   # 编译后的 worker
            ├── workerDemo.js         # 编译后的 worker
            └── testProgressDemo.js   # 复制的 worker
```

### 构建步骤

1. **Worker 构建**：`npm run build:workers`
   - 编译 TypeScript worker 文件
   - 复制 JavaScript worker 文件
   - 生成独立的 worker bundle

2. **主进程构建**：`npm run compile`
   - 构建主进程代码
   - 将 worker 模块标记为外部依赖
   - 避免 worker 代码打包进主进程

## 使用方法

### 开发模式

```bash
# 构建所有代码（包括 worker）
npm run compile

# 只构建 worker
npm run build:workers

# 监听模式构建 worker
npm run build:workers:watch
```

### 生产模式

```bash
# 生产构建
npm run package
```

### Watch 模式

```bash
# 监听所有文件变化
npm run watch

# 只监听 worker 文件变化
npm run watch:workers
```

## 配置说明

### Worker 构建配置

在 `scripts/build-workers.js` 中配置：

```javascript
const workerFiles = [
    {
        entry: 'src/components/code_graph/workers/fileParserWorker.ts',
        outfile: 'dist/components/code_graph/workers/fileParserWorker.js',
        description: 'File parser worker for code analysis'
    },
    // ... 更多 worker 文件
];
```

### 外部依赖配置

在 `esbuild.js` 中配置外部依赖：

```javascript
external: [
    'vscode',
    'tree-sitter',
    'tree-sitter-c',
    'tree-sitter-np',
    'web-tree-sitter',
    // 将 worker 相关模块标记为外部依赖
    'src/components/code_graph/workers/*',
    'dist/components/code_graph/workers/*'
]
```

## 优势

### 1. 代码分离
- Worker 代码与主进程代码完全分离
- 避免不必要的代码打包
- 提高主进程启动速度

### 2. 独立构建
- Worker 可以独立编译和测试
- 支持 TypeScript 和 JavaScript 混合
- 更好的错误定位

### 3. 开发体验
- 支持 watch 模式
- 详细的构建日志
- 清晰的错误提示

### 4. 性能优化
- 减少主进程 bundle 大小
- 按需加载 worker
- 更好的内存管理

## 注意事项

### 1. 依赖管理
- Worker 中的依赖需要明确声明
- 避免循环依赖
- 注意版本兼容性

### 2. 路径引用
- Worker 文件路径需要正确配置
- 相对路径和绝对路径要区分清楚
- 确保运行时路径正确

### 3. 调试
- Worker 错误需要单独调试
- 日志输出要清晰
- 错误堆栈要完整

## 故障排除

### 常见问题

1. **Worker 文件未找到**
   - 检查构建路径配置
   - 确认文件存在
   - 验证构建脚本执行

2. **依赖缺失**
   - 检查 external 配置
   - 确认依赖已安装
   - 验证版本兼容性

3. **构建失败**
   - 查看详细错误日志
   - 检查 TypeScript 配置
   - 验证 esbuild 配置

### 调试技巧

1. **查看构建日志**
   ```bash
   npm run build:workers -- --verbose
   ```

2. **检查生成文件**
   ```bash
   ls -la dist/components/code_graph/workers/
   ```

3. **验证依赖**
   ```bash
   node -e "console.log(require('./dist/components/code_graph/workers/fileParserWorker.js'))"
   ```

## 扩展

### 添加新的 Worker

1. 在 `src/components/code_graph/workers/` 中创建新文件
2. 在 `scripts/build-workers.js` 中添加配置
3. 更新相关文档

### 自定义构建配置

1. 修改 `scripts/build-workers.js` 中的构建参数
2. 调整 `esbuild.js` 中的外部依赖
3. 更新 package.json 中的脚本

### 集成测试

1. 为 worker 编写单元测试
2. 集成到 CI/CD 流程
3. 添加性能测试 