# 上下文搜索架构重构总结

## 重构目标

根据用户需求，重新设计和实现 `collectContextsSeparately` 中的上下文搜索架构，解决BM25搜索分类错误的问题。

## 问题分析

### 原有问题
1. **BM25搜索分类错误**：BM25关键词搜索被错误地归类为"相似上下文"，但实际上应该属于"相关上下文"
2. **相关上下文定义不完整**：相关上下文应该包含两大部分，但原实现只包含了调用链部分
3. **架构混乱**：关键词搜索和真正的相似性搜索混在一起

### 正确的架构设计

**相关上下文（Related Context）**应该分为两大部分：
1. **真实已经发生的调用链**：
   - 上游函数（调用当前函数的函数）
   - 下游函数（当前函数调用的函数）
   - 相关变量、结构体、宏定义

2. **潜在可用上下文**（通过BM25搜索）：
   - 当前模块可见范围内的符号（如导入的模块中的函数、宏等）
   - 全局范围内的相关符号

**相似上下文（Similar Context）**应该专注于真正的相似性：
- 结构相似的函数（基于参数、返回类型、复杂度等）
- 向量搜索结果
- 用户自定义片段

## 重构实现

### 1. GraphContextProviderImpl 重构

#### 1.1 getRelatedContext 方法重构
```typescript
async getRelatedContext(functionNode: ASTNode, ...): Promise<ContextItem[]> {
  const contextItems: ContextItem[] = [];

  // 第一部分：真实已经发生的调用链
  const actualCallChainItems = await this.getActualCallChainContext(functionNode);
  contextItems.push(...actualCallChainItems);

  // 第二部分：潜在可用上下文（BM25搜索）
  const potentialContextItems = await this.getPotentialRelatedContext(functionNode);
  contextItems.push(...potentialContextItems);

  return contextItems;
}
```

#### 1.2 新增方法
- `getActualCallChainContext()`: 获取真实调用链
- `getPotentialRelatedContext()`: 通过BM25搜索获取潜在相关上下文
- `searchInModuleScope()`: 在模块可见范围内搜索
- `getModuleFiles()`: 获取模块相关文件列表

#### 1.3 getSimilarContext 方法重构
```typescript
async getSimilarContext(functionNode: ASTNode): Promise<ContextItem[]> {
  // 现在专注于真正的相似性搜索，不再包含BM25关键词搜索
  const similarFunctions = await this.findStructurallySimilarFunctions(functionNode);
  // 基于函数结构、参数类型、返回类型等进行相似性判断
}
```

#### 1.4 新增相似性算法
- `findStructurallySimilarFunctions()`: 查找结构相似的函数
- `extractFunctionFeatures()`: 提取函数特征
- `calculateFunctionSimilarity()`: 计算函数相似度
- `calculateJaccardSimilarity()`: 计算Jaccard相似度
- `calculateComplexity()`: 计算代码复杂度

### 2. ContextCollector 重构

#### 2.1 collectContextsSeparately 方法更新
```typescript
async collectContextsSeparately(...): Promise<{
  relatedContext: ContextItem[];
  similarContext: ContextItem[];
}> {
  // 收集相关上下文（调用链 + BM25搜索的潜在相关上下文）
  const relatedGraphItems = await this.graphProvider.getRelatedContext(astNode);

  // 收集相似上下文（结构相似函数 + 向量搜索 + 用户自定义片段）
  // 注意：不再包含BM25关键词搜索
  const [similarGraphItems, embeddingItems, userSnippetsItems] = await Promise.all([
    this.graphProvider.getSimilarContext(astNode),
    this.embeddingProvider.searchByEmbedding(code),
    this.userCustomProvider.searchByUserSnippets(code, astNode.language || 'c'),
  ]);
}
```

#### 2.2 其他方法同步更新
- `collectContext()`: 主流程统一入口
- `collectSimilarContext()`: 移除BM25搜索

## 架构改进效果

### 1. 概念清晰化
- **相关上下文**：真正与当前代码相关的内容（调用链 + 可见范围内的符号）
- **相似上下文**：结构或语义相似的代码片段

### 2. 搜索精度提升
- BM25搜索现在在正确的范围内进行（模块可见范围 > 全局范围）
- 相似性搜索基于真正的结构特征，而不是简单的关键词匹配

### 3. 性能优化
- 分层搜索策略：优先搜索相关性更高的范围
- 避免重复搜索：BM25搜索不再在相似上下文中重复进行

### 4. 可扩展性
- 清晰的职责分离，便于后续添加新的搜索算法
- 结构化的相似性计算，可以轻松调整权重和特征

## 测试建议

1. **功能测试**：验证调用链检索是否正常工作
2. **相关性测试**：验证BM25搜索是否能找到模块可见范围内的相关符号
3. **相似性测试**：验证结构相似的函数是否能被正确识别
4. **性能测试**：对比重构前后的搜索性能

## 后续优化方向

1. **向量搜索集成**：将来可以用embedding搜索替代或补充BM25搜索
2. **机器学习优化**：使用ML模型优化相似性计算
3. **缓存机制**：为频繁搜索的结果添加缓存
4. **用户反馈**：根据用户选择的上下文优化搜索算法
