# 前端开发规范

## 1. 布局设计原则

### 1.1 全局布局考虑
- 新增页面时必须考虑全局布局，参考已有页面的布局结构
- 保持整体设计风格的一致性

### 1.2 页面稳定性
- 页面表现要固定，尽量避免出现滚动条
- 功能按钮位置要固定，不能随着内容增加而位置发生变化

## 2. 页面设计规范

### 2.1 内容布局
- 页面内容要铺满页面，不要留下过多空白
- 合理利用空间，确保内容密度适中

### 2.2 响应式设计
- 设计子页面时要考虑主页面的菜单栏
- 基于父容器进行设置，避免使用固定的宽高
- 不要使用 vw、vh 单位，优先使用相对单位

## 3. 组件设计规范

### 3.1 组件拆分原则
- 注意页面文件的大小，进行合理的拆分设计
- 将大页面拆分成多个子组件
- 每个组件职责单一，便于维护和测试

### 3.2 复杂逻辑处理
- 复杂的算法逻辑要提供单独的文件
- 确保逻辑可测试，便于单元测试编写

## 4. 代码组织规范

### 4.1 文件结构
```
src/
├── components/          # 通用组件
├── pages/              # 页面组件
├── utils/              # 工具函数
├── hooks/              # 自定义Hooks
└── styles/             # 样式文件
```

### 4.2 命名规范
- 组件名使用PascalCase
- 文件名使用kebab-case
- 变量和函数使用camelCase

## 5. 样式规范

### 5.1 CSS类命名
- 使用BEM命名法
- 避免过深的嵌套，最多3层
- 使用语义化的类名

### 5.2 样式组织
- 全局样式放在styles/global.css
- 组件样式使用CSS Modules或styled-components
- 避免内联样式，优先使用类名

## 6. 性能优化

### 6.1 组件优化
- 合理使用React.memo、useMemo、useCallback
- 避免不必要的重渲染
- 懒加载大型组件

### 6.2 资源优化
- 图片使用适当的格式和大小
- 使用CDN加速静态资源

## 7. 测试规范

### 7.1 单元测试
- 每个组件都要有对应的测试文件
- 测试覆盖率不低于80%
- 测试用例要覆盖正常、边界、异常场景

### 7.2 集成测试
- 关键业务流程要有集成测试
- 测试用户交互流程

## 8. 文档规范

### 8.1 组件文档
- 每个组件都要有README文档
- 说明组件的用途、参数、使用示例

### 8.2 代码注释
- 复杂逻辑要有详细注释
- 使用JSDoc格式注释

## 9. 版本控制

### 9.1 提交规范
- 使用语义化的提交信息
- 每次提交只包含一个功能或修复

### 9.2 分支管理
- 使用Git Flow工作流
- feature分支从develop分支创建

## 10. 部署规范

### 10.1 构建优化
- 使用webpack等工具进行代码分割
- 压缩和混淆代码

### 10.2 环境配置
- 区分开发、测试、生产环境
- 使用环境变量管理配置 