# 自动索引配置说明

## 概述

Code Partner 插件新增了 `autoIndexing` 配置参数，用于控制是否启用自动索引和自动增量索引功能。这个配置与现有的 `enableIndexing` 配置配合使用，提供更精细的索引控制。

## 配置参数

### enableIndexing
- **类型**: `boolean`
- **默认值**: `true`
- **说明**: 是否启用代码索引功能
  - `true`: 启用索引功能，可以进行代码分析和检索
  - `false`: 禁用索引功能，不会进行任何索引操作

### autoIndexing
- **类型**: `boolean`
- **默认值**: `true`
- **说明**: 是否启用自动索引和自动增量索引（仅在启用索引功能时有效）
  - `true`: 启用自动索引，插件激活时自动构建索引，文件变更时自动更新
  - `false`: 禁用自动索引，需要手动执行"初始化工作区索引"命令

## 配置组合效果

| enableIndexing | autoIndexing | 效果描述 |
|----------------|--------------|----------|
| `true` | `true` | 索引功能和自动索引都启用，会自动构建和更新索引 |
| `true` | `false` | 索引功能启用但自动索引禁用，需要手动触发索引 |
| `false` | `true` | 索引功能完全禁用，不会进行任何索引操作 |
| `false` | `false` | 索引功能完全禁用，不会进行任何索引操作 |

## 配置文件位置

配置文件位于工作区的 `.vscode/code-partner.json` 文件中：

```json
{
  "enableIndexing": true,
  "autoIndexing": true,
  "languageExts": ["*"],
  "rules": [
    {
      "type": "include",
      "pattern": "**/*.{asm}"
    }
  ]
}
```

## 使用场景

### 场景1: 完全自动索引（默认）
```json
{
  "enableIndexing": true,
  "autoIndexing": true
}
```
- 插件激活时自动构建索引
- 文件变更时自动增量更新索引
- 适合大多数开发场景

### 场景2: 手动控制索引
```json
{
  "enableIndexing": true,
  "autoIndexing": false
}
```
- 插件激活时不会自动构建索引
- 文件变更时不会自动更新索引
- 需要手动执行"初始化工作区索引"命令
- 适合对性能要求较高的场景或大型项目

### 场景3: 完全禁用索引
```json
{
  "enableIndexing": false,
  "autoIndexing": false
}
```
- 完全禁用索引功能
- 不会进行任何索引操作
- 适合不需要代码分析功能的场景

## 相关命令

### 手动索引命令
- **初始化工作区索引**: 手动构建工作区索引
- **重置索引**: 清理现有索引并重新构建
- **更新代码仓库索引**: 增量更新索引
- **清理索引数据**: 清理所有索引数据

### 查看命令
- **查看索引内容**: 显示当前索引的详细信息
- **查询相关函数**: 基于索引数据查询函数关系

## 性能考虑

### 启用自动索引时
- **优点**: 索引始终是最新的，代码分析功能立即可用
- **缺点**: 可能影响启动速度和文件保存性能

### 禁用自动索引时
- **优点**: 启动速度快，文件操作性能好
- **缺点**: 需要手动触发索引，索引可能不是最新的

## 最佳实践

1. **小型项目**: 建议启用自动索引，获得最佳开发体验
2. **大型项目**: 可以考虑禁用自动索引，在需要时手动触发
3. **CI/CD环境**: 建议禁用自动索引，避免不必要的资源消耗
4. **调试时**: 可以临时禁用自动索引，专注于调试问题

## 配置变更

修改配置文件后，需要重启 VS Code 扩展才能生效：

1. 修改 `.vscode/code-partner.json` 文件
2. 按 `Ctrl+Shift+P`（Windows/Linux）或 `Cmd+Shift+P`（Mac）
3. 输入 "Developer: Reload Window" 重新加载窗口
4. 或者重启 VS Code

## 故障排除

### 索引不自动更新
1. 检查 `enableIndexing` 是否为 `true`
2. 检查 `autoIndexing` 是否为 `true`
3. 检查配置文件格式是否正确
4. 查看输出面板中的日志信息

### 手动索引失败
1. 确保工作区已打开
2. 检查文件权限
3. 查看错误日志
4. 尝试清理索引数据后重新初始化

### 性能问题
1. 考虑禁用自动索引
2. 调整 `languageExts` 配置，只索引需要的文件类型
3. 使用 `.gitignore` 或 `rules` 配置排除不需要的文件 