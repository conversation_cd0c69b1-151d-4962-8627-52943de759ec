# NP解析器函数提取修复总结

## 问题描述

用户报告 `.asm` 文件应该识别为 `np` 语言，但是代码索引没有提取出函数，只提取了module。

## 问题分析

通过测试发现，问题的根本原因是：

1. **语言映射正确**：`.asm` 文件已经正确映射为 `np` 语言
2. **NP解析器能够识别函数**：tree-sitter-np 能够解析 `.asm` 文件中的函数定义
3. **解析结果异常**：函数定义被解析为 `ERROR` 节点和 `declare_variable` 节点，而不是标准的函数定义节点

### 测试结果

使用 `test_projects/np/demo.asm` 文件进行测试：

```asm
// NP语言演示代码
function calculate_sum {
    statement1;
    statement2;
    return result;
}

function process_data {
    call calculate_sum;
    statement3;
    if condition {
        statement4;
    }
}
```

**Tree-sitter-np 解析结果**：
- 找到 4 个 `ERROR` 节点，其中第一个包含 `function calculate_sum {`
- 找到 4 个 `declare_variable` 节点，其中两个包含函数定义：
  - `function calculate_sum`
  - `function process_data`

## 解决方案

### 1. 恢复语言映射

将 `.asm` 文件映射恢复为 `np` 语言：

```typescript
// src/components/code_graph/types/language.ts
export const FILE_TYPE_MAP: Record<string, SupportedLanguage> = {
    '.c': 'c',
    '.h': 'c',
    '.go': 'go',
    '.py': 'python',
    '.asm': 'np', // NP语言文件
};
```

### 2. 增强NP解析器

在 `src/components/code_graph/parser/NPParser.ts` 中添加了两个新方法：

#### `processErrorNode` 方法
处理 `ERROR` 节点，从中提取函数定义：

```typescript
private processErrorNode(node: Node, parentType?: string, parentNode?: Node): ASTNode {
  const nodeText = node.text.trim();
  
  // 尝试匹配函数定义模式
  const functionMatch = nodeText.match(/^function\s+(\w+)\s*\{/);
  if (functionMatch) {
    const functionName = functionMatch[1];
    console.log(`从ERROR节点提取函数定义: ${functionName}`);
    
    // 创建函数定义节点
    const astNode = this.nodeFactory.createFunctionDefinition(
      functionName,
      [], // 参数暂时为空
      {
        caller: this.currentFunction,
        extractedFromError: true
      }
    );
    // ... 设置位置和文本
    return astNode;
  }
  
  return this.createDefaultNode(node);
}
```

#### `processFunctionFromDeclareVariable` 方法
处理 `declare_variable` 节点中的函数定义：

```typescript
private processFunctionFromDeclareVariable(node: Node): ASTNode {
  // 从文本中提取函数名
  const functionMatch = node.text.match(/function\s+(\w+)/);
  if (!functionMatch) {
    return this.createDefaultNode(node);
  }
  
  const functionName = functionMatch[1];
  console.log(`从declare_variable提取函数名: ${functionName}`);

  const astNode = this.nodeFactory.createFunctionDefinition(
    functionName,
    params,
    {
      ...metadata,
      caller: this.currentFunction,
      extractedFromDeclareVariable: true
    }
  );
  // ... 设置位置和文本
  return astNode;
}
```

### 3. 更新节点处理逻辑

在 `processNode` 方法中添加了对这些特殊节点的处理：

```typescript
switch (node.type) {
  // 处理ERROR节点，尝试提取函数定义
  case 'ERROR':
    return this.processErrorNode(node, parentType, parentNode);
  
  // bundle 定义/声明 - 可能被解析为 variable_declaration 或 declare_variable
  case 'declare_variable':
    // 检查是否包含函数定义
    if (node.text.includes('function ')) {
      console.log('处理declare_variable中的函数定义，节点类型:', node.type, '文本:', node.text);
      return this.processFunctionFromDeclareVariable(node);
    }
    return this.processVariableDeclaration(node);
  
  // ... 其他节点类型
}
```

### 4. 增强调试日志

在 `CodeGraphBuilder` 中添加了调试日志，帮助诊断函数提取过程：

```typescript
// 添加调试日志
if (node.kind === ASTNodeKind.FUNCTION && node.name) {
  console.log(`[CodeGraphBuilder] 发现函数节点: ${node.name}, 语言: ${node.language}, 元数据:`, {
    isCall: node.metadata?.isCall,
    type: node.metadata?.type,
    isMacro: node.metadata?.isMacro,
    extractedFromError: node.metadata?.extractedFromError,
    extractedFromDeclareVariable: node.metadata?.extractedFromDeclareVariable
  });
}
```

## 修复效果

修复后，NP解析器能够：

1. **正确识别函数定义**：从ERROR节点和declare_variable节点中提取函数定义
2. **创建标准AST节点**：使用 `ASTNodeFactory.createFunctionDefinition()` 创建标准的函数定义节点
3. **保持元数据**：在节点元数据中标记提取来源（`extractedFromError` 或 `extractedFromDeclareVariable`）
4. **支持代码索引**：CodeGraphBuilder 能够正确处理这些节点并添加到全局函数索引

## 验证方法

可以通过以下方式验证修复效果：

1. **手动检查**：在 `.asm` 文件中添加函数定义，保存文件触发索引
2. **查看日志**：观察控制台输出中的调试信息
3. **查询函数**：使用代码图查询接口查找函数节点
4. **上下文检索**：在代码补全中验证函数上下文是否正确提取

## 总结

通过增强NP解析器对特殊节点的处理能力，成功解决了 `.asm` 文件中函数定义无法被正确提取的问题。修复方案保持了向后兼容性，同时提供了详细的调试信息帮助问题诊断。 