# 🏗️ VSCode 异步索引构建 - 完整设计指南

## 📋 目录
1. [项目背景与目标](#项目背景与目标)
2. [技术架构设计](#技术架构设计)
3. [分离式构建策略](#分离式构建策略)
4. [Worker 实现模式](#worker-实现模式)
5. [性能优化与监控](#性能优化与监控)
6. [最佳实践总结](#最佳实践总结)

---

## 🎯 项目背景与目标

### **核心挑战**
构建高性能的 VSCode 代码解析扩展，支持 C 和 NP 语言的 AST 解析，提供实时代码分析和索引功能。

### **技术难点**
- **性能瓶颈**：大量代码文件解析不能阻塞 VSCode 主 UI
- **资源管理**：WASM 模块体积大（~1MB），需要高效加载和缓存
- **并发处理**：支持多文件并行解析，提升索引速度
- **环境限制**：VSCode 扩展主进程必须使用 CommonJS，但需要利用现代 JavaScript 特性

### **解决方案概览**
```mermaid
graph TB
    A[VSCode 主进程] --> B[WorkerPool 管理器]
    B --> C[Worker 1: ES Module]
    B --> D[Worker 2: ES Module]
    B --> E[Worker N: ES Module]

    C --> F[tree-sitter WASM]
    D --> G[tree-sitter WASM]
    E --> H[tree-sitter WASM]

    subgraph "分离构建"
        I[主进程构建: CommonJS]
        J[Worker 构建: ES Module]
    end
```

---

## 🏛️ 技术架构设计

## 🎯 一、Worker 的角色与价值

### **为什么需要 Worker？**
- **主线程保护**：防止耗时任务（AST 解析、代码索引）阻塞 VSCode UI
- **并发处理**：利用多核 CPU，提升大型项目的处理速度
- **用户体验**：保持编辑器响应性，用户可以继续编码

### **Worker 的本质**
```typescript
// Worker 是独立的 JavaScript 执行环境
// ❌ 不能访问：主线程变量、DOM、VSCode API
// ✅ 可以访问：Node.js API、独立的内存空间、文件系统
```

---

## 🔄 二、生命周期管理（完整流程）

### **1. 创建与初始化**
```typescript
// 主线程：WorkerPool.ts
class WorkerPool {
  private createWorker(): Worker {
    const worker = new Worker(this.workerScriptPath, {
      workerData: { wasmPaths: this.wasmPaths } // 传递初始化数据
    });
    
    // 监听 Worker 状态
    worker.on('message', this.handleWorkerMessage.bind(this));
    worker.on('error', this.handleWorkerError.bind(this));
    worker.on('exit', this.handleWorkerExit.bind(this));
    
    return worker;
  }
}

// Worker 线程：fileParserWorker.ts
// 启动时立即发送就绪信号
parentPort?.postMessage({ 
  type: 'ready', 
  workerId: process.pid 
});
```

### **2. 任务分发与执行**
```typescript
// 主线程：任务队列管理
async parseFileDirect(filePath: string): Promise<WorkerResult> {
  return new Promise((resolve, reject) => {
    const taskId = Math.random().toString(36).substr(2, 9);
    
    // 添加到任务队列
    this.taskQueue.push({ 
      task: { id: taskId, filePath, workspacePath },
      resolve, 
      reject 
    });
    
    // 尝试分配给可用 Worker
    this.assignTasksToWorkers();
  });
}

// Worker 线程：任务处理
parentPort?.on('message', async (message: ParseFileMessage) => {
  if (message.type === 'parse_file') {
    try {
      const result = await parseFile(message.filePath);
      parentPort?.postMessage({
        type: 'result',
        id: message.id,
        success: true,
        ...result
      });
    } catch (error) {
      parentPort?.postMessage({
        type: 'result',
        id: message.id,
        success: false,
        error: error.message
      });
    }
  }
});
```

### **3. 优雅关闭**
```typescript
// 主线程：关闭 Worker 池
async shutdown(): Promise<void> {
  this.isShuttingDown = true;
  
  // 等待当前任务完成
  while (this.taskQueue.length > 0) {
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  
  // 发送关闭信号
  const shutdownPromises = this.workers.map(worker => {
    return new Promise<void>((resolve) => {
      worker.postMessage({ type: 'shutdown' });
      worker.on('message', (msg) => {
        if (msg.type === 'shutdown_ack') resolve();
      });
      setTimeout(resolve, 2000); // 超时保护
    });
  });
  
  await Promise.all(shutdownPromises);
  
  // 强制终止
  this.workers.forEach(worker => worker.terminate());
}

// Worker 线程：响应关闭
parentPort?.on('message', (message) => {
  if (message.type === 'shutdown') {
    // 清理资源
    cleanup();
    parentPort?.postMessage({ type: 'shutdown_ack' });
    process.exit(0);
  }
});
```

---

## 📡 三、通信机制设计

### **统一消息格式**
```typescript
// 定义标准消息接口
interface WorkerMessage {
  type: 'parse_file' | 'result' | 'error' | 'progress' | 'ready' | 'shutdown';
  id?: string;
  payload?: any;
  timestamp?: number;
}

// 类型安全的消息发送
function sendMessage(worker: Worker, message: WorkerMessage) {
  worker.postMessage({
    ...message,
    timestamp: Date.now()
  });
}
```

### **错误处理策略**
```typescript
// Worker 线程：全局异常捕获
process.on('uncaughtException', (err) => {
  console.error('[WORKER uncaughtException]', err);
  parentPort?.postMessage({ 
    type: 'error', 
    message: err.stack || err.message 
  });
  process.exit(1);
});

process.on('unhandledRejection', (reason) => {
  console.error('[WORKER unhandledRejection]', reason);
  parentPort?.postMessage({ 
    type: 'error', 
    message: String(reason) 
  });
  process.exit(1);
});

// 主线程：Worker 错误处理
worker.on('error', (error) => {
  logger.error(`Worker 错误: ${error.message}`);
  this.restartWorker(worker); // 重启故障 Worker
});

worker.on('exit', (code) => {
  if (code !== 0) {
    logger.warn(`Worker 异常退出，代码: ${code}`);
    this.restartWorker(worker);
  }
});
```

### **进度反馈机制**
```typescript
// Worker 线程：报告进度
function reportProgress(completed: number, total: number, currentFile?: string) {
  parentPort?.postMessage({
    type: 'progress',
    payload: {
      completed,
      total,
      percentage: Math.round((completed / total) * 100),
      currentFile
    }
  });
}

// 主线程：聚合进度
private onProgress?: (progress: ProgressInfo) => void;

handleWorkerMessage(message: any) {
  if (message.type === 'progress' && this.onProgress) {
    this.onProgress(message.payload);
  }
}
```

---

## 🏗️ 四、构建配置最佳实践

### **分离构建策略**
```javascript
// scripts/build.js - 协调构建
async function separatedBuild() {
  // 1. 清理构建目录
  await cleanBuildDir();
  
  // 2. 准备共享资源（WASM 文件等）
  await prepareSharedResources();
  
  // 3. 并行构建主进程和 Worker
  await Promise.all([
    buildMain(),    // 主进程构建
    buildWorkers()  // Worker 构建
  ]);
}

// 主进程构建配置
const MAIN_CONFIGS = [{
  entryPoints: ['src/extension.ts'],
  outfile: 'dist/extension.js',
  format: 'cjs',
  platform: 'node',
  external: ['vscode'] // VSCode API 不打包
}];

// Worker 构建配置
const WORKER_CONFIGS = [{
  entryPoints: ['src/components/code_graph/workers/fileParserWorker.ts'],
  outfile: 'dist/workers/fileParserWorker.mjs',
  format: 'esm',  // Worker 使用 ESM
  platform: 'node',
  external: ['vscode'] // Worker 不能使用 VSCode API
}];
```

### **资源隔离管理**
```javascript
// 主进程资源
const mainAssets = [
  'web-tree-sitter',     // 主进程也需要解析能力
  'tree-sitter-c'
];

// Worker 专用资源
const workerAssets = [
  'tree-sitter.wasm',    // WASM 文件
  'tree-sitter-c.wasm',
  'tree-sitter-np.wasm'
];

// 避免资源重复，各自管理依赖
```

---

## 🔧 五、调试与监控

### **调试技巧**
```typescript
// 1. 结构化日志
class WorkerLogger {
  static log(level: 'info' | 'warn' | 'error', message: string, data?: any) {
    const logMessage = {
      timestamp: new Date().toISOString(),
      level,
      worker: process.pid,
      message,
      data
    };
    
    console.log(JSON.stringify(logMessage));
    parentPort?.postMessage({ type: 'log', ...logMessage });
  }
}

// 2. 性能监控
function measurePerformance<T>(name: string, fn: () => T): T {
  const start = performance.now();
  const result = fn();
  const duration = performance.now() - start;
  
  WorkerLogger.log('info', `Performance: ${name}`, { duration });
  return result;
}

// 3. 内存监控
setInterval(() => {
  const memUsage = process.memoryUsage();
  WorkerLogger.log('info', 'Memory usage', {
    rss: Math.round(memUsage.rss / 1024 / 1024) + 'MB',
    heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024) + 'MB'
  });
}, 30000); // 每30秒报告一次
```

### **常见问题诊断**
```typescript
// Worker 健康检查
async function healthCheck(): Promise<boolean> {
  return new Promise((resolve) => {
    const timeout = setTimeout(() => resolve(false), 5000);
    
    worker.postMessage({ type: 'ping' });
    worker.once('message', (msg) => {
      if (msg.type === 'pong') {
        clearTimeout(timeout);
        resolve(true);
      }
    });
  });
}

// 自动重启机制
async function restartWorker(failedWorker: Worker) {
  logger.warn('重启故障 Worker...');
  
  // 1. 终止故障 Worker
  failedWorker.terminate();
  
  // 2. 创建新 Worker
  const newWorker = this.createWorker();
  
  // 3. 替换到 Worker 池中
  const index = this.workers.indexOf(failedWorker);
  if (index !== -1) {
    this.workers[index] = newWorker;
  }
  
  // 4. 重新分配任务
  this.assignTasksToWorkers();
}
```

---

## 📊 六、性能优化策略

### **Worker 池大小调优**
```typescript
// 动态调整 Worker 数量
class AdaptiveWorkerPool {
  private calculateOptimalWorkerCount(): number {
    const cpuCount = require('os').cpus().length;
    const memoryGB = require('os').totalmem() / (1024 ** 3);

    // 基于 CPU 核心数和内存大小
    const basedOnCPU = Math.max(1, cpuCount - 1); // 保留一个核心给主线程
    const basedOnMemory = Math.floor(memoryGB / 0.5); // 每个 Worker 约 500MB

    return Math.min(basedOnCPU, basedOnMemory, 8); // 最多 8 个 Worker
  }
}
```

### **任务调度优化**
```typescript
// 优先级队列
interface PriorityTask extends ParseFileTask {
  priority: 'high' | 'normal' | 'low';
  estimatedTime?: number;
}

// 智能任务分配
private assignTasksToWorkers() {
  // 按优先级和预估时间排序
  this.taskQueue.sort((a, b) => {
    if (a.task.priority !== b.task.priority) {
      const priorityOrder = { high: 3, normal: 2, low: 1 };
      return priorityOrder[b.task.priority] - priorityOrder[a.task.priority];
    }
    return (a.task.estimatedTime || 0) - (b.task.estimatedTime || 0);
  });

  // 分配给空闲 Worker
  while (this.availableWorkers.length > 0 && this.taskQueue.length > 0) {
    const worker = this.availableWorkers.pop()!;
    const taskItem = this.taskQueue.shift()!;
    this.executeTask(worker, taskItem);
  }
}
```

### **内存管理**
```typescript
// Worker 内存限制
const worker = new Worker(scriptPath, {
  resourceLimits: {
    maxOldGenerationSizeMb: 512, // 限制老生代内存
    maxYoungGenerationSizeMb: 128 // 限制新生代内存
  }
});

// 定期内存清理
setInterval(() => {
  if (global.gc) {
    global.gc(); // 手动触发垃圾回收
  }
}, 60000);
```

---

## 🛡️ 七、错误处理与容错

### **分级错误处理**
```typescript
enum ErrorSeverity {
  RECOVERABLE = 'recoverable',   // 可恢复错误，重试任务
  WORKER_FATAL = 'worker_fatal', // Worker 致命错误，重启 Worker
  SYSTEM_FATAL = 'system_fatal'  // 系统错误，停止处理
}

class ErrorHandler {
  static handle(error: Error, context: string): ErrorSeverity {
    if (error.message.includes('out of memory')) {
      return ErrorSeverity.WORKER_FATAL;
    }

    if (error.message.includes('ENOENT')) {
      return ErrorSeverity.RECOVERABLE;
    }

    return ErrorSeverity.SYSTEM_FATAL;
  }
}
```

### **重试机制**
```typescript
interface TaskWithRetry extends ParseFileTask {
  retryCount: number;
  maxRetries: number;
  lastError?: string;
}

async function executeTaskWithRetry(task: TaskWithRetry): Promise<WorkerResult> {
  try {
    return await this.executeTask(task);
  } catch (error) {
    const severity = ErrorHandler.handle(error, 'task_execution');

    if (severity === ErrorSeverity.RECOVERABLE && task.retryCount < task.maxRetries) {
      task.retryCount++;
      task.lastError = error.message;

      // 指数退避重试
      const delay = Math.pow(2, task.retryCount) * 1000;
      await new Promise(resolve => setTimeout(resolve, delay));

      return this.executeTaskWithRetry(task);
    }

    throw error;
  }
}
```

---

## 📋 八、最佳实践总结

### **✅ 推荐做法**

1. **架构设计**
   ```
   src/
     extension.ts              ← 主线程入口
     workers/
       fileParserWorker.ts     ← Worker 入口
       WorkerPool.ts           ← Worker 池管理
     shared/
       types.ts                ← 共享类型定义
       utils.ts                ← 共享工具函数
   ```

2. **通信规范**
   - 使用类型安全的消息接口
   - 统一错误处理格式
   - 实现超时和重试机制

3. **资源管理**
   - 合理设置 Worker 数量
   - 及时清理无用资源
   - 监控内存使用情况

4. **构建策略**
   - 主进程和 Worker 分离构建
   - 明确依赖边界
   - 优化打包体积

### **❌ 避免的陷阱**

1. **不要在 Worker 中使用 VSCode API**
   ```typescript
   // ❌ 错误：Worker 中不能使用
   import * as vscode from 'vscode';

   // ✅ 正确：通过消息传递数据
   parentPort?.postMessage({ type: 'show_message', text: 'Hello' });
   ```

2. **不要忽略 Worker 生命周期**
   ```typescript
   // ❌ 错误：不处理 Worker 退出
   const worker = new Worker(script);

   // ✅ 正确：完整的生命周期管理
   worker.on('exit', this.handleWorkerExit);
   worker.on('error', this.handleWorkerError);
   ```

3. **不要阻塞 Worker 线程**
   ```typescript
   // ❌ 错误：同步阻塞操作
   const data = fs.readFileSync(largefile);

   // ✅ 正确：异步非阻塞操作
   const data = await fs.readFile(largefile);
   ```

---

## 🎯 总结

> **Worker 的核心价值在于并发处理和主线程保护。成功的关键是：**
>
> 1. **清晰的职责分离** - 主线程负责协调，Worker 负责计算
> 2. **健壮的通信机制** - 类型安全、错误处理、超时保护
> 3. **完善的生命周期管理** - 创建、监控、重启、销毁
> 4. **合理的资源配置** - CPU、内存、任务队列的平衡
>
> 遵循这些原则，Worker 将成为 VSCode 插件性能提升的强大工具！
```
