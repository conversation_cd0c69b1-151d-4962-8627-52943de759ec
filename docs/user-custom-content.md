# 用户自定义代码片段和规则功能

## 功能概述

Code Partner 现在支持用户自定义代码片段和规则功能，让您可以根据个人需求定制代码续写的上下文和约束。

## 主要特性

### 1. 用户自定义代码片段
- ✅ **多语言支持**：支持 JavaScript、TypeScript、Python、Java、C/C++、C#、PHP、Go、Rust 等
- ✅ **智能匹配**：基于代码相似度和标签匹配自动选择相关片段
- ✅ **优先级控制**：设置片段优先级（1-10），影响匹配权重
- ✅ **标签系统**：为片段添加标签，提高匹配精度
- ✅ **持久化存储**：配置保存在本地，支持导入导出

### 2. 用户自定义规则
- ✅ **语言特定规则**：为不同编程语言设置特定规则
- ✅ **通用规则**：设置适用于所有语言的通用规则
- ✅ **优先级控制**：规则优先级影响在提示词中的排序
- ✅ **提示词集成**：规则内容自动加入到代码续写提示词中

## 使用方法

### 命令面板操作

#### 1. 打开管理面板
```
Ctrl+Shift+P (或 Cmd+Shift+P) → "Code Partner: 用户自定义内容管理"
```

#### 2. 快速添加代码片段
```
Ctrl+Shift+P → "Code Partner: 快速添加代码片段"
```

#### 3. 快速添加规则
```
Ctrl+Shift+P → "Code Partner: 快速添加规则"
```

#### 4. 导入/导出配置
```
Ctrl+Shift+P → "Code Partner: 导出用户自定义配置"
Ctrl+Shift+P → "Code Partner: 导入用户自定义配置"
```

### 管理面板功能

管理面板提供完整的 Web 界面，支持：

- **代码片段管理**
  - 添加、编辑、删除代码片段
  - 设置语言、标签、优先级
  - 启用/禁用片段
  - 预览片段内容

- **规则管理**
  - 添加、编辑、删除规则
  - 设置适用语言、优先级
  - 启用/禁用规则
  - 预览规则内容

- **配置管理**
  - 导出配置到 JSON 文件
  - 从 JSON 文件导入配置
  - 重置为默认配置

## 配置示例

### 代码片段示例

```json
{
  "snippets": [
    {
      "id": "snippet-001",
      "name": "React 函数组件模板",
      "description": "标准的 React 函数组件模板",
      "code": "import React from 'react';\n\ninterface Props {\n  // 组件属性定义\n}\n\nconst ComponentName: React.FC<Props> = (props) => {\n  return (\n    <div>\n      {/* 组件内容 */}\n    </div>\n  );\n};\n\nexport default ComponentName;",
      "language": "typescript",
      "tags": ["react", "component", "typescript", "template"],
      "priority": 9,
      "createdAt": 1640995200000,
      "updatedAt": 1640995200000,
      "isEnabled": true
    },
    {
      "id": "snippet-002",
      "name": "快速排序算法",
      "description": "JavaScript 快速排序实现",
      "code": "function quickSort(arr) {\n  if (arr.length <= 1) return arr;\n  \n  const pivot = arr[Math.floor(arr.length / 2)];\n  const left = arr.filter(x => x < pivot);\n  const middle = arr.filter(x => x === pivot);\n  const right = arr.filter(x => x > pivot);\n  \n  return [...quickSort(left), ...middle, ...quickSort(right)];\n}",
      "language": "javascript",
      "tags": ["algorithm", "sort", "quicksort", "array"],
      "priority": 8,
      "createdAt": 1640995200000,
      "updatedAt": 1640995200000,
      "isEnabled": true
    }
  ]
}
```

### 规则示例

```json
{
  "rules": [
    {
      "id": "rule-001",
      "name": "JavaScript 代码风格",
      "description": "JavaScript 代码风格规范",
      "content": "使用 const 和 let 声明变量，避免使用 var。使用箭头函数，使用模板字符串，使用解构赋值。",
      "language": "javascript",
      "priority": 8,
      "createdAt": 1640995200000,
      "updatedAt": 1640995200000,
      "isEnabled": true
    },
    {
      "id": "rule-002",
      "name": "通用命名规范",
      "description": "通用的变量和函数命名规范",
      "content": "使用有意义的变量名，避免使用单字母变量（除了循环计数器）。函数名使用动词开头，类名使用名词。",
      "language": null,
      "priority": 7,
      "createdAt": 1640995200000,
      "updatedAt": 1640995200000,
      "isEnabled": true
    },
    {
      "id": "rule-003",
      "name": "Python 风格指南",
      "description": "遵循 PEP 8 风格指南",
      "content": "使用 snake_case 命名变量和函数，使用 PascalCase 命名类。每行不超过 79 个字符，使用 4 个空格缩进。",
      "language": "python",
      "priority": 9,
      "createdAt": 1640995200000,
      "updatedAt": 1640995200000,
      "isEnabled": true
    }
  ]
}
```

## 工作原理

### 代码片段匹配算法

1. **语言过滤**：首先过滤出适用于当前语言的片段
2. **相似度计算**：使用 Jaccard 相似度计算代码相似度
3. **标签匹配**：计算标签匹配度
4. **综合评分**：结合相似度和优先级计算最终得分
5. **排序选择**：按得分排序，选择最相关的片段

### 规则应用机制

1. **语言匹配**：根据当前文件语言选择适用规则
2. **优先级排序**：按优先级排序规则
3. **提示词集成**：将规则内容加入到续写提示词中
4. **约束应用**：AI 模型根据规则约束生成代码

### 配置存储

- **存储位置**：`.vscode/user-snippets.json`
- **格式**：JSON 格式，包含版本信息
- **备份**：支持导出为独立文件
- **同步**：可与其他开发者共享配置

## 最佳实践

### 代码片段设计

1. **明确用途**：为每个片段设置清晰的名称和描述
2. **合理标签**：使用相关标签提高匹配精度
3. **适度大小**：片段不宜过长，建议 10-50 行
4. **语言特定**：为不同语言创建专门的片段
5. **定期维护**：定期检查和更新片段内容

### 规则编写

1. **简洁明确**：规则内容要简洁明了
2. **具体可行**：避免过于抽象的描述
3. **语言相关**：针对特定语言编写具体规则
4. **优先级合理**：重要规则设置更高优先级
5. **定期更新**：根据项目需求调整规则

### 配置管理

1. **版本控制**：将配置文件加入版本控制
2. **团队共享**：与团队成员共享配置
3. **定期备份**：定期导出配置作为备份
4. **环境隔离**：为不同项目使用不同配置

## 故障排除

### 常见问题

**Q: 代码片段没有匹配到？**
A: 检查片段是否启用，语言是否匹配，标签是否相关。

**Q: 规则没有生效？**
A: 确认规则已启用，语言匹配正确，优先级设置合理。

**Q: 配置文件损坏？**
A: 使用重置功能恢复默认配置，或从备份文件导入。

**Q: 管理面板无法打开？**
A: 检查 VSCode 版本，重启 VSCode 或重新安装插件。

### 性能优化

1. **片段数量**：建议每个语言不超过 50 个片段
2. **规则数量**：建议总规则数不超过 20 个
3. **标签数量**：每个片段标签数建议 3-8 个
4. **定期清理**：删除不再使用的片段和规则

## 更新日志

### v1.0.0 (2024-01-XX)
- ✅ 初始版本发布
- ✅ 支持用户自定义代码片段
- ✅ 支持用户自定义规则
- ✅ Web 管理界面
- ✅ 配置导入导出
- ✅ 智能匹配算法
- ✅ 多语言支持 