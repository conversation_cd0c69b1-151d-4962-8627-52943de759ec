# 项目需求文档（requirements.md）

> 本文档用于记录本项目的所有功能性与非功能性需求，持续更新，保持与代码和设计同步。

## 项目简介

请在此补充项目的整体目标和背景说明。

## 需求列表

| 需求编号 | 需求描述 | 优先级 | 状态 | 备注 |
| -------- | -------- | ------ | ---- | ---- |

# 项目需求总览

## 4. 上下文融合检索

### 4.1 关键字检索（BM25）
- 支持通过 BM25 算法对代码/文档进行关键字相关性检索
- 提供统一接口 `KeywordContextProvider`，实现类 `KeywordContextProviderImpl`，异步方法 `searchByKeywords(code: string): Promise<ContextItem[]>`
- 具备完善的单元测试，覆盖正常、边界、异常场景

### 4.2 调用链检索
- 支持基于函数/方法调用链的上下文检索
- 提供统一接口 `GraphContextProvider`，实现类 `GraphContextProviderImpl`，异步方法 `getContextFromGraph(functionNode: ASTNode): Promise<ContextItem[]>`
- 具备完善的单元测试，覆盖正常、边界、异常场景

### 4.3 向量检索
- 支持基于向量（如 embedding）的上下文语义检索
- 提供统一接口 `EmbeddingContextProvider`，实现类 `EmbeddingContextProviderImpl`，异步方法 `searchByEmbedding(code: string): Promise<ContextItem[]>`
- 具备完善的单元测试，覆盖正常、边界、异常场景

#### 相关实现
- 关键字检索：src/components/code_context/KeywordContextProviderImpl.ts
- 调用链检索：src/components/code_context/GraphContextProviderImpl.ts
- 向量检索：src/components/code_context/EmbeddingContextProviderImpl.ts
- 相关接口定义：src/components/code_context/types.ts
- 相关测试：src/components/code_context/__tests__/

## 6. 数据提取与SFT训练

### 6.1 核心功能
- 支持多种数据提取策略，从代码仓库抽取结构化样本用于SFT训练
- 提供统一数据结构 `SFTSample`，便于下游训练和评估
- 支持单文件提取、全策略对比提取、工作区批量提取

### 6.2 支持的提取策略
- **AST语句级提取** (`statement`)：解析AST遍历语句，生成细粒度训练样本
- **函数级提取** (`function`)：以函数为单位提取完整函数体
- **函数+上下文提取** (`function_with_context`)：结合上下文融合引擎获取相关代码片段

### 6.3 支持的编程语言
- C语言 (`.c`)
- NP汇编 (`.np`)  
- 汇编语言 (`.asm`)

### 6.4 主要接口
- `extractSFTData(filePath, strategy)` - 单文件提取
- `extractAllStrategies(filePath)` - 全策略并发提取
- `extractSFTDataFromWorkspace(strategy)` - 工作区批量提取

### 6.5 集成方式
- 通过VSCode命令 `code-partner.extractSFTData` 触发交互式提取
- 支持编程接口调用，便于批量处理和自动化集成
- 具备完善的错误处理和健壮性保障

### 6.6 质量保证
- 每种策略都有对应的单元测试和集成测试

- 具备完整的文档和使用示例

### 6.7 相关实现
- 核心类：src/components/data_extraction/DataExtractor.ts
- 策略实现：src/components/data_extraction/*ExtractionStrategy.ts
- 类型定义：src/components/data_extraction/types.ts
- 测试用例：src/components/data_extraction/__tests__/

### 6.8 相关文档
- 设计文档：docs/design.md#6
- 详细用法：docs/modules/data_extraction.md
- 任务追溯：docs/tasks/task-20240611-data-extraction.md
