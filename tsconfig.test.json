{"extends": "./tsconfig.json", "compilerOptions": {"types": ["jest", "node"], "esModuleInterop": true, "module": "commonjs", "target": "ES2020", "lib": ["ES2020"], "sourceMap": true, "rootDir": ".", "strict": true, "skipLibCheck": true, "baseUrl": ".", "paths": {"src/*": ["src/*"]}}, "include": ["src/**/*.test.ts", "src/**/__tests__/*.test.ts", "tests/**/*.test.ts"], "exclude": ["node_modules/@types/mocha"]}