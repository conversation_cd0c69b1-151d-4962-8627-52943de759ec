/**
 * 统一的语义类型枚举，用于跨语言 AST 处理
 */
export enum ASTNodeKind {
  // 函数相关
  FUNCTION = 'function',
  FUNCTION_DEFINITION = 'function_definition',
  FUNCTION_DECLARATION = 'function_declaration',
  FUNCTION_CALL = 'function_call',
  
  // 变量相关
  VARIABLE = 'variable',
  VARIABLE_DECLARATION = 'variable_declaration',
  VARIABLE_ASSIGNMENT = 'variable_assignment',
  
  // 类型相关
  TYPE = 'type',
  TYPE_DEFINITION = 'type_definition',
  STRUCT = 'struct',
  UNION = 'union',
  ENUM = 'enum',
  
  // 宏相关
  MACRO = 'macro',
  MACRO_DEFINITION = 'macro_definition',
  MACRO_CALL = 'macro_call',
  
  // 模块相关
  MODULE = 'module',
  MODULE_IMPORT = 'module_import',
  MODULE_EXPORT = 'module_export',
  
  // 类相关
  CLASS = 'class',
  CLASS_DEFINITION = 'class_definition',
  CLASS_DECLARATION = 'class_declaration',
  
  // 接口相关
  INTERFACE = 'interface',
  INTERFACE_DEFINITION = 'interface_definition',
  
  // 其他
  EXPRESSION = 'expression',
  STATEMENT = 'statement',
  PROGRAM = 'program'
}

/**
 * 统一的 AST 节点接口
 * 屏蔽各个代码解析器的 type 差异，提供统一的语义抽象
 */
export interface ASTNode {
  /**
   * 节点名称（函数名、变量名、类型名等）
   */
  name?: string;
  
  /**
   * 统一的语义类型，用于跨语言 AST 处理
   * 所有解析器都应该将具体的 type 映射到这些统一的 kind
   */
  kind: ASTNodeKind;
  
  /**
   * 原始语法类型，来自 tree-sitter 的 node.type
   * 仅用于调试和底层语法分析，不应在业务逻辑中使用
   */
  originalType?: string;
  
  /**
   * 节点文本内容
   */
  text?: string;
  
  /**
   * 子节点列表
   */
  children: ASTNode[];
  
  /**
   * 位置信息
   */
  location?: {
    start: { line: number; column: number };
    end: { line: number; column: number };
  };
  
  /**
   * 语言标识
   */
  language?: string;
  
  /**
   * 文件路径
   */
  file?: string;
  
  /**
   * 模块路径
   */
  module?: string;
  
  /**
   * 父节点ID
   */
  parentId?: string;
  
  /**
   * 扩展元数据
   */
  metadata?: {
    // 函数相关
    isStatic?: boolean;
    isExtern?: boolean;
    isInline?: boolean;
    isBundle?: boolean;
    isMacro?: boolean;
    isCall?: boolean;
    isMacroCall?: boolean;
    
    // 可见性
    visibility?: 'public' | 'private' | 'protected';
    
    // 类型信息
    returnType?: string;
    variableType?: string;
    
    // 参数信息
    parameters?: Array<{
      name: string;
      type: string;
    }>;
    
    // 调用信息
    caller?: string;
    arguments?: string[];
    
    // 宏相关
    value?: string;
    
    // 其他
    [key: string]: any;
  };
}

/**
 * AST 节点工厂接口
 * 用于创建标准化的 AST 节点
 */
export interface ASTNodeFactory {
  createFunctionDefinition(name: string, params?: Array<{name: string, type: string}>, metadata?: any): ASTNode;
  createFunctionDeclaration(name: string, params?: Array<{name: string, type: string}>, metadata?: any): ASTNode;
  createFunctionCall(name: string, args?: string[], metadata?: any): ASTNode;
  createVariableDeclaration(name: string, type: string, metadata?: any): ASTNode;
  createTypeDefinition(name: string, metadata?: any): ASTNode;
  createMacroDefinition(name: string, value?: string, metadata?: any): ASTNode;
  createMacroCall(name: string, args?: string[], metadata?: any): ASTNode;
  createModuleImport(name: string, metadata?: any): ASTNode;
}

/**
 * 默认的 AST 节点工厂实现
 */
export class DefaultASTNodeFactory implements ASTNodeFactory {
  createFunctionDefinition(name: string, params?: Array<{name: string, type: string}>, metadata?: any): ASTNode {
    return {
      name,
      kind: ASTNodeKind.FUNCTION, // 使用通用的 function 类型以保持兼容性
      children: [],
      metadata: {
        parameters: params || [],
        ...metadata
      }
    };
  }

  createFunctionDeclaration(name: string, params?: Array<{name: string, type: string}>, metadata?: any): ASTNode {
    return {
      name,
      kind: ASTNodeKind.FUNCTION, // 使用通用的 function 类型以保持兼容性
      children: [],
      metadata: {
        parameters: params || [],
        ...metadata
      }
    };
  }

  createFunctionCall(name: string, args?: string[], metadata?: any): ASTNode {
    return {
      name,
      kind: ASTNodeKind.FUNCTION, // 使用通用的 function 类型以保持兼容性
      children: [],
      metadata: {
        arguments: args || [],
        isCall: true,
        ...metadata
      }
    };
  }

  createVariableDeclaration(name: string, type: string, metadata?: any): ASTNode {
    return {
      name,
      kind: ASTNodeKind.VARIABLE_DECLARATION,
      children: [],
      metadata: {
        variableType: type,
        ...metadata
      }
    };
  }

  createTypeDefinition(name: string, metadata?: any): ASTNode {
    return {
      name,
      kind: ASTNodeKind.TYPE_DEFINITION,
      children: [],
      metadata
    };
  }

  createMacroDefinition(name: string, value?: string, metadata?: any): ASTNode {
    return {
      name,
      kind: ASTNodeKind.FUNCTION, // 根据项目规范，宏统一为function类型
      children: [],
      metadata: {
        value,
        type: 'macro', // 在元数据中标记实际类型
        isMacro: true, // 保持向后兼容
        ...metadata
      }
    };
  }

  createMacroCall(name: string, args?: string[], metadata?: any): ASTNode {
    return {
      name,
      kind: ASTNodeKind.FUNCTION, // 使用通用的 function 类型以保持兼容性
      children: [],
      metadata: {
        arguments: args || [],
        isCall: true,
        isMacroCall: true,
        ...metadata
      }
    };
  }

  createModuleImport(name: string, metadata?: any): ASTNode {
    return {
      name,
      kind: ASTNodeKind.MODULE_IMPORT,
      children: [],
      metadata
    };
  }
} 