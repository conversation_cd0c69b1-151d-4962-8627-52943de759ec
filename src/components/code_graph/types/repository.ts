import { CodeNodeType, Language, NodeKind } from './graph';

export interface CodeNode {
    id: string;
    name: string;
    type: CodeNodeType;
    language: Language;
    kind: NodeKind;
    file?: string;
    module?: string;
    parentId?: string;
    metadata?: Record<string, any>;
    definition?: string;
    documentation?: string;
    location?: {
        start: { line: number; column: number };
        end: { line: number; column: number };
    };
}

/**
 * 表示一个源码文件或逻辑模块（如 .c 文件、.go 文件、.py 文件、包、模块等）
 */
export interface CodeModule {
    path: string;                // 文件路径，如 src/main.c
    language: Language;
    nodeIds: Set<string>;
    imports: Set<string>;        // 引用的模块路径
    exports?: Set<string>;       // 导出的节点 ID（对外可见）
    metadata?: Record<string, any>; // 其他自定义字段，如 include 类型、注释等
}

export interface CodeRepository {
    workspacePath: string;
    modules: Map<string, CodeModule>;
    graph: {
        nodes: Map<string, CodeNode>;
        edges: [string, string, string?][];
    };
}
