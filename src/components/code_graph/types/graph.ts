//code_graph/types/graph.ts
// 语言类型
export type Language =
  | 'c'
  | 'go'
  | 'python'
  | 'javascript'
  | 'typescript'
  | 'java'
  | 'csharp'
  | string;

// 节点种类：声明 / 定义 / 推断 / 引入
export type NodeKind = 'module'|'function' | 'variable' | 'class' | 'interface' | 'type' | 'macro' ;

// 节点类型
export type CodeNodeType =
  | 'function'
  | 'variable'
  | 'class'
  | 'struct'
  | 'interface'
  | 'macro'
  | 'module'
  | 'package'
  | 'namespace'
  | 'entry'
  | string;

// 通用代码节点结构
export interface GraphCodeNode {
  readonly id: string;
  readonly name: string;
  readonly type: CodeNodeType;
  readonly language: Language;
  readonly kind: NodeKind;
  readonly file?: string;
  readonly module?: string;
  readonly parentId?: string;
  readonly metadata?: Record<string, any>;
  readonly definition?: string;
  readonly documentation?: string;
  readonly location?: {
    start: { line: number; column: number };
    end: { line: number; column: number };
  };
}


/**
 * 表示节点之间的边（语义关系）
 */
export interface CodeEdge {
  from: string;                    // 起点节点 ID
  to: string;                      // 终点节点 ID
  type: 'calls'                   // 函数调用
      | 'inherits'                // 类继承
      | 'uses'                    // 使用变量、类型
      | 'imports'                 // 模块导入
      | 'defines'                 // 模块定义了某个节点
      | string;                   // 可扩展的边类型
}

/**
 * 代码图结构
 */
export interface ICodeGraph {
  nodes: Map<string, GraphCodeNode>;
  edges: CodeEdge[];
  
  addNode(node: GraphCodeNode): void;
  removeNode(nodeId: string): void;
  addEdge(from: string, to: string, type?: string): void;
  removeEdge(from: string, to: string): void;
  node(id: string): GraphCodeNode | undefined;
  getIncomingEdges(nodeId: string): CodeEdge[];
  getOutgoingEdges(nodeId: string): CodeEdge[];
  getRelatedNodes(nodeId: string,depth:number): GraphCodeNode[];
  getModuleRelatedNodes(nodeId: string, modulePath: string): GraphCodeNode[];
}