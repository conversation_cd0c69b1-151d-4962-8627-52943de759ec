import { ICodeGraph } from './graph';
import { CodeNode } from './repository';

export interface IRepoIndexer {
  /** 工作区路径 */
  readonly workspacePath: string;

  /**
   * 分析代码仓库
   * @param incremental 是否增量分析
   */
  analyze(incremental?: boolean): Promise<void>;

  /**
   * 获取代码图
   */
  getGraph(): ICodeGraph;

  /**
   * 导出数据
   */
  exportData(): {
    workspacePath: string;
    languageExts: string[];
    graph: ICodeGraph;
    lastModifiedTime: [string, number][];
  };

  /**
   * 从数据恢复
   */
  restoreFromData(data: any): Promise<void>;

  /**
   * 节点查询接口
   */

  /**
   * 根据名称和类型查找节点
   * @param name 节点名称
   * @param type 节点类型，默认为 'function'
   */
  findNodeByName(name: string, type?: string): CodeNode | undefined;

  /**
   * 在特定文件中查找节点
   * @param filePath 文件路径
   * @param name 节点名称
   * @param type 节点类型，默认为 'function'
   */
  findNodeInFile(filePath: string, name: string, type?: string): CodeNode | undefined;

  /**
   * 查找所有同名节点
   * @param name 节点名称
   * @param type 节点类型，默认为 'function'
   */
  findAllNodesByName(name: string, type?: string): CodeNode[];

  /**
   * 关系查询接口
   */

  /**
   * 查找函数的调用者
   * @param name 函数名称
   * @param type 节点类型，默认为 'function'
   */
  findCallersByName(name: string, type?: string): CodeNode[];

  /**
   * 查找函数调用的其他函数
   * @param name 函数名称
   * @param type 节点类型，默认为 'function'
   */
  findCalleesByName(name: string, type?: string): CodeNode[];

  /**
   * 查找所有相关函数
   * @param name 函数名称
   * @param type 节点类型，默认为 'function'
   */
  findRelatedFunctionsByName(name: string, depth: number, type?: string): CodeNode[]

  /**
   * 查找相似函数
   * @param name 函数名称
   * @param type 节点类型，默认为 'function'
   * @param similarityThreshold 相似度阈值，默认为0.6
   */
  findSimilarFunctionsByName(name: string, type?: string, similarityThreshold?: number): CodeNode[];

  /**
   * 查找模块中的相关节点
   * @param name 节点名称
   * @param filePath 文件路径
   * @param type 节点类型，默认为 'function'
   */
  getModuleRelatedNodesByName(name: string, filePath: string, type?: string): CodeNode[];
}
