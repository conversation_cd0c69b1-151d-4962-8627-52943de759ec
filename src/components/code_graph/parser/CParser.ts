// 延迟加载 web-tree-sitter，避免 locateFile 未就绪时提早拉取 WASM
// 注意：不再在这里定义 Parser，而是从 LanguageLoader 获取

// 定义 Node 类型
type Node = any;
import { ASTNode, ASTNodeFactory, ASTNodeKind, DefaultASTNodeFactory } from '../types/ast';
import { ICodeParser } from '../types/codeparserIntf';
import { LanguageLoader } from './LanguageLoader';

export class CParser implements ICodeParser {
  private parser: any;
  private currentFunction: string | null = null;
  private nodeFactory: ASTNodeFactory;

  private constructor(private includePaths: string[] = []) {
    this.nodeFactory = new DefaultASTNodeFactory();
  }

  static async create(includePaths: string[] = []) {
    const instance = new CParser(includePaths);
    await instance.init();
    return instance;
  }

  /**
   * 使用预加载的语言模块创建解析器
   * 用于 Worker 环境，避免重复加载 WASM 文件
   */
  static async createWithLanguage(C: any, includePaths: string[] = []) {
    const instance = new CParser(includePaths);
    await instance.initWithLanguage(C);
    return instance;
  }

  private async init() {
    try {
      console.log('🔄 CParser 开始初始化...');
      
      // 使用统一的语言加载器
      const C = await LanguageLoader.loadCLanguage();
      
      // 检查语言模块是否加载成功
      if (!C) {
        console.warn('⚠️ C 语言模块版本不兼容，使用 mock 模式');
        this.parser = null;
        return;
      }
      
      console.log('🔄 创建 Parser 实例...');
      // 从 LanguageLoader 获取正确的 Parser 类
      const { Parser: TreeSitterParser } = LanguageLoader.getClasses();
      this.parser = new TreeSitterParser();
      
      console.log('🔄 设置语言...');
      console.log(`📊 C 语言对象类型: ${typeof C}`);
      console.log(`📊 C 语言对象: ${C ? '存在' : '不存在'}`);

      try {
        // 添加详细的调试信息
        console.log('🔍 [CParser] 语言对象详细信息:', {
          C: typeof C,
          'C.version': C?.version,
          'C.name': C?.name,
          'C.constructor': C?.constructor?.name,
          'C.toString': C?.toString?.()
        });

        this.parser.setLanguage(C);
        console.log('CParser初始化成功，使用tree-sitter-c解析器');
      } catch (error) {
        console.error('❌ 语言设置失败:', error);

        // 添加更详细的错误信息
        console.error('🔍 [CParser] 错误时的语言对象状态:', {
          C: typeof C,
          'C.version': C?.version,
          'C.name': C?.name,
          parser: typeof this.parser,
          'parser.setLanguage': typeof this.parser?.setLanguage
        });

        // 检查是否是版本兼容性问题
        if (error instanceof Error && error.message.includes('Incompatible language version')) {
          console.log('🔄 检测到版本兼容性问题，尝试使用兼容模式...');

          // 如果是版本 0，说明语言模块加载失败
          if (error.message.includes('version 0')) {
            throw new Error(`语言模块加载失败，返回版本 0。这通常表示 WASM 文件加载失败或损坏。错误: ${error.message}`);
          }

          throw new Error(`语言模块版本不兼容: ${error.message}。请考虑升级 web-tree-sitter 或使用兼容的语言模块版本。`);
        }
        throw error;
      }
    } catch (error) {
      console.warn('CParser初始化失败，使用mock模式:', error);
      console.error('详细错误信息:', error);
      if (error instanceof Error) {
        console.error('错误堆栈:', error.stack);
      }
      // 如果tree-sitter初始化失败，使用mock模式
      this.parser = null;
    }
  }

  /**
   * 使用预加载的语言模块初始化解析器
   * 用于 Worker 环境，避免重复加载 WASM 文件
   */
  private async initWithLanguage(C: any) {
    try {
      console.log('🔄 CParser 使用预加载语言模块初始化...');
      
      // 检查语言模块是否有效
      if (!C) {
        console.warn('⚠️ 预加载的 C 语言模块无效，使用 mock 模式');
        this.parser = null;
        return;
      }
      
      console.log('🔄 创建 Parser 实例...');
      // 从 LanguageLoader 获取正确的 Parser 类
      const { Parser: TreeSitterParser } = LanguageLoader.getClasses();
      this.parser = new TreeSitterParser();
      
      console.log('🔄 设置语言...');
      console.log(`📊 C 语言对象类型: ${typeof C}`);
      console.log(`📊 C 语言对象: ${C ? '存在' : '不存在'}`);

      try {
        // 添加详细的调试信息
        console.log('🔍 [CParser initWithLanguage] 语言对象详细信息:', {
          C: typeof C,
          'C.version': C?.version,
          'C.name': C?.name,
          'C.constructor': C?.constructor?.name
        });

        this.parser.setLanguage(C);
        console.log('CParser初始化成功，使用预加载的tree-sitter-c解析器');
      } catch (languageError) {
        console.error('❌ 语言设置失败:', languageError);

        // 添加更详细的错误信息
        console.error('🔍 [CParser initWithLanguage] 错误时的语言对象状态:', {
          C: typeof C,
          'C.version': C?.version,
          'C.name': C?.name,
          parser: typeof this.parser,
          'parser.setLanguage': typeof this.parser?.setLanguage
        });

        // 检查是否是版本兼容性问题
        if (languageError instanceof Error && languageError.message.includes('Incompatible language version')) {
          console.log('🔄 检测到版本兼容性问题，尝试使用兼容模式...');

          // 如果是版本 0，说明语言模块加载失败
          if (languageError.message.includes('version 0')) {
            throw new Error(`语言模块加载失败，返回版本 0。这通常表示 WASM 文件加载失败或损坏。错误: ${languageError.message}`);
          }

          // 抛出到外层 catch 块，让它处理为 mock 模式
          throw new Error(`语言模块版本不兼容: ${languageError.message}。系统将使用 mock 模式。`);
        }
        throw languageError;
      }
    } catch (error) {
      console.warn('CParser初始化失败，使用mock模式:', error);
      console.error('详细错误信息:', error);
      if (error instanceof Error) {
        console.error('错误堆栈:', error.stack);
      }
      // 如果tree-sitter初始化失败，使用mock模式
      this.parser = null;
    }
  }

  public parse(content: string): ASTNode {
    if (!this.parser) {
      throw new Error('CParser未初始化，tree-sitter解析器不可用');
    }
    
    try {
      const tree = this.parser.parse(content);
      this.currentFunction = null;
      const astNode = this.processNode(tree.rootNode);
      return astNode;
    } catch (error) {
      console.error('CParser解析失败:', error);
      throw error;
    }
  }

  private processNode(node: Node): ASTNode {
    const astNode: ASTNode = {
      kind: ASTNodeKind.PROGRAM, // 默认类型
      originalType: node.type,
      text: node.text,
      children: [],
      language: 'c',
      location: {
        start: {
          line: node.startPosition.row,
          column: node.startPosition.column
        },
        end: {
          line: node.endPosition.row,
          column: node.endPosition.column
        }
      }
    };

    // 根据 tree-sitter 的 node.type 映射到统一的 kind
    switch (node.type) {
      // 函数定义
      case 'function_definition':
        return this.processFunctionDefinition(node);
      
      // 函数声明
      case 'function_declaration':
        return this.processFunctionDeclaration(node);
      
      // 函数调用
      case 'call_expression':
        return this.processFunctionCall(node);
      
      // 变量声明
      case 'declaration':
        return this.processVariableDeclaration(node);
      
      // 宏定义
      case 'preproc_function_def':
      case 'preproc_def':
        return this.processMacroDefinition(node);
      
      // 宏调用
      case 'preproc_call':
        return this.processMacroCall(node);
      
      // 模块导入
      case 'preproc_include':
        return this.processModuleImport(node);
      
      // 翻译单元，递归处理子节点
      case 'translation_unit':
        // 递归处理子节点
        for (const child of node.namedChildren) {
          if (child) {
            const childNode = this.processNode(child);
            if (childNode.kind !== ASTNodeKind.PROGRAM) {
              astNode.children.push(childNode);
            } else {
              // 如果子节点是 PROGRAM，将其子节点合并到当前节点
              astNode.children.push(...childNode.children);
            }
          }
        }
        return astNode;
      
      // 其他节点，递归处理子节点
      default:
        // 递归处理子节点
        for (const child of node.namedChildren) {
          if (child) {
            const childNode = this.processNode(child);
            if (childNode.kind !== ASTNodeKind.PROGRAM) {
              astNode.children.push(childNode);
            } else {
              // 如果子节点是 PROGRAM，将其子节点合并到当前节点
              astNode.children.push(...childNode.children);
            }
          }
        }
        return astNode;
    }
  }

  private processFunctionDefinition(node: Node): ASTNode {
    const identifier = node.namedChildren.find((c: Node | null) => c !== null && c.type === 'identifier');
    if (!identifier) {
      return this.createDefaultNode(node);
    }

    const params = this.extractParameters(node);
    const metadata = this.extractFunctionMetadata(node);
    
    const astNode = this.nodeFactory.createFunctionDefinition(
      identifier.text,
      params,
      {
        ...metadata,
        caller: this.currentFunction
      }
    );

    // 设置位置和文本
    astNode.location = {
      start: { line: node.startPosition.row, column: node.startPosition.column },
      end: { line: node.endPosition.row, column: node.endPosition.column }
    };
    astNode.text = node.text;
    astNode.language = 'c';

    // // 调试：显示函数解析的详细信息
    // console.log(`=== CParser解析函数 ===`);
    // console.log(`函数名: ${identifier.text}`);
    // console.log(`tree-sitter位置: ${node.startPosition.row}:${node.startPosition.column} - ${node.endPosition.row}:${node.endPosition.column}`);
    // console.log(`AST位置: ${astNode.location.start.line}:${astNode.location.start.column} - ${astNode.location.end.line}:${astNode.location.end.column}`);
    // console.log(`函数文本长度: ${astNode.text?.length || 0}`);
    // console.log(`函数文本预览: "${astNode.text?.substring(0, 200)}..."`);

    // 递归处理函数体
    for (const child of node.namedChildren) {
      if (child && child.type !== 'identifier' && child.type !== 'parameter_list') {
        const childNode = this.processNode(child);
        if (childNode.kind !== ASTNodeKind.PROGRAM) {
          astNode.children.push(childNode);
        } else {
          astNode.children.push(...childNode.children);
        }
      }
    }

    this.currentFunction = identifier.text;
    return astNode;
  }

  private processFunctionDeclaration(node: Node): ASTNode {
    const identifier = node.namedChildren.find((c: Node | null) => c !== null && c.type === 'identifier');
    if (!identifier) {
      return this.createDefaultNode(node);
    }

    const params = this.extractParameters(node);
    const metadata = this.extractFunctionMetadata(node);
    
    const astNode = this.nodeFactory.createFunctionDeclaration(
      identifier.text,
      params,
      {
        ...metadata,
        caller: this.currentFunction
      }
    );

    astNode.location = {
      start: { line: node.startPosition.row, column: node.startPosition.column },
      end: { line: node.endPosition.row, column: node.endPosition.column }
    };
    astNode.text = node.text;
    astNode.language = 'c';

    return astNode;
  }

  private processFunctionCall(node: Node): ASTNode {
    const functionNode = node.namedChildren.find((c: Node | null) => c !== null && c.type === 'identifier');
    if (!functionNode) {
      return this.createDefaultNode(node);
    }

    const args = this.extractArguments(node);
    
    const astNode = this.nodeFactory.createFunctionCall(
      functionNode.text,
      args,
      {
        caller: this.currentFunction
      }
    );

    astNode.location = {
      start: { line: node.startPosition.row, column: node.startPosition.column },
      end: { line: node.endPosition.row, column: node.endPosition.column }
    };
    astNode.text = node.text;
    astNode.language = 'c';

    return astNode;
  }

  private processVariableDeclaration(node: Node): ASTNode {
    const varType = node.childForFieldName('type');
    const varName = node.namedChildren.find((c: Node | null) => c !== null && c.type === 'identifier');
    
    if (!varName) {
      return this.createDefaultNode(node);
    }

    const astNode = this.nodeFactory.createVariableDeclaration(
      varName.text,
      varType?.text || 'unknown'
    );

    astNode.location = {
      start: { line: node.startPosition.row, column: node.startPosition.column },
      end: { line: node.endPosition.row, column: node.endPosition.column }
    };
    astNode.text = node.text;
    astNode.language = 'c';

    return astNode;
  }

  private processMacroDefinition(node: Node): ASTNode {
    // 根据调试输出，宏名称是identifier子节点，值是preproc_arg子节点
    const macroName = node.namedChildren.find((c: Node | null) => c !== null && c.type === 'identifier');
    if (!macroName) {
      return this.createDefaultNode(node);
    }

    const value = node.namedChildren.find((c: Node | null) => c !== null && c.type === 'preproc_arg')?.text || '';

    const astNode = this.nodeFactory.createMacroDefinition(macroName.text, value);

    astNode.location = {
      start: { line: node.startPosition.row, column: node.startPosition.column },
      end: { line: node.endPosition.row, column: node.endPosition.column }
    };
    astNode.text = node.text;
    astNode.language = 'c';

    this.currentFunction = macroName.text;
    return astNode;
  }

  private processMacroCall(node: Node): ASTNode {
    const macroName = node.namedChildren.find((c: Node | null) => c !== null && c.type === 'identifier');
    if (!macroName) {
      return this.createDefaultNode(node);
    }

    const args = node.namedChildren
      .filter((c: Node | null) => c !== null && c.type === 'preproc_arg')
      .map((c: Node | null) => c?.text || '');

    const astNode = this.nodeFactory.createMacroCall(
      macroName.text,
      args,
      {
        caller: this.currentFunction
      }
    );

    astNode.location = {
      start: { line: node.startPosition.row, column: node.startPosition.column },
      end: { line: node.endPosition.row, column: node.endPosition.column }
    };
    astNode.text = node.text;
    astNode.language = 'c';

    return astNode;
  }

  private processModuleImport(node: Node): ASTNode {
    const moduleName = node.namedChildren.find((c: Node | null) => c !== null && c.type === 'string_literal');
    if (!moduleName) {
      return this.createDefaultNode(node);
    }

    const astNode = this.nodeFactory.createModuleImport(moduleName.text);

    astNode.location = {
      start: { line: node.startPosition.row, column: node.startPosition.column },
      end: { line: node.endPosition.row, column: node.endPosition.column }
    };
    astNode.text = node.text;
    astNode.language = 'c';

    return astNode;
  }

  private extractParameters(node: Node): Array<{name: string, type: string}> {
    const parameters = node.namedChildren.find((c: Node | null) => c !== null && c.type === 'parameter_list');
    if (!parameters) {
      return [];
    }

    const paramList: Array<{name: string, type: string}> = [];
    for (const param of parameters.namedChildren) {
      if (param && param.type === 'parameter_declaration') {
        const paramType = param.childForFieldName('type');
        const paramName = param.childForFieldName('declarator');
        if (paramType && paramName) {
          paramList.push({
            name: paramName.text,
            type: paramType.text
          });
        }
      }
    }
    return paramList;
  }

  private extractArguments(node: Node): string[] {
    const argList = node.namedChildren.find((c: Node | null) => c !== null && c.type === 'argument_list');
    if (!argList) {
      return [];
    }

    return argList.namedChildren.map((arg: Node | null) => arg?.text || '');
  }

  private extractFunctionMetadata(node: Node): any {
    const metadata: any = {};
    
    // 修饰符
    const storageClass = node.childForFieldName('storage_class');
    if (storageClass) {
      metadata.storageClass = storageClass.text;
    }

    return metadata;
  }

  private createDefaultNode(node: Node): ASTNode {
    return {
      kind: ASTNodeKind.PROGRAM,
      originalType: node.type,
      text: node.text,
      children: [],
      language: 'c',
      location: {
        start: { line: node.startPosition.row, column: node.startPosition.column },
        end: { line: node.endPosition.row, column: node.endPosition.column }
      }
    };
  }
} 