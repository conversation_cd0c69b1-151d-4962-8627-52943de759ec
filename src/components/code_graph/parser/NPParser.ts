// 延迟加载 web-tree-sitter，避免 locateFile 未就绪时提早拉取 WASM
// 注意：不再在这里定义 Parser，而是从 LanguageLoader 获取

// 定义 Node 类型
type Node = any;
import { ASTNode, ASTNodeFactory, ASTNodeKind, DefaultASTNodeFactory } from '../types/ast';
import { ICodeParser } from '../types/codeparserIntf';
import { LanguageLoader } from './LanguageLoader';

export class NPParser implements ICodeParser {
  private parser: any;
  private currentFunction: string | null = null;
  private nodeFactory: ASTNodeFactory;

  private constructor(private includePaths: string[] = []) {
    this.nodeFactory = new DefaultASTNodeFactory();
  }

  static async create(includePaths: string[] = []) {
    const instance = new NPParser(includePaths);
    await instance.init();
    return instance;
  }

  /**
   * 使用预加载的语言模块创建解析器
   * 用于 Worker 环境，避免重复加载 WASM 文件
   */
  static async createWithLanguage(NP: any, includePaths: string[] = []) {
    const instance = new NPParser(includePaths);
    await instance.initWithLanguage(NP);
    return instance;
  }

  private async init() {
    try {
      console.log('🔄 NPParser 开始初始化...');
      
      // 使用统一的语言加载器
      const NP = await LanguageLoader.loadNPLanguage();
      
      // 检查语言模块是否加载成功
      if (!NP) {
        console.warn('⚠️ NP 语言模块版本不兼容，使用 mock 模式');
        this.parser = null;
        return;
      }
      
      console.log('🔄 创建 Parser 实例...');
      // 从 LanguageLoader 获取正确的 Parser 类
      const { Parser: TreeSitterParser } = LanguageLoader.getClasses();
      this.parser = new TreeSitterParser();
      
      console.log('🔄 设置语言...');
      console.log(`📊 NP 语言对象类型: ${typeof NP}`);
      console.log(`📊 NP 语言对象: ${NP ? '存在' : '不存在'}`);
      
      this.parser.setLanguage(NP);
      console.log('NPParser初始化成功，使用tree-sitter-np解析器');
    } catch (error) {
      console.warn('NPParser初始化失败，使用mock模式:', error);
      console.error('详细错误信息:', error);
      if (error instanceof Error) {
        console.error('错误堆栈:', error.stack);
      }
      // 如果tree-sitter初始化失败，使用mock模式
      this.parser = null;
    }
  }

  /**
   * 使用预加载的语言模块初始化解析器
   * 用于 Worker 环境，避免重复加载 WASM 文件
   */
  private async initWithLanguage(NP: any) {
    try {
      console.log('🔄 NPParser 使用预加载语言模块初始化...');
      
      // 检查语言模块是否有效
      if (!NP) {
        console.warn('⚠️ 预加载的 NP 语言模块无效，使用 mock 模式');
        this.parser = null;
        return;
      }
      
      
      console.log('🔄 创建 Parser 实例...');
      // 从 LanguageLoader 获取正确的 Parser 类
      const { Parser: TreeSitterParser } = LanguageLoader.getClasses();
      this.parser = new TreeSitterParser();
      
      console.log('🔄 设置语言...');
      console.log(`📊 NP 语言对象类型: ${typeof NP}`);
      console.log(`📊 NP 语言对象: ${NP ? '存在' : '不存在'}`);
      
      this.parser.setLanguage(NP);
      console.log('NPParser初始化成功，使用预加载的tree-sitter-np解析器');
    } catch (error) {
      console.warn('NPParser初始化失败，使用mock模式:', error);
      console.error('详细错误信息:', error);
      if (error instanceof Error) {
        console.error('错误堆栈:', error.stack);
      }
      // 如果tree-sitter初始化失败，使用mock模式
      this.parser = null;
    }
  }

  public parse(content: string): ASTNode {
    if (!this.parser) {
      throw new Error('NPParser未初始化，tree-sitter解析器不可用');
    }
    
    try {
      const tree = this.parser.parse(content);
      this.currentFunction = null;
      
      const astNode = this.processNode(tree.rootNode);
      
      return astNode;
    } catch (error) {
      console.error('NPParser解析失败:', error);
      throw error;
    }
  }

  private processNode(node: Node, parentType?: string, parentNode?: Node): ASTNode {
    // 叶子节点处理 - 只处理特定的 identifier 节点
    if (!node.namedChildren || node.namedChildren.length === 0) {
      if (node.type === 'identifier') {
        // 只有在特定上下文中才将 identifier 处理为函数
        if (parentType === 'declare_bundle' || parentType === 'extern_bundle_declaration' || parentType === 'statement_bundle_definition') {
          return {
            name: node.text,
            kind: ASTNodeKind.FUNCTION,
            originalType: 'function',
            text: node.text,
            children: [],
            language: 'np',
            location: {
              start: { line: node.startPosition.row, column: node.startPosition.column },
              end: { line: node.endPosition.row, column: node.endPosition.column }
            },
            metadata: { isBundle: true }
          };
        }
        if (parentType === 'define_statement' || parentType === 'macro_definition' || parentType === 'statement_marco_definition') {
          // 使用 tree-sitter 解析宏值，根据 NP grammar 使用 field 名称
          let value = '';
          if (parentNode) {
            // 根据 NP grammar: define_statement 有 field("value", $.constant)
            const valueNode = parentNode.childForFieldName('value');
            if (valueNode) {
              value = valueNode.text;
            } else {
              // 如果没有 value field，尝试从文本中提取（但使用更精确的方法）
              const defineText = parentNode.text;
              const defineIndex = defineText.indexOf('#define');
              if (defineIndex !== -1) {
                const afterDefine = defineText.slice(defineIndex + 7).trim();
                // 使用更精确的方法查找宏名称的结束位置
                let nameEndIndex = -1;
                for (let i = 0; i < afterDefine.length; i++) {
                  if (afterDefine[i] === ' ' || afterDefine[i] === '\t' || afterDefine[i] === '\n') {
                    nameEndIndex = i;
                    break;
                  }
                }
                if (nameEndIndex !== -1) {
                  value = afterDefine.slice(nameEndIndex).trim();
                }
              }
            }
          }
          return {
            name: node.text,
            kind: ASTNodeKind.FUNCTION,
            originalType: 'function',
            text: node.text,
            children: [],
            language: 'np',
            location: {
              start: { line: node.startPosition.row, column: node.startPosition.column },
              end: { line: node.endPosition.row, column: node.endPosition.column }
            },
            metadata: { isMacro: true, value }
          };
        }
        if (parentType === 'statement_marco_call' || parentType === 'macro_call') {
          return {
            name: node.text,
            kind: ASTNodeKind.FUNCTION,
            originalType: 'call_expression',
            text: node.text,
            children: [],
            language: 'np',
            location: {
              start: { line: node.startPosition.row, column: node.startPosition.column },
              end: { line: node.endPosition.row, column: node.endPosition.column }
            },
            metadata: { isCall: true, isMacroCall: true }
          };
        }
        // 其他 identifier 节点不处理，返回 null 或默认节点
        return this.createDefaultNode(node);
      }
      // 其他叶子节点不处理，返回默认节点
      return this.createDefaultNode(node);
    }
    const astNode: ASTNode = {
      kind: ASTNodeKind.PROGRAM, // 默认类型
      originalType: node.type,
      text: node.text,
      children: [],
      language: 'np',
      location: {
        start: {
          line: node.startPosition.row,
          column: node.startPosition.column
        },
        end: {
          line: node.endPosition.row,
          column: node.endPosition.column
        }
      }
    };

    // 根据 tree-sitter 的 node.type 映射到统一的 kind
    switch (node.type) {
      // 函数定义
      case 'statement_function_def':
      case 'declare_function':
        return this.processFunctionDefinition(node);
      
      // 处理ERROR节点，尝试提取函数定义
      case 'ERROR':
        return this.processErrorNode(node, parentType, parentNode);
      
      // 函数调用 - 添加对call_expression的支持
      case 'statement_function_call':
      case 'expression_function_call':
      case 'call_expression':
        const functionCallNode = this.processFunctionCall(node);
        functionCallNode.originalType = 'call_expression';
        return functionCallNode;
      
      // bundle 定义/声明 - 可能被解析为 variable_declaration 或 declare_variable
      case 'statement_bundle_definition':
      case 'declare_bundle':
      case 'extern_bundle_declaration':
      case 'variable_declaration':
      case 'declare_variable':
        // 检查是否是 bundle 类型
        if (node.text.includes('bundle')) {
          return this.processBundleDefinition(node);
        }
        // 检查是否包含函数定义
        if (node.text.includes('function ')) {
          return this.processFunctionFromDeclareVariable(node);
        }
        // 检查是否包含C风格函数定义（如 void main()）
        if (this.isCStyleFunctionDefinition(node.text)) {
          return this.processCStyleFunctionDefinition(node);
        }
        return this.processVariableDeclaration(node);
      
      // 宏定义
      case 'define_statement':
      case 'statement_marco_definition':
      case 'macro_definition':
        return this.processMacroDefinition(node);
      
      // 宏调用
      case 'statement_marco_call':
      case 'macro_call':
        return this.processMacroCall(node);
      
      // 模块导入
      case 'include_statement':
        return this.processModuleImport(node);
      
      // 其他节点，递归处理子节点
      default:
        for (const child of node.namedChildren) {
          if (child) {
            const childNode = this.processNode(child, node.type, node);
            if (childNode.kind !== ASTNodeKind.PROGRAM) {
              astNode.children.push(childNode);
            } else {
              astNode.children.push(...childNode.children);
            }
          }
        }
        return astNode;
    }
  }

  private processFunctionDefinition(node: Node): ASTNode {
    // 使用改进的函数名提取方法
    const functionName = this.extractFunctionName(node);
    if (!functionName) {
      return this.createDefaultNode(node);
    }

    const params = this.extractFunctionParameters(node);
    const returnType = this.extractReturnType(node);
    const metadata = this.extractFunctionMetadata(node);
    
    const astNode = this.nodeFactory.createFunctionDefinition(
      functionName,
      params,
      {
        ...metadata,
        caller: this.currentFunction,
        returnType
      }
    );

    // 设置位置和文本
    astNode.location = {
      start: { line: node.startPosition.row, column: node.startPosition.column },
      end: { line: node.endPosition.row, column: node.endPosition.column }
    };
    astNode.text = node.text;
    astNode.language = 'np';

    // 递归处理函数体
    for (const child of node.namedChildren) {
      if (child && child.type !== 'identifier' && child.type !== 'parameters') {
        const childNode = this.processNode(child);
        if (childNode.kind !== ASTNodeKind.PROGRAM) {
          astNode.children.push(childNode);
        } else {
          // 如果子节点是 PROGRAM，将其子节点合并到当前节点
          astNode.children.push(...childNode.children);
        }
      }
    }

    this.currentFunction = functionName;
    return astNode;
  }

  /**
   * 处理ERROR节点，尝试从中提取函数定义
   * 当tree-sitter无法正确解析语法时，会生成ERROR节点
   * 我们需要手动分析文本内容来提取函数定义
   */
  private processErrorNode(node: Node, parentType?: string, parentNode?: Node): ASTNode {
    const nodeText = node.text.trim();
    
    // 尝试匹配函数定义模式 - 使用更精确的语法分析而不是正则表达式
    const functionMatch = this.parseFunctionDefinitionFromText(nodeText);
    if (functionMatch) {
      const functionName = functionMatch.name;
      
      // 创建函数定义节点
      const astNode = this.nodeFactory.createFunctionDefinition(
        functionName,
        functionMatch.parameters || [], // 参数暂时为空
        {
          caller: this.currentFunction,
          extractedFromError: true,
          returnType: functionMatch.returnType
        }
      );

      // 设置位置和文本
      astNode.location = {
        start: { line: node.startPosition.row, column: node.startPosition.column },
        end: { line: node.endPosition.row, column: node.endPosition.column }
      };
      astNode.text = node.text;
      astNode.language = 'np';

      // 递归处理子节点
      for (const child of node.namedChildren) {
        if (child) {
          const childNode = this.processNode(child, node.type, node);
          if (childNode.kind !== ASTNodeKind.PROGRAM) {
            astNode.children.push(childNode);
          } else {
            astNode.children.push(...childNode.children);
          }
        }
      }

      this.currentFunction = functionName;
      return astNode;
    }
    
    // 如果不是函数定义，返回默认节点
    return this.createDefaultNode(node);
  }

  private processFunctionCall(node: Node): ASTNode {
    // 使用改进的函数名提取方法
    const functionName = this.extractFunctionNameFromCall(node);
    if (!functionName) {
      return this.createDefaultNode(node);
    }

    const args = this.extractArguments(node);
    
    // 不在这里判断是否为宏调用，让CodeGraphBuilder通过全局图来判断
    const astNode = this.nodeFactory.createFunctionCall(
      functionName,
      args,
      {
        caller: this.currentFunction,
        type: 'function',  // 默认为function，让CodeGraphBuilder判断具体类型
        isCall: true,  // 明确标记为函数调用
        isMacroCall: false  // 默认为false，让CodeGraphBuilder判断
      }
    );

    astNode.location = {
      start: { line: node.startPosition.row, column: node.startPosition.column },
      end: { line: node.endPosition.row, column: node.endPosition.column }
    };
    astNode.text = node.text;
    astNode.language = 'np';

    return astNode;
  }

  private processVariableDeclaration(node: Node): ASTNode {
    const varType = node.childForFieldName('variable_type');
    const varName = node.namedChildren.find((c: Node | null) => c !== null && c.type === 'identifier');
    
    if (!varName) {
      return this.createDefaultNode(node);
    }

    const astNode = this.nodeFactory.createVariableDeclaration(
      varName.text,
      varType?.text || 'unknown'
    );

    astNode.location = {
      start: { line: node.startPosition.row, column: node.startPosition.column },
      end: { line: node.endPosition.row, column: node.endPosition.column }
    };
    astNode.text = node.text;
    astNode.language = 'np';

    return astNode;
  }

  private processBundleDefinition(node: Node): ASTNode {
    // 对于 bundle 声明，需要从文本中提取函数名
    // 例如: "extern bundle add(a, b)" -> 提取 "add"
    const bundleMatch = this.parseBundleDeclaration(node.text);
    if (!bundleMatch) {
      return this.createDefaultNode(node);
    }

    return {
      name: bundleMatch.name,
      kind: ASTNodeKind.FUNCTION,
      originalType: 'function',
      text: node.text,
      children: [],
      language: 'np',
      location: {
        start: { line: node.startPosition.row, column: node.startPosition.column },
        end: { line: node.endPosition.row, column: node.endPosition.column }
      },
      metadata: { 
        type: 'bundle'  // 在元数据中标记类型，而不是使用 isBundle
      }
    };
  }

  private processMacroDefinition(node: Node): ASTNode {
    // 查找宏名称（identifier）
    const identifierNode = this.findIdentifierNode(node);
    if (!identifierNode) {
      return this.createDefaultNode(node);
    }

    // 根据 NP grammar: define_statement 有 field("value", $.constant)
    let value = '';
    
    // 查找宏值节点
    const valueNode = node.childForFieldName('value');
    
    if (valueNode) {
      value = valueNode.text;
    } else {
      // 如果没有专门的宏值节点，尝试从文本中提取
      const defineText = node.text;
      const defineIndex = defineText.indexOf('#define');
      if (defineIndex !== -1) {
        const afterDefine = defineText.slice(defineIndex + 7).trim();
        // 使用更精确的方法查找宏名称的结束位置
        let nameEndIndex = -1;
        for (let i = 0; i < afterDefine.length; i++) {
          if (afterDefine[i] === ' ' || afterDefine[i] === '\t' || afterDefine[i] === '\n') {
            nameEndIndex = i;
            break;
          }
        }
        if (nameEndIndex !== -1) {
          value = afterDefine.slice(nameEndIndex).trim();
        }
      }
    }

    return {
      name: identifierNode.text,
      kind: ASTNodeKind.FUNCTION,
      originalType: 'function',
      text: node.text,
      children: [],
      language: 'np',
      location: {
        start: { line: node.startPosition.row, column: node.startPosition.column },
        end: { line: node.endPosition.row, column: node.endPosition.column }
      },
      metadata: { 
        type: 'macro',  // 在元数据中标记类型，而不是使用 isMacro
        value 
      }
    };
  }

  private processMacroCall(node: Node): ASTNode {
    // 查找宏名称（identifier）
    const identifierNode = this.findIdentifierNode(node);
    if (!identifierNode) {
      return this.createDefaultNode(node);
    }

    return {
      name: identifierNode.text,
      kind: ASTNodeKind.FUNCTION,
      originalType: 'call_expression',
      text: node.text,
      children: [],
      language: 'np',
      location: {
        start: { line: node.startPosition.row, column: node.startPosition.column },
        end: { line: node.endPosition.row, column: node.endPosition.column }
      },
      metadata: { isCall: true, isMacroCall: true }
    };
  }

  private processModuleImport(node: Node): ASTNode {
    // 根据 NP grammar: include_statement 有 field('path', $.include_path)
    const pathNode = node.childForFieldName('path');
    if (!pathNode) {
      return this.createDefaultNode(node);
    }

    // 根据 NP grammar: include_path 可以是:
    // seq('"', $.file_name, '"') 或 seq('<', $.file_name, '>')
    let moduleName = '';
    
    // 查找 file_name 子节点
    const fileNameNode = pathNode.namedChildren.find((c: Node | null) => 
      c !== null && c.type === 'file_name'
    );
    
    if (fileNameNode) {
      moduleName = fileNameNode.text;
    } else {
      // 如果没有 file_name 节点，从文本中提取
      const pathText = pathNode.text;
      // 移除开头的引号或尖括号，移除结尾的引号或尖括号
      let cleanPath = pathText;
      if (cleanPath.startsWith('"') || cleanPath.startsWith('<')) {
        cleanPath = cleanPath.slice(1);
      }
      if (cleanPath.endsWith('"') || cleanPath.endsWith('>')) {
        cleanPath = cleanPath.slice(0, -1);
      }
      moduleName = cleanPath;
    }

    const astNode = this.nodeFactory.createModuleImport(moduleName);

    astNode.location = {
      start: { line: node.startPosition.row, column: node.startPosition.column },
      end: { line: node.endPosition.row, column: node.endPosition.column }
    };
    astNode.text = node.text;
    astNode.language = 'np';

    return astNode;
  }

  private processFunctionFromDeclareVariable(node: Node): ASTNode {
    // 从文本中提取函数名 - 使用语法分析而不是正则表达式
    const functionMatch = this.parseFunctionDefinitionFromText(node.text);
    if (!functionMatch) {
      return this.createDefaultNode(node);
    }
    
    const functionName = functionMatch.name;
    
    const params = this.extractFunctionParameters(node);
    const returnType = this.extractReturnType(node);
    const metadata = this.extractFunctionMetadata(node);
    
    const astNode = this.nodeFactory.createFunctionDefinition(
      functionName,
      params,
      {
        ...metadata,
        caller: this.currentFunction,
        extractedFromDeclareVariable: true,
        returnType
      }
    );

    // 设置位置和文本
    astNode.location = {
      start: { line: node.startPosition.row, column: node.startPosition.column },
      end: { line: node.endPosition.row, column: node.endPosition.column }
    };
    astNode.text = node.text;
    astNode.language = 'np';

    // 递归处理子节点
    for (const child of node.namedChildren) {
      if (child) {
        const childNode = this.processNode(child);
        if (childNode.kind !== ASTNodeKind.PROGRAM) {
          astNode.children.push(childNode);
        } else {
          // 如果子节点是 PROGRAM，将其子节点合并到当前节点
          astNode.children.push(...childNode.children);
        }
      }
    }

    this.currentFunction = functionName;
    return astNode;
  }

  private processCStyleFunctionDefinition(node: Node): ASTNode {
    // 匹配C风格函数定义：类型 函数名(参数)
    // 例如：void main() -> 提取 "main"
    const functionMatch = this.parseCStyleFunctionDefinition(node.text);
    if (!functionMatch) {
      return this.createDefaultNode(node);
    }

    const functionName = functionMatch.name;
    
    const params = this.extractCStyleParameters(node);
    const returnType = functionMatch.returnType;
    const metadata = this.extractFunctionMetadata(node);

    const astNode = this.nodeFactory.createFunctionDefinition(
      functionName,
      params,
      {
        ...metadata,
        caller: this.currentFunction,
        extractedFromCStyle: true,
        returnType
      }
    );

    // 设置位置和文本
    astNode.location = {
      start: { line: node.startPosition.row, column: node.startPosition.column },
      end: { line: node.endPosition.row, column: node.endPosition.column }
    };
    astNode.text = node.text;
    astNode.language = 'np';

    // 递归处理子节点
    for (const child of node.namedChildren) {
      if (child) {
        const childNode = this.processNode(child);
        if (childNode.kind !== ASTNodeKind.PROGRAM) {
          astNode.children.push(childNode);
        } else {
          // 如果子节点是 PROGRAM，将其子节点合并到当前节点
          astNode.children.push(...childNode.children);
        }
      }
    }

    this.currentFunction = functionName;
    return astNode;
  }

  /**
   * 改进的函数名提取方法，参考Python版本的实现
   */
  private extractFunctionName(node: Node): string | null {
    // 在statement_function_def中，函数名是identifier类型的子节点
    if (node.type === "statement_function_def") {
      const children = node.namedChildren;
      for (const child of children) {
        if (child && child.type === "identifier") {
          return child.text;
        }
      }
    }
    
    // 递归查找
    for (const child of node.namedChildren) {
      if (child) {
        const name = this.extractFunctionName(child);
        if (name) {
          return name;
        }
      }
    }
    return null;
  }

  /**
   * 从函数调用节点中提取函数名
   */
  private extractFunctionNameFromCall(node: Node): string | null {
    // 处理各种函数调用节点类型
    if (node.type === "statement_function_call" || node.type === "expression_function_call" || node.type === "call_expression") {
      // 对于call_expression，第一个子节点通常是函数名
      for (const child of node.namedChildren) {
        if (child && child.type === "identifier") {
          return child.text;
        }
      }
      
      // 如果没有找到identifier，尝试从文本中提取
      // 例如: "add(a, b)" -> 提取 "add"
      const text = node.text.trim();
      const parenIndex = text.indexOf('(');
      if (parenIndex !== -1) {
        const functionName = text.slice(0, parenIndex).trim();
        // 验证函数名是否有效
        if (functionName && /^[a-zA-Z_][a-zA-Z0-9_]*$/.test(functionName)) {
          return functionName;
        }
      }
    }
    
    // 递归查找
    for (const child of node.namedChildren) {
      if (child) {
        const name = this.extractFunctionNameFromCall(child);
        if (name) {
          return name;
        }
      }
    }
    return null;
  }

  /**
   * 改进的参数提取方法，参考Python版本的实现
   */
  private extractFunctionParameters(node: Node): Array<{name: string, type: string}> {
    const parameters: Array<{name: string, type: string}> = [];
    
    const findParameters = (n: Node) => {
      if (n.type === "parameter_list") {
        for (const paramNode of n.namedChildren) {
          if (paramNode && paramNode.type === "parameter_declaration") {
            let paramName: string | null = null;
            let paramType: string | null = null;
            
            for (const child of paramNode.namedChildren) {
              if (child && child.type === "identifier") {
                paramName = child.text;
              } else if (child && child.type === "type_identifier") {
                paramType = child.text;
              }
            }
            
            if (paramName) {
              parameters.push({
                name: paramName,
                type: paramType || "unknown"
              });
            }
          }
        }
      }
      
      // 递归查找
      for (const child of n.namedChildren) {
        if (child) {
          findParameters(child);
        }
      }
    };
    
    findParameters(node);
    return parameters;
  }

  /**
   * 提取返回类型，参考Python版本的实现
   */
  private extractReturnType(node: Node): string | null {
    const findReturnType = (n: Node): string | null => {
      if (n.type === "type_identifier") {
        return n.text;
      }
      
      // 递归查找
      for (const child of n.namedChildren) {
        if (child) {
          const result = findReturnType(child);
          if (result) {
            return result;
          }
        }
      }
      return null;
    };
    
    return findReturnType(node);
  }

  private extractArguments(node: Node): string[] {
    // 查找参数列表节点
    const argList = node.namedChildren.find((c: Node | null) => c !== null && c.type === 'argument_list');
    if (argList) {
      return argList.namedChildren.map((arg: Node | null) => arg?.text || '');
    }
    
    // 如果没有找到argument_list，尝试从文本中提取参数
    // 例如: "add(a, b)" -> 提取 ["a", "b"]
    const text = node.text.trim();
    const parenIndex = text.indexOf('(');
    const closeParenIndex = text.lastIndexOf(')');
    
    if (parenIndex !== -1 && closeParenIndex !== -1 && closeParenIndex > parenIndex) {
      const argsText = text.slice(parenIndex + 1, closeParenIndex).trim();
      if (argsText) {
        // 简单的参数分割，处理基本的参数列表
        return argsText.split(',').map((arg: string) => arg.trim()).filter((arg: string) => arg.length > 0);
      }
    }
    
    return [];
  }

  private extractCStyleParameters(node: Node): Array<{name: string, type: string}> {
    const params: Array<{name: string, type: string}> = [];
    let currentParamType: string | null = null;
    let currentParamName: string | null = null;

    for (const child of node.namedChildren) {
      if (child && child.type === 'type_specifier') {
        currentParamType = child.text;
      } else if (child && child.type === 'identifier') {
        currentParamName = child.text;
        if (currentParamType && currentParamName) {
          params.push({ name: currentParamName, type: currentParamType });
          currentParamType = null;
          currentParamName = null;
        }
      }
    }
    return params;
  }

  private extractFunctionMetadata(node: Node): any {
    const metadata: any = {};
    
    // 修饰符
    const linkage = node.namedChildren.find((c: Node | null) => c !== null && c.type === 'linkage_type');
    if (linkage) {
      metadata.linkage = linkage.text;
    }

    return metadata;
  }

  private createDefaultNode(node: Node): ASTNode {
    return {
      kind: ASTNodeKind.PROGRAM,
      originalType: node.type,
      text: node.text,
      children: [],
      language: 'np',
      location: {
        start: { line: node.startPosition.row, column: node.startPosition.column },
        end: { line: node.endPosition.row, column: node.endPosition.column }
      }
    };
  }

  private findIdentifierNode(node: Node): Node | null {
    if (node.type === 'identifier') {
      return node;
    }
    for (const child of node.namedChildren) {
      if (child) {
        const found = this.findIdentifierNode(child);
        if (found) {return found;}
      }
    }
    return null;
  }

  /**
   * 使用语法分析而不是正则表达式来解析函数定义
   */
  private parseFunctionDefinitionFromText(text: string): { name: string; parameters?: Array<{name: string, type: string}>; returnType?: string } | null {
    const trimmedText = text.trim();
    
    // 检查是否是 function 关键字开头的函数定义
    if (trimmedText.startsWith('function ')) {
      const afterFunction = trimmedText.slice(9).trim();
      const spaceIndex = afterFunction.indexOf(' ');
      const braceIndex = afterFunction.indexOf('{');
      
      if (spaceIndex !== -1 && (braceIndex === -1 || spaceIndex < braceIndex)) {
        const functionName = afterFunction.slice(0, spaceIndex).trim();
        return { name: functionName };
      } else if (braceIndex !== -1) {
        const functionName = afterFunction.slice(0, braceIndex).trim();
        return { name: functionName };
      }
    }
    
    return null;
  }

  /**
   * 使用语法分析而不是正则表达式来解析C风格函数定义
   */
  private parseCStyleFunctionDefinition(text: string): { name: string; returnType: string } | null {
    const trimmedText = text.trim();
    
    // 检查C风格函数定义：类型 函数名(参数)
    // 例如：void main(), int add(int a, int b)
    const cStyleTypes = ['void', 'int', 'char', 'float', 'double', 'long', 'short', 'unsigned', 'signed'];
    
    for (const type of cStyleTypes) {
      if (trimmedText.startsWith(type + ' ')) {
        const afterType = trimmedText.slice(type.length + 1).trim();
        const parenIndex = afterType.indexOf('(');
        
        if (parenIndex !== -1) {
          const functionName = afterType.slice(0, parenIndex).trim();
          if (functionName && /^[a-zA-Z_][a-zA-Z0-9_]*$/.test(functionName)) {
            return { name: functionName, returnType: type };
          }
        }
      }
    }
    
    return null;
  }

  /**
   * 检查是否是C风格函数定义，使用语法分析而不是正则表达式
   */
  private isCStyleFunctionDefinition(text: string): boolean {
    const result = this.parseCStyleFunctionDefinition(text);
    return result !== null;
  }



  /**
   * 解析bundle声明，使用语法分析而不是正则表达式
   */
  private parseBundleDeclaration(text: string): { name: string } | null {
    const trimmedText = text.trim();
    
    // 检查是否是 bundle 声明模式
    // 例如: "extern bundle add(a, b)" -> 提取 "add"
    // 或者: "extern bundle add" -> 提取 "add"
    if (trimmedText.includes('bundle ')) {
      const bundleIndex = trimmedText.indexOf('bundle ');
      const afterBundle = trimmedText.slice(bundleIndex + 7).trim();
      
      const parenIndex = afterBundle.indexOf('(');
      
      if (parenIndex !== -1) {
        // 有参数的情况: "add(a, b)" -> 提取 "add"
        const functionName = afterBundle.slice(0, parenIndex).trim();
        if (functionName && /^[a-zA-Z_][a-zA-Z0-9_]*$/.test(functionName)) {
          return { name: functionName };
        }
      } else {
        // 没有参数的情况: "add" -> 提取 "add"
        const functionName = afterBundle.trim();
        if (functionName && /^[a-zA-Z_][a-zA-Z0-9_]*$/.test(functionName)) {
          return { name: functionName };
        }
      }
    }
    
    return null;
  }
}