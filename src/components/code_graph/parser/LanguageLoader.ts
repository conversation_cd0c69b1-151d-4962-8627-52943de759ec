import * as fs from 'fs';
import * as path from 'path';
// 延迟加载 web-tree-sitter，避免在 locateFile 未就绪时立即拉取 WASM
let ParserT: any;
let Parser: any;
let Language: any;
let Query: any;

/**
 * 统一的语言模块加载器
 * 强制使用 buffer 方式加载 WASM 文件，避免路径问题
 */
export class LanguageLoader {
  private static initialized = false;
  private static languageCache: Map<string, any> = new Map();
  private static loadingPromises: Map<string, Promise<any>> = new Map();

  /**
   * 初始化 Parser（必须在任何 load 之前调用）
   */
  static async init() {
    if (!this.initialized) {
      console.log(`🔄 [LanguageLoader] 开始初始化...`);

      // 在 Node.js 环境中，需要明确指定 WASM 文件路径
      // 优先从 assets 目录查找 WASM 文件
      let wasmPath = this.findWasmFile('assets/wasm/tree-sitter.wasm');
      if (!wasmPath) {
        // 如果 assets 目录没有，回退到原来的路径
        wasmPath = this.findWasmFile('web-tree-sitter/tree-sitter.wasm');
      }
      console.log(`🔍 [LanguageLoader] 找到 web-tree-sitter WASM: ${wasmPath || '未找到'}`);

      // 设置 fetch polyfill 来处理 WASM 文件加载
      if (wasmPath) {
        await this.setupFetchPolyfill(wasmPath);
      }

      // 动态导入 web-tree-sitter
      if (!ParserT) {
        console.log(`🔄 [LanguageLoader] 导入 web-tree-sitter...`);

        try {
          // 检测运行环境
          const isMainProcess = typeof require !== 'undefined' && typeof module !== 'undefined';
          console.log(`🔍 [LanguageLoader] 运行环境: ${isMainProcess ? '主进程 (CommonJS)' : 'Worker (ES Module)'}`);

          let mod: any;
          if (isMainProcess) {
            // 主进程环境：使用 require 导入 CommonJS 版本
            console.log(`🔄 [LanguageLoader] 主进程环境，使用 require 导入...`);
            mod = require('web-tree-sitter');
          } else {
            // Worker 环境：使用动态 import
            console.log(`🔄 [LanguageLoader] Worker 环境，使用 import 导入...`);
            mod = await import('web-tree-sitter');
          }
          console.log(`🔍 [LanguageLoader] 原始导入对象:`, {
            mod: typeof mod,
            default: typeof mod.default,
            Parser: typeof mod.Parser,
            Language: typeof mod.Language,
            Query: typeof mod.Query
          });

          // 在新版本的 web-tree-sitter 中，模块结构发生了变化
          const webTreeSitter = mod.default || mod;

          console.log(`🔍 [LanguageLoader] 解析后的对象:`, {
            webTreeSitter: typeof webTreeSitter,
            'webTreeSitter.Parser': typeof webTreeSitter.Parser,
            'webTreeSitter.Language': typeof webTreeSitter.Language,
            'webTreeSitter.Query': typeof webTreeSitter.Query,
            'webTreeSitter.init': typeof webTreeSitter.init
          });

          // 在新版本中，直接使用模块导出的类
          if (webTreeSitter.Parser && webTreeSitter.Language && webTreeSitter.Query) {
            // 新版本 API 结构
            ParserT = webTreeSitter;
            Parser = webTreeSitter.Parser;
            Language = webTreeSitter.Language;
            Query = webTreeSitter.Query;
            console.log(`🔍 [LanguageLoader] 使用新版本 API 结构`);
          } else {
            // 旧版本 API 结构（兼容性）
            ParserT = webTreeSitter;
            Parser = webTreeSitter.Parser || webTreeSitter;
            Language = webTreeSitter.Language;
            Query = webTreeSitter.Query;
            console.log(`🔍 [LanguageLoader] 使用旧版本 API 结构`);
          }

          console.log(`✅ [LanguageLoader] web-tree-sitter 导入成功`);
        } catch (error) {
          console.error(`❌ [LanguageLoader] 导入 web-tree-sitter 失败:`, error);
          throw error;
        }
      }

      console.log(`🔄 [LanguageLoader] 调用 Parser.init()...`);

      // 根据官方文档，使用 locateFile 函数初始化
      const treeSitterWasmPath = LanguageLoader.findWasmFile('assets/wasm/tree-sitter.wasm');
      if (!treeSitterWasmPath) {
        throw new Error('找不到 tree-sitter.wasm 文件');
      }

      console.log(`🔍 [LanguageLoader] 找到 web-tree-sitter WASM: ${treeSitterWasmPath}`);

      // 按照官方文档的方式调用 Parser.init() 并传递 locateFile
      let initMethod = null;
      if (typeof ParserT.init === 'function') {
        initMethod = ParserT.init.bind(ParserT);
        console.log(`🔍 [LanguageLoader] 使用 ParserT.init()`);
      } else if (typeof Parser.init === 'function') {
        initMethod = Parser.init.bind(Parser);
        console.log(`🔍 [LanguageLoader] 使用 Parser.init()`);
      } else {
        console.log(`⚠️ [LanguageLoader] 未找到 init 方法，尝试跳过初始化`);
      }

      if (initMethod) {
        // 按照官方文档传递 locateFile 函数
        await initMethod({
          locateFile(scriptName: string, scriptDirectory: string) {
            console.log(`🔍 [locateFile] 查找: ${scriptName} 在目录: ${scriptDirectory}`);

            // 如果是 tree-sitter.wasm，返回我们找到的路径
            if (scriptName === 'tree-sitter.wasm') {
              console.log(`🔍 [locateFile] 返回 tree-sitter.wasm 路径: ${treeSitterWasmPath}`);
              return treeSitterWasmPath;
            }

            // 其他文件使用默认路径
            console.log(`🔍 [locateFile] 使用默认路径: ${scriptName}`);
            return scriptName;
          }
        });
        console.log(`✅ [LanguageLoader] Parser.init() 完成`);
      } else {
        console.log(`⚠️ [LanguageLoader] 跳过 init 调用，可能不需要显式初始化`);
      }

      // 调试：检查 Language 对象状态
      console.log(`🔍 [LanguageLoader] 检查 Language 对象:`, {
        ParserT: typeof ParserT,
        Parser: typeof Parser,
        Language: typeof Language,
        'Language.load': typeof Language?.load,
        'ParserT.Language': typeof ParserT?.Language,
        'ParserT.Language.load': typeof ParserT?.Language?.load
      });

      // 确保 Language 正确设置
      if (!Language || !Language.load) {
        console.log(`🔧 [LanguageLoader] 重新设置 Language 对象...`);
        Language = ParserT.Language;
        console.log(`🔍 [LanguageLoader] 重新设置后 Language.load:`, typeof Language?.load);
      }

      this.initialized = true;
      console.log(`✅ [LanguageLoader] 初始化完成`);
    }
  }

  /**
   * 获取 web-tree-sitter 的类，供其他模块使用
   */
  static getClasses() {
    if (!this.initialized) {
      throw new Error('LanguageLoader 未初始化，请先调用 init()');
    }
    return {
      Parser,
      Language,
      Query
    };
  }

  /**
   * 设置 fetch polyfill 来处理 WASM 文件加载
   */
  private static async setupFetchPolyfill(wasmPath: string) {
    // 如果已经有 fetch polyfill，不要重复设置
    if ((global as any)._treeSitterFetchPolyfillSet) {
      console.log(`🔧 [LanguageLoader] fetch polyfill 已设置，跳过`);
      return;
    }

    console.log(`🔧 [LanguageLoader] 设置 fetch polyfill，WASM 路径: ${wasmPath}`);

    // 保存原始的 fetch 函数
    const originalFetch = (global as any).fetch;

    // 创建 polyfill fetch 函数
    (global as any).fetch = async (input: string | Request, init?: RequestInit) => {
      const url = typeof input === 'string' ? input : input.url;
      console.log(`🌐 [LanguageLoader] fetch 被调用: ${url}`);

      // 检查是否是文件系统路径（不是 http/https/file:// URL）
      if (typeof url === 'string' && !url.startsWith('http') && !url.startsWith('file://')) {
        console.log(`🎯 [LanguageLoader] 拦截文件系统路径: ${url}`);

        // 检查文件是否存在
        const fs = await import('fs');
        if (fs.existsSync(url)) {
          const buffer = fs.readFileSync(url);
          console.log(`📦 [LanguageLoader] 读取文件: ${buffer.length} bytes`);

          // 创建模拟的 Response 对象
          return new Response(buffer, {
            status: 200,
            statusText: 'OK',
            headers: {
              'Content-Type': url.endsWith('.wasm') ? 'application/wasm' : 'application/octet-stream',
              'Content-Length': buffer.length.toString()
            }
          });
        } else {
          console.error(`❌ [LanguageLoader] 文件不存在: ${url}`);
          throw new Error(`File not found: ${url}`);
        }
      }

      // 对于其他请求，使用原始的 fetch
      if (originalFetch) {
        console.log(`🔄 [LanguageLoader] 使用原始 fetch: ${url}`);
        return originalFetch(input, init);
      } else {
        console.error(`❌ [LanguageLoader] 原始 fetch 不可用: ${url}`);
        throw new Error(`Fetch not available for: ${url}`);
      }
    };

    // 同时设置 locateFile polyfill，这是 Emscripten WASM 模块的另一种文件定位方式
    try {
      if (typeof (global as any).Module === 'undefined') {
        (global as any).Module = {};
      }

      // 确保 Module 对象存在且可写
      if ((global as any).Module && typeof (global as any).Module === 'object') {
        (global as any).Module.locateFile = (path: string, scriptDirectory: string) => {
          console.log(`🔍 [locateFile polyfill] 请求: ${path}, 脚本目录: ${scriptDirectory}`);

          // 如果是 tree-sitter.wasm 请求，返回我们的 WASM 文件路径
          if (path.includes('tree-sitter.wasm') || path.endsWith('tree-sitter.wasm')) {
            console.log(`🔄 [locateFile polyfill] 重定向到: ${wasmPath}`);
            return wasmPath;
          }

          // 对于其他文件，返回默认路径
          return scriptDirectory + path;
        };
        console.log(`✅ [LanguageLoader] locateFile polyfill 设置成功`);
      } else {
        console.log(`⚠️ [LanguageLoader] Module 对象不可用，跳过 locateFile 设置`);
      }
    } catch (error) {
      console.log(`⚠️ [LanguageLoader] locateFile 设置失败:`, error);
    }

    // 标记已设置
    (global as any)._treeSitterFetchPolyfillSet = true;
    console.log(`✅ [LanguageLoader] fetch 和 locateFile polyfill 设置完成`);
  }

  /**
   * 获取扩展根目录路径
   * 在 VS Code 扩展环境中尝试多种方式定位扩展根目录
   */
  /**
   * 查找 WASM 文件路径
   * @param wasmFileName 相对于 node_modules 的文件路径
   */
  private static findWasmFile(wasmFileName: string): string | null {
    try {
      // 1. 传入已是 file:// 或绝对路径，直接返回
      if (wasmFileName.startsWith('file://')) {
        console.log(`[WASM 路径探测] 直接返回 file URL: ${wasmFileName}`);
        return wasmFileName;
      }
      if (path.isAbsolute(wasmFileName) && fs.existsSync(wasmFileName)) {
        console.log(`[WASM 路径探测] 直接返回绝对路径: ${wasmFileName}`);
        return wasmFileName;
      }

      // 2. 获取环境信息
      const extensionPath = this.getExtensionPath();
      const cwd = process.cwd();
      const scriptArgv1 = process.argv[1];
      const isWorkerThread = typeof process.env.WORKER_THREAD_ID !== 'undefined' ||
                            (scriptArgv1 && scriptArgv1.includes('worker'));

      console.log(`[WASM 路径探测] 环境信息:`);
      console.log(`  - extensionPath: ${extensionPath}`);
      console.log(`  - cwd: ${cwd}`);
      console.log(`  - argv[1]: ${scriptArgv1}`);
      console.log(`  - isWorkerThread: ${isWorkerThread}`);

      const possiblePaths: string[] = [];

      // 3. Worker 环境特殊处理
      if (isWorkerThread && scriptArgv1) {
        const scriptDir = path.dirname(scriptArgv1);
        console.log(`[WASM 路径探测] Worker 环境，脚本目录: ${scriptDir}`);

        // 在分离构建架构中，Worker 脚本在 dist/workers/ 目录
        // 优先使用 Worker 专用的 WASM 文件
        const workerDistPaths = [
          // 1. Worker 专用资源目录（最高优先级）
          path.resolve(scriptDir, 'assets', wasmFileName),
          path.resolve(scriptDir, 'assets', path.basename(wasmFileName)),

          // 2. Worker 脚本目录
          path.resolve(scriptDir, wasmFileName),
          path.resolve(scriptDir, path.basename(wasmFileName)),

          // 3. 向上查找主进程资源
          path.resolve(scriptDir, '..', 'assets', 'wasm', wasmFileName),
          path.resolve(scriptDir, '..', 'assets', 'wasm', path.basename(wasmFileName)),

          // 4. 兼容旧路径（低优先级）
          path.resolve(scriptDir, '..', '..', '..', wasmFileName),
          path.resolve(scriptDir, '..', '..', '..', 'node_modules', wasmFileName),
          path.resolve(scriptDir, '..', '..', '..', 'dist', 'node_modules', wasmFileName),
          path.resolve(scriptDir, '..', '..', '..', '..', 'dist', 'node_modules', wasmFileName),
        ];

        possiblePaths.push(...workerDistPaths);
        console.log(`[WASM 路径探测] Worker 候选路径:`, workerDistPaths);
      }

      // 4. 基于扩展路径的路径（高优先级）
      if (extensionPath) {
        possiblePaths.push(
          // 优先从 assets 目录查找
          path.resolve(extensionPath, 'dist', wasmFileName),
          path.resolve(extensionPath, 'dist', 'assets', 'wasm', path.basename(wasmFileName)),
          path.resolve(extensionPath, 'dist', 'node_modules', wasmFileName),
          path.resolve(extensionPath, 'node_modules', wasmFileName),
          // VS Code 扩展特殊路径
          path.resolve(extensionPath, 'out', 'dist', wasmFileName),
          path.resolve(extensionPath, 'out', 'dist', 'assets', 'wasm', path.basename(wasmFileName))
        );
      }

      // 5. 基于当前工作目录的路径
      possiblePaths.push(
        // 优先从 assets 目录查找
        path.resolve(cwd, 'dist', wasmFileName),
        path.resolve(cwd, 'dist', 'assets', 'wasm', path.basename(wasmFileName)),
        path.resolve(cwd, 'dist', 'node_modules', wasmFileName),
        path.resolve(cwd, 'node_modules', wasmFileName),
        // VS Code 扩展特殊路径
        path.resolve(cwd, 'out', 'dist', wasmFileName),
        path.resolve(cwd, 'out', 'dist', 'assets', 'wasm', path.basename(wasmFileName))
      );

      // 6. 基于脚本路径的通用路径
      if (scriptArgv1) {
        const scriptDir = path.dirname(scriptArgv1);
        // 尝试多个层级向上查找
        for (let i = 0; i < 6; i++) {
          const upPath = path.resolve(scriptDir, ...Array(i).fill('..'));
          possiblePaths.push(
            // 优先从 assets 目录查找
            path.resolve(upPath, 'dist', wasmFileName),
            path.resolve(upPath, 'dist', 'assets', 'wasm', path.basename(wasmFileName)),
            path.resolve(upPath, 'dist', 'node_modules', wasmFileName),
            path.resolve(upPath, 'node_modules', wasmFileName),
            // VS Code 扩展特殊路径
            path.resolve(upPath, 'out', 'dist', wasmFileName),
            path.resolve(upPath, 'out', 'dist', 'assets', 'wasm', path.basename(wasmFileName))
          );
        }
      }

      // 7. 路径去重，过滤空/undefined
      const uniquePaths = [...new Set(possiblePaths.filter(Boolean))];
      console.log(`[WASM 路径探测] 查找文件: ${wasmFileName}`);
      console.log(`[WASM 路径探测] 文件名: ${path.basename(wasmFileName)}`);
      console.log(`[WASM 路径探测] 所有候选路径 (${uniquePaths.length} 个):`, uniquePaths);

      // 8. 逐个检查路径
      for (const candidate of uniquePaths) {
        if (typeof candidate !== 'string' || !candidate) {
          continue;
        }
        try {
          const exists = fs.existsSync(candidate);
          console.log(`[WASM 路径探测] 检查: ${candidate} -> ${exists ? '存在' : '不存在'}`);
          if (exists) {
            const realPath = fs.realpathSync(candidate);
            const stat = fs.statSync(realPath);
            if (stat.size > 0) {
              console.log(`🔍 [路径解析] 找到有效 WASM 文件: ${candidate} -> ${realPath} (${stat.size} bytes)`);
              return realPath;
            } else {
              console.log(`⚠️ [路径解析] WASM 文件为空: ${candidate}`);
            }
          }
        } catch (error) {
          console.log(`❌ [路径解析] 检查路径失败: ${candidate} - ${error}`);
        }
      }

      console.error(`[WASM 路径探测] 所有路径均未找到有效文件: ${wasmFileName}`);
      return null;
    } catch (error) {
      console.error(`[WASM 路径探测] 异常:`, error);
      return null;
    }
  }

  private static getExtensionPath(): string | null {
    try {
      console.log(`[扩展路径探测] 开始查找扩展根目录...`);

      // 方法1: 通过 __dirname 推导（适用于编译后的代码）
      if (typeof __dirname !== 'undefined') {
        console.log(`[扩展路径探测] __dirname: ${__dirname}`);
        // 从不同深度的目录推导到根目录
        const candidates = [
          path.resolve(__dirname, '..'), // dist -> root
          path.resolve(__dirname, '../..'), // dist/subdir -> root
          path.resolve(__dirname, '../../..'), // dist/subdir/subdir -> root
          path.resolve(__dirname, '../../../..'), // dist/components/code_graph/parser -> root
          path.resolve(__dirname, '../../../../..'), // 更深层级
        ];

        for (const candidate of candidates) {
          const packageJsonPath = path.join(candidate, 'package.json');
          if (fs.existsSync(packageJsonPath)) {
            console.log(`[扩展路径探测] 通过 __dirname 找到根目录: ${candidate}`);
            return candidate;
          }
        }
      }

      // 方法2: 通过 process.cwd() 检查
      const cwd = process.cwd();
      console.log(`[扩展路径探测] process.cwd(): ${cwd}`);
      if (fs.existsSync(path.join(cwd, 'package.json'))) {
        console.log(`[扩展路径探测] 通过 cwd 找到根目录: ${cwd}`);
        return cwd;
      }

      // 方法3: 通过 process.argv[1] 推导
      if (process.argv[1]) {
        const scriptPath = process.argv[1];
        const scriptDir = path.dirname(scriptPath);
        console.log(`[扩展路径探测] 脚本路径: ${scriptPath}`);
        console.log(`[扩展路径探测] 脚本目录: ${scriptDir}`);

        // 尝试更多层级的向上查找
        const candidates = [];
        for (let i = 1; i <= 8; i++) {
          const upPath = path.resolve(scriptDir, ...Array(i).fill('..'));
          candidates.push(upPath);
        }

        for (const candidate of candidates) {
          const packageJsonPath = path.join(candidate, 'package.json');
          if (fs.existsSync(packageJsonPath)) {
            console.log(`[扩展路径探测] 通过脚本路径找到根目录: ${candidate}`);
            return candidate;
          }
        }
      }

      // 方法4: 尝试一些常见的 VS Code 扩展路径
      const commonPaths = [
        process.cwd(),
        path.resolve(process.cwd(), '..'),
        path.resolve(process.cwd(), '../..'),
      ];

      for (const commonPath of commonPaths) {
        if (fs.existsSync(path.join(commonPath, 'package.json'))) {
          console.log(`[扩展路径探测] 通过常见路径找到根目录: ${commonPath}`);
          return commonPath;
        }
      }

      console.warn(`[扩展路径探测] 未找到扩展根目录`);
      return null;
    } catch (error) {
      console.warn('[扩展路径探测] 获取扩展路径失败:', error);
      return null;
    }
  }

  /**
   * 主线程使用：自动推导路径方式加载 wasm
   * @param wasmFileName 例如 tree-sitter-c/tree-sitter-c.wasm
   */
  static async loadLanguage(wasmFileName: string): Promise<any> {
    // 检查缓存
    if (this.languageCache.has(wasmFileName)) {
      console.log(`🎯 [LanguageLoader] 从缓存加载 WASM: ${wasmFileName}`);
      return this.languageCache.get(wasmFileName);
    }

    // 检查是否正在加载中（防止并发重复加载）
    if (this.loadingPromises.has(wasmFileName)) {
      console.log(`⏳ [LanguageLoader] 等待并发加载完成: ${wasmFileName}`);
      return await this.loadingPromises.get(wasmFileName);
    }

    // 创建加载 Promise
    const loadingPromise = this._loadLanguageInternal(wasmFileName);
    this.loadingPromises.set(wasmFileName, loadingPromise);

    try {
      const language = await loadingPromise;
      // 加载成功，缓存结果
      this.languageCache.set(wasmFileName, language);
      console.log(`✅ [LanguageLoader] WASM 加载并缓存成功: ${wasmFileName}`);
      return language;
    } catch (error) {
      // 加载失败，从 Promise 缓存中移除
      this.loadingPromises.delete(wasmFileName);
      throw error;
    } finally {
      // 清理 Promise 缓存
      this.loadingPromises.delete(wasmFileName);
    }
  }

  /**
   * 内部加载方法：实际执行 WASM 文件加载逻辑
   */
  private static async _loadLanguageInternal(wasmFileName: string): Promise<any> {
    await this.init();

    console.log(`🔍 [LanguageLoader] 开始加载 WASM: ${wasmFileName}`);

    // 首先尝试从 assets 目录查找
    // 注意：wasmFileName 可能已经包含路径，需要提取文件名
    const fileName = path.basename(wasmFileName);
    let wasmPath = this.findWasmFile(`assets/wasm/${fileName}`);

    if (!wasmPath) {
      // 如果 assets 目录没有，使用原来的查找方法
      wasmPath = this.findWasmFile(wasmFileName);
    }

    if (wasmPath) {
      console.log(`✅ [LanguageLoader] 通过 findWasmFile 找到 WASM: ${wasmPath}`);
      return await this.loadFromAbsolutePath(wasmPath);
    }

    // 如果 findWasmFile 失败，使用备用方法
    console.log(`⚠️ [LanguageLoader] findWasmFile 失败，使用备用方法`);

    // 获取扩展根目录
    const extensionPath = this.getExtensionPath();

    // 构建所有可能的路径，按优先级排序
    const possiblePaths: string[] = [];

    // 1. 基于扩展路径的路径（最高优先级）
    if (extensionPath) {
      possiblePaths.push(
        path.resolve(extensionPath, 'dist', 'node_modules', wasmFileName),
        path.resolve(extensionPath, 'node_modules', wasmFileName)
      );
    }

    // 2. 基于当前工作目录的路径
    const cwd = process.cwd();
    possiblePaths.push(
      path.resolve(cwd, 'dist', 'node_modules', wasmFileName),
      path.resolve(cwd, 'node_modules', wasmFileName)
    );

    // 3. 在 Worker 环境中，尝试从当前脚本位置向上查找
    if (typeof __filename !== 'undefined') {
      const scriptDir = path.dirname(__filename);
      console.log(`[LanguageLoader] __filename 脚本目录: ${scriptDir}`);
      const candidates = [
        path.resolve(scriptDir, '..', '..', '..', '..'), // 从 dist/components/code_graph/parser 到根目录
        path.resolve(scriptDir, '..', '..', '..'), // 从 src/components/code_graph/parser 到根目录
        path.resolve(scriptDir, '..', '..'), // 从 components/code_graph/parser 到根目录
        path.resolve(scriptDir, '..'), // 从 code_graph/parser 到根目录
      ];

      for (const candidate of candidates) {
        possiblePaths.push(
          path.resolve(candidate, 'dist', 'node_modules', wasmFileName),
          path.resolve(candidate, 'node_modules', wasmFileName)
        );
      }
    }

    // 4. 基于 process.argv[1] 的相对路径
    if (process.argv[1]) {
      const scriptDir = path.dirname(process.argv[1]);
      console.log(`[LanguageLoader] argv[1] 脚本目录: ${scriptDir}`);
      const relativePaths = [
        '',
        '..',
        '../..',
        '../../..',
        '../../../..',
        '../../../../..',
        '../../../../../..'
      ];

      for (const relativePath of relativePaths) {
        const basePath = path.resolve(scriptDir, relativePath);
        possiblePaths.push(
          path.resolve(basePath, 'dist', 'node_modules', wasmFileName),
          path.resolve(basePath, 'node_modules', wasmFileName)
        );
      }
    }

    // 去重
    const uniquePaths = [...new Set(possiblePaths)];

    // 添加调试信息
    console.log(`📂 [LanguageLoader] 扩展路径: ${extensionPath || 'null'}`);
    console.log(`📂 [LanguageLoader] 当前工作目录: ${process.cwd()}`);
    console.log(`📂 [LanguageLoader] process.argv[1]: ${process.argv[1] || 'null'}`);
    console.log(`📂 [LanguageLoader] 备用候选路径数量: ${uniquePaths.length}`);

    let foundWasmPath = null;
    const checkedPaths: string[] = [];

    for (const p of uniquePaths) {
      checkedPaths.push(p);
      try {
        if (fs.existsSync(p)) {
          // 解析符号链接到真实路径
          const realPath = fs.realpathSync(p);
          const stat = fs.statSync(realPath);

          if (stat.size > 0) {
            console.log(`✅ [LanguageLoader] 备用方法找到 WASM 文件: ${p}`);
            if (p !== realPath) {
              console.log(`🔗 [LanguageLoader] 符号链接解析: ${p} -> ${realPath}`);
            }
            console.log(`📦 [LanguageLoader] 文件大小: ${stat.size} bytes`);
            foundWasmPath = realPath; // 使用真实路径
            break;
          } else {
            console.log(`⚠️ [LanguageLoader] WASM 文件为空: ${p}`);
          }
        }
      } catch (error) {
        console.log(`❌ [LanguageLoader] 检查路径失败: ${p} - ${error}`);
      }
    }

    if (!foundWasmPath) {
      const errorMsg = `❌ 无法定位 WASM 文件: ${wasmFileName}\n尝试路径:\n${checkedPaths.join('\n')}`;
      console.error(errorMsg);
      throw new Error(errorMsg);
    }

    return await this.loadFromAbsolutePath(foundWasmPath);
  }

  /**
   * Worker 使用：明确路径方式加载 wasm
   * @param wasmPath 绝对路径
   */
  static async loadFromAbsolutePath(wasmPath: string): Promise<any> {
    // 使用绝对路径作为缓存键
    const cacheKey = path.resolve(wasmPath);
    
    // 检查缓存
    if (this.languageCache.has(cacheKey)) {
      console.log(`🎯 [LanguageLoader] 从缓存加载 WASM (绝对路径): ${wasmPath}`);
      return this.languageCache.get(cacheKey);
    }

    // 检查是否正在加载中
    if (this.loadingPromises.has(cacheKey)) {
      console.log(`⏳ [LanguageLoader] 等待并发加载完成 (绝对路径): ${wasmPath}`);
      return await this.loadingPromises.get(cacheKey);
    }

    // 创建加载 Promise
    const loadingPromise = this._loadFromAbsolutePathInternal(wasmPath);
    this.loadingPromises.set(cacheKey, loadingPromise);

    try {
      const language = await loadingPromise;
      // 加载成功，缓存结果
      this.languageCache.set(cacheKey, language);
      console.log(`✅ [LanguageLoader] WASM 加载并缓存成功 (绝对路径): ${wasmPath}`);
      return language;
    } catch (error) {
      // 加载失败，从 Promise 缓存中移除
      this.loadingPromises.delete(cacheKey);
      throw error;
    } finally {
      // 清理 Promise 缓存
      this.loadingPromises.delete(cacheKey);
    }
  }

  /**
   * 从 WASM Buffer 加载语言模块
   */
  static async loadFromBuffer(wasmBuffer: Uint8Array): Promise<any> {
    await this.init();
    console.log(`🔄 [LanguageLoader] 从 Buffer 加载语言模块: ${wasmBuffer.length} bytes`);

    // 调试：检查 Language 对象状态
    console.log(`🔍 [Buffer加载前检查] Language 对象:`, {
      Language: typeof Language,
      'Language.load': typeof Language?.load,
      ParserT: typeof ParserT,
      'ParserT.Language': typeof ParserT?.Language,
      'ParserT.Language.load': typeof ParserT?.Language?.load
    });

    try {
      // 确保使用正确的 Language.load 方法
      let loadMethod = Language?.load;
      if (!loadMethod && ParserT?.Language?.load) {
        console.log(`🔧 [Buffer加载] 使用 ParserT.Language.load 作为备用`);
        loadMethod = ParserT.Language.load.bind(ParserT.Language);
      }

      if (!loadMethod) {
        throw new Error('Language.load 方法不可用');
      }

      const language = await loadMethod(wasmBuffer);
      console.log(`✅ [LanguageLoader] 从 Buffer 加载成功`);

      // 调试：检查加载的语言对象
      console.log(`🔍 [Buffer加载后检查] 语言对象:`, {
        language: typeof language,
        'language.version': language?.version,
        'language.name': language?.name
      });

      // 检查语言对象的有效性
      if (!language || typeof language !== 'object') {
        throw new Error(`语言模块加载失败：返回的对象无效 (${typeof language})`);
      }

      // 检查版本号
      if (language.version === 0 || language.version === undefined || language.version === null) {
        console.error(`❌ [Buffer加载] 语言模块版本无效:`, {
          version: language.version,
          bufferSize: wasmBuffer.length,
          bufferFirst16: Array.from(wasmBuffer.slice(0, 16)).map(b => b.toString(16).padStart(2, '0')).join(' ')
        });

        throw new Error(`语言模块版本无效 (${language.version})。WASM Buffer 可能损坏或不兼容。`);
      }

      console.log(`✅ [Buffer加载] 语言模块验证通过，版本: ${language.version}`);
      return language;
    } catch (error) {
      console.error(`❌ [LanguageLoader] 从 Buffer 加载失败:`, error);
      throw error;
    }
  }

  /**
   * 内部方法：实际执行绝对路径 WASM 文件加载
   */
  private static async _loadFromAbsolutePathInternal(wasmPath: string): Promise<any> {
    await this.init();

    if (!fs.existsSync(wasmPath)) {
      throw new Error(`❌ 找不到 WASM 文件: ${wasmPath}`);
    }

    const buffer = fs.readFileSync(wasmPath);
    if (buffer.length === 0) {
      throw new Error(`❌ WASM 文件为空: ${wasmPath}`);
    }

    const header = buffer.subarray(0, 4);
    const expected = Buffer.from([0x00, 0x61, 0x73, 0x6d]);
    if (!header.equals(expected)) {
      throw new Error(`❌ 无效的 WASM 魔数: ${header.toString('hex')}`);
    }

    console.log(`🔍 [加载 WASM] 路径: ${wasmPath}`);
    console.log(`📦 Buffer 大小: ${buffer.length} bytes`);

    // 验证 WASM 文件格式
    if (buffer.length < 8) {
      throw new Error(`WASM 文件太小 (${buffer.length} bytes)，可能损坏`);
    }

    // 检查 WASM 魔数 (0x00 0x61 0x73 0x6d)
    const wasmMagic = [0x00, 0x61, 0x73, 0x6d];
    const fileMagic = Array.from(buffer.subarray(0, 4));
    const magicMatch = wasmMagic.every((byte, index) => byte === fileMagic[index]);

    console.log(`🔍 [WASM 验证] 魔数检查: ${magicMatch ? '✅ 通过' : '❌ 失败'}`);
    console.log(`🔍 [WASM 验证] 期望: [${wasmMagic.map(b => '0x' + b.toString(16).padStart(2, '0')).join(', ')}]`);
    console.log(`🔍 [WASM 验证] 实际: [${fileMagic.map(b => '0x' + b.toString(16).padStart(2, '0')).join(', ')}]`);

    if (!magicMatch) {
      throw new Error(`WASM 文件格式无效，魔数不匹配。路径: ${wasmPath}`);
    }

    // 调试：检查 Language 对象状态
    console.log(`🔍 [加载前检查] Language 对象:`, {
      Language: typeof Language,
      'Language.load': typeof Language?.load,
      ParserT: typeof ParserT,
      'ParserT.Language': typeof ParserT?.Language,
      'ParserT.Language.load': typeof ParserT?.Language?.load
    });

    try {
      // 根据官方文档，使用 Parser.Language.load 方法
      let loadMethod = null;

      // 按照官方文档的方式：Parser.Language.load()
      if (ParserT?.Language?.load) {
        loadMethod = ParserT.Language.load.bind(ParserT.Language);
        console.log(`🔧 [加载 WASM] 使用 Parser.Language.load (官方推荐方式)`);
      } else if (Language?.load) {
        loadMethod = Language.load.bind(Language);
        console.log(`🔧 [加载 WASM] 使用 Language.load 作为备用`);
      }

      if (!loadMethod) {
        throw new Error('Language.load 方法不可用');
      }

      // 检测 VS Code 扩展环境
      const isVSCodeExtension = process.env.VSCODE_PID ||
                               process.env.VSCODE_IPC_HOOK ||
                               (typeof process !== 'undefined' && process.versions?.electron);

      if (isVSCodeExtension) {
        console.log(`🔍 [加载 WASM] 检测到 VS Code 扩展环境，使用特殊处理`);
      }

      console.log(`🔄 [加载 WASM] 调用 Language.load...`);

      // 在 VS Code 扩展环境中，可能需要重试机制
      let language;
      let lastError;
      const maxRetries = 3;

      for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
          console.log(`🔄 [加载 WASM] 尝试 ${attempt}/${maxRetries}...`);
          language = await loadMethod(buffer);
          console.log(`✅ 语言模块加载成功 (尝试 ${attempt})`);
          break;
        } catch (error) {
          lastError = error;
          console.error(`❌ [加载 WASM] 尝试 ${attempt} 失败:`, error);

          if (attempt < maxRetries) {
            // 等待一小段时间后重试
            await new Promise(resolve => setTimeout(resolve, 100 * attempt));
          }
        }
      }

      if (!language) {
        throw lastError || new Error('Language.load 重试失败');
      }

      // 调试：检查加载的语言对象
      console.log(`🔍 [加载后检查] 语言对象:`, {
        language: typeof language,
        'language.version': language?.version,
        'language.name': language?.name
      });

      // 检查语言对象的有效性
      if (!language || typeof language !== 'object') {
        throw new Error(`语言模块加载失败：返回的对象无效 (${typeof language})`);
      }

      // 检查版本号 - 添加强制验证和重试机制
      if (language.version === 0 || language.version === undefined || language.version === null) {
        console.error(`❌ [加载 WASM] 语言模块版本无效:`, {
          version: language.version,
          wasmPath: wasmPath,
          bufferSize: buffer.length,
          bufferFirst16: Array.from(buffer.subarray(0, 16)).map(b => '0x' + b.toString(16).padStart(2, '0')).join(' '),
          languageObject: {
            type: typeof language,
            constructor: language.constructor?.name,
            version: language.version,
            name: language.name,
            keys: Object.keys(language).slice(0, 10)
          },
          loadMethod: loadMethod.name || 'anonymous',
          timestamp: new Date().toISOString(),
          environment: {
            isWorkerThread: typeof (globalThis as any).WorkerGlobalScope !== 'undefined',
            nodeVersion: process.version,
            cwd: process.cwd(),
            argv1: process.argv[1]
          }
        });

        // 如果版本为 0，尝试强制重新加载 WASM 文件
        console.log(`🔄 [加载 WASM] 版本 0 错误，尝试强制重新加载...`);

        // 清除可能的缓存
        if (LanguageLoader.languageCache.has(wasmPath)) {
          LanguageLoader.languageCache.delete(wasmPath);
          console.log(`🗑️ [加载 WASM] 清除缓存: ${wasmPath}`);
        }

        // 重新读取文件
        const freshBuffer = fs.readFileSync(wasmPath);
        console.log(`📦 [加载 WASM] 重新读取文件，大小: ${freshBuffer.length} bytes`);

        // 再次验证魔数
        const freshMagic = Array.from(freshBuffer.subarray(0, 4));
        const expectedMagic = [0x00, 0x61, 0x73, 0x6d];
        if (!freshMagic.every((byte, i) => byte === expectedMagic[i])) {
          throw new Error(`WASM 文件魔数无效: ${freshMagic.map(b => '0x' + b.toString(16)).join(' ')}`);
        }

        // 尝试用新的 buffer 重新加载
        try {
          console.log(`🔄 [加载 WASM] 使用新 buffer 重新加载...`);
          const freshLanguage = await loadMethod(freshBuffer);

          if (freshLanguage && freshLanguage.version && freshLanguage.version > 0) {
            console.log(`✅ [加载 WASM] 重新加载成功，版本: ${freshLanguage.version}`);
            language = freshLanguage;
          } else {
            throw new Error(`重新加载后版本仍为 ${freshLanguage?.version || 'undefined'}`);
          }
        } catch (reloadError) {
          console.error(`❌ [加载 WASM] 重新加载失败:`, reloadError);
          throw new Error(`语言模块版本无效 (${language.version})。WASM 文件可能损坏或不兼容。路径: ${wasmPath}`);
        }
      }

      // 如果重新加载成功，继续验证
      if (language.version === 0 || language.version === undefined || language.version === null) {
        throw new Error(`语言模块版本无效 (${language.version})。WASM 文件可能损坏或不兼容。路径: ${wasmPath}`);
      }

      console.log(`✅ [加载 WASM] 语言模块验证通过，版本: ${language.version}`);
      return language;
    } catch (err: any) {
      console.error(`❌ 加载语言模块失败: ${err.message}`);
      if (err.stack) {
        console.error(err.stack);
      }
      throw err;
    }
  }

  /**
   * 便捷方法：加载 C 语言模块
   */
  static async loadCLanguage(): Promise<any> {
    return await this.loadLanguage('tree-sitter-c/tree-sitter-c.wasm');
  }

  /**
   * 便捷方法：加载 NP 语言模块
   */
  static async loadNPLanguage(): Promise<any> {
    return await this.loadLanguage('tree-sitter-np/tree-sitter-np.wasm');
  }

  /**
   * 清理缓存（用于测试或重新加载）
   */
  static clearCache(): void {
    console.log('🧹 [LanguageLoader] 清理语言模块缓存');
    this.languageCache.clear();
    this.loadingPromises.clear();
  }

  /**
   * 获取缓存状态（用于调试）
   */
  static getCacheInfo(): { cached: string[], loading: string[] } {
    return {
      cached: Array.from(this.languageCache.keys()),
      loading: Array.from(this.loadingPromises.keys())
    };
  }
}
