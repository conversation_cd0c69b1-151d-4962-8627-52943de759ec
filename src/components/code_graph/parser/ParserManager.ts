import * as path from 'path';
import * as vscode from 'vscode';
import { ASTNode } from '../types/ast';
import { FILE_TYPE_MAP, SupportedLanguage } from '../types/language';
import { CParser } from './CParser';
import { NPParser } from './NPParser';
// import { JavaScriptParser } from './JavaScriptParser';
// import { PythonParser } from './PythonParser';
// import { TypeScriptParser } from './TypeScriptParser';

export class ParserManager {
  private parsers: Map<SupportedLanguage, any> = new Map();
  private parserPromises: Map<SupportedLanguage, Promise<any>> = new Map();
  private initialized: boolean = false;

  constructor() {
    // 不立即初始化解析器，等待显式调用
    // 解析器将在 getParser 或 initWithLanguages 中初始化
  }

  // 实例方法
  public getLanguageFromFile(filePath: string): SupportedLanguage {
    return ParserManager.getLanguageFromFile(filePath);
  }

  // 静态方法
  public static getLanguageFromFile(filePath: string): SupportedLanguage {
    const ext = path.extname(filePath).toLowerCase();
    return FILE_TYPE_MAP[ext] || 'c'; // 默认返回 'c'
  }

  async getParser(language: SupportedLanguage) {
    // 如果已经初始化过，直接返回
    if (this.parsers.has(language)) {
      return this.parsers.get(language);
    }

    // 如果还没有初始化，使用默认方式初始化
    if (!this.initialized) {
      console.log('🔄 ParserManager 使用默认方式初始化解析器...');
      try {
        // 使用默认方式初始化解析器
        this.parserPromises.set('c', CParser.create());
        this.parserPromises.set('np', NPParser.create());
        this.initialized = true;
      } catch (error) {
        console.error('❌ 默认解析器初始化失败:', error);
        return null;
      }
    }

    // 等待Promise完成并缓存结果
    const promise = this.parserPromises.get(language);
    if (promise) {
      try {
        const parser = await promise;
        this.parsers.set(language, parser);
        return parser;
      } catch (error) {
        console.error(`初始化 ${language} 解析器失败:`, error);
        return null;
      }
    }

    return null;
  }

  /**
   * 使用预加载的语言模块初始化解析器
   * 用于 Worker 环境，避免重复加载 WASM 文件
   */
  async initWithLanguages(cLanguage: any, npLanguage: any) {
    console.log('🔄 使用预加载语言模块初始化解析器...');
    
    try {
      // 创建使用预加载语言模块的解析器
      const cParser = await CParser.createWithLanguage(cLanguage);
      const npParser = await NPParser.createWithLanguage(npLanguage);
      
      // 直接设置解析器，跳过 Promise 等待
      this.parsers.set('c', cParser);
      this.parsers.set('np', npParser);
      
      // 标记为已初始化，避免重复初始化
      this.initialized = true;
      
      console.log('✅ 解析器初始化完成');
    } catch (error) {
      console.error('❌ 解析器初始化失败:', error);
      throw error;
    }
  }

  async findFunctionAtPosition(document:vscode.TextDocument, position: vscode.Position): Promise<ASTNode | null> {

    const filePath = document.uri.fsPath;
    try {
      // 根据文件名后缀推断语言类型
      const language = ParserManager.getLanguageFromFile(filePath);
      const parser = await this.getParser(language);
      if (!parser) {
        return null;
      }

      const fileContent = document.getText();
      
      
      // 调试：显示文件内容和光标位置
      console.log(`=== 调试文件解析 ===`);
      console.log(`文件路径: ${filePath}`);
      console.log(`光标位置: ${position.line}:${position.character}`);
      console.log(`文件总行数: ${fileContent.split('\n').length}`);
      console.log(`光标所在行内容: "${fileContent.split('\n')[position.line]}"`);
      
      const fileAst = parser.parse(fileContent);
      const functionASTNode = findFunctionNodeInAST(fileAst, position);

      if (functionASTNode) {
        // 确保设置文件路径
        functionASTNode.file = filePath;
        console.log(`找到函数: ${functionASTNode.name}, 位置: ${functionASTNode.location?.start?.line}:${functionASTNode.location?.start?.column} - ${functionASTNode.location?.end?.line}:${functionASTNode.location?.end?.column}`);
        console.log(`函数文本: "${functionASTNode.text?.substring(0, 200)}..."`);
        console.log(`文件路径: ${functionASTNode.file}`);
      } else {
        console.log('未找到函数节点');
      }

      return functionASTNode;
    } catch (error) {
      console.error('解析文件失败:', error);
      return null;
    }
  }
}

// 递归遍历 AST，查找 position 是否在某个函数节点范围内
function findFunctionNodeInAST(ast: any, position: vscode.Position): ASTNode | null {
  if (!ast) {
    return null;
  }

  // // 调试：显示当前节点的基本信息
  // console.log(`检查节点: type=${ast.type}, kind=${ast.kind}, name=${ast.name || 'unnamed'}`);

  // 检查当前节点是否为函数定义
  if (ast.kind === 'function' || ast.type === 'function_definition' || ast.type === 'statement_function_def') {
    const { start, end } = ast.location || ast.loc || {};
    if (start && end) {
      // // 调试：显示AST位置信息的详细格式
      // console.log(`检查函数节点: ${ast.name || ast.type}`);
      // console.log(`AST位置信息:`, { start, end });
      // console.log(`光标位置: ${position.line}:${position.character}`);
      // console.log(`AST节点文本: "${ast.text?.substring(0, 100)}..."`);
      
      // 更精确的位置比较：考虑行和列
      const isInRange = (
        (position.line > start.line || (position.line === start.line && position.character >= start.column)) &&
        (position.line < end.line || (position.line === end.line && position.character <= end.column))
      );
            
      if (isInRange) {
        console.log(`找到函数节点: ${ast.name || ast.type}, 位置: ${start.line}:${start.column} - ${end.line}:${end.column}`);
        return ast as ASTNode;
      }
    } else {
      console.log(`函数节点 ${ast.name || ast.type} 缺少位置信息:`, { location: ast.location, loc: ast.loc });
    }
  }

  // 递归检查子节点
  if (ast.children && Array.isArray(ast.children)) {
    for (const child of ast.children) {
      const found = findFunctionNodeInAST(child, position);
      if (found) {
        return found;
      }
    }
  }

  // 处理不同语言的 AST 结构
  if (ast.body && Array.isArray(ast.body)) {
    for (const node of ast.body) {
      const found = findFunctionNodeInAST(node, position);
      if (found) {
        return found;
      }
    }
  }

  return null;
}
