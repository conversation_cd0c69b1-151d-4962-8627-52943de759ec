// graph/GraphlibCodeGraph.ts
import { Graph } from '@dagrejs/graphlib';
import { performance } from 'perf_hooks';
import { Logger } from '../../../common/log/logger';
import type { CodeEdge, GraphCodeNode, ICodeGraph } from '../types/graph';

const logger = new Logger('GraphlibCodeGraph');

export class GraphlibCodeGraph implements ICodeGraph {
  private graph: Graph;
  private nodeCache = new Map<string, GraphCodeNode>();
  private edgeCache = new Map<string, CodeEdge[]>();
  private relatedNodesCache = new Map<string, Map<number, GraphCodeNode[]>>();

  constructor() {
    this.graph = new Graph({ directed: true, multigraph: false, compound: false });
  }

  get nodes(): Map<string, GraphCodeNode> {
    const nodeMap = new Map<string, GraphCodeNode>();
    for (const id of this.graph.nodes()) {
      const node = this.graph.node(id);
      if (node) { nodeMap.set(id, node); }
    }
    return nodeMap;
  }

  get edges(): CodeEdge[] {
    return this.graph.edges().map(edge => {
      const data = this.graph.edge(edge);
      return {
        from: edge.v,
        to: edge.w,
        type: data?.type || 'calls'
      };
    });
  }

  addNode(node: GraphCodeNode): void {
    try {
      this.graph.setNode(node.id, node);
      this.nodeCache.set(node.id, node);
      // 清除相关节点缓存，因为新节点可能影响关系
      this.relatedNodesCache.clear();
    } catch (error) {
      logger.error(`添加节点失败: ${node.id}`, error);
      throw error;
    }
  }

  removeNode(nodeId: string): void {
    if (this.graph.hasNode(nodeId)) {
      this.graph.removeNode(nodeId);
    }
  }

  removeEdge(from: string, to: string): void {
    try {
      if (this.graph.hasEdge(from, to)) {
        this.graph.removeEdge(from, to);
        
        // 更新边缓存
        const fromEdges = this.edgeCache.get(from);
        if (fromEdges) {
          const index = fromEdges.findIndex(edge => edge.from === from && edge.to === to);
          if (index !== -1) {
            fromEdges.splice(index, 1);
          }
        }
        
        const toEdges = this.edgeCache.get(to);
        if (toEdges) {
          const index = toEdges.findIndex(edge => edge.from === from && edge.to === to);
          if (index !== -1) {
            toEdges.splice(index, 1);
          }
        }
        
        // 清除相关节点缓存，因为删除边可能影响关系
        this.relatedNodesCache.clear();
      }
    } catch (error) {
      logger.error(`删除边失败: ${from} -> ${to}`, error);
      throw error;
    }
  }

  addEdge(from: string, to: string, type: string = 'calls'): void {
    try {
      this.graph.setEdge(from, to, { type });

      // 更新边缓存
      const fromEdges = this.edgeCache.get(from) || [];
      const toEdges = this.edgeCache.get(to) || [];

      const edge: CodeEdge = { from, to, type };
      fromEdges.push(edge);
      toEdges.push(edge);

      this.edgeCache.set(from, fromEdges);
      this.edgeCache.set(to, toEdges);

      // 清除相关节点缓存，因为新边可能影响关系
      this.relatedNodesCache.clear();
    } catch (error) {
      logger.error(`添加边失败: ${from} -> ${to}`, error);
      throw error;
    }
  }

  node(id: string): GraphCodeNode | undefined {
    try {
      // 优先从缓存获取
      if (this.nodeCache.has(id)) {
        return this.nodeCache.get(id);
      }
      return this.graph.node(id);
    } catch (error) {
      logger.error(`获取节点失败: ${id}`, error);
      throw error;
    }
  }

  getIncomingEdges(nodeId: string): CodeEdge[] {
    try {
      // 优先从缓存获取
      if (this.edgeCache.has(nodeId)) {
        return this.edgeCache.get(nodeId)!.filter(edge => edge.to === nodeId);
      }

      const edges: CodeEdge[] = [];
      const inEdges = this.graph.inEdges(nodeId) || [];

      for (const edge of inEdges) {
        const edgeValue = this.graph.edge(edge);
        edges.push({
          from: edge.v,
          to: edge.w,
          type: edgeValue.type
        });
      }

      return edges;
    } catch (error) {
      logger.error(`获取入边失败: ${nodeId}`, error);
      throw error;
    }
  }

  getOutgoingEdges(nodeId: string): CodeEdge[] {
    try {
      // 优先从缓存获取
      if (this.edgeCache.has(nodeId)) {
        return this.edgeCache.get(nodeId)!.filter(edge => edge.from === nodeId);
      }

      const edges: CodeEdge[] = [];
      const outEdges = this.graph.outEdges(nodeId) || [];

      for (const edge of outEdges) {
        const edgeValue = this.graph.edge(edge);
        edges.push({
          from: edge.v,
          to: edge.w,
          type: edgeValue.type
        });
      }

      return edges;
    } catch (error) {
      logger.error(`获取出边失败: ${nodeId}`, error);
      throw error;
    }
  }

  getRelatedNodes(nodeId: string, depth: number): GraphCodeNode[] {
    const startTime = performance.now();
    try {
      // // 优先从缓存获取
      // const depthCache = this.relatedNodesCache.get(nodeId);
      // if (depthCache && depthCache.has(depth)) {
      //   return depthCache.get(depth)!;
      // }

      const visited = new Set<string>();
      const result: GraphCodeNode[] = [];
      const queue: Array<{ id: string; depth: number }> = [{ id: nodeId, depth: 0 }];

      while (queue.length > 0) {
        const current = queue.shift()!;

        if (visited.has(current.id)) { continue; }
        visited.add(current.id);

        const node = this.node(current.id);
        if (node) {
          result.push(node);
        }

        if (current.depth < depth) {
          // 获取相关节点
          const inEdges = this.getIncomingEdges(current.id);
          const outEdges = this.getOutgoingEdges(current.id);

          // 添加调用者到队列
          for (const edge of inEdges) {
            if (!visited.has(edge.from)) {
              queue.push({ id: edge.from, depth: current.depth + 1 });
            }
          }

          // 添加被调用者到队列
          for (const edge of outEdges) {
            if (!visited.has(edge.to)) {
              queue.push({ id: edge.to, depth: current.depth + 1 });
            }
          }
        }
      }

      // 更新缓存
      if (!this.relatedNodesCache.has(nodeId)) {
        this.relatedNodesCache.set(nodeId, new Map());
      }
      this.relatedNodesCache.get(nodeId)!.set(depth, result);

      const endTime = performance.now();
      logger.debug(`获取相关节点完成: ${nodeId}, 深度: ${depth}, 节点数: ${result.length}, 耗时: ${endTime - startTime}ms`);

      return result;
    } catch (error) {
      logger.error(`获取相关节点失败: ${nodeId}`, error);
      throw error;
    }
  }


  getModuleRelatedNodes(nodeId: string, modulePath: string): GraphCodeNode[] {
    const startTime = performance.now();
    try {
      const allRelatedNodes = this.getRelatedNodes(nodeId, 1);
      // 过滤掉模块节点，只返回函数、宏等实际的代码节点
      const moduleNodes = allRelatedNodes.filter(node => 
        node.module === modulePath && 
        node.type !== 'module' && 
        node.type !== 'entry'
      );

      const endTime = performance.now();
      logger.debug(`获取模块相关节点完成: ${nodeId}, 模块: ${modulePath}, 节点数: ${moduleNodes.length}, 耗时: ${endTime - startTime}ms`);

      return moduleNodes;
    } catch (error) {
      logger.error(`获取模块相关节点失败: ${nodeId}, 模块: ${modulePath}`, error);
      throw error;
    }
  }

  /**
   * 清理缓存
   */
  clearCache(): void {
    this.nodeCache.clear();
    this.edgeCache.clear();
    this.relatedNodesCache.clear();
  }
}
