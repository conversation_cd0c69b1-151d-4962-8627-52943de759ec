import * as fs from 'fs';
import * as path from 'path';
import * as vscode from 'vscode';
import { Logger } from '../../common/log/logger';
import { RepoIndexer } from './repoIndexer/repoIndexer';
import { RepoIndexerManager } from './repoIndexer/repoIndexerManager';
// Worker demo imports removed - using simplified worker architecture

const INDEX_CONFIG_FILE = '.vscode/code-partner.json';

export class CodeGraphCommands {
    private repoIndexerManager = RepoIndexerManager.getInstance();
    private _initialized = false;
  private logger = new Logger('CodeGraphCommands');
  
    constructor(private context: vscode.ExtensionContext) {
      // 移除自动初始化，改为懒加载
    // this.initWorkspaceIndices().catch(err => this.logger.error('初始化索引失败', err));
    }
  
    /**
     * 确保RepoIndexerManager已初始化
     */
    private async ensureInitialized(): Promise<void> {
      if (!this._initialized) {
      this.logger.info('初始化RepoIndexerManager...');
      this.repoIndexerManager.initialize(this.context);
      this._initialized = true;
      this.logger.info('RepoIndexerManager初始化完成');
      }
    }
  
    /**
     * 懒加载初始化工作区索引
     */
    private async lazyInitWorkspaceIndices(): Promise<void> {
      await this.ensureInitialized();
      
      const folders = vscode.workspace.workspaceFolders ?? [];
      for (const folder of folders) {
        const workspacePath = folder.uri.fsPath;
        if (!(await this.isIndexingEnabled(workspacePath))) {
        this.logger.info(`跳过 ${folder.name}：未启用代码索引`);
          continue;
        }
        await this.buildAndSaveIndex(workspacePath, folder.name);
      }
    }
  
    private async initWorkspaceIndices(): Promise<void> {
      const folders = vscode.workspace.workspaceFolders ?? [];
      for (const folder of folders) {
        const workspacePath = folder.uri.fsPath;
        if (!(await this.isIndexingEnabled(workspacePath))) {
        this.logger.info(`跳过 ${folder.name}：未启用代码索引`);
          continue;
        }
        await this.buildAndSaveIndex(workspacePath, folder.name);
      }
    }
  
    private async buildAndSaveIndex(workspacePath: string, folderName: string): Promise<void> {
      const indexer = new RepoIndexer(workspacePath);
      await vscode.window.withProgress(
        { location: vscode.ProgressLocation.Notification, title: `索引 ${folderName}`, cancellable: false },
        async (progress) => {
          progress.report({ increment: 0 });
          await indexer.analyze();
          progress.report({ increment: 100 });
        }
      );
      this.repoIndexerManager.setRepoIndexer(indexer);
      await this.saveIndexData();
    this.logger.info(`完成索引并保存: ${folderName}`);
    }
  
    private async saveIndexData(): Promise<void> {
      const indexer = this.repoIndexerManager.getRepoIndexer();
    if (!indexer) { return; }
      try {
      // 让RepoIndexerManager统一管理存储
      await this.repoIndexerManager.saveCurrentIndexData();
      this.logger.info('索引数据已保存');
      } catch (err) {
      this.logger.error('保存索引数据失败', err as Error);
      }
    }
  
    public async initializeRepoIndex(): Promise<void> {
      await this.ensureInitialized();
      
      const workspacePath = this.getCurrentWorkspacePath();
      if (!workspacePath) {
        vscode.window.showErrorMessage('请先打开工作区');
        return;
      }
      await this.buildAndSaveIndex(workspacePath, path.basename(workspacePath));
      vscode.window.showInformationMessage('代码仓库索引初始化完成');
    }
  
    public async updateRepoIndex(): Promise<void> {
      await this.ensureInitialized();
      
      const indexer = this.repoIndexerManager.getRepoIndexer();
      if (!indexer) {
        vscode.window.showErrorMessage('请先初始化代码仓库索引');
        return;
      }
      await vscode.window.withProgress(
        { location: vscode.ProgressLocation.Notification, title: '正在更新代码仓库索引...', cancellable: false },
        async (progress) => {
          progress.report({ increment: 0 });
          await indexer.analyze(true); // 增量更新
          progress.report({ increment: 100 });
        }
      );
      await this.saveIndexData();
      vscode.window.showInformationMessage('代码仓库索引更新完成');
    }
  
    public async queryRelatedFunctions(): Promise<void> {
      await this.ensureInitialized();
      
      const indexer = this.repoIndexerManager.getRepoIndexer();
      if (!indexer) {
        vscode.window.showErrorMessage('请先初始化代码仓库索引');
        return;
      }
      const editor = vscode.window.activeTextEditor;
      if (!editor) {
        vscode.window.showErrorMessage('请先打开一个文件');
        return;
      }
      const selectedText = editor.document.getText(editor.selection).trim();
      if (!selectedText) {
        vscode.window.showErrorMessage('请先选择函数名');
        return;
      }
      const moduleName = editor.document.fileName;
      const related = indexer.getModuleRelatedNodesByName(selectedText, moduleName);
      if (related.length === 0) {
        vscode.window.showInformationMessage(`未找到 ${selectedText} 的相关函数`);
        return;
      }

    // 使用Logger输出到outputChannel
    this.logger.info(`=== ${selectedText} 的相关函数 ===`, { toOutput: true });
      for (const node of related) {
      this.logger.info(`${node.name} (${node.type}) - ${node.file}`, { toOutput: true });
      }
    }

    public async showRepoIndex(): Promise<void> {
    this.logger.info('=== 开始执行showRepoIndex命令 ===');
    this.logger.info('1. 检查RepoIndexerManager状态...');
      // 不调用ensureInitialized()，避免触发自动初始化
      // 只设置context，不触发自动恢复
      if (!this._initialized) {
        this.repoIndexerManager.initialize(this.context, false); // 不自动初始化索引
        this._initialized = true;
        this.logger.info('RepoIndexerManager已初始化（仅设置context，不自动初始化索引）');
      }
      
    this.logger.info('2. 获取RepoIndexer实例...');
      const indexer = this.repoIndexerManager.getRepoIndexer();
    this.logger.info('RepoIndexer实例状态:', {
      hasIndexer: !!indexer,
      indexerType: indexer ? indexer.constructor.name : 'null'
    });

      if (!indexer) {
      this.logger.info('RepoIndexer实例为空，显示提示信息');
        vscode.window.showInformationMessage('索引数据为空，请先执行"初始化工作区索引"命令');
        return;
      }
    this.logger.info('RepoIndexer实例获取成功');

    // 检查索引数据是否真的存在
    try {
      this.logger.info('3. 导出索引数据...');
      const data = indexer.exportData();
      this.logger.info('索引数据导出成功:', {
        hasData: !!data,
        hasGraph: !!data?.graph,
        nodesSize: data?.graph?.nodes?.length || 0,
        edgesLength: data?.graph?.edges?.length || 0,
        workspacePath: data?.workspacePath || 'undefined'
      });

      if (!data || !data.graph || data.graph.nodes.length === 0) {
        this.logger.info('索引数据为空，显示提示信息');
        vscode.window.showInformationMessage('索引数据为空，请先执行索引');
        return;
      }

      this.logger.info('4. 显示索引内容...');
      // 使用Logger输出到outputChannel
      this.logger.info('=== 当前索引内容 ===', { toOutput: true });
      this.logger.info(`索引节点数量: ${data.graph.nodes.length}`, { toOutput: true });
      this.logger.info(`索引边数量: ${data.graph.edges.length}`, { toOutput: true });
      this.logger.info(`工作区路径: ${data.workspacePath}`, { toOutput: true });
      this.logger.info('=== 详细数据 ===', { toOutput: true });
      this.logger.info(JSON.stringify(data, null, 2), { toOutput: true });
      this.logger.info('索引内容显示完成');
    } catch (error) {
      this.logger.error('获取索引数据失败:', error as Error);
      vscode.window.showErrorMessage('获取索引数据失败，请重新初始化索引');
    }
    }

  /** 获取当前工作区路径 */
  private getCurrentWorkspacePath(): string | undefined {
    const folders = vscode.workspace.workspaceFolders;
    if (!folders || folders.length === 0) { return undefined; }
    return folders[0].uri.fsPath;
  }

  /**
   * 初始化工作区索引（命令入口）
   */
  public async initializeWorkspaceIndex(): Promise<void> {
    await this.ensureInitialized();
    await this.repoIndexerManager.initializeWorkspaceIndex(this.context);
  }

  /**
   * 重置索引（命令入口）
   */
  public async resetIndex(): Promise<void> {
    await this.ensureInitialized();
    await this.repoIndexerManager.resetIndex();
  }

  /**
   * 清理索引数据（命令入口）
   */
  public async cleanIndexData(): Promise<void> {
    // 不调用ensureInitialized()，避免触发自动初始化
    // 直接调用RepoIndexerManager的清理方法
    await this.repoIndexerManager.cleanIndexData(this.context);
  }

  /**
   * 增量更新索引（文件监听器入口）
   */
  public async updateIndexForFiles(added: string[], removed: string[], changed: string[]): Promise<void> {
    await this.ensureInitialized();
    await this.repoIndexerManager.updateIndexForFiles(added, removed, changed);
  }

  /**
   * 测试 Worker 线程解析功能 (简化版)
   */
  public async testWorkerDemo(): Promise<void> {
    try {
      this.logger.info('Worker Demo 功能已简化，使用分离构建架构');
      vscode.window.showInformationMessage('Worker 系统已升级为分离构建架构');
    } catch (error) {
      this.logger.error('Worker Demo 测试失败:', error);
      vscode.window.showErrorMessage(`Worker Demo 测试失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 测试 Worker 池功能 (简化版)
   */
  public async testWorkerPoolDemo(): Promise<void> {
    try {
      this.logger.info('Worker Pool Demo 功能已简化，使用分离构建架构');
      vscode.window.showInformationMessage('Worker Pool 系统已升级为分离构建架构');
    } catch (error) {
      this.logger.error('Worker Pool Demo 测试失败:', error);
      vscode.window.showErrorMessage(`Worker Pool Demo 测试失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  private async isIndexingEnabled(workspacePath: string): Promise<boolean> {
    const configPath = path.join(workspacePath, INDEX_CONFIG_FILE);
    if (!fs.existsSync(configPath)) {
      this.logger.info(`未找到配置文件: ${INDEX_CONFIG_FILE}`);
      return false;
    }
    try {
      const content = fs.readFileSync(configPath, 'utf-8');
      const config = JSON.parse(content);
      return Boolean(config.enableIndexing);
    } catch {
      this.logger.info(`配置文件格式错误: ${INDEX_CONFIG_FILE}`);
      return false;
    }
  }
}
  