/**
 * Worker 专用路径解析器 (简化版)
 */

import * as path from 'path';
import * as fs from 'fs';

export class WorkerPathResolver {
    private static instance: WorkerPathResolver;
    private workerDir: string;
    private assetsDir: string;

    private constructor() {
        this.workerDir = __dirname;
        this.assetsDir = path.join(this.workerDir, 'assets');
        console.log(`🔧 [WorkerPathResolver] 初始化: ${this.workerDir}`);
    }

    public static getInstance(): WorkerPathResolver {
        if (!WorkerPathResolver.instance) {
            WorkerPathResolver.instance = new WorkerPathResolver();
        }
        return WorkerPathResolver.instance;
    }

    public resolveWasmPath(filename: string): string | null {
        const candidates = [
            path.join(this.assetsDir, filename),
            path.join(this.workerDir, filename),
            path.join(this.workerDir, '..', 'assets', filename)
        ];

        for (const candidate of candidates) {
            try {
                if (fs.existsSync(candidate)) {
                    const stats = fs.statSync(candidate);
                    if (stats.size > 0) {
                        console.log(`✅ 找到 WASM: ${candidate}`);
                        return candidate;
                    }
                }
            } catch (error) {
                // 忽略错误，继续查找
            }
        }

        return null;
    }

    public setupModuleLocateFile(): void {
        // 简化版本，避免 TypeScript 错误
        try {
            const g = global as any;
            if (g && typeof g === 'object') {
                g.Module = g.Module || {};
                g.Module.locateFile = (filename: string) => {
                    const resolved = this.resolveWasmPath(filename);
                    return resolved || filename;
                };
                console.log('✅ Module.locateFile 已设置');
            }
        } catch (error) {
            console.log('⚠️ 无法设置 Module.locateFile:', error);
        }
    }
}

// 自动初始化
const resolver = WorkerPathResolver.getInstance();
resolver.setupModuleLocateFile();

export default WorkerPathResolver;
