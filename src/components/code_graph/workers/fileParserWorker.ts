// 全局异常捕获，便于主进程调试 worker 报错
process.on('uncaughtException', (err) => {
  // eslint-disable-next-line no-console
  console.error('[WORKER uncaughtException]', err);
  if (typeof parentPort !== 'undefined' && parentPort) {
    parentPort.postMessage({ type: 'log', message: '[uncaughtException] ' + (err.stack || err) });
  }
  process.exit(1);
});
process.on('unhandledRejection', (reason) => {
  // eslint-disable-next-line no-console
  console.error('[WORKER unhandledRejection]', reason);
  if (typeof parentPort !== 'undefined' && parentPort) {
    parentPort.postMessage({ type: 'log', message: '[unhandledRejection] ' + reason });
  }
  process.exit(1);
});

import * as fs from 'fs/promises';
import * as path from 'path';
import { parentPort, workerData } from 'worker_threads';
import { ParserManager } from '../parser/ParserManager';
import { FILE_TYPE_MAP, SupportedLanguage } from '../types/language';
import './WorkerPathResolver'; // 自动设置 WASM 路径解析

interface ParseFileMessage {
  type: 'parse_file';
  id: string;
  filePath: string;
  workspacePath: string;
}

interface TestModulePathMessage {
  type: 'test_module_path';
  test: boolean;
}

interface DebugWasmPathsMessage {
  type: 'debug_wasm_paths';
  wasmPaths: string[];
  requirePaths: string[];
}

interface DiagnoseWasmMessage {
  type: 'diagnose_wasm';
  wasmPaths?: {
    treeSitterC: string;
    treeSitterNp: string;
  };
}

interface InitWorkerMessage {
  type: 'init_worker';
  wasmPaths?: {
    treeSitterC: string;
    treeSitterNp: string;
  };
  wasmBuffers?: {
    treeSitterC: Uint8Array;
    treeSitterNp: Uint8Array;
  };
}

interface WorkerResult {
  success: boolean;
  filePath: string;
  ast?: any;
  mtimeMs?: number;
  error?: string;
}

interface ModulePathTestResult {
  type: 'module_path_test';
  currentDir: string;
  dirname: string;
  cwd: string;
  webTreeSitterPath: string;
  treeSitterNpPath: string;
  wasmExists: boolean;
}

// 初始化解析器管理器
const parserManager = new ParserManager();

// 设置 Worker 线程标识，便于路径解析逻辑识别
process.env.WORKER_THREAD_ID = 'true';

// 从 workerData 获取 WASM 路径
const wasmPaths = workerData?.wasmPaths || null;
const workerId = workerData?.workerId || 'unknown';

console.log(`🚀 Worker ${workerId} 启动`);
console.log(`📂 Worker 当前目录: ${process.cwd()}`);
console.log(`📂 Worker 脚本路径: ${process.argv[1]}`);
console.log(`📂 Worker __dirname: ${typeof __dirname !== 'undefined' ? __dirname : 'undefined'}`);

// 异步初始化函数
async function initializeWorker() {
  // 如果 workerData 中有 WASM 路径，立即初始化
  if (wasmPaths) {
    console.log(`🔄 Worker ${workerId} 从 workerData 获取到 WASM 路径，开始初始化...`);
    console.log(`📁 C WASM 路径: ${wasmPaths.treeSitterC}`);
    console.log(`📁 NP WASM 路径: ${wasmPaths.treeSitterNp}`);

    // 验证 WASM 路径是否存在
    const fs = await import('fs');
    const cExists = fs.existsSync(wasmPaths.treeSitterC.replace('file://', ''));
    const npExists = fs.existsSync(wasmPaths.treeSitterNp.replace('file://', ''));
    console.log(`📁 C WASM 文件存在: ${cExists}`);
    console.log(`📁 NP WASM 文件存在: ${npExists}`);

    try {
      // 立即初始化解析器
      const result = await initWorker(wasmPaths);
      if (result.success) {
        console.log(`✅ Worker ${workerId} 初始化完成`);
        // 通知主线程初始化成功
        if (parentPort) {
          parentPort.postMessage({
            type: 'worker_ready',
            workerId: workerId,
            success: true
          });
        }
      } else {
        console.error(`❌ Worker ${workerId} 初始化失败:`, result.error);
        // 通知主线程初始化失败
        if (parentPort) {
          parentPort.postMessage({
            type: 'worker_ready',
            workerId: workerId,
            success: false,
            error: result.error
          });
        }
      }
    } catch (error) {
      console.error(`❌ Worker ${workerId} 初始化异常:`, error);
      // 通知主线程初始化异常
      if (parentPort) {
        parentPort.postMessage({
          type: 'worker_ready',
          workerId: workerId,
          success: false,
          error: error instanceof Error ? error.message : String(error)
        });
      }
    }
  } else {
    console.log(`⚠️ Worker ${workerId} 未从 workerData 获取到 WASM 路径，等待显式初始化消息`);
    // 通知主线程等待初始化
    if (parentPort) {
      parentPort.postMessage({
        type: 'worker_waiting',
        workerId: workerId
      });
    }
  }
}

// 启动异步初始化
initializeWorker().catch(error => {
  console.error(`❌ Worker ${workerId} 启动失败:`, error);
  if (parentPort) {
    parentPort.postMessage({
      type: 'worker_ready',
      workerId: workerId,
      success: false,
      error: error instanceof Error ? error.message : String(error)
    });
  }
});

// 初始化 Worker，使用主线程传入的 WASM 路径或 Buffer
async function initWorker(
  wasmPaths?: { treeSitterC: string; treeSitterNp: string },
  wasmBuffers?: { treeSitterC: Uint8Array; treeSitterNp: Uint8Array }
) {
  console.log('🔄 Worker 开始初始化...');

  if (wasmBuffers) {
    console.log('📦 使用 WASM Buffer 初始化');
    console.log('📦 C WASM Buffer 大小:', wasmBuffers.treeSitterC.length);
    console.log('📦 NP WASM Buffer 大小:', wasmBuffers.treeSitterNp.length);
  } else if (wasmPaths) {
    console.log('📁 使用 WASM 路径初始化');
    console.log('📁 C WASM 路径:', wasmPaths.treeSitterC);
    console.log('📁 NP WASM 路径:', wasmPaths.treeSitterNp);
  } else {
    throw new Error('必须提供 wasmPaths 或 wasmBuffers');
  }

  try {
    // 使用绝对路径初始化解析器
    console.log('🔄 开始导入 LanguageLoader...');
    const { LanguageLoader } = await import('../parser/LanguageLoader.js');
    console.log('✅ LanguageLoader 导入成功');

    let cLanguage: any;
    let npLanguage: any;

    if (wasmBuffers) {
      // 使用 Buffer 加载
      console.log('🔄 开始从 Buffer 加载 C 语言模块...');
      cLanguage = await LanguageLoader.loadFromBuffer(wasmBuffers.treeSitterC);
      console.log('✅ C 语言模块从 Buffer 加载成功');

      console.log('🔄 开始从 Buffer 加载 NP 语言模块...');
      npLanguage = await LanguageLoader.loadFromBuffer(wasmBuffers.treeSitterNp);
      console.log('✅ NP 语言模块从 Buffer 加载成功');
    } else if (wasmPaths) {
      // 使用路径加载
      const fs = await import('fs');

      // 处理 file:// URL 格式的路径
      let cWasmPath = wasmPaths.treeSitterC;
      let npWasmPath = wasmPaths.treeSitterNp;

      if (cWasmPath.startsWith('file://')) {
        cWasmPath = cWasmPath.replace('file://', '');
      }
      if (npWasmPath.startsWith('file://')) {
        npWasmPath = npWasmPath.replace('file://', '');
      }

      console.log('📁 处理后的 C WASM 路径:', cWasmPath);
      console.log('📁 处理后的 NP WASM 路径:', npWasmPath);

      // 验证文件是否存在
      if (!fs.existsSync(cWasmPath)) {
        throw new Error(`C WASM 文件不存在: ${cWasmPath}`);
      }
      if (!fs.existsSync(npWasmPath)) {
        throw new Error(`NP WASM 文件不存在: ${npWasmPath}`);
      }

      // 验证文件大小
      const cStat = fs.statSync(cWasmPath);
      const npStat = fs.statSync(npWasmPath);
      console.log(`📦 C WASM 文件大小: ${cStat.size} bytes`);
      console.log(`📦 NP WASM 文件大小: ${npStat.size} bytes`);

      if (cStat.size === 0) {
        throw new Error(`C WASM 文件为空: ${cWasmPath}`);
      }
      if (npStat.size === 0) {
        throw new Error(`NP WASM 文件为空: ${npWasmPath}`);
      }

      // 加载 C 语言模块
      console.log('🔄 开始加载 C 语言模块...');
      cLanguage = await LanguageLoader.loadFromAbsolutePath(cWasmPath);
      console.log('✅ C 语言模块加载成功');

      // 加载 NP 语言模块
      console.log('🔄 开始加载 NP 语言模块...');
      npLanguage = await LanguageLoader.loadFromAbsolutePath(npWasmPath);
      console.log('✅ NP 语言模块加载成功');
    }

    // 重新初始化解析器管理器
    console.log('🔄 开始初始化解析器管理器...');
    await parserManager.initWithLanguages(cLanguage, npLanguage);
    console.log('✅ Worker 初始化完成');

    return { success: true };
  } catch (error) {
    console.error('❌ Worker 初始化失败:', error);

    // 提供更详细的错误信息
    let errorMessage = error instanceof Error ? error.message : String(error);
    if (error instanceof Error && error.stack) {
      console.error('❌ 错误堆栈:', error.stack);
      errorMessage += '\n堆栈: ' + error.stack;
    }

    return {
      success: false,
      error: errorMessage
    };
  }
}

// 获取文件语言类型
function getLanguageFromFile(filePath: string): SupportedLanguage | null {
  const ext = path.extname(filePath).toLowerCase();
  return FILE_TYPE_MAP[ext] || null;
}

// 测试模块路径
async function testModulePath(): Promise<ModulePathTestResult> {
  const fsSync = await import('fs');
  
  // 获取当前文件路径
  const currentDir = process.cwd();
  const cwd = process.cwd();
  
  // 测试 web-tree-sitter 路径
  let webTreeSitterPath = '';
  try {
    // 尝试直接导入 web-tree-sitter
    await import('web-tree-sitter');
    webTreeSitterPath = 'imported successfully';
  } catch (error) {
    webTreeSitterPath = 'not found';
  }
  
  // 测试 tree-sitter-np 路径
  let treeSitterNpPath = '';
  try {
    // 检查 WASM 文件是否存在，而不是 require 模块
    const wasmPath = path.resolve(process.cwd(), 'node_modules/tree-sitter-np/tree-sitter-np.wasm');
    if (fsSync.existsSync(wasmPath)) {
      treeSitterNpPath = 'WASM file exists';
    } else {
      treeSitterNpPath = 'WASM file not found';
    }
  } catch (error) {
    treeSitterNpPath = 'error checking WASM';
  }
  
  // 测试 WASM 文件
  const possibleWasmPaths = [
    path.resolve(process.cwd(), 'node_modules/tree-sitter-np/tree-sitter-np.wasm'),
    path.resolve(process.cwd(), 'dist/node_modules/tree-sitter-np/tree-sitter-np.wasm'),
    path.resolve(currentDir, '../../../../node_modules/tree-sitter-np/tree-sitter-np.wasm'),
    path.resolve(currentDir, '../../../../../node_modules/tree-sitter-np/tree-sitter-np.wasm'),
    path.resolve(currentDir, '../../../../../../dist/node_modules/tree-sitter-np/tree-sitter-np.wasm')
  ];
  
  let wasmExists = false;
  for (const wasmPath of possibleWasmPaths) {
    if (fsSync.existsSync(wasmPath)) {
      wasmExists = true;
      break;
    }
  }
  
  return {
    type: 'module_path_test',
    currentDir,
    dirname: currentDir,
    cwd,
    webTreeSitterPath,
    treeSitterNpPath,
    wasmExists
  };
}

// 解析单个文件
async function parseFile(filePath: string, workspacePath: string): Promise<WorkerResult> {
  console.log(`🔄 [Worker] 开始解析文件: ${filePath}`);
  console.log(`📁 [Worker] 工作区路径: ${workspacePath}`);

  try {
    // 获取文件语言类型
    const language = getLanguageFromFile(filePath);
    console.log(`🔍 [Worker] 检测到语言类型: ${language}`);

    if (!language) {
      console.log(`❌ [Worker] 不支持的文件类型: ${path.extname(filePath)}`);
      return {
        success: false,
        filePath,
        error: `不支持的文件类型: ${path.extname(filePath)}`
      };
    }

    // 获取解析器
    console.log(`🔄 [Worker] 获取解析器: ${language}`);
    const parser = await parserManager.getParser(language);
    if (!parser) {
      console.log(`❌ [Worker] 未找到解析器: ${language}`);
      return {
        success: false,
        filePath,
        error: `未找到解析器: ${language}`
      };
    }
    console.log(`✅ [Worker] 解析器获取成功: ${language}`);

    // 读取文件内容
    console.log(`📖 [Worker] 读取文件内容: ${filePath}`);
    const content = await fs.readFile(filePath, 'utf-8');
    console.log(`📄 [Worker] 文件内容长度: ${content.length} 字符`);

    // 使用真实的 tree-sitter 解析器解析 AST
    console.log(`🔄 [Worker] 开始解析 AST...`);
    const ast = await parser.parse(content);
    console.log(`✅ [Worker] AST 解析完成`);

    // 获取文件修改时间
    const stats = await fs.stat(filePath);
    console.log(`📊 [Worker] 文件修改时间: ${stats.mtimeMs}`);

    console.log(`🎉 [Worker] 文件解析成功: ${filePath}`);
    return {
      success: true,
      filePath,
      ast,
      mtimeMs: stats.mtimeMs
    };
  } catch (error) {
    console.log(`❌ [Worker] 文件解析失败: ${filePath}`, error);
    return {
      success: false,
      filePath,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

// 诊断 WASM 加载问题
async function diagnoseWasm(wasmPaths?: { treeSitterC: string; treeSitterNp: string }) {
  const fs = await import('fs');

  console.log('🔍 [Worker 诊断] 开始 WASM 诊断...');

  // 收集环境信息
  const envInfo = {
    cwd: process.cwd(),
    argv0: process.argv[0],
    argv1: process.argv[1],
    dirname: typeof __dirname !== 'undefined' ? __dirname : 'undefined',
    nodeVersion: process.version,
    platform: process.platform,
    arch: process.arch,
    isWorkerThread: typeof process.env.WORKER_THREAD_ID !== 'undefined'
  };

  console.log('📊 [Worker 诊断] 环境信息:', envInfo);

  // 发送环境信息
  if (parentPort) {
    parentPort.postMessage({
      type: 'diagnose_env',
      envInfo
    });
  }

  // 如果提供了 WASM 路径，测试这些路径
  if (wasmPaths) {
    console.log('🔍 [Worker 诊断] 测试提供的 WASM 路径...');
    const wasmTests = [];

    for (const [name, wasmPath] of Object.entries(wasmPaths)) {
      let actualPath = wasmPath;
      if (wasmPath.startsWith('file://')) {
        actualPath = wasmPath.replace('file://', '');
      }

      const test = {
        name,
        originalPath: wasmPath,
        actualPath,
        exists: false,
        size: 0,
        readable: false,
        error: null as string | null
      };

      try {
        test.exists = fs.existsSync(actualPath);
        if (test.exists) {
          const stat = fs.statSync(actualPath);
          test.size = stat.size;
          test.readable = stat.isFile();
        }
      } catch (error) {
        test.error = error instanceof Error ? error.message : String(error);
      }

      wasmTests.push(test);
      console.log(`📁 [Worker 诊断] ${name}: ${actualPath} -> 存在:${test.exists}, 大小:${test.size}, 可读:${test.readable}`);
    }

    if (parentPort) {
      parentPort.postMessage({
        type: 'diagnose_wasm_paths',
        wasmTests
      });
    }
  }

  // 测试 LanguageLoader 的路径查找能力
  console.log('🔍 [Worker 诊断] 测试 LanguageLoader 路径查找...');
  try {
    const { LanguageLoader } = await import('../parser/LanguageLoader.js');

    // 测试查找 web-tree-sitter WASM
    const webTreeSitterWasm = (LanguageLoader as any).findWasmFile?.('web-tree-sitter/tree-sitter.wasm');
    console.log(`📁 [Worker 诊断] web-tree-sitter WASM: ${webTreeSitterWasm || '未找到'}`);

    // 测试查找 tree-sitter-c WASM
    const cWasm = (LanguageLoader as any).findWasmFile?.('tree-sitter-c/tree-sitter-c.wasm');
    console.log(`📁 [Worker 诊断] tree-sitter-c WASM: ${cWasm || '未找到'}`);

    // 测试查找 tree-sitter-np WASM
    const npWasm = (LanguageLoader as any).findWasmFile?.('tree-sitter-np/tree-sitter-np.wasm');
    console.log(`📁 [Worker 诊断] tree-sitter-np WASM: ${npWasm || '未找到'}`);

    if (parentPort) {
      parentPort.postMessage({
        type: 'diagnose_language_loader',
        results: {
          webTreeSitterWasm,
          cWasm,
          npWasm
        }
      });
    }
  } catch (error) {
    console.error('❌ [Worker 诊断] LanguageLoader 测试失败:', error);
    if (parentPort) {
      parentPort.postMessage({
        type: 'diagnose_language_loader',
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  console.log('✅ [Worker 诊断] 诊断完成');
}

// 调试 WASM 路径（保留兼容性）
async function debugWasmPaths(wasmPaths: string[], requirePaths: string[]) {
  const fsSync = await import('fs');
  const path = await import('path');

  // 在 ES 模块中获取 __dirname 的等效值
  const currentDir = path.dirname(process.argv[1] || process.cwd());

  // 发送环境信息
  parentPort!.postMessage({
    type: 'debug_info',
    cwd: process.cwd(),
    dirname: currentDir,
    argv0: process.argv[0],
    argv1: process.argv[1]
  });

  // 测试 WASM 文件路径
  const wasmResults = [];
  for (const wasmPath of wasmPaths) {
    const exists = fsSync.existsSync(wasmPath);
    const size = exists ? fsSync.statSync(wasmPath).size : 0;
    wasmResults.push({ path: wasmPath, exists, size });
  }
  parentPort!.postMessage({
    type: 'wasm_path_test',
    results: wasmResults
  });

  // 测试 require 路径
  const requireResults = [];
  for (const requirePath of requirePaths) {
    try {
      const { createRequire } = await import('module');
      const require = createRequire(process.argv[1] || __filename);
      require(requirePath);
      requireResults.push({ path: requirePath, success: true, error: null });
    } catch (error) {
      requireResults.push({
        path: requirePath,
        success: false,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }
  parentPort!.postMessage({
    type: 'require_test',
    results: requireResults
  });
}

// 监听主线程消息
if (parentPort) {
  parentPort.on('message', async (message: ParseFileMessage | TestModulePathMessage | DebugWasmPathsMessage | InitWorkerMessage | DiagnoseWasmMessage) => {
    if (message.type === 'init_worker') {
      const result = await initWorker(message.wasmPaths, message.wasmBuffers);
      parentPort!.postMessage({
        type: 'init_result',
        ...result
      });
    } else if (message.type === 'parse_file') {
      console.log(`📨 [Worker] 收到解析文件请求: ${message.filePath}, ID: ${message.id}`);
      const result = await parseFile(message.filePath, message.workspacePath);
      console.log(`📤 [Worker] 发送解析结果: ${result.success ? '成功' : '失败'}, ID: ${message.id}`);
      // 确保返回的结果包含任务ID
      parentPort!.postMessage({
        ...result,
        id: message.id
      });
    } else if (message.type === 'test_module_path') {
      const result = await testModulePath();
      parentPort!.postMessage(result);
    } else if (message.type === 'debug_wasm_paths') {
      await debugWasmPaths(message.wasmPaths, message.requirePaths);
    } else if (message.type === 'diagnose_wasm') {
      await diagnoseWasm(message.wasmPaths);
    }
  });
}