import * as fs from 'fs';
import * as path from 'path';
import { Worker } from 'worker_threads';
import { Logger } from '../../../common/log/logger';

const logger = new Logger('WorkerPool');

interface ParseFileTask {
  id: string;
  filePath: string;
  workspacePath: string;
}

interface WorkerResult {
  id: string;
  success: boolean;
  filePath: string;
  ast?: any;
  mtimeMs?: number;
  error?: string;
}

interface WorkerPoolOptions {
  maxWorkers?: number;
  workerScriptPath?: string;
  onProgress?: (progress: ProgressInfo) => void;
  wasmPaths?: {
    treeSitterC: string;
    treeSitterNp: string;
  };
}

interface ProgressInfo {
  completed: number;
  total: number;
  currentFile?: string;
  percentage: number;
}

export class WorkerPool {
  private workers: Worker[] = [];
  private taskQueue: Array<{ task: ParseFileTask; resolve: (result: WorkerResult) => void; reject: (error: Error) => void; assigned?: boolean }> = [];
  private availableWorkers: Worker[] = [];
  private maxWorkers: number;
  private workerScriptPath: string;
  private isShuttingDown = false;
  private onProgress?: (progress: ProgressInfo) => void;
  private totalTasks = 0;
  private completedTasks = 0;
  private wasmPaths?: {
    treeSitterC: string;
    treeSitterNp: string;
  };

  constructor(options: WorkerPoolOptions = {}) {
    this.maxWorkers = options.maxWorkers || 3; // 默认最多3个worker
    this.wasmPaths = options.wasmPaths;
    
    // 使用更健壮的路径处理，确保在Windows环境下也能正确工作
    if (options.workerScriptPath) {
      this.workerScriptPath = path.resolve(options.workerScriptPath);
    } else {
      // 分离构建架构：Worker 文件位于 dist/workers/ 目录
      const currentDir = __dirname;

      // 候选路径列表（按优先级排序）
      // 优化：将项目根目录路径提升为最高优先级，这样无论 WorkerPool 在哪里都能工作
      const workerPaths = [
        // 1. 项目根目录路径（最可靠，优先级最高）
        path.resolve(process.cwd(), 'dist/workers/fileParserWorker.mjs'),

        // 2. 分离构建的相对路径（当前工作方式的后备）
        path.resolve(currentDir, '../../../workers/fileParserWorker.mjs'), // 从 dist/components/code_graph/workers 到 dist/workers
        path.resolve(currentDir, '../../workers/fileParserWorker.mjs'),    // 从 dist/components/code_graph 到 dist/workers
        path.resolve(currentDir, '../workers/fileParserWorker.mjs'),       // 从 dist/components 到 dist/workers
        path.resolve(currentDir, 'workers/fileParserWorker.mjs'),          // 从 dist 到 dist/workers

        // 3. 如果在 src 目录中，指向 dist/workers
        path.resolve(currentDir.replace('src', 'dist').replace('components/code_graph/workers', 'workers'), 'fileParserWorker.mjs'),

        // 4. 兼容旧路径（向后兼容）
        path.resolve(currentDir, 'fileParserWorker.mjs'),
        path.resolve(currentDir, 'workers/fileParserWorker.mjs'),
        path.resolve(currentDir, 'components/code_graph/workers/fileParserWorker.mjs'),
        path.resolve(currentDir, 'dist/components/code_graph/workers/fileParserWorker.mjs')
      ];

      // 查找第一个存在的路径
      this.workerScriptPath = workerPaths.find(p => {
        try {
          return fs.existsSync(p);
        } catch {
          return false;
        }
      }) || workerPaths[0]; // 如果都不存在，使用第一个作为默认值
    }
    
    this.onProgress = options.onProgress;
    
    logger.info(`WorkerPool 初始化，最大 worker 数: ${this.maxWorkers} (固定限制)`);
    logger.info(`Worker 脚本路径: ${this.workerScriptPath}`);
    if (this.wasmPaths) {
      logger.info(`WASM 路径配置: C=${this.wasmPaths.treeSitterC}, NP=${this.wasmPaths.treeSitterNp}`);
    }
  }

  /**
   * 初始化 worker 池
   */
  async initialize(): Promise<void> {
    logger.info(`开始初始化 WorkerPool，最大 worker 数: ${this.maxWorkers}`);
    
    const promises = [];
    for (let i = 0; i < this.maxWorkers; i++) {
      promises.push(this.createWorker());
    }
    
    await Promise.all(promises);
    logger.info(`WorkerPool 初始化完成，成功创建 ${this.workers.length} 个 worker`);
  }

  /**
   * 创建单个 worker
   */
  private async createWorker(): Promise<void> {
    // 检查是否已达到最大worker数量限制
    if (this.workers.length >= this.maxWorkers) {
      logger.warn(`已达到最大worker数量限制(${this.maxWorkers})，跳过创建新worker`);
      return Promise.resolve();
    }
    
    return new Promise((resolve, reject) => {
      try {
        logger.info(`创建新的 worker，脚本路径: ${this.workerScriptPath}`);
        
        // 使用 workerData 传递 WASM 路径
        const workerData = {
          workerId: this.workers.length + 1,
          wasmPaths: this.wasmPaths
        };
        
        const worker = new Worker(this.workerScriptPath, {
          workerData
        });
        
        worker.on('message', (result: any) => {
          // 处理Worker日志
          if (result.type === 'log') {
            logger.info(`[Worker] ${result.message}`);
            return;
          }

          // 处理Worker初始化结果
          if (result.type === 'init_result') {
            if (result.success) {
              logger.info(`Worker ${result.workerId || this.workers.length + 1} 初始化成功`);
            } else {
              logger.error(`Worker ${result.workerId || this.workers.length + 1} 初始化失败: ${result.error}`);
            }
            return;
          }

          // 处理Worker就绪消息
          if (result.type === 'worker_ready') {
            if (result.success) {
              logger.info(`Worker ${result.workerId} 已准备就绪`);
            } else {
              logger.error(`Worker ${result.workerId} 准备失败: ${result.error}`);
            }
            return;
          }

          // 处理Worker等待初始化消息
          if (result.type === 'worker_waiting') {
            logger.info(`Worker ${result.workerId} 等待初始化`);
            return;
          }

          // 处理Worker就绪消息（兼容旧版本）
          if (result.type === 'ready') {
            logger.info(`Worker ${this.workers.length + 1} 已准备就绪`);
            return;
          }

          // 只有当结果包含 id 且不是特殊类型时，才作为解析结果处理
          if (result.id && !result.type) {
            logger.info(`收到解析结果，任务ID: ${result.id}, 成功: ${result.success}`);
            this.handleWorkerMessage(worker, result as WorkerResult);
          } else if (result.id) {
            logger.info(`收到带ID的特殊消息，类型: ${result.type}, ID: ${result.id}`);
          }
        });

        worker.on('error', (error) => {
          logger.error('Worker 错误:', error);
          this.handleWorkerError(worker, error);
        });

        worker.on('exit', (code) => {
          if (code !== 0) {
            logger.warn(`Worker 异常退出，退出码: ${code}`);
            if (code === 1) {
              logger.error('Worker 退出码为1，可能是脚本路径错误或语法错误');
              logger.error(`Worker 脚本路径: ${this.workerScriptPath}`);
              
              // 检查文件是否存在
              const fs = require('fs');
              if (fs.existsSync(this.workerScriptPath)) {
                logger.error('Worker 文件存在，可能是运行时错误');
              } else {
                logger.error('Worker 文件不存在！');
              }
            }
          } else {
            logger.info('Worker 正常退出');
          }
          this.handleWorkerExit(worker);
        });

        this.workers.push(worker);
        this.availableWorkers.push(worker);
        logger.info(`Worker ${this.workers.length} 创建成功，当前总worker数: ${this.workers.length}`);
        
        // 如果配置了 WASM 路径，发送初始化消息
        if (this.wasmPaths) {
          logger.info(`向 Worker ${this.workers.length} 发送初始化消息`);
          worker.postMessage({
            type: 'init_worker',
            wasmPaths: this.wasmPaths
          });
        }
        
        resolve();
      } catch (error) {
        logger.error('创建 worker 失败:', error);
        reject(error);
      }
    });
  }

  /**
   * 处理 worker 消息
   */
  private handleWorkerMessage(worker: Worker, result: WorkerResult): void {
    logger.info(`收到 Worker 响应，任务ID: ${result.id}, 成功: ${result.success}`);

    // 如果正在关闭，不处理消息
    if (this.isShuttingDown) {
      logger.info('WorkerPool 正在关闭，忽略 worker 消息');
      return;
    }

    // 找到对应的任务并解析
    const taskIndex = this.taskQueue.findIndex(item => {
      return item.task.id === result.id;
    });

    if (taskIndex !== -1) {
      const task = this.taskQueue.splice(taskIndex, 1)[0];
      logger.info(`找到匹配任务 ${result.id}，从队列中移除，剩余任务: ${this.taskQueue.length}`);
      task.resolve(result);
    } else {
      logger.warn(`未找到匹配的任务 ${result.id}，当前队列中有 ${this.taskQueue.length} 个任务`);
    }

    // 只有在非关闭状态下才将 worker 放回可用队列
    if (!this.isShuttingDown) {
      this.availableWorkers.push(worker);
      logger.info(`Worker 归还到可用队列，当前可用 Worker 数: ${this.availableWorkers.length}`);
      // 处理队列中的下一个任务
      this.processNextTask();
    }
  }

  /**
   * 处理 worker 错误
   */
  private handleWorkerError(worker: Worker, error: Error): void {
    // 找到对应的任务并拒绝
    const taskIndex = this.taskQueue.findIndex(item => {
      return true;
    });

    if (taskIndex !== -1) {
      const task = this.taskQueue.splice(taskIndex, 1)[0];
      task.reject(error);
    }

    // 重新创建 worker
    this.recreateWorker(worker);
  }

  /**
   * 处理 worker 退出
   */
  private handleWorkerExit(worker: Worker): void {
    logger.info('处理 worker 退出...');
    
    // 从 worker 列表中移除
    const workerIndex = this.workers.indexOf(worker);
    if (workerIndex !== -1) {
      this.workers.splice(workerIndex, 1);
      logger.info(`从 workers 列表中移除 worker，剩余: ${this.workers.length}`);
    }

    // 从可用 workers 列表中移除（确保只移除一次）
    const availableIndex = this.availableWorkers.indexOf(worker);
    if (availableIndex !== -1) {
      this.availableWorkers.splice(availableIndex, 1);
      logger.info(`从可用 workers 列表中移除 worker，剩余: ${this.availableWorkers.length}`);
    }

    // 如果不是关闭状态，且当前worker数量少于最大限制，才重新创建 worker
    if (!this.isShuttingDown && this.workers.length < this.maxWorkers) {
      logger.info(`WorkerPool 未关闭，当前worker数(${this.workers.length}) < 最大限制(${this.maxWorkers})，尝试重新创建 worker...`);
      this.createWorker().catch(error => {
        logger.error('重新创建 worker 失败:', error);
      });
    } else if (!this.isShuttingDown) {
      logger.info(`WorkerPool 未关闭，但当前worker数(${this.workers.length})已达到最大限制(${this.maxWorkers})，不重新创建worker`);
    } else {
      logger.info('WorkerPool 正在关闭，不重新创建 worker');
    }
  }

  /**
   * 重新创建 worker
   */
  private async recreateWorker(oldWorker: Worker): Promise<void> {
    // 检查是否已达到最大worker数量限制
    if (this.workers.length >= this.maxWorkers) {
      logger.warn(`已达到最大worker数量限制(${this.maxWorkers})，跳过重新创建`);
      return;
    }
    
    try {
      await this.createWorker();
      logger.info('Worker 重新创建成功');
    } catch (error) {
      logger.error('Worker 重新创建失败:', error);
    }
  }

  /**
   * 处理队列中的下一个任务（并发安全）
   */
  private processNextTask(): void {
    // 找到未分配的任务
    const pendingTasks = this.taskQueue.filter(item => !item.assigned);

    // 使用循环处理所有可能的任务，直到没有可用的 Worker 或未分配的任务
    while (pendingTasks.length > 0 && this.availableWorkers.length > 0) {
      const worker = this.availableWorkers.shift()!;
      const taskItem = pendingTasks.shift()!;

      // 标记任务为已分配
      taskItem.assigned = true;

      logger.info(`分配任务 ${taskItem.task.id} 给 Worker，剩余可用 Worker: ${this.availableWorkers.length}`);

      try {
        worker.postMessage({
          type: 'parse_file',
          id: taskItem.task.id,
          filePath: taskItem.task.filePath,
          workspacePath: taskItem.task.workspacePath
        });
      } catch (error) {
        logger.error(`发送任务给 Worker 失败: ${taskItem.task.id}`, error);

        // 从队列中移除失败的任务
        const taskIndex = this.taskQueue.findIndex(item => item.task.id === taskItem.task.id);
        if (taskIndex !== -1) {
          this.taskQueue.splice(taskIndex, 1);
        }

        taskItem.reject(error as Error);
        // 将 Worker 放回可用队列
        this.availableWorkers.push(worker);
      }
    }
  }

  /**
   * 解析单个文件
   */
  async parseFile(filePath: string, workspacePath: string): Promise<WorkerResult> {
    return new Promise((resolve, reject) => {
      const taskId = Math.random().toString(36).substr(2, 9);
      const task: ParseFileTask = { id: taskId, filePath, workspacePath };
      
      this.taskQueue.push({ task, resolve, reject });
      this.processNextTask();
    });
  }

  /**
   * 批量解析文件（流式非阻塞版本）
   */
  async parseFiles(tasks: Array<{ filePath: string; workspacePath: string }>): Promise<WorkerResult[]> {
    logger.info(`开始流式解析 ${tasks.length} 个文件，使用 ${this.workers.length} 个 worker`);

    // 重置进度
    this.totalTasks = tasks.length;
    this.completedTasks = 0;

    return new Promise((resolve, reject) => {
      const results: WorkerResult[] = [];
      let processedCount = 0;
      let currentBatchIndex = 0;
      const batchSize = this.maxWorkers;
      const totalBatches = Math.ceil(tasks.length / batchSize);

      // 流式处理函数
      const processNextBatch = async () => {
        if (currentBatchIndex >= totalBatches) {
          // 所有批次处理完成
          logger.info(`流式解析完成，成功: ${results.filter(r => r.success).length}/${tasks.length}`);
          resolve(results);
          return;
        }

        const startIndex = currentBatchIndex * batchSize;
        const batch = tasks.slice(startIndex, startIndex + batchSize);
        const batchNumber = currentBatchIndex + 1;

        logger.info(`流式处理第 ${batchNumber}/${totalBatches} 批，包含 ${batch.length} 个文件`);

        try {
          // 并发处理当前批次
          const batchPromises = batch.map((task) =>
            this.parseFileDirect(task.filePath, task.workspacePath, path.basename(task.filePath))
          );

          const batchResults = await Promise.allSettled(batchPromises);

          // 处理批次结果
          const batchParsedResults: WorkerResult[] = batchResults.map((result, index) => {
            if (result.status === 'fulfilled') {
              return result.value;
            } else {
              return {
                id: Math.random().toString(36).substr(2, 9),
                success: false,
                filePath: batch[index].filePath,
                error: result.reason?.message || 'Unknown error'
              };
            }
          });

          results.push(...batchParsedResults);
          processedCount += batch.length;
          currentBatchIndex++;

          // 更新进度
          const percentage = Math.round((processedCount / this.totalTasks) * 100);
          logger.info(`批次 ${batchNumber} 完成，进度: ${processedCount}/${this.totalTasks} (${percentage}%)`);

          if (this.onProgress) {
            this.onProgress({
              completed: processedCount,
              total: this.totalTasks,
              percentage
            });
          }

          // 使用 setImmediate 让出控制权，避免阻塞主线程
          setImmediate(() => {
            processNextBatch().catch(reject);
          });

        } catch (error) {
          logger.error(`批次 ${batchNumber} 处理失败:`, error);
          reject(error);
        }
      };

      // 开始流式处理
      setImmediate(() => {
        processNextBatch().catch(reject);
      });
    });
  }

  /**
   * 直接使用 worker 解析文件（不通过任务队列）
   */
  private async parseFileDirect(filePath: string, workspacePath: string, fileName: string): Promise<WorkerResult> {
    return new Promise<WorkerResult>((resolve, reject) => {
      const taskId = Math.random().toString(36).substr(2, 9);
      logger.info(`开始解析文件: ${fileName} (任务ID: ${taskId})`);

      // 将任务添加到队列，让统一的任务处理器分配 Worker
      this.taskQueue.push({
        task: { id: taskId, filePath, workspacePath },
        resolve: (result: WorkerResult) => {
          // 更新进度
          this.completedTasks++;
          const percentage = Math.round((this.completedTasks / this.totalTasks) * 100);

          logger.info(`文件解析完成: ${fileName} (${result.success ? '成功' : '失败'})`);

          if (this.onProgress) {
            this.onProgress({
              completed: this.completedTasks,
              total: this.totalTasks,
              currentFile: fileName,
              percentage
            });
          }

          resolve(result);
        },
        reject: (error: Error) => {
          logger.error(`文件解析错误: ${fileName}`, error);
          reject(error);
        }
      });

      // 尝试处理队列中的任务（包括刚添加的这个）
      this.processNextTask();
    });
  }

  /**
   * 关闭 worker 池
   */
  async shutdown(): Promise<void> {
    logger.info('开始关闭 worker 池...');
    this.isShuttingDown = true;

    // 等待所有任务完成
    let waitCount = 0;
    while (this.taskQueue.length > 0 && waitCount < 50) { // 最多等待5秒
      await new Promise(resolve => setTimeout(resolve, 100));
      waitCount++;
    }

    if (this.taskQueue.length > 0) {
      logger.warn(`关闭时仍有 ${this.taskQueue.length} 个任务未完成`);
      // 拒绝所有未完成的任务
      this.taskQueue.forEach(({ reject }) => {
        reject(new Error('WorkerPool 正在关闭'));
      });
      this.taskQueue = [];
    }

    // 先发送关闭消息给所有worker
    logger.info(`发送关闭消息给 ${this.workers.length} 个 worker...`);
    const shutdownPromises = this.workers.map((worker, index) => {
      return new Promise<void>((resolve) => {
        // 设置超时
        const timeout = setTimeout(() => {
          logger.warn(`Worker ${index} 关闭消息超时，强制终止`);
          resolve();
        }, 2000); // 2秒超时

        // 监听关闭确认
        const messageHandler = (message: any) => {
          if (message.type === 'shutdown_ack') {
            clearTimeout(timeout);
            logger.info(`Worker ${index} 确认关闭`);
            resolve();
          }
        };

        worker.on('message', messageHandler);

        // 发送关闭消息
        try {
          worker.postMessage({ type: 'shutdown' });
        } catch (error) {
          logger.warn(`Worker ${index} 发送关闭消息失败:`, error);
          clearTimeout(timeout);
          resolve();
        }
      });
    });

    await Promise.all(shutdownPromises);

    // 给Worker一些额外时间来处理关闭
    logger.info('等待Worker完成关闭处理...');
    await new Promise(resolve => setTimeout(resolve, 500));

    // 终止所有 worker
    logger.info(`开始终止 ${this.workers.length} 个 worker...`);
    const terminationPromises = this.workers.map((worker, index) => {
      return new Promise<void>((resolve) => {
        // 设置超时
        const timeout = setTimeout(() => {
          logger.warn(`Worker ${index} 终止超时，强制终止`);
          resolve();
        }, 3000); // 3秒超时

        worker.terminate()
          .then(() => {
            clearTimeout(timeout);
            logger.info(`Worker ${index} 正常终止`);
            resolve();
          })
          .catch((error) => {
            clearTimeout(timeout);
            logger.warn(`Worker ${index} 终止时出错:`, error);
            resolve(); // 即使出错也要继续
          });
      });
    });

    await Promise.all(terminationPromises);
    
    // 最终验证和清理
    this.validateWorkerCounts();
    
    this.workers = [];
    this.availableWorkers = [];
    this.taskQueue = [];
    
    logger.info('Worker 池已关闭');
  }

  /**
   * 验证worker数量一致性
   */
  private validateWorkerCounts(): void {
    const totalWorkers = this.workers.length;
    const availableWorkers = this.availableWorkers.length;
    const busyWorkers = totalWorkers - availableWorkers;
    
    // 验证availableWorkers中的worker都在workers列表中
    const invalidWorkers = this.availableWorkers.filter(worker => !this.workers.includes(worker));
    if (invalidWorkers.length > 0) {
      logger.warn(`发现 ${invalidWorkers.length} 个无效的可用worker，正在清理...`);
      this.availableWorkers = this.availableWorkers.filter(worker => this.workers.includes(worker));
    }
    
    logger.info(`Worker数量验证: 总数=${totalWorkers}, 可用=${this.availableWorkers.length}, 忙碌=${busyWorkers}`);
  }

  /**
   * 诊断 WASM 加载问题
   */
  async diagnoseWasmLoading(): Promise<void> {
    logger.info('🔍 开始诊断 WASM 加载问题...');

    if (this.workers.length === 0) {
      logger.warn('⚠️ 没有可用的 Worker 进行诊断');
      return;
    }

    // 选择第一个可用的 Worker 进行诊断
    const worker = this.workers[0];

    return new Promise((resolve) => {
      const timeout = setTimeout(() => {
        logger.warn('⚠️ Worker 诊断超时');
        resolve();
      }, 10000); // 10秒超时

      const messageHandler = (message: any) => {
        if (message.type?.startsWith('diagnose_')) {
          logger.info(`📊 [诊断结果] ${message.type}:`, message);

          if (message.type === 'diagnose_language_loader') {
            clearTimeout(timeout);
            worker.off('message', messageHandler);
            resolve();
          }
        }
      };

      worker.on('message', messageHandler);

      // 发送诊断消息
      worker.postMessage({
        type: 'diagnose_wasm',
        wasmPaths: this.wasmPaths
      });
    });
  }

  /**
   * 获取池状态
   */
  getStatus() {
    // 验证worker数量一致性
    this.validateWorkerCounts();

    const status = {
      maxWorkers: this.maxWorkers,
      totalWorkers: this.workers.length,
      availableWorkers: this.availableWorkers.length,
      busyWorkers: this.workers.length - this.availableWorkers.length,
      queuedTasks: this.taskQueue.length,
      isShuttingDown: this.isShuttingDown,
      workerLimitReached: this.workers.length >= this.maxWorkers
    };

    logger.info(`WorkerPool 状态: 最大worker数=${status.maxWorkers}, 总worker数=${status.totalWorkers}, 可用worker数=${status.availableWorkers}, 忙碌worker数=${status.busyWorkers}, 队列任务数=${status.queuedTasks}, 是否关闭中=${status.isShuttingDown}, 是否达到限制=${status.workerLimitReached}`);

    return status;
  }
} 