import fs from 'fs';
import path from 'path';

export class ProjectScanner {
  constructor(private rootDir: string, private languageExts: string[]) {}

  scanFiles(): string[] {
    const result: string[] = [];
    this.walkDir(this.rootDir, result);
    return result;
  }

  private walkDir(dir: string, result: string[]) {
    const entries = fs.readdirSync(dir, { withFileTypes: true });
    for (const entry of entries) {
      const fullPath = path.join(dir, entry.name);
      if (entry.isDirectory()) {
        this.walkDir(fullPath, result);
      } else if (this.languageExts.some(ext => fullPath.endsWith(ext))) {
        result.push(fullPath);
      }
    }
  }
}
