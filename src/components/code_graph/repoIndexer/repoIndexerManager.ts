import * as fs from 'fs/promises';
import * as path from 'path';
import { performance } from 'perf_hooks';
import * as vscode from 'vscode';
import { Logger } from '../../../common/log/logger';
import { RepoIndexer } from './repoIndexer';

const logger = new Logger('RepoIndexerManager');

export type IndexingStatus = 'idle' | 'indexing' | 'success' | 'error';

export class RepoIndexerManager {
  private static instance: RepoIndexerManager;
  private repoIndexer: RepoIndexer | null = null;
  private context: vscode.ExtensionContext | null = null;
  private indexingPromise: Promise<void> | null = null;
  private lastIndexTime: number = 0;
  private readonly CONFIG_FILE = '.vscode/code-partner.json';
  private readonly STORAGE_KEY = 'repoIndexerData';
  private readonly LAST_INDEX_TIME_KEY = 'lastIndexTime';
  private readonly INCREMENTAL_UPDATE_INTERVAL = 5 * 60 * 1000; // 5分钟（用于测试，生产环境可以调整）
  private _statusEmitter = new vscode.EventEmitter<IndexingStatus>();
  private _currentStatus: IndexingStatus = 'idle';

  private constructor() { }

  public static getInstance(): RepoIndexerManager {
    if (!RepoIndexerManager.instance) {
      RepoIndexerManager.instance = new RepoIndexerManager();
    }
    return RepoIndexerManager.instance;
  }

  /**
   * 初始化 RepoIndexerManager
   * @param context VSCode 扩展上下文
   */
  public async initialize(context: vscode.ExtensionContext, autoInitIndex: boolean = true): Promise<void> {
    logger.info('=== 开始初始化RepoIndexerManager ===', { toOutput: true });
    logger.info('开始初始化RepoIndexerManager', { toOutput: true });

    this.context = context;
    logger.info('1. 设置context完成', { toOutput: true });

    // 恢复上次索引时间
    logger.info('2. 恢复上次索引时间...', { toOutput: true });
    this.restoreLastIndexTime();
    logger.info(`上次索引时间: ${this.lastIndexTime > 0 ? new Date(this.lastIndexTime).toLocaleString() : '从未索引'}`, { toOutput: true });

    // 注册文件监听器
    logger.info('3. 注册文件监听器...', { toOutput: true });
    this.registerFileWatcher(context);
    logger.info('文件监听器注册完成', { toOutput: true });

    // 根据参数决定是否自动初始化索引
    if (autoInitIndex) {
      logger.info('4. 开始自动初始化索引...', { toOutput: true });
      try {
        await this.autoInitializeIndex();
      } catch (error) {
        logger.error('自动初始化索引失败:', error as Error, { toOutput: true });
        // 不抛出错误，让初始化继续完成
      }
    } else {
      logger.info('4. 跳过自动初始化索引（手动控制模式）', { toOutput: true });
    }

    logger.info('仓库索引管理器初始化完成', { toOutput: true });
    logger.info('=== RepoIndexerManager初始化完成 ===', { toOutput: true });
  }

  /**
   * 自动初始化索引（插件加载时自动调用）
   * 1. 如果有配置文件
   *    - 按照配置文件，选择是否加载索引
   *    - 如果配置文件决定加载，而索引不存在，则执行全量索引构建
   * 2. 如果没有配置文件
   *    - 创建配置文件
   *    - 执行全量索引
   */
  private async autoInitializeIndex(): Promise<void> {
    try {
      logger.info('开始自动初始化索引...', { toOutput: true });

      // 检查工作区
      const workspaceFolders = vscode.workspace.workspaceFolders;
      if (!workspaceFolders) {
        logger.warn('未找到工作区，跳过自动索引初始化', { toOutput: true });
        return;
      }

      const workspacePath = workspaceFolders[0].uri.fsPath;
      logger.info(`工作区路径: ${workspacePath}`, { toOutput: true });
      const configPath = vscode.Uri.file(path.join(workspacePath, this.CONFIG_FILE));

      // 检查配置文件是否存在
      try {
        const configContent = await vscode.workspace.fs.readFile(configPath);
        const config = JSON.parse(configContent.toString());

        if (config.enableIndexing) {
          logger.info('检测到enableIndexing=true，启用代码仓索引...', { toOutput: true });

          // 无论 autoIndexing 是否为 true，都尝试加载持久化索引
            const savedData = this.context?.globalState.get(this.STORAGE_KEY);
            if (savedData) {
              logger.info('发现已保存的索引数据，启动异步恢复...', { toOutput: true });
              // 异步恢复索引数据，不阻塞初始化
              this.loadFromFile(savedData as string).then(() => {
                logger.info('索引数据恢复成功', { toOutput: true });
                this._currentStatus = 'success';
                this._statusEmitter.fire('success');
              }).catch(error => {
                logger.error('索引数据恢复失败:', error, { toOutput: true });
                this._currentStatus = 'error';
                this._statusEmitter.fire('error');
              });
            } else {
            // 没有持久化索引，根据 autoIndexing 配置决定是否自动构建
            if (config.autoIndexing) {
              logger.info('检测到autoIndexing=true，未找到持久化索引，启动异步索引构建...', { toOutput: true });
              // 异步启动索引，不阻塞初始化
              this.initializeAndAnalyze(workspacePath, config.languageExts || ['*']).catch(error => {
                logger.error('异步索引构建失败:', error, { toOutput: true });
              });
            } else {
              logger.info('autoIndexing=false，未找到持久化索引，跳过自动索引构建（等待用户手动操作）', { toOutput: true });
            }
          }
        } else {
          logger.info('索引功能已禁用 (enableIndexing=false)', { toOutput: true });
        }
      } catch (error) {
        logger.info('未找到配置文件，创建默认配置文件并启动异步索引...', { toOutput: true });
        // 没有配置文件，创建默认配置并异步启动索引
        await this.createDefaultConfig(workspacePath, ['*']);
        // 异步启动索引，不阻塞初始化
        this.initializeAndAnalyze(workspacePath, ['*']).catch(error => {
          logger.error('异步索引构建失败:', error, { toOutput: true });
        });
      }
    } catch (error) {
      logger.error('自动初始化索引失败:', error, { toOutput: true });
      throw error;
      }
  }


  /**
   * 初始化并分析代码仓（异步非阻塞）
   */
  private async initializeAndAnalyze(workspacePath: string, languageExts: string[] = ['*']): Promise<void> {
    if (!this.context) {
      throw new Error('RepoIndexerManager 未初始化');
    }

    // 先创建 RepoIndexer，但不立即开始分析
    this.repoIndexer = await this.createRepoIndexer(workspacePath, languageExts);

    // 设置进度回调 - 强制输出到 VS Code 输出通道
    this.repoIndexer.setProgressCallback((progress) => {
      logger.info(`索引进度: ${progress.percentage}% (${progress.completed}/${progress.total})`, { toOutput: true });
      if (progress.currentFile) {
        logger.info(`当前处理文件: ${progress.currentFile}`, { toOutput: true });
      }
    });

    // 异步启动索引分析，不阻塞初始化
    this.startAsyncIndexing();
  }

  /**
   * 异步启动索引分析（不阻塞初始化）
   */
  private startAsyncIndexing(): void {
    if (!this.repoIndexer) {
      logger.error('RepoIndexer 未初始化，无法启动索引', { toOutput: true });
      return;
    }

    // 设置状态为正在索引
    this._currentStatus = 'indexing';
    this._statusEmitter.fire('indexing');

    // 异步执行索引，不阻塞主线程
    setTimeout(async () => {
      try {
        logger.info('🚀 开始异步索引分析...', { toOutput: true });
        await this.repoIndexer!.analyze(false);

        // 使用统一的存储方法保存分析结果
        await this.saveCurrentIndexData();

        this._currentStatus = 'success';
        this._statusEmitter.fire('success');
        logger.info('✅ 异步索引分析完成', { toOutput: true });
      } catch (error) {
        this._currentStatus = 'error';
        this._statusEmitter.fire('error');
        logger.error('❌ 异步索引分析失败:', error, { toOutput: true });
      }
    }, 100); // 100ms 延迟，确保初始化完成后再开始索引
  }

  /**
   * 触发索引更新
   */
  async triggerIndexing(force: boolean = false): Promise<void> {
    if (!this.repoIndexer) {
      throw new Error('仓库索引器未初始化');
    }

    // 如果已经有正在进行的索引任务，直接返回该Promise
    if (this.indexingPromise) {
      logger.info('索引任务正在进行中，跳过本次触发');
      return this.indexingPromise;
    }

    const currentTime = Date.now();
    
    // 改进增量索引判断逻辑
    // 1. 如果是强制模式，执行全量索引
    // 2. 如果从未索引过（lastIndexTime为0），执行全量索引
    // 3. 如果距离上次索引超过阈值，执行全量索引
    // 4. 否则执行增量索引
    const shouldIncremental = !force && 
      this.lastIndexTime > 0 && 
      (currentTime - this.lastIndexTime) <= this.INCREMENTAL_UPDATE_INTERVAL;

    const startTime = performance.now();
    this._currentStatus = 'indexing';
    this._statusEmitter.fire('indexing');
    
    // 详细的入口日志 - 强制输出到 VS Code 输出通道
    if (shouldIncremental) {
      logger.info('🚀 开始增量索引...', { toOutput: true });
      logger.info(`📊 索引统计: 上次索引时间 ${new Date(this.lastIndexTime).toLocaleTimeString()}, 间隔 ${Math.round((currentTime - this.lastIndexTime) / 1000)}秒`, { toOutput: true });
      vscode.window.showInformationMessage('🔄 正在执行增量索引，请稍候...');
    } else {
      logger.info('🚀 开始全量索引...', { toOutput: true });
      if (force) {
        logger.info('📋 强制全量索引模式', { toOutput: true });
        vscode.window.showInformationMessage('🔄 正在执行全量索引，请稍候...');
      } else if (this.lastIndexTime === 0) {
        logger.info('📋 首次索引，执行全量索引', { toOutput: true });
        vscode.window.showInformationMessage('🔄 正在执行全量索引，请稍候...');
      } else {
        logger.info('📋 间隔超过阈值，执行全量索引', { toOutput: true });
        vscode.window.showInformationMessage('🔄 正在执行全量索引，请稍候...');
      }
    }

    try {
      this.indexingPromise = this.repoIndexer.analyze(shouldIncremental);
      await this.indexingPromise;

      // 保存索引结果
      await this.saveCurrentIndexData();

      this.lastIndexTime = currentTime;
      // 保存索引时间到持久化存储
      this.saveLastIndexTime();
      
      const endTime = performance.now();
      const duration = Math.round(endTime - startTime);
      
      // 详细的完成日志 - 强制输出到 VS Code 输出通道
      if (shouldIncremental) {
        logger.info(`✅ 增量索引完成，耗时: ${duration}ms`, { toOutput: true });
        vscode.window.showInformationMessage(`✅ 增量索引完成，耗时 ${duration}ms`);
      } else {
        logger.info(`✅ 全量索引完成，耗时: ${duration}ms`, { toOutput: true });
        vscode.window.showInformationMessage(`✅ 全量索引完成，耗时 ${duration}ms`);
      }
      
      this._currentStatus = 'success';
      this._statusEmitter.fire('success');
    } catch (error) {
      this._currentStatus = 'error';
      this._statusEmitter.fire('error');
      logger.error('❌ 索引失败', error);
      vscode.window.showErrorMessage('❌ 索引失败，请查看日志了解详情');
      throw error;
    } finally {
      this.indexingPromise = null;
    }
  }

  /**
   * 恢复上次索引时间
   */
  private restoreLastIndexTime(): void {
    if (!this.context) {
      return;
    }
    
    try {
      const savedTime = this.context.globalState.get(this.LAST_INDEX_TIME_KEY) as number;
      if (savedTime && typeof savedTime === 'number') {
        this.lastIndexTime = savedTime;
        logger.info(`恢复上次索引时间: ${new Date(savedTime).toLocaleString()}`);
      } else {
        logger.info('未找到保存的上次索引时间，使用默认值0');
      }
    } catch (error) {
      logger.warn('恢复上次索引时间失败:', error);
    }
  }

  /**
   * 保存当前索引时间
   */
  private saveLastIndexTime(): void {
    if (!this.context) {
      return;
    }
    
    try {
      this.context.globalState.update(this.LAST_INDEX_TIME_KEY, this.lastIndexTime);
      logger.info(`保存索引时间: ${new Date(this.lastIndexTime).toLocaleString()}`);
    } catch (error) {
      logger.warn('保存索引时间失败:', error);
    }
  }

  /**
   * 注册文件变更监听器
   */
  private registerFileWatcher(context: vscode.ExtensionContext): void {
    // 只监听支持的文件类型，避免监听所有文件
    const supportedPatterns = [
      '**/*.c',
      '**/*.h', 
      '**/*.asm',
      '**/*.cpp',
      '**/*.cc',
      '**/*.cxx',
      '**/*.hpp',
      '**/*.hxx'
    ];
    
    // 修复文件监听器模式 - 使用正确的glob模式
    const globPattern = '**/*.{c,h,asm,cpp,cc,cxx,hpp,hxx}';
    logger.info(`注册文件监听器，模式: ${globPattern}`);
    const fileWatcher = vscode.workspace.createFileSystemWatcher(globPattern);

    // 监听文件创建
    fileWatcher.onDidCreate(async uri => {
      try {
        logger.info(`🔔 检测到文件创建: ${uri.fsPath}`);
        await this.handleFileChange(uri);
      } catch (error) {
        logger.error(`处理文件创建事件失败: ${uri.fsPath}`, error);
      }
    });

    // 监听文件变更
    fileWatcher.onDidChange(async uri => {
      try {
        logger.info(`🔔 检测到文件变更: ${uri.fsPath}`);
        await this.handleFileChange(uri);
      } catch (error) {
        logger.error(`处理文件变更事件失败: ${uri.fsPath}`, error);
      }
    });

    // 监听文件删除
    fileWatcher.onDidDelete(async uri => {
      try {
        logger.info(`🔔 检测到文件删除: ${uri.fsPath}`);
        await this.handleFileChange(uri);
      } catch (error) {
        logger.error(`处理文件删除事件失败: ${uri.fsPath}`, error);
      }
    });

    context.subscriptions.push(fileWatcher);
  }

  /**
   * 处理文件变更
   */
  private async handleFileChange(uri: vscode.Uri): Promise<void> {
    logger.info(`📝 开始处理文件变更: ${uri.fsPath}`);
    
    // 检查自动索引配置
    if (!this.context) {
      logger.warn('❌ context未初始化，跳过文件变更处理');
      return;
    }

    const workspaceFolders = vscode.workspace.workspaceFolders;
    if (!workspaceFolders) {
      logger.warn('❌ 未找到工作区，跳过文件变更处理');
      return;
    }

    const workspacePath = workspaceFolders[0].uri.fsPath;
    const configPath = vscode.Uri.file(path.join(workspacePath, this.CONFIG_FILE));

    // 检查配置文件并获取配置
    let enableIndexing = true;
    let autoIndexing = true;
    
    try {
      const configContent = await vscode.workspace.fs.readFile(configPath);
      const config = JSON.parse(configContent.toString());
      enableIndexing = config.enableIndexing !== false; // 默认为true
      autoIndexing = config.autoIndexing !== false; // 默认为true
      logger.info(`📋 配置文件读取成功: enableIndexing=${enableIndexing}, autoIndexing=${autoIndexing}`);
    } catch (error) {
      logger.info('📋 无法读取配置文件，使用默认配置（启用索引和自动索引）');
      // 如果配置文件不存在，使用默认配置
      enableIndexing = true;
      autoIndexing = true;
    }

    // 只有在启用索引且启用自动索引时才处理文件变更
    if (!enableIndexing || !autoIndexing) {
      logger.info('❌ 自动索引已禁用，跳过文件变更处理');
      return;
    }

    // 检查文件是否在支持的文件类型中
    const supportedExtensions = ['.c', '.h', '.asm', '.cpp', '.cc', '.cxx', '.hpp', '.hxx'];
    const fileExtension = path.extname(uri.fsPath).toLowerCase();
    logger.info(`📄 文件扩展名: ${fileExtension}, 支持的文件类型: ${supportedExtensions.join(', ')}`);
    
    if (!supportedExtensions.includes(fileExtension)) {
      logger.info(`❌ 文件类型不支持: ${fileExtension}，跳过索引`);
      return;
    }
    logger.info(`✅ 文件类型支持: ${fileExtension}`);

    // 如果文件变更间隔小于增量更新间隔，则延迟触发增量更新
    const currentTime = Date.now();
    const timeSinceLastIndex = currentTime - this.lastIndexTime;
    logger.info(`⏰ 时间检查: 当前时间=${new Date(currentTime).toLocaleTimeString()}, 上次索引时间=${this.lastIndexTime > 0 ? new Date(this.lastIndexTime).toLocaleTimeString() : '从未'}, 间隔=${Math.round(timeSinceLastIndex/1000)}秒, 阈值=${this.INCREMENTAL_UPDATE_INTERVAL/1000}秒`);
    
    if (timeSinceLastIndex < this.INCREMENTAL_UPDATE_INTERVAL) {
      const remainingTime = Math.round((this.INCREMENTAL_UPDATE_INTERVAL - timeSinceLastIndex) / 1000);
      logger.info(`⏰ 文件变更检测: ${path.basename(uri.fsPath)}, 距离下次索引还有 ${remainingTime} 秒`);
      return;
    }

    // 检查是否有索引器，如果没有则跳过（避免在初始化前触发索引）
    if (!this.repoIndexer) {
      logger.debug('索引器未初始化，跳过文件变更处理');
      return;
    }

    logger.info(`📝 检测到文件变更: ${path.basename(uri.fsPath)}`);
    logger.info(`🔄 准备触发增量索引...`);

    try {
      // 强制使用增量索引，避免全量索引
      logger.info(`🚀 调用 triggerIndexing(false) 开始增量索引`);
      await this.triggerIndexing(false);
      logger.info(`✅ 增量索引调用完成`);
    } catch (error) {
      logger.error(`❌ 处理文件变更失败: ${uri.fsPath}`, error);
    }
  }

  /**
   * 创建新的 RepoIndexer 实例并初始化
   * @param workspacePath 仓库路径
   * @param languageExts 支持的语言扩展名数组
   */
  public async createRepoIndexer(workspacePath: string, languageExts: string[] = ['*']): Promise<RepoIndexer> {
    this.repoIndexer = new RepoIndexer(workspacePath, languageExts);
    
    // 设置 WASM 路径 - 使用分离构建的 Worker 专用路径
    if (this.context) {
      const extensionPath = this.context.extensionPath;
      logger.info(`插件根目录: ${extensionPath}`);

      // 构造 WASM 文件的绝对路径 - 主进程使用 node_modules 中的 WASM
      const wasmPaths = {
        treeSitterC: path.join(extensionPath, 'dist/node_modules/tree-sitter-c/tree-sitter-c.wasm'),
        treeSitterNp: path.join(extensionPath, 'dist/node_modules/tree-sitter-np/tree-sitter-np.wasm')
      };
      
      // 验证文件是否存在
      const fs = require('fs');
      if (fs.existsSync(wasmPaths.treeSitterC)) {
        logger.info(`✅ C WASM 文件存在: ${wasmPaths.treeSitterC}`);
      } else {
        logger.warn(`⚠️ C WASM 文件不存在: ${wasmPaths.treeSitterC}`);
      }
      
      if (fs.existsSync(wasmPaths.treeSitterNp)) {
        logger.info(`✅ NP WASM 文件存在: ${wasmPaths.treeSitterNp}`);
      } else {
        logger.warn(`⚠️ NP WASM 文件不存在: ${wasmPaths.treeSitterNp}`);
      }
      
      this.repoIndexer.setWasmPaths(wasmPaths);
      logger.info(`设置 WASM 路径: C=${wasmPaths.treeSitterC}, NP=${wasmPaths.treeSitterNp}`);
    } else {
      logger.warn('⚠️ ExtensionContext 未设置，无法获取插件根目录');
    }
    
    return this.repoIndexer;
  }

  public getRepoIndexer(): RepoIndexer | null {
    return this.repoIndexer;
  }

  public hasRepoIndexer(): boolean {
    return this.repoIndexer !== null;
  }

  /**
   * 获取索引状态信息
   */
  public getIndexStatus(): {
    initialized: boolean;
    hasRepoIndexer: boolean;
    hasContext: boolean;
    lastIndexTime: number;
    isIndexing: boolean;
  } {
    return {
      initialized: this.context !== null,
      hasRepoIndexer: this.repoIndexer !== null,
      hasContext: this.context !== null,
      lastIndexTime: this.lastIndexTime,
      isIndexing: this.indexingPromise !== null
    };
  }

  public onIndexingStatusChange(listener: (status: IndexingStatus) => any, thisArgs?: any): vscode.Disposable {
    return this._statusEmitter.event(listener, thisArgs);
  }
  public getCurrentIndexingStatus(): IndexingStatus {
    return this._currentStatus;
  }

  /**
   * 强制重新初始化索引
   */
  /**
   * 重置索引（用户手动触发）
   * 1. 如果有索引，删除重建
   * 2. 如果没有索引，直接创建（包括配置文件）
   */
  public async resetIndex(): Promise<void> {
    if (!this.context) {
      throw new Error('RepoIndexerManager 未初始化');
    }

    logger.info('开始重置索引...');

    // 检查工作区
    const workspaceFolders = vscode.workspace.workspaceFolders;
    if (!workspaceFolders) {
      throw new Error('未找到工作区');
    }

    const workspacePath = workspaceFolders[0].uri.fsPath;

    // 如果有索引，先清理现有索引数据
    if (this.repoIndexer || this.context.globalState.get(this.STORAGE_KEY)) {
      logger.info('检测到现有索引，清理索引数据...');
      await this.cleanExistingIndexData();
    }

    // 检查配置文件是否存在
    const configPath = vscode.Uri.file(path.join(workspacePath, this.CONFIG_FILE));
    try {
      const configContent = await vscode.workspace.fs.readFile(configPath);
      const config = JSON.parse(configContent.toString());

      if (config.enableIndexing) {
        logger.info('使用现有配置文件重新构建索引...');
        await this.initializeAndAnalyze(workspacePath, config.languageExts || ['*']);
      } else {
        logger.info('配置文件禁用索引，启用索引并重新构建...');
        // 更新配置文件启用索引
        config.enableIndexing = true;
        await vscode.workspace.fs.writeFile(configPath, Buffer.from(JSON.stringify(config, null, 2)));
        await this.initializeAndAnalyze(workspacePath, config.languageExts || ['*']);
      }
    } catch (error) {
      logger.info('配置文件不存在，创建默认配置文件并构建索引...');
      // 没有配置文件，创建默认配置并执行全量索引
      await this.createDefaultConfig(workspacePath, ['*']);
      await this.initializeAndAnalyze(workspacePath, ['*']);
    }

    logger.info('索引重置完成');
  }

  /**
   * 保存当前 RepoIndexer 的数据到指定文件路径（JSON格式）
   * @param filePath 保存文件路径
   */
  public async saveToFile(filePath: string): Promise<void> {
    if (!this.repoIndexer) {
      throw new Error('RepoIndexer 未初始化');
    }

    // 确保目录存在
    const dir = path.dirname(filePath);
    try {
      await fs.mkdir(dir, { recursive: true });
    } catch (error) {
      logger.warn(`创建目录失败: ${dir}`, error);
    }

    const data = this.repoIndexer.exportData();
    const json = JSON.stringify(data, null, 2);
    await fs.writeFile(filePath, json, 'utf-8');
  }

  /**
   * 保存当前索引数据到统一存储
   */
  public async saveCurrentIndexData(): Promise<void> {
    if (!this.repoIndexer || !this.context) {
      throw new Error('RepoIndexer 或 Context 未初始化');
    }

    const data = this.repoIndexer.exportData();
    const savePath = vscode.Uri.file(path.join(this.context.globalStorageUri.fsPath, 'repoIndexerData.json'));

    // 确保存储目录存在
    try {
      await fs.mkdir(this.context.globalStorageUri.fsPath, { recursive: true });
    } catch (error) {
      logger.warn(`创建存储目录失败: ${this.context.globalStorageUri.fsPath}`, error);
    }

    await this.saveToFile(savePath.fsPath);
    await this.context.globalState.update(this.STORAGE_KEY, savePath.fsPath);
    logger.info('索引数据已保存到统一存储');
  }

  /**
   * 从文件加载 RepoIndexer 数据，并恢复实例状态
   * @param filePath 文件路径
   */
  public async loadFromFile(filePath: string): Promise<RepoIndexer> {
    const content = await fs.readFile(filePath, 'utf-8');
    const data = JSON.parse(content);

    const workspacePath = data.workspacePath || '';
    const languageExts = data.languageExts || ['*'];

    this.repoIndexer = new RepoIndexer(workspacePath, languageExts);
    await this.repoIndexer.restoreFromData(data);
    return this.repoIndexer;
  }

  public setRepoIndexer(indexer: RepoIndexer): void {
    this.repoIndexer = indexer;
  }

  /**
   * 清理资源
   */
  dispose(): void {
    if (this.repoIndexer) {
      // TODO: 实现资源清理逻辑
      this.repoIndexer = null;
    }
  }

  /**
   * 清理现有索引数据
   */
  public async cleanExistingIndexData(): Promise<void> {
    try {
      logger.info('=== 开始清理现有索引数据 ===');
      logger.info('=== 开始清理现有索引数据 ===');

      // 清理内存中的索引器
      if (this.repoIndexer) {
        logger.info('1. 清理内存中的索引器...');
        logger.info('清理前的RepoIndexer状态:', {
          hasIndexer: !!this.repoIndexer,
          indexerType: this.repoIndexer.constructor.name,
          workspacePath: this.repoIndexer.workspacePath
        });
        this.repoIndexer = null;
        logger.info('内存中的索引器已清理');
        logger.info('清理后的RepoIndexer状态:', {
          hasIndexer: !!this.repoIndexer
        });
      } else {
        logger.info('1. 内存中没有索引器，跳过清理');
        logger.info('内存中没有索引器，跳过清理');
      }

      // 先获取存储的索引文件路径，再清理globalState
      let savedDataPath: string | undefined;
      try {
        logger.info('2. 获取存储的索引文件路径...');
        savedDataPath = this.context?.globalState.get(this.STORAGE_KEY) as string;
        if (savedDataPath) {
          logger.info(`找到存储的索引文件路径: ${savedDataPath}`);
          logger.info(`找到存储的索引文件路径: ${savedDataPath}`);
        } else {
          logger.info('未找到存储的索引文件路径');
          logger.info('未找到存储的索引文件路径');
        }
      } catch (error) {
        logger.warn('获取存储的索引文件路径失败:', error);
        logger.warn('获取存储的索引文件路径失败:', error);
      }

      // 清理存储的索引数据
      logger.info('3. 清理globalState中的索引数据...');
      await this.context?.globalState.update(this.STORAGE_KEY, undefined);
      logger.info('globalState中的索引数据已清理');
      logger.info('globalState中的索引数据已清理');

      // 清理持久化的索引文件
      if (savedDataPath && typeof savedDataPath === 'string') {
        try {
          logger.info(`4. 删除索引文件: ${savedDataPath}`);
          logger.info(`4. 删除索引文件: ${savedDataPath}`);
          await fs.unlink(savedDataPath);
          logger.info(`索引文件已删除: ${savedDataPath}`);
          logger.info(`索引文件已删除: ${savedDataPath}`);
      } catch (error) {
          logger.warn('删除索引文件失败:', error);
          logger.warn('删除索引文件失败:', error);
      }
      } else {
        logger.info('4. 没有索引文件需要删除');
        logger.info('4. 没有索引文件需要删除');
      }

      // 清理工作区配置文件（注意：这里不删除配置文件，只清理索引数据）
      // 配置文件应该保留，因为用户可能只是想清理索引数据而不是配置

      // 重置状态
      logger.info('5. 重置索引状态...');
      this.lastIndexTime = 0;
      // 清理持久化的索引时间
      await this.context?.globalState.update(this.LAST_INDEX_TIME_KEY, undefined);
      this._currentStatus = 'idle';
      this._statusEmitter.fire('idle');
      logger.info('索引状态已重置');
      logger.info('索引状态已重置');

      // 验证清理结果
      logger.info('6. 验证清理结果...');
      const finalIndexer = this.getRepoIndexer();
      const finalStatus = this.getIndexStatus();
      logger.info('清理后的最终状态:', {
        hasIndexer: !!finalIndexer,
        indexStatus: finalStatus
      });

      logger.info('=== 索引数据清理完成 ===');
      logger.info('=== 索引数据清理完成 ===');
    } catch (error) {
      logger.error('清理索引数据失败:', error);
      logger.error('清理索引数据失败:', error);
      throw error;
    }
  }



  /**
   * 加载代码仓索引（命令入口）
   */
  public async loadRepoIndex(context: vscode.ExtensionContext): Promise<void> {
    try {
      this.initialize(context);
      const startTime = Date.now();
      await this.triggerIndexing(false);
      const endTime = Date.now();
      logger.info(`代码仓索引加载完成，耗时: ${endTime - startTime}ms`);
    } catch (error) {
      logger.error('代码仓索引加载失败', error);
      throw error;
    }
  }

  /**
   * 索引工作区（命令入口）
   */
  public async indexWorkspace(context: vscode.ExtensionContext): Promise<void> {
    try {
      this.initialize(context);
      const workspaceFolders = vscode.workspace.workspaceFolders;
      if (!workspaceFolders) {
        throw new Error('未找到工作区');
      }
      const workspacePath = workspaceFolders[0].uri.fsPath;
      // 先初始化并分析，确保 repoIndexer 已初始化
      await this.initializeAndAnalyze(workspacePath, ['*']);
      const startTime = Date.now();
      await this.triggerIndexing();
      const endTime = Date.now();
      logger.info(`代码仓索引完成，耗时: ${endTime - startTime}ms`);
    } catch (error) {
      logger.error('代码仓索引失败', error);
      throw error;
    }
  }

  /**
   * 清理索引数据（命令入口）
   */
  public async cleanIndexData(context: vscode.ExtensionContext): Promise<void> {
    logger.info('=== 开始执行清理索引数据命令 ===');
    logger.info('=== 开始执行清理索引数据命令 ===');

    try {
      logger.info('1. 设置context（不触发自动初始化）...');
      // 只设置context，不调用initialize避免触发自动恢复
      this.context = context;

      const startTime = Date.now();
      logger.info('2. 开始清理现有索引数据...');
      await this.cleanExistingIndexData();
      const endTime = Date.now();

      logger.info(`3. 索引数据清理完成，耗时: ${endTime - startTime}ms`);
      logger.info(`索引数据清理完成，耗时: ${endTime - startTime}ms`);
    } catch (error) {
      logger.error('索引数据清理失败', error);
      logger.error('索引数据清理失败:', error);
      throw error;
    }
  }

  /**
   * 增量更新索引：只处理新增、删除、变更的文件
   */
  public async updateIndexForFiles(added: string[], removed: string[], changed: string[]): Promise<void> {
    try {
      logger.info(`🔄 开始增量更新索引...`);
      logger.info(`📊 文件变更统计: 新增 ${added.length} 个，删除 ${removed.length} 个，变更 ${changed.length} 个`);
      
      if (added.length > 0) {
        logger.info(`📁 新增文件: ${added.map(f => path.basename(f)).join(', ')}`);
      }
      if (removed.length > 0) {
        logger.info(`🗑️ 删除文件: ${removed.map(f => path.basename(f)).join(', ')}`);
      }
      if (changed.length > 0) {
        logger.info(`✏️ 变更文件: ${changed.map(f => path.basename(f)).join(', ')}`);
      }
      
      if (!this.repoIndexer) {
        logger.warn('⚠️ RepoIndexer未初始化，自动执行全量索引');
        vscode.window.showInformationMessage('⚠️ 索引器未初始化，正在执行全量索引...');
        await this.triggerIndexing();
        return;
      }

      // 直接调用 RepoIndexer 的增量分析方法，避免重复逻辑
      await this.repoIndexer.analyze(true); // incremental = true
      logger.info('✅ 增量索引完成');
      vscode.window.showInformationMessage('✅ 增量索引完成');
    } catch (error) {
      logger.error('❌ 增量索引失败', error);
      vscode.window.showErrorMessage('❌ 增量索引失败，请查看日志了解详情');
      throw error;
    }
  }

  /**
   * 创建默认配置文件
   * @param workspacePath 工作区路径
   * @param languageExts 支持的语言扩展名数组
   */
  private async createDefaultConfig(workspacePath: string, languageExts: string[] = ['*']): Promise<void> {
    const configDir = path.join(workspacePath, '.vscode');
    const configPath = path.join(configDir, 'code-partner.json');

    // 确保.vscode目录存在
    try {
      await fs.mkdir(configDir, { recursive: true });
    } catch (error) {
      logger.warn('创建.vscode目录失败:', error);
    }

    // 创建默认配置
    const defaultConfig = {
      enableIndexing: true,
      autoIndexing: true,
      languageExts: languageExts,
      rules: [
        {
          type: 'include',
          pattern: `**/*.{${languageExts.join(',')}}`
        }
      ]
    };

    try {
      await fs.writeFile(configPath, JSON.stringify(defaultConfig, null, 2), 'utf-8');
      logger.info(`配置文件已创建: ${configPath}`);
    } catch (error) {
      logger.error('创建配置文件失败:', error);
      throw error;
    }
  }

  /**
   * 手动初始化工作区索引（命令入口）
   * 1. 检查是否已有索引，如果有则跳过
   * 2. 创建配置文件（如不存在）
   * 3. 执行索引工作
   */
  public async initializeWorkspaceIndex(context: vscode.ExtensionContext): Promise<void> {
    try {
      this.initialize(context);

      const workspaceFolders = vscode.workspace.workspaceFolders;
      if (!workspaceFolders) {
        throw new Error('未找到工作区');
      }

      const workspacePath = workspaceFolders[0].uri.fsPath;
      logger.info(`开始初始化工作区索引: ${workspacePath}`);

      // 检查是否已有索引（内存中的索引器或存储的索引数据）
      const hasMemoryIndex = this.repoIndexer && this.hasRepoIndexer();
      const hasStoredIndex = this.context?.globalState.get(this.STORAGE_KEY);

      if (hasMemoryIndex || hasStoredIndex) {
        logger.info('检测到已有索引存在，跳过初始化。如需重新索引，请使用"重置索引"命令');
        return;
      }

      // 1. 创建配置文件（如果不存在）
      const configPath = vscode.Uri.file(path.join(workspacePath, this.CONFIG_FILE));
      try {
        await vscode.workspace.fs.readFile(configPath);
        logger.info('配置文件已存在，跳过创建');
      } catch (error) {
        logger.info('配置文件不存在，创建默认配置');
        await this.createDefaultConfig(workspacePath, ['*']);
      }

      // 2. 执行索引工作
      await this.initializeAndAnalyze(workspacePath, ['*']);

      logger.info('工作区索引初始化完成');
    } catch (error) {
      logger.error('工作区索引初始化失败', error);
      throw error;
    }
  }
}

