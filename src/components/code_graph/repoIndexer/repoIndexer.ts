import * as fs from 'fs';
import * as path from 'path';
import { CodeGraphBuilder } from '../builder/CodeGraphBuilder';
import { GraphlibCodeGraph } from '../graph/GraphlibCodeGraph';
import { ParserManager } from '../parser/ParserManager';
import type { ICodeGraph } from '../types/graph';
import { FILE_TYPE_MAP, SupportedLanguage } from '../types/language';
import type { CodeNode } from '../types/repository';
import { WorkerPool } from '../workers/workerPool';

export class RepoIndexer {
  private parserManager: ParserManager | null = null;
  private graphBuilder = new CodeGraphBuilder();
  private graph = new GraphlibCodeGraph();
  private lastModifiedTime = new Map<string, number>();
  private workerPool: WorkerPool | null = null;
  private onProgress?: (progress: { completed: number; total: number; currentFile?: string; percentage: number }) => void;
  private wasmPaths?: {
    treeSitterC: string;
    treeSitterNp: string;
  };

  constructor(public workspacePath: string, private languageExts: string[] = ['*']) {
    this.languageExts = languageExts.includes('*')
      ? Object.keys(FILE_TYPE_MAP)
      : languageExts.map(e => e.startsWith('.') ? e : `.${e}`);

    // 自动推断 dist/node_modules 下 wasm 文件的绝对路径
    this.wasmPaths = this.generateWasmPaths();
  }

  /**
   * 生成 WASM 文件路径
   */
  private generateWasmPaths(): { treeSitterC: string; treeSitterNp: string } {
    const { pathToFileURL } = require('url');
    const fs = require('fs');

    console.log('🔍 [RepoIndexer] 开始生成 WASM 路径...');
    console.log(`📂 [RepoIndexer] __dirname: ${__dirname}`);
    console.log(`📂 [RepoIndexer] process.cwd(): ${process.cwd()}`);

    // 尝试多个可能的路径
    const possibleBasePaths = [
      // 从当前文件位置推断
      path.resolve(__dirname, '../../../..', 'dist/node_modules'),
      path.resolve(__dirname, '../../..', 'dist/node_modules'),
      path.resolve(__dirname, '../..', 'dist/node_modules'),
      path.resolve(__dirname, '..', 'dist/node_modules'),
      // 从工作目录推断
      path.resolve(process.cwd(), 'dist/node_modules'),
      path.resolve(process.cwd(), 'node_modules'),
      // 从脚本路径推断
      ...(process.argv[1] ? [
        path.resolve(path.dirname(process.argv[1]), 'dist/node_modules'),
        path.resolve(path.dirname(process.argv[1]), '..', 'dist/node_modules'),
        path.resolve(path.dirname(process.argv[1]), '../..', 'dist/node_modules'),
      ] : [])
    ];

    console.log(`📂 [RepoIndexer] 候选基础路径:`, possibleBasePaths);

    let cWasmPath = '';
    let npWasmPath = '';

    // 查找有效的 WASM 文件路径
    for (const basePath of possibleBasePaths) {
      const cPath = path.join(basePath, 'tree-sitter-c', 'tree-sitter-c.wasm');
      const npPath = path.join(basePath, 'tree-sitter-np', 'tree-sitter-np.wasm');

      console.log(`🔍 [RepoIndexer] 检查路径: ${basePath}`);
      console.log(`  - C WASM: ${cPath} -> ${fs.existsSync(cPath) ? '存在' : '不存在'}`);
      console.log(`  - NP WASM: ${npPath} -> ${fs.existsSync(npPath) ? '存在' : '不存在'}`);

      if (fs.existsSync(cPath) && fs.existsSync(npPath)) {
        // 验证文件大小
        const cStat = fs.statSync(cPath);
        const npStat = fs.statSync(npPath);

        if (cStat.size > 0 && npStat.size > 0) {
          cWasmPath = pathToFileURL(cPath).toString();
          npWasmPath = pathToFileURL(npPath).toString();
          console.log(`✅ [RepoIndexer] 找到有效的 WASM 文件:`);
          console.log(`  - C WASM: ${cWasmPath} (${cStat.size} bytes)`);
          console.log(`  - NP WASM: ${npWasmPath} (${npStat.size} bytes)`);
          break;
        } else {
          console.log(`⚠️ [RepoIndexer] WASM 文件为空: C=${cStat.size}, NP=${npStat.size}`);
        }
      }
    }

    if (!cWasmPath || !npWasmPath) {
      console.error('❌ [RepoIndexer] 无法找到有效的 WASM 文件');
      // 使用分离构建的默认路径作为后备
      const defaultWorkerAssetsDir = path.resolve(__dirname, '../../../..', 'dist/workers/assets');
      cWasmPath = pathToFileURL(path.join(defaultWorkerAssetsDir, 'tree-sitter-c.wasm')).toString();
      npWasmPath = pathToFileURL(path.join(defaultWorkerAssetsDir, 'tree-sitter-np.wasm')).toString();
      console.log(`⚠️ [RepoIndexer] 使用分离构建的默认路径:`);
      console.log(`  - C WASM: ${cWasmPath}`);
      console.log(`  - NP WASM: ${npWasmPath}`);
    }

    return {
      treeSitterC: cWasmPath,
      treeSitterNp: npWasmPath
    };
  }

  /**
   * 设置 WASM 路径
   */
  setWasmPaths(wasmPaths: { treeSitterC: string; treeSitterNp: string }): void {
    this.wasmPaths = wasmPaths;
  }

  /**
   * 初始化解析器管理器
   */
  private async initializeParserManager(): Promise<void> {
    if (!this.parserManager) {
      this.parserManager = new ParserManager();
      
      // 如果配置了 WASM 路径，使用显式路径初始化
      if (this.wasmPaths) {
        try {
          const { LanguageLoader } = await import('../parser/LanguageLoader');
          
          // 加载 C 语言模块
          const cLanguage = await LanguageLoader.loadFromAbsolutePath(this.wasmPaths.treeSitterC);
          console.log('✅ 主进程 C 语言模块加载成功');
          
          // 加载 NP 语言模块
          const npLanguage = await LanguageLoader.loadFromAbsolutePath(this.wasmPaths.treeSitterNp);
          console.log('✅ 主进程 NP 语言模块加载成功');
          
          // 使用预加载语言模块初始化解析器
          await this.parserManager.initWithLanguages(cLanguage, npLanguage);
          console.log('✅ 主进程解析器初始化完成');
        } catch (error) {
          console.error('❌ 主进程解析器初始化失败:', error);
          // 降级到默认初始化
          console.log('🔄 降级到默认解析器初始化...');
          try {
            // 确保执行默认初始化
            await this.parserManager.getParser('c');  // 触发默认初始化
            await this.parserManager.getParser('np'); // 触发默认初始化
            console.log('✅ 默认解析器初始化完成');
          } catch (defaultError) {
            console.error('❌ 默认解析器初始化也失败:', defaultError);
          }
        }
      }
    }
  }

  /**
   * 设置进度回调
   */
  setProgressCallback(callback: (progress: { completed: number; total: number; currentFile?: string; percentage: number }) => void): void {
    this.onProgress = callback;
  }

  /**
   * 初始化 worker 池
   */
  private async initializeWorkerPool(): Promise<void> {
    if (!this.workerPool) {
      // 确保解析器管理器已初始化
      await this.initializeParserManager();
      
      this.workerPool = new WorkerPool({
        maxWorkers: 2, // 限制最多3个worker
        onProgress: this.onProgress,
        wasmPaths: this.wasmPaths
      });
      await this.workerPool.initialize();
    }
  }

  /**
   * 关闭 worker 池
   */
  private async shutdownWorkerPool(): Promise<void> {
    if (this.workerPool) {
      await this.workerPool.shutdown();
      this.workerPool = null;
    }
  }

  /**
   * 分析代码仓库
   * @param incremental 是否增量分析
   */
  async analyze(incremental = false): Promise<void> {
    console.log(`RepoIndexer.analyze 被调用，incremental: ${incremental}, 当前图节点数: ${this.graph.nodes.size}`);
    if (incremental && this.graph.nodes.size === 0) {
      console.log('调用 fullAnalyze (incremental=true, 图为空)');
      await this.fullAnalyze();
    } else if (incremental) {
      console.log('调用 incrementalAnalyze (incremental=true, 图不为空)');
      await this.incrementalAnalyze();
    } else {
      console.log('调用 fullAnalyze (incremental=false)');
      await this.fullAnalyze();
    }
  }

  /**
   * 获取代码图
   */
  getGraph(): ICodeGraph {
    return this.graph;
  }

  /**
   * 导出数据
   */
  exportData() {
    // 将图谱数据序列化为可存储的格式
    const graphData = {
      nodes: Array.from(this.graph.nodes.entries()),
      edges: this.graph.edges
    };

    return {
      workspacePath: this.workspacePath,
      languageExts: this.languageExts,
      graph: graphData,
      lastModifiedTime: Array.from(this.lastModifiedTime.entries())
    };
  }

  /**
   * 从数据恢复
   */
  async restoreFromData(data: any): Promise<void> {
    this.workspacePath = data.workspacePath;
    this.languageExts = data.languageExts || ['*'];

    // 创建新的图谱实例
    this.graph = new GraphlibCodeGraph();

    // 恢复节点
    if (data.graph && data.graph.nodes) {
      for (const [nodeId, node] of data.graph.nodes) {
        // 清理节点ID中的控制字符
        const cleanedNodeId = this.cleanNodeId(nodeId);
        const cleanedNode = { ...node };
        if (cleanedNode.id) {
          cleanedNode.id = this.cleanNodeId(cleanedNode.id);
        }
        this.graph.addNode(cleanedNode);
      }
    }

    // 恢复边
    if (data.graph && data.graph.edges) {
      for (const edge of data.graph.edges) {
        const cleanedFrom = this.cleanNodeId(edge.from);
        const cleanedTo = this.cleanNodeId(edge.to);
        this.graph.addEdge(cleanedFrom, cleanedTo, edge.type);
      }
    }

    this.lastModifiedTime = new Map(data.lastModifiedTime);
  }

  /**
   * 节点查询接口
   */

  /**
   * 根据名称和类型查找节点
   * @param name 节点名称
   * @param type 节点类型，默认为 'function'
   */
  findNodeByName(name: string, type: string = 'function'): CodeNode | undefined {
    for (const [_, node] of Array.from(this.graph.nodes)) {
      if (node.name === name && node.type === type) {
        return node;
      }
    }
    return undefined;
  }

  /**
   * 在特定文件中查找节点
   * @param filePath 文件路径
   * @param name 节点名称
   * @param type 节点类型，默认为 'function'
   */
  findNodeInFile(filePath: string, name: string, type: string = 'function'): CodeNode | undefined {
    // 处理文件路径：如果是绝对路径，转换为相对路径；如果是相对路径，直接使用
    //BUG: to-do 目前的索引 使用的是绝对路径，这造成了 这里有问题
    let relativePath: string;
    if (path.isAbsolute(filePath)) {
      relativePath = path.relative(this.workspacePath, filePath);
    } else {
      // 如果是相对路径，直接使用，但需要确保格式一致
      relativePath = filePath;
    }

    for (const [_, node] of Array.from(this.graph.nodes)) {
      if ((node.file === relativePath || node.file === filePath) && node.name === name && node.type === type) {
        return node;
      }
    }

    return undefined;
  }

  /**
   * 查找所有同名节点
   * @param name 节点名称
   * @param type 节点类型，默认为 'function'
   */
  findAllNodesByName(name: string, type: string = 'function'): CodeNode[] {
    const nodes: CodeNode[] = [];
    for (const [_, node] of Array.from(this.graph.nodes)) {
      if (node.name === name && node.type === type) {
        nodes.push(node);
      }
    }
    return nodes;
  }

  /**
   * 关系查询接口
   */

  /**
   * 查找函数的调用者
   * @param name 函数名称
   * @param type 节点类型，默认为 'function'
   */
  findCallersByName(name: string, type: string = 'function'): CodeNode[] {
    const node = this.findNodeByName(name, type);
    if (!node) { return []; }
    return this.findCallers(node.id);
  }

  /**
   * 查找函数调用的其他函数
   * @param name 函数名称
   * @param type 节点类型，默认为 'function'
   */
  findCalleesByName(name: string, type: string = 'function'): CodeNode[] {
    const node = this.findNodeByName(name, type);
    if (!node) { return []; }
    return this.findCallees(node.id);
  }

  /**
   * 查找所有相关函数
   * @param name 函数名称
   * @param depth 调用链深度
   * @param type 节点类型，默认为 'function'
   */
  findRelatedFunctionsByName(name: string, depth: number, type: string = 'function'): CodeNode[] {
    // 查找所有同名节点，然后合并它们的所有相关函数
    const allNodes = this.findAllNodesByName(name, type);
    if (allNodes.length === 0) { return []; }
    
    const allRelatedFunctions = new Map<string, CodeNode>();
    
    for (const node of allNodes) {
      const relatedFunctions = this.findRelatedFunctions(node.id, depth);
      for (const func of relatedFunctions) {
        allRelatedFunctions.set(func.id, func);
      }
    }
    
    return Array.from(allRelatedFunctions.values());
  }

  /**
   * 查找相似函数
   * @param name 函数名称
   * @param type 节点类型，默认为 'function'
   * @param similarityThreshold 相似度阈值，默认为0.6
   */
  findSimilarFunctionsByName(name: string, type: string = 'function', similarityThreshold: number = 0.6): CodeNode[] {
    const node = this.findNodeByName(name, type);
    if (!node) { return []; }
    return this.findSimilarFunctions(node.id, similarityThreshold);
  }

  /**
   * 查找模块中的相关节点
   * @param name 节点名称
   * @param filePath 文件路径
   * @param type 节点类型，默认为 'function'
   */
  getModuleRelatedNodesByName(name: string, filePath: string, type: string = 'function'): CodeNode[] {
    const node = this.findNodeInFile(filePath, name, type);
    if (!node) { return []; }
    return this.getModuleRelatedNodes(node.id, filePath);
  }

  /**
   * 内部方法
   */

  private async parseAllFiles(fileParseTasks: Array<{ file: string; language: SupportedLanguage }>) {
    console.log(`🔄 开始统一解析 ${fileParseTasks.length} 个文件`);

    // 初始化 worker 池
    console.log(`🔧 初始化 Worker 池...`);
    await this.initializeWorkerPool();
    console.log(`✅ Worker 池初始化完成`);

    try {
      // 准备 worker 池任务
      console.log(`📋 准备 Worker 任务...`);
      const workerTasks = fileParseTasks.map(({ file }) => ({
        filePath: file,
        workspacePath: this.workspacePath
      }));

      console.log(`🚀 开始并发解析文件...`);
      // 使用 worker 池批量解析文件
      const workerResults = await this.workerPool!.parseFiles(workerTasks);

      // 处理解析结果
      console.log(`📊 处理解析结果...`);
      let successCount = 0;
      let errorCount = 0;
      const allAsts: Array<{ file: string; ast: any; mtimeMs: number }> = [];

      for (const result of workerResults) {
        if (result.success && result.ast) {
          allAsts.push({
            file: result.filePath,
            ast: result.ast,
            mtimeMs: result.mtimeMs || 0
          });
          successCount++;
        } else {
          console.warn(`❌ 文件解析失败: ${path.basename(result.filePath)}, 错误: ${result.error}`);
          errorCount++;
        }
      }

      console.log(`📈 文件解析统计: 成功 ${successCount} 个，失败 ${errorCount} 个，总计 ${fileParseTasks.length} 个`);

      // 统一构建图谱
      if (allAsts.length > 0) {
        console.log(`🏗️ 开始构建统一图谱...`);
        this.buildUnifiedGraph(allAsts);
        console.log(`✅ 统一图谱构建完成`);
      } else {
        console.warn(`⚠️ 没有成功解析的文件，跳过图谱构建`);
      }
    } finally {
      // 关闭 worker 池
      console.log(`🔧 关闭 Worker 池...`);
      await this.shutdownWorkerPool();
      console.log(`✅ Worker 池已关闭`);
    }
  }

  private async parseFilesByLanguage(files: string[], language: SupportedLanguage) {
    // 确保解析器管理器已初始化
    await this.initializeParserManager();
    
    if (!this.parserManager) {
      console.warn(`解析器管理器未初始化`);
      return;
    }
    
    const parser = await this.parserManager.getParser(language);
    if (!parser) {
      console.warn(`未找到 ${language} 语言的解析器`);
      return;
    }

    console.log(`开始解析 ${language} 语言的 ${files.length} 个文件`);

    const parseResults = await Promise.allSettled(
      files.map(async (file) => {
        try {
          const content = await fs.promises.readFile(file, 'utf-8');
          console.log(`解析文件: ${file}, 内容长度: ${content.length}`);
          const ast = await parser.parse(content);
          console.log(`AST解析成功: ${file}, AST类型: ${ast?.type}, 子节点数: ${ast?.children?.length || 0}`);
          const stats = await fs.promises.stat(file);
          return { file, ast, mtimeMs: stats.mtimeMs };
        } catch (error) {
          console.error(`解析文件失败: ${file}`, error);
          return null;
        }
      })
    );

    let successCount = 0;
    for (const res of parseResults) {
      if (res.status === 'fulfilled' && res.value) {
        console.log(`更新图谱: ${res.value.file}`);
        this.updateModuleAndGraph(res.value.file, res.value.ast, res.value.mtimeMs);
        successCount++;
      }
    }
    console.log(`${language} 语言解析完成，成功解析 ${successCount}/${files.length} 个文件`);
  }

  private buildUnifiedGraph(allAsts: Array<{ file: string; ast: any; mtimeMs: number }>) {
    console.log(`🏗️ 开始统一构建图谱，包含 ${allAsts.length} 个文件的AST`);

    const beforeNodes = this.graph.nodes.size;
    const beforeEdges = this.graph.edges.length;

    console.log(`📊 构建前图谱状态: 节点 ${beforeNodes} 个，边 ${beforeEdges} 个`);

    // 重置CodeGraphBuilder状态，确保每次构建都是干净的
    console.log(`🔧 重置CodeGraphBuilder状态...`);
    this.graphBuilder.reset();

    // 使用CodeGraphBuilder的buildFromMultipleAsts方法统一构建图谱
    const astData = allAsts.map(({ file, ast }) => ({
      ast,
      filePath: file
    }));

    console.log(`🔧 调用 CodeGraphBuilder.buildFromMultipleAsts...`);
    this.graphBuilder.buildFromMultipleAsts(astData);

    // 更新所有文件的修改时间
    console.log(`⏰ 更新文件修改时间...`);
    for (const { file, mtimeMs } of allAsts) {
      this.lastModifiedTime.set(file, mtimeMs);
    }

    // 获取构建后的图谱
    this.graph = this.graphBuilder.getGraph();
    
    const afterNodes = this.graph.nodes.size;
    const afterEdges = this.graph.edges.length;
    const newNodeCount = afterNodes - beforeNodes;
    const newEdgeCount = afterEdges - beforeEdges;

    console.log(`📊 构建后图谱状态: 节点 ${afterNodes} 个，边 ${afterEdges} 个`);
    console.log(`📈 图谱增长: 新增节点 ${newNodeCount} 个，新增边 ${newEdgeCount} 个`);
    
    // 添加详细的节点类型统计
    const nodeTypes = new Map<string, number>();
    for (const node of this.graph.nodes.values()) {
      const type = node.kind || node.type || 'unknown';
      nodeTypes.set(type, (nodeTypes.get(type) || 0) + 1);
    }
    console.log(`📊 节点类型统计:`, Object.fromEntries(nodeTypes));
    
    console.log(`✅ 统一图谱构建完成`);
  }

  private updateModuleAndGraph(filePath: string, ast: any, mtimeMs: number) {
    const moduleName = path.relative(this.workspacePath, filePath);
    console.log(`构建图谱: ${moduleName}`);
    
    const beforeNodes = this.graph.nodes.size;
    const beforeEdges = this.graph.edges.length;
    
    this.graphBuilder.buildFromAst(ast, moduleName);
    this.lastModifiedTime.set(filePath, mtimeMs);
    this.graph = this.graphBuilder.getGraph();
    
    const afterNodes = this.graph.nodes.size;
    const afterEdges = this.graph.edges.length;
    
    console.log(`图谱更新完成: ${moduleName}, 新增节点: ${afterNodes - beforeNodes}, 新增边: ${afterEdges - beforeEdges}`);
  }

  private async fullAnalyze() {
    console.log(`🚀 开始全量分析...`);
    console.log(`📁 工作目录: ${this.workspacePath}`);
    console.log(`🔧 支持语言: ${this.languageExts.join(', ')}`);
    
    const startTime = performance.now();
    
    const allFiles = await this.getAllFiles(this.workspacePath);
    console.log(`📊 文件扫描完成: 找到 ${allFiles.length} 个文件`);
    
    // 不再按语言分组，统一处理所有文件以支持跨语言调用关系
    const fileParseTasks: Array<{ file: string; language: SupportedLanguage }> = [];

    console.log(`🔍 开始文件分类...`);
    for (const file of allFiles) {
      const lang = this.getLanguageFromFile(file);
      if (lang) {
        fileParseTasks.push({ file, language: lang });
        console.log(`✅ 文件分类: ${path.basename(file)} -> ${lang}`);
      } else {
        console.log(`⚠️ 不支持的文件类型: ${path.basename(file)}`);
      }
    }

    console.log(`📋 文件分类完成: 准备解析 ${fileParseTasks.length} 个文件`);

    // 统一解析所有文件，保持跨语言调用关系
    await this.parseAllFiles(fileParseTasks);
    
    const endTime = performance.now();
    const duration = Math.round(endTime - startTime);
    console.log(`✅ 全量分析完成，总耗时: ${duration}ms`);
    console.log(`📊 最终图谱: 节点 ${this.graph.nodes.size} 个，边 ${this.graph.edges.length} 个`);
  }

  private async incrementalAnalyze() {
    console.log('🔄 开始增量索引分析...');
    
    const { added, removed, changed } = await this.getFileChanges();
    
    if (added.length === 0 && removed.length === 0 && changed.length === 0) {
      console.log('ℹ️ 没有文件变更，跳过增量索引');
      return;
    }

    console.log(`📊 增量索引统计: 新增${added.length}个文件，删除${removed.length}个文件，修改${changed.length}个文件`);

    // 显示详细的文件列表
    if (added.length > 0) {
      console.log(`📁 新增文件列表: ${added.map(f => path.basename(f)).join(', ')}`);
    }
    if (removed.length > 0) {
      console.log(`🗑️ 删除文件列表: ${removed.map(f => path.basename(f)).join(', ')}`);
    }
    if (changed.length > 0) {
      console.log(`✏️ 修改文件列表: ${changed.map(f => path.basename(f)).join(', ')}`);
    }

    const startTime = performance.now();
    let processedCount = 0;
    const totalFiles = added.length + removed.length + changed.length;

    // 1. 处理删除的文件
    console.log(`🗑️ 开始处理删除的文件 (${removed.length}个)...`);
    for (const filePath of removed) {
      console.log(`🗑️ 处理删除文件: ${path.basename(filePath)}`);
      await this.handleFileRemoved(filePath);
      processedCount++;
      console.log(`📈 进度: ${processedCount}/${totalFiles} (${Math.round(processedCount/totalFiles*100)}%)`);
    }

    // 2. 处理修改的文件
    console.log(`✏️ 开始处理修改的文件 (${changed.length}个)...`);
    for (const filePath of changed) {
      console.log(`✏️ 处理修改文件: ${path.basename(filePath)}`);
      await this.handleFileModified(filePath);
      processedCount++;
      console.log(`📈 进度: ${processedCount}/${totalFiles} (${Math.round(processedCount/totalFiles*100)}%)`);
    }

    // 3. 处理新增的文件
    console.log(`📁 开始处理新增的文件 (${added.length}个)...`);
    for (const filePath of added) {
      console.log(`📁 处理新增文件: ${path.basename(filePath)}`);
      await this.handleFileAdded(filePath);
      processedCount++;
      console.log(`📈 进度: ${processedCount}/${totalFiles} (${Math.round(processedCount/totalFiles*100)}%)`);
    }

    const endTime = performance.now();
    const duration = Math.round(endTime - startTime);
    console.log(`✅ 增量索引完成，总耗时: ${duration}ms，处理文件: ${totalFiles}个`);
  }

  /**
   * 获取文件变更情况
   */
  private async getFileChanges(): Promise<{
    added: string[];
    removed: string[];
    changed: string[];
  }> {
    const allFiles = await this.getAllFiles(this.workspacePath);
    const added: string[] = [];
    const removed: string[] = [];
    const changed: string[] = [];

    // 检查新增和修改的文件
    for (const file of allFiles) {
      const lastTime = this.lastModifiedTime.get(file);
      if (!lastTime) {
        // 新文件
        added.push(file);
      } else {
        try {
          const stat = fs.statSync(file);
          if (stat.mtimeMs > lastTime) {
            // 修改的文件
            changed.push(file);
          }
        } catch {
          // 文件不存在，忽略
        }
      }
    }

    // 检查删除的文件（在 lastModifiedTime 中存在但实际文件不存在）
    for (const [filePath, lastTime] of this.lastModifiedTime) {
      try {
        fs.statSync(filePath);
      } catch {
        // 文件不存在，标记为删除
        removed.push(filePath);
      }
    }

    return { added, removed, changed };
  }

  /**
   * 处理新增文件
   * 1. 全量分析A文件，并建立A文件的节点和其他节点的边
   */
  private async handleFileAdded(filePath: string): Promise<void> {
    console.log(`📁 开始处理新增文件: ${path.basename(filePath)}`);
    
    const language = this.getLanguageFromFile(filePath);
    if (!language) {
      console.log(`⚠️ 不支持的文件类型: ${path.basename(filePath)}`);
      return;
    }

    // 确保解析器管理器已初始化
    await this.initializeParserManager();
    
    if (!this.parserManager) {
      console.log(`❌ 解析器管理器未初始化`);
      return;
    }

    const parser = await this.parserManager.getParser(language);
    if (!parser) {
      console.log(`❌ 未找到解析器: ${language}`);
      return;
    }

    try {
      console.log(`🔍 解析文件: ${path.basename(filePath)} (语言: ${language})`);
      
      // 1. 解析文件
      const content = await fs.promises.readFile(filePath, 'utf-8');
      const ast = await parser.parse(content);
      const stats = await fs.promises.stat(filePath);

      console.log(`✅ 文件解析成功: ${path.basename(filePath)}`);

      // 2. 构建该文件的节点和边
      const moduleName = path.relative(this.workspacePath, filePath);
      const beforeNodes = this.graph.nodes.size;
      const beforeEdges = this.graph.edges.length;

      console.log(`🏗️ 构建图谱: ${moduleName}`);

      // 创建新的 CodeGraphBuilder 实例来构建该文件的图谱
      const tempBuilder = new CodeGraphBuilder();
      tempBuilder.buildFromAst(ast, filePath);
      
      // 获取新构建的图谱并合并到主图谱
      const newGraph = tempBuilder.getGraph();
      this.mergeGraph(newGraph);

      // 3. 建立与其他文件的引用关系
      console.log(`🔗 建立跨文件引用关系: ${path.basename(filePath)}`);
      await this.buildCrossFileReferences(filePath, ast);

      // 4. 更新文件修改时间
      this.lastModifiedTime.set(filePath, stats.mtimeMs);

      const afterNodes = this.graph.nodes.size;
      const afterEdges = this.graph.edges.length;
      const newNodeCount = afterNodes - beforeNodes;
      const newEdgeCount = afterEdges - beforeEdges;
      
      console.log(`✅ 新增文件索引完成: ${path.basename(filePath)}`);
      console.log(`📊 图谱更新: 新增节点 ${newNodeCount} 个，新增边 ${newEdgeCount} 个`);
    } catch (error) {
      console.error(`❌ 处理新增文件失败: ${path.basename(filePath)}`, error);
    }
  }

  /**
   * 处理删除文件
   * 将与A有关的节点和边都删除
   */
  private async handleFileRemoved(filePath: string): Promise<void> {
    console.log(`🗑️ 开始处理删除文件: ${path.basename(filePath)}`);
    
    const relativePath = path.relative(this.workspacePath, filePath);
    const nodesToRemove: string[] = [];
    const edgesToRemove: Array<{ from: string; to: string }> = [];

    console.log(`🔍 查找相关节点和边: ${relativePath}`);

    // 1. 找到所有属于该文件的节点
    for (const [nodeId, node] of Array.from(this.graph.nodes)) {
      if (node.file === relativePath) {
        nodesToRemove.push(nodeId);
      }
    }

    console.log(`📋 找到 ${nodesToRemove.length} 个相关节点`);

    // 2. 找到所有与该文件节点相关的边
    for (const edge of this.graph.edges) {
      if (nodesToRemove.includes(edge.from) || nodesToRemove.includes(edge.to)) {
        edgesToRemove.push({ from: edge.from, to: edge.to });
      }
    }

    console.log(`🔗 找到 ${edgesToRemove.length} 个相关边`);

    // 3. 删除边
    console.log(`🗑️ 删除相关边...`);
    for (const edge of edgesToRemove) {
      this.graph.removeEdge(edge.from, edge.to);
    }

    // 4. 删除节点
    console.log(`🗑️ 删除相关节点...`);
    for (const nodeId of nodesToRemove) {
      this.graph.removeNode(nodeId);
    }

    // 5. 从修改时间记录中删除
    this.lastModifiedTime.delete(filePath);

    console.log(`✅ 删除文件索引完成: ${path.basename(filePath)}`);
    console.log(`📊 清理统计: 删除节点 ${nodesToRemove.length} 个，删除边 ${edgesToRemove.length} 个`);
  }

  /**
   * 处理修改文件
   * 1. 先将与A有关的节点和边删除
   * 2. 全量分析A，建立节点和导入关系的边
   * 3. 通过模块查找所有其他模块的引用关系，如果引用了A则重新建立边
   */
  private async handleFileModified(filePath: string): Promise<void> {
    console.log(`✏️ 开始处理修改文件: ${path.basename(filePath)}`);
    
    // 1. 先删除该文件的所有节点和边（类似删除操作）
    console.log(`🗑️ 清理旧数据: ${path.basename(filePath)}`);
    await this.handleFileRemoved(filePath);
    
    // 2. 重新添加该文件（类似新增操作）
    console.log(`📁 重新索引文件: ${path.basename(filePath)}`);
    await this.handleFileAdded(filePath);
    
    console.log(`✅ 修改文件索引完成: ${path.basename(filePath)}`);
  }

  /**
   * 建立跨文件引用关系
   */
  private async buildCrossFileReferences(filePath: string, ast: any): Promise<void> {
    console.log(`建立跨文件引用关系: ${filePath}`);
    
    // 这里需要根据具体的AST结构来解析导入/引用关系
    // 对于C语言，可能需要解析 #include 语句
    // 对于其他语言，可能需要解析 import/require 语句
    
    // 示例：解析C语言的 #include 语句
    const includes = this.extractIncludes(ast);
    
    for (const include of includes) {
      // 查找被引用的文件
      const referencedFile = await this.findReferencedFile(filePath, include);
      if (referencedFile) {
        // 建立引用关系
        await this.buildReferenceEdges(filePath, referencedFile);
      }
    }
  }

  /**
   * 从AST中提取include语句
   */
  private extractIncludes(ast: any): string[] {
    const includes: string[] = [];
    
    const extractFromNode = (node: any) => {
      if (node.type === 'preprocessor_functionlike_macro_call' && node.name === 'include') {
        // 提取include的文件名
        if (node.arguments && node.arguments.length > 0) {
          const arg = node.arguments[0];
          if (arg.type === 'string_literal') {
            includes.push(arg.value.replace(/['"]/g, ''));
          }
        }
      }
      
      // 递归处理子节点
      if (node.children) {
        for (const child of node.children) {
          extractFromNode(child);
        }
      }
    };
    
    extractFromNode(ast);
    return includes;
  }

  /**
   * 查找被引用的文件
   */
  private async findReferencedFile(sourceFile: string, includeName: string): Promise<string | null> {
    // 这里需要实现查找逻辑
    // 1. 检查是否是系统头文件
    // 2. 检查相对路径
    // 3. 检查项目中的其他文件
    
    const sourceDir = path.dirname(sourceFile);
    
    // 尝试相对路径
    const relativePath = path.join(sourceDir, includeName);
    try {
      await fs.promises.access(relativePath);
      return relativePath;
    } catch {
      // 文件不存在
    }
    
    // 尝试项目根目录
    const rootPath = path.join(this.workspacePath, includeName);
    try {
      await fs.promises.access(rootPath);
      return rootPath;
    } catch {
      // 文件不存在
    }
    
    return null;
  }

  /**
   * 建立引用关系的边
   */
  private async buildReferenceEdges(sourceFile: string, targetFile: string): Promise<void> {
    const sourceModule = path.relative(this.workspacePath, sourceFile);
    const targetModule = path.relative(this.workspacePath, targetFile);
    
    // 找到源文件的所有函数节点
    const sourceNodes = Array.from(this.graph.nodes.values())
      .filter(node => node.file === sourceModule);
    
    // 找到目标文件的所有函数节点
    const targetNodes = Array.from(this.graph.nodes.values())
      .filter(node => node.file === targetModule);
    
    // 建立引用关系（这里可以根据具体需求调整）
    for (const sourceNode of sourceNodes) {
      for (const targetNode of targetNodes) {
        // 添加引用边
        this.graph.addEdge(sourceNode.id, targetNode.id, 'references');
      }
    }
  }

  /**
   * 合并图谱
   */
  private mergeGraph(newGraph: ICodeGraph): void {
    // 合并节点
    for (const [nodeId, node] of newGraph.nodes) {
      this.graph.addNode(node);
    }
    
    // 合并边
    for (const edge of newGraph.edges) {
      this.graph.addEdge(edge.from, edge.to, edge.type);
    }
  }

  private async getModifiedFiles(): Promise<string[]> {
    const allFiles = await this.getAllFiles(this.workspacePath);
    return allFiles.filter(file => {
      const lastTime = this.lastModifiedTime.get(file);
      if (!lastTime) { return true; }

      try {
        const stat = fs.statSync(file);
        return stat.mtimeMs > lastTime;
      } catch {
        return false;
      }
    });
  }

  private async getAllFiles(dir: string): Promise<string[]> {
    const entries = await fs.promises.readdir(dir, { withFileTypes: true });
    let files: string[] = [];
    for (const entry of entries) {
      const fullPath = path.join(dir, entry.name);
      if (entry.isDirectory()) {
        if (!entry.name.startsWith('.') && entry.name !== 'node_modules') {
          files = files.concat(await this.getAllFiles(fullPath));
        }
      } else if (entry.isFile()) {
        const ext = path.extname(entry.name).toLowerCase();
        // 调试信息
        console.log(`检查文件: ${entry.name}, 扩展名: ${ext}, 支持的语言: ${FILE_TYPE_MAP[ext] || '不支持'}`);
        
        // 如果 languageExts 包含 '*'，则接受所有支持的文件类型
        // 否则只接受指定的文件扩展名
        if (this.languageExts.includes('*')) {
          // 当 languageExts 包含 '*' 时，只接受 FILE_TYPE_MAP 中定义的文件类型
          if (FILE_TYPE_MAP[ext]) {
            files.push(fullPath);
            console.log(`  -> 接受文件 (languageExts包含*): ${fullPath}`);
          }
        } else if (this.languageExts.includes(ext)) {
          files.push(fullPath);
          console.log(`  -> 接受文件 (languageExts包含扩展名): ${fullPath}`);
        } else {
          console.log(`  -> 拒绝文件: ${fullPath}`);
        }
      }
    }
    return files;
  }

  private getLanguageFromFile(filePath: string): SupportedLanguage | null {
    const ext = path.extname(filePath).toLowerCase();
    return FILE_TYPE_MAP[ext] || null;
  }

  private findCallers(nodeId: string): CodeNode[] {
    return this.graph.getIncomingEdges(nodeId)
      .filter(edge => edge.type === 'calls')
      .map(edge => this.graph.node(edge.from))
      .filter((node): node is CodeNode => node !== undefined && (node.type === 'function' || node.type === 'macro'));
  }

  private findCallees(nodeId: string): CodeNode[] {
    return this.graph.getOutgoingEdges(nodeId)
      .filter(edge => edge.type === 'calls')
      .map(edge => this.graph.node(edge.to))
      .filter((node): node is CodeNode => node !== undefined && (node.type === 'function' || node.type === 'macro'));
  }

  private findRelatedFunctions(nodeId: string, call_chain_depth: number): CodeNode[] {
    // 获取所有相关节点
    const allRelatedNodes = this.graph.getRelatedNodes(nodeId, call_chain_depth);
    // 返回函数和宏类型的节点，并过滤掉当前节点自己
    return allRelatedNodes.filter(node => (node.type === 'function' || node.type === 'macro') && node.id !== nodeId);
  }

  private findSimilarFunctions(nodeId: string, similarityThreshold: number = 0.6): CodeNode[] {
    const node = this.graph.node(nodeId);
    if (!node || node.type !== 'function') {
      return [];
    }

    const similarFunctions: CodeNode[] = [];
    const allFunctions = Array.from(this.graph.nodes.values()).filter(n => n.type === 'function');

    for (const otherNode of allFunctions) {
      if (otherNode.id === nodeId) { continue; } // 排除自身

      const similarity = this.calculateFunctionSimilarity(node, otherNode);
      if (similarity >= similarityThreshold) {
        similarFunctions.push(otherNode);
      }
    }
    return similarFunctions;
  }

  private calculateFunctionSimilarity(node1: CodeNode, node2: CodeNode): number {
    // 这里可以实现更复杂的相似度计算逻辑
    // 例如，基于函数签名、参数类型、返回类型等进行匹配
    // 目前简单地判断名称是否相同
    if (node1.name === node2.name) {
      return 1.0; // 名称相同，相似度最高
    }
    return 0.0; // 名称不同，相似度为0
  }

  private getModuleRelatedNodes(nodeId: string, modulePath: string): CodeNode[] {
    return this.graph.getModuleRelatedNodes(nodeId, modulePath);
  }

  /**
   * 清理图谱数据中的控制字符
   */
  private cleanGraphData(graph: any): any {
    if (!graph) { return graph; }

    // 创建新的图谱实例
    const cleanedGraph = new GraphlibCodeGraph();

    // 清理节点
    const nodes = (graph.nodes as Map<string, any>) || new Map();
    for (const [nodeId, node] of Array.from(nodes)) {
      if (node) {
        const cleanedNode = { ...node };
        // 清理节点ID中的控制字符
        if (cleanedNode.id) {
          cleanedNode.id = this.cleanNodeId(cleanedNode.id);
        }
        cleanedGraph.addNode(cleanedNode);
      }
    }

    // 清理边
    const edges = (graph.edges as any[]) || [];
    for (const edge of edges) {
      const cleanedFrom = this.cleanNodeId(edge.from);
      const cleanedTo = this.cleanNodeId(edge.to);
      cleanedGraph.addEdge(cleanedFrom, cleanedTo, edge.type);
    }

    return cleanedGraph;
  }

  /**
   * 清理节点ID中的控制字符
   */
  private cleanNodeId(nodeId: string): string {
    if (!nodeId) { return nodeId; }

    // 移除所有控制字符（ASCII 0-31，除了制表符、换行符、回车符）
    return nodeId.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '');
  }

  /**
   * 增量索引单个文件
   */
  async indexFile(filePath: string): Promise<void> {
    const language = this.getLanguageFromFile(filePath);
    if (!language) { return; }
    
    // 确保解析器管理器已初始化
    await this.initializeParserManager();
    
    if (!this.parserManager) {
      console.log(`❌ 解析器管理器未初始化`);
      return;
    }
    
    const parser = await this.parserManager.getParser(language);
    if (!parser) { return; }
    try {
      const content = await fs.promises.readFile(filePath, 'utf-8');
      const ast = await parser.parse(content);
      const stats = await fs.promises.stat(filePath);
      this.updateModuleAndGraph(filePath, ast, stats.mtimeMs);
    } catch (error) {
      console.warn(`增量索引文件失败: ${filePath}`, error);
    }
  }

  /**
   * 从索引中移除单个文件的所有节点
   */
  removeFileFromIndex(filePath: string): void {
    const relativePath = path.relative(this.workspacePath, filePath);
    // 移除所有属于该文件的节点
    for (const [id, node] of Array.from(this.graph.nodes)) {
      if (node.file === relativePath) {
        this.graph.nodes.delete(id);
      }
    }
    // 也可移除相关边（如有需要）
  }
}