import type { CodeRepository } from '../types/repository';

/**
 * 打印 CodeRepository 的统计信息，包括模块数、节点数、类型分布、边数与边类型分布
 */
export function printRepositoryStats(repo: CodeRepository): void {
  const totalModules = repo.modules.size;
  const allNodes = [...repo.graph.nodes.values()];
  const allEdges = repo.graph.edges;

  const nodeTypeCounter: Record<string, number> = {};
  const edgeTypeCounter: Record<string, number> = {};

  for (const node of allNodes) {
    const type = node.type || 'unknown';
    nodeTypeCounter[type] = (nodeTypeCounter[type] || 0) + 1;
  }

  for (const edge of allEdges) {
    const type = (edge as any).type || 'calls';
    edgeTypeCounter[type] = (edgeTypeCounter[type] || 0) + 1;
  }

  console.log('📊 Code Repository Stats');
  console.log('------------------------');
  console.log(`📁 Modules         : ${totalModules}`);
  console.log(`🔧 Total Nodes     : ${allNodes.length}`);
  console.log('🔎 Node Type Distribution:');
  for (const [type, count] of Object.entries(nodeTypeCounter)) {
    console.log(`  - ${type}: ${count}`);
  }
  console.log(`🔗 Total Edges     : ${allEdges.length}`);
  console.log('🔀 Edge Type Distribution:');
  for (const [type, count] of Object.entries(edgeTypeCounter)) {
    console.log(`  - ${type}: ${count}`);
  }
}
