import { GraphlibCodeGraph } from '../graph/GraphlibCodeGraph';
import { ASTNode, ASTNodeKind } from '../types/ast';
import { GraphCodeNode } from '../types/graph';
import { CodeModule } from '../types/repository';

export class CodeGraphBuilder {
  private graph = new GraphlibCodeGraph();
  private modules = new Map<string, CodeModule>();
  private nodeMap = new Map<string, ASTNode>();
  private globalFunctionIndex = new Map<string, Array<{modulePath: string, nodeId: string}>>();
  private moduleImports = new Map<string, Set<string>>();

  /**
   * 重置CodeGraphBuilder的状态
   * 在每次新的构建开始前调用，确保状态干净
   */
  reset(): void {
    console.log('[CodeGraphBuilder] 重置状态...');
    this.graph = new GraphlibCodeGraph();
    this.modules.clear();
    this.nodeMap.clear();
    this.globalFunctionIndex.clear();
    this.moduleImports.clear();
    console.log('[CodeGraphBuilder] 状态重置完成');
  }

  private getLanguageFromFile(filePath: string): string {
    const ext = filePath.split('.').pop()?.toLowerCase();
    switch (ext) {
      case 'c':
      case 'h':
        return 'c';
      case 'asm':
        return 'np';
      case 'py':
        return 'python';
      default:
        return 'unknown';
    }
  }

  buildFromAst(ast: ASTNode, filePath: string): void {
    console.log(`[CodeGraphBuilder] 开始构建单文件图谱: ${filePath}`);
    
    // 重置状态，确保每次构建都是干净的
    this.reset();
    
    const language = this.getLanguageFromFile(filePath);
    const module: CodeModule = {
      path: filePath,
      language: language,
      nodeIds: new Set(),
      imports: new Set()
    };

    this.modules.set(filePath, module);

    // 添加模块入口节点
    const moduleEntryId = this.generateNodeId('__module_entry__', filePath, ASTNodeKind.MODULE);
    this.addNode({
      id: moduleEntryId,
      name: '__module_entry__',
      type: 'entry',
      language: language,
      kind: 'module',
      file: filePath,
      module: filePath
    });
    module.nodeIds.add(moduleEntryId);

    // 第一阶段：收集所有函数定义和导入关系
    this.collectFunctionDefinitions(ast, module);
    this.collectModuleImports(ast, module);

    // 第二阶段：构建调用关系
    this.buildCallRelationships(ast, module, moduleEntryId);

    // 构建节点映射
    this.buildNodeMap(ast);
    
    console.log(`[CodeGraphBuilder] 单文件图谱构建完成: ${filePath}, 节点数: ${this.graph.nodes.size}, 边数: ${this.graph.edges.length}`);
  }

  buildFromMultipleAsts(asts: Array<{ast: ASTNode, filePath: string}>): void {
    console.log(`[CodeGraphBuilder] 开始构建多文件图谱，文件数量: ${asts.length}`);
    
    // 重置状态，确保每次构建都是干净的
    this.reset();
    
    // 第一阶段：收集所有模块的函数定义和导入关系
    for (const {ast, filePath} of asts) {
      const language = this.getLanguageFromFile(filePath);
      const module: CodeModule = {
        path: filePath,
        language: language,
        nodeIds: new Set(),
        imports: new Set()
      };

      this.modules.set(filePath, module);

      // 添加模块入口节点
      const moduleEntryId = this.generateNodeId('__module_entry__', filePath, ASTNodeKind.MODULE);
      this.addNode({
        id: moduleEntryId,
        name: '__module_entry__',
        type: 'entry',
        language: language,
        kind: 'module',
        file: filePath,
        module: filePath
      });
      module.nodeIds.add(moduleEntryId);

      // 收集函数定义和导入关系
      this.collectFunctionDefinitions(ast, module);
      this.collectModuleImports(ast, module);
    }

    // 第二阶段：为所有模块构建调用关系
    for (const {ast, filePath} of asts) {
      const module = this.modules.get(filePath);
      if (module) {
        const moduleEntryId = this.generateNodeId('__module_entry__', filePath, ASTNodeKind.MODULE);
        this.buildCallRelationships(ast, module, moduleEntryId);
      }
    }
    
    console.log(`[CodeGraphBuilder] 多文件图谱构建完成，最终节点数: ${this.graph.nodes.size}, 边数: ${this.graph.edges.length}`);
  }

  /**
   * 第一阶段：收集所有函数定义到全局索引
   * 使用统一的 ASTNodeKind 枚举，屏蔽各个解析器的差异
   */
  private collectFunctionDefinitions(node: ASTNode, module: CodeModule): void {
    // 添加调试日志
    if (node.kind === ASTNodeKind.FUNCTION && node.name) {
      console.log(`[CodeGraphBuilder] 发现函数节点: ${node.name}, 语言: ${node.language}, 元数据:`, {
        isCall: node.metadata?.isCall,
        type: node.metadata?.type,
        isMacro: node.metadata?.isMacro,
        extractedFromError: node.metadata?.extractedFromError,
        extractedFromDeclareVariable: node.metadata?.extractedFromDeclareVariable
      });
    }

    // 处理函数定义 - 排除宏定义和函数调用
    if ((node.kind === ASTNodeKind.FUNCTION || node.kind === ASTNodeKind.FUNCTION_DEFINITION) && node.name && !node.metadata?.isCall && node.metadata?.type !== 'macro' && !node.metadata?.isMacro) {
      
      console.log(`[CodeGraphBuilder] 添加函数定义: ${node.name}`);
      const nodeId = this.generateNodeId(node.name, module.path, ASTNodeKind.FUNCTION);
      const functionNode: GraphCodeNode = {
        id: nodeId,
        name: node.name,
        type: 'function',
        language: node.language || module.language,
        kind: 'function',
        file: module.path,
        module: module.path,
        location: node.location,
        metadata: {
          ...node.metadata,
          definition: node.text
        },
        definition: node.text
      };
      this.addNode(functionNode);
      module.nodeIds.add(nodeId);

      // 添加到全局函数索引
      if (!this.globalFunctionIndex.has(node.name)) {
        this.globalFunctionIndex.set(node.name, []);
      }
      this.globalFunctionIndex.get(node.name)!.push({
        modulePath: module.path,
        nodeId: nodeId
      });
    }
    
    // 处理宏定义 - 排除宏调用
    // 根据项目规范：宏的kind=FUNCTION，在metadata.type='macro'中标记实际类型
    else if ((node.kind === ASTNodeKind.FUNCTION && (node.metadata?.type === 'macro' || node.metadata?.isMacro) && !node.metadata?.isCall) && node.name) {
      
      console.log(`[CodeGraphBuilder] 添加宏定义: ${node.name}`);
      const nodeId = this.generateNodeId(node.name, module.path, ASTNodeKind.FUNCTION);
      const macroNode: GraphCodeNode = {
        id: nodeId,
        name: node.name,
        type: 'function', // 根据项目规范，宏统一为function类型
        language: node.language || module.language,
        kind: 'function', // 根据项目规范，宏统一为function类型
        file: module.path,
        module: module.path,
        location: node.location,
        metadata: {
          ...node.metadata,
          definition: node.text
        },
        definition: node.text
      };
      this.addNode(macroNode);
      module.nodeIds.add(nodeId);

      // 添加到全局函数索引（宏也可以被调用）
      if (!this.globalFunctionIndex.has(node.name)) {
        this.globalFunctionIndex.set(node.name, []);
      }
      this.globalFunctionIndex.get(node.name)!.push({
        modulePath: module.path,
        nodeId: nodeId
      });
    }

    // 注意：函数调用和宏调用不应该创建节点，它们只应该创建边
    // 移除错误的宏调用节点创建逻辑

    // 处理变量声明
    else if ((node.kind === ASTNodeKind.VARIABLE || node.kind === ASTNodeKind.VARIABLE_DECLARATION) && node.name) {
      const nodeId = this.generateNodeId(node.name, module.path, ASTNodeKind.VARIABLE);
      const variableNode: GraphCodeNode = {
        id: nodeId,
        name: node.name,
        type: 'variable',
        language: node.language || module.language,
        kind: 'variable',
        file: module.path,
        module: module.path,
        location: node.location,
        metadata: {
          ...node.metadata,
          definition: node.text
        },
        definition: node.text
      };
      this.addNode(variableNode);
      module.nodeIds.add(nodeId);
    }

    // 处理类型定义（结构体、联合体、枚举等）
    else if ((node.kind === ASTNodeKind.TYPE || node.kind === ASTNodeKind.TYPE_DEFINITION) && node.name) {
      const nodeId = this.generateNodeId(node.name, module.path, ASTNodeKind.TYPE);
      const typeNode: GraphCodeNode = {
        id: nodeId,
        name: node.name,
        type: 'type',
        language: node.language || module.language,
        kind: 'type',
        file: module.path,
        module: module.path,
        location: node.location,
        metadata: {
          ...node.metadata,
          definition: node.text
        },
        definition: node.text
      };
      this.addNode(typeNode);
      module.nodeIds.add(nodeId);
    }

    // 递归处理子节点
    for (const child of node.children) {
      this.collectFunctionDefinitions(child, module);
    }
  }

  public parserASTNodeToGraphNode(astNode: ASTNode, filePath: string): GraphCodeNode {
    const name = astNode.name || astNode.originalType || 'unnamed'; // 添加默认值
    const gcn = {
      id: this.generateNodeId(name, filePath, astNode.kind),
      name: name, // 确保这里是 string
      type: astNode.kind || astNode.originalType || 'function',
      language: astNode.language || 'unknown',
      kind: astNode.kind || 'function',
      file: filePath,
      module: astNode.module,
      parentId: astNode.parentId,
      metadata: astNode.metadata,
      definition: astNode.text,
      location: astNode.location
    };
    return gcn as GraphCodeNode; // 添加类型断言
  }
  /**
   * 收集模块导入关系
   */
  private collectModuleImports(node: ASTNode, module: CodeModule): void {
    // 处理头文件包含/模块导入
    // 支持多种格式：
    // 1. ASTNodeKind.MODULE_IMPORT - 直接的模块导入节点
    // 2. ASTNodeKind.MODULE with originalType: 'include' - 头文件包含
    // 3. ASTNodeKind.MODULE with originalType: 'preprocessor_include' - C语言预处理器包含
    if ((node.kind === ASTNodeKind.MODULE_IMPORT) || 
        (node.kind === ASTNodeKind.MODULE && (node.originalType === 'include' || node.originalType === 'preprocessor_include'))) {
      
      if (node.name) {
        module.imports.add(node.name);
        
        // 记录模块导入关系
        if (!this.moduleImports.has(module.path)) {
          this.moduleImports.set(module.path, new Set());
        }
        this.moduleImports.get(module.path)!.add(node.name);
        
        // 创建模块节点
        const nodeId = this.generateNodeId(node.name, module.path, ASTNodeKind.MODULE);
        const moduleNode: GraphCodeNode = {
          id: nodeId,
          name: node.name,
          type: 'module',
          language: node.language || module.language,
          kind: 'module',
          file: module.path,
          module: module.path,
          location: node.location,
          metadata: {
            ...node.metadata,
            definition: node.text
          },
          definition: node.text
        };
        this.addNode(moduleNode);
        module.nodeIds.add(nodeId);
      }
    }

    // 递归处理子节点
    if (node.children) {
      for (const child of node.children) {
        this.collectModuleImports(child, module);
      }
    }
  }

  /**
   * 第二阶段：构建调用关系
   * 使用统一的 ASTNodeKind 枚举，屏蔽各个解析器的差异
   */
  private buildCallRelationships(node: ASTNode, module: CodeModule, moduleEntryId: string): void {
    // 处理函数定义内的调用
    if ((node.kind === ASTNodeKind.FUNCTION || node.kind === ASTNodeKind.FUNCTION_DEFINITION) && node.name) {
      const callerId = this.generateNodeId(node.name, module.path, ASTNodeKind.FUNCTION);

      // 处理函数体内的调用
      if (node.children) {
        for (const child of node.children) {
          this.processFunctionBody(child, callerId, module);
        }
      }
    }

    // 处理模块顶层的函数调用和宏调用
    if (node.kind === ASTNodeKind.FUNCTION && node.metadata?.isCall && node.name) {
      // 通过全局函数索引判断是否为宏调用
      const isMacroCall = this.isMacroCallByGlobalIndex(node.name);
      
      // 查找被调用的函数/宏定义
      const calleeId = this.findFunctionNode(node.name, module.path);
      if (calleeId) {
        this.addEdge(moduleEntryId, calleeId, 'calls');
        
        // 更新节点的元数据，标记正确的类型
        if (isMacroCall) {
          node.metadata = {
            ...node.metadata,
            type: 'macro',
            isMacroCall: true
          };
        }
      }
    }

    // 递归处理子节点
    if (node.children) {
      for (const child of node.children) {
        this.buildCallRelationships(child, module, moduleEntryId);
      }
    }
  }

  /**
   * 通过全局函数索引判断是否为宏调用
   */
  private isMacroCallByGlobalIndex(functionName: string): boolean {
    const functionEntries = this.globalFunctionIndex.get(functionName);
    if (!functionEntries || functionEntries.length === 0) {
      return false;
    }
    
    // 检查全局索引中是否有对应的宏定义
    // 宏定义通常在收集阶段被标记为 type: 'macro'
    for (const entry of functionEntries) {
      const node = this.graph.nodes.get(entry.nodeId);
      if (node && node.metadata && node.metadata.type === 'macro') {
        return true;
      }
    }
    
    return false;
  }

  /**
   * 查找函数节点：优先在当前模块查找，然后跨模块查找
   */
  private findFunctionNode(functionName: string, currentModulePath: string): string | null {
    const functionEntries = this.globalFunctionIndex.get(functionName);
    if (!functionEntries || functionEntries.length === 0) {
      return null;
    }

    // 1. 优先在当前模块查找
    const currentModuleEntry = functionEntries.find(entry => entry.modulePath === currentModulePath);
    if (currentModuleEntry) {
      return currentModuleEntry.nodeId;
    }

    // 2. 在导入的模块中查找（包括递归查找）
    const visitedModules = new Set<string>();
    const result = this.findFunctionInImportedModules(functionName, currentModulePath, visitedModules);
    if (result) {
      return result;
    }

    // 3. 如果还是找不到，返回第一个匹配的函数（可能是全局函数）
    return functionEntries[0].nodeId;
  }

  /**
   * 在导入的模块中递归查找函数
   */
  private findFunctionInImportedModules(functionName: string, modulePath: string, visitedModules: Set<string>): string | null {
    // 防止循环依赖
    if (visitedModules.has(modulePath)) {
      return null;
    }
    visitedModules.add(modulePath);

    const imports = this.moduleImports.get(modulePath);
    if (!imports) {
      return null;
    }

    for (const importedModule of imports) {
      const functionEntries = this.globalFunctionIndex.get(functionName);
      if (functionEntries) {
        const importedModuleEntry = functionEntries.find(entry => entry.modulePath === importedModule);
        if (importedModuleEntry) {
          return importedModuleEntry.nodeId;
        }
      }

      // 递归查找
      const result = this.findFunctionInImportedModules(functionName, importedModule, visitedModules);
      if (result) {
        return result;
      }
    }

    return null;
  }

  /**
   * 处理函数体内的调用关系
   * 使用统一的 ASTNodeKind 枚举，屏蔽各个解析器的差异
   * 函数调用是节点和节点之间的关系，而不是一个实体节点
   */
  private processFunctionBody(node: ASTNode, callerId: string, module: CodeModule): void {
    // 处理函数调用和宏调用
    // 支持两种格式：
    // 1. ASTNodeKind.FUNCTION_CALL - 直接的函数调用节点
    // 2. ASTNodeKind.FUNCTION with isCall metadata - 带元数据的函数调用
    if ((node.kind === ASTNodeKind.FUNCTION_CALL) || 
        (node.kind === ASTNodeKind.FUNCTION && node.metadata?.isCall)) {
      
      if (node.name) {
        // 通过全局函数索引判断是否为宏调用
        const isMacroCall = this.isMacroCallByGlobalIndex(node.name);
        
        // 查找被调用的函数/宏定义
        const calleeId = this.findFunctionNode(node.name, module.path);
        if (calleeId) {
          this.addEdge(callerId, calleeId, 'calls');
          
          // 更新节点的元数据，标记正确的类型
          if (isMacroCall) {
            node.metadata = {
              ...node.metadata,
              type: 'macro',
              isMacroCall: true
            };
          }
        }
      }
    }

    // 递归处理子节点
    if (node.children) {
      for (const child of node.children) {
        this.processFunctionBody(child, callerId, module);
      }
    }
  }

  /**
   * 构建节点映射
   */
  private buildNodeMap(node: ASTNode): void {
    if (node.name) {
      this.nodeMap.set(node.name, node);
    }
    if (node.children) {
      for (const child of node.children) {
        this.buildNodeMap(child);
      }
    }
  }

  /**
   * 生成节点ID
   * 使用统一的kind字段屏蔽语言差异，而不是使用type字段
   * @param name 节点名称
   * @param modulePath 模块路径
   * @param kind 统一的节点类型（来自ASTNodeKind）
   */
  private generateNodeId(name: string, modulePath: string, kind: string): string {
    return `${modulePath}::${kind}::${name}`;
  }

  private processAstNode(node: ASTNode, module: CodeModule, moduleEntryId: string): void {
    // 处理函数定义
    if (node.kind === ASTNodeKind.FUNCTION_DEFINITION && node.name) {
      const nodeId = this.generateNodeId(node.name, module.path, ASTNodeKind.FUNCTION);
      const functionNode: GraphCodeNode = {
        id: nodeId,
        name: node.name,
        type: 'function',
        language: node.language || module.language,
        kind: 'function',
        file: module.path,
        module: module.path,
        location: node.location,
        metadata: {
          ...node.metadata,
          definition: node.text,
          parameters: node.metadata?.parameters || []
        },
        definition: node.text
      };
      this.addNode(functionNode);
      module.nodeIds.add(nodeId);

      // 添加到全局函数索引
      if (!this.globalFunctionIndex.has(node.name)) {
        this.globalFunctionIndex.set(node.name, []);
      }
      this.globalFunctionIndex.get(node.name)!.push({
        modulePath: module.path,
        nodeId: nodeId
      });

      // 添加从模块入口到函数的边
      this.addEdge(moduleEntryId, nodeId, 'contains');
    }

    // 处理变量声明
    else if (node.kind === ASTNodeKind.VARIABLE_DECLARATION && node.name) {
      const nodeId = this.generateNodeId(node.name, module.path, ASTNodeKind.VARIABLE);
      const variableNode: GraphCodeNode = {
        id: nodeId,
        name: node.name,
        type: 'variable',
        language: node.language || module.language,
        kind: 'variable',
        file: module.path,
        module: module.path,
        location: node.location,
        metadata: {
          ...node.metadata,
          definition: node.text
        },
        definition: node.text
      };
      this.addNode(variableNode);
      module.nodeIds.add(nodeId);

      // 添加从模块入口到变量的边
      this.addEdge(moduleEntryId, nodeId, 'contains');
    }

    // 处理类型定义
    else if (node.kind === ASTNodeKind.TYPE_DEFINITION && node.name) {
      const nodeId = this.generateNodeId(node.name, module.path, ASTNodeKind.TYPE);
      const typeNode: GraphCodeNode = {
        id: nodeId,
        name: node.name,
        type: 'type',
        language: node.language || module.language,
        kind: 'type',
        file: module.path,
        module: module.path,
        location: node.location,
        metadata: {
          ...node.metadata,
          definition: node.text
        },
        definition: node.text
      };
      this.addNode(typeNode);
      module.nodeIds.add(nodeId);

      // 添加从模块入口到类型的边
      this.addEdge(moduleEntryId, nodeId, 'contains');
    }

    // 递归处理子节点
    if (node.children) {
      for (const child of node.children) {
        this.processAstNode(child, module, moduleEntryId);
      }
    }
  }

  addModule(module: CodeModule): void {
    this.modules.set(module.path, module);
  }

  addNode(node: GraphCodeNode): void {
    this.graph.addNode(node);
  }

  addEdge(from: string, to: string, type: string = 'calls'): void {
    this.graph.addEdge(from, to, type);
  }

  getGraph(): GraphlibCodeGraph {
    return this.graph;
  }

  getModules(): Map<string, CodeModule> {
    return this.modules;
  }

  getGlobalFunctionIndex(): Map<string, Array<{modulePath: string, nodeId: string}>> {
    return this.globalFunctionIndex;
  }

  getModuleImports(): Map<string, Set<string>> {
    return this.moduleImports;
  }
} 