/**
 * 创建一个延迟指定毫秒数的 Promise
 * @param ms 延迟的毫秒数
 * @returns Promise<void>
 */
export function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * 带随机延迟的 sleep 函数，用于模拟真实场景
 * @param minMs 最小延迟毫秒数
 * @param maxMs 最大延迟毫秒数
 * @returns Promise<void>
 */
export function randomSleep(minMs: number, maxMs: number): Promise<void> {
  const delay = Math.random() * (maxMs - minMs) + minMs;
  return sleep(delay);
}
