import * as vscode from 'vscode';

export function textToolEscapeString(ctx:vscode.ExtensionContext){
    return ()=>{
        const panel = vscode.window.createWebviewPanel(
            'customPopup', // 面板 ID
            '自定义弹出框', // 面板标题
            vscode.ViewColumn.One, // 显示位置
            {
                enableScripts: true // 允许在 Webview 中运行脚本
            }
        );
    
        // 设置 Webview 内容
        panel.webview.html = getWebviewContent();
    
        // 处理来自 Webview 的消息
        panel.webview.onDidReceiveMessage(message => {
            switch (message.command) {
                case 'close':
                    panel.dispose();
                    break;
            }
        }, undefined, ctx.subscriptions);
    };
}

function getWebviewContent() {
    return `
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <style>
                body {
                    font-family: Arial, sans-serif;
                    background-color: #f4f4f4;
                    padding: 20px;
                    border-radius: 5px;
                    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
                }
                h1 {
                    color: #333;
                }
                button {
                    background-color: #007BFF;
                    color: white;
                    padding: 10px 20px;
                    border: none;
                    border-radius: 5px;
                    cursor: pointer;
                }
                button:hover {
                    background-color: #0056b3;
                }
            </style>
        </head>
        <body>
            <h1>自定义弹出框</h1>
            <p>这是一个自定义样式的弹出框。</p>
            <button id="closeButton">关闭</button>
            <script>
                const vscode = acquireVsCodeApi();
                const closeButton = document.getElementById('closeButton');
                closeButton.addEventListener('click', () => {
                    vscode.postMessage({
                        command: 'close'
                    });
                });
            </script>
        </body>
        </html>
    `;
}

export function deactivate() {}    