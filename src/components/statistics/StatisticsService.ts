import axios, { AxiosRequestHeaders } from 'axios';
import * as vscode from 'vscode';
import { ConfigurationManager } from '../../common/config/ConfigurationManager';
import { Logger } from '../../common/log/logger';
import {
    Context,
    IStatisticsService,
    Scope,
    StatisticsEvent,
    StatisticsEventData,
    StatisticsEventType,
    StatisticsResponse,
    UserInfo,
} from '../types/statistics';
import { getCurrentUser } from '../user/UserService';


/**
 * 统计服务实现
 * 负责收集和上报续写相关的统计信息
 */
export class StatisticsService implements IStatisticsService {
    private static _instance: StatisticsService;
    private logger: Logger;
    private sessionId: string;
    private endpoint: string;
    private configManager: ConfigurationManager;

    private constructor() {
        this.logger = new Logger('StatisticsService');
        this.sessionId = this.generateSessionId();
        this.configManager = ConfigurationManager.getInstance();
        // 从配置中读取统计上报端点
        this.endpoint = this.configManager.get('statisticsEndpoint');
    }

    public static getInstance(): StatisticsService {
        if (!this._instance) {
            this._instance = new StatisticsService();
        }
        return this._instance;
    }

    /**
     * 生成会话ID
     */
    private generateSessionId(): string {
        return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * 获取当前用户信息
     */
    private async getCurrentUserInfo(): Promise<UserInfo> {
        try {
            const user = await getCurrentUser();
            let userId = 'unknown';
            if (user && user.info) {
                if (user.info.name && user.info.name !== '') {
                    userId = user.info.name;
                } else if (user.info.id && user.info.id !== '') {
                    userId = user.info.id;
                } else if (user.info.nickname && user.info.nickname !== '') {
                    userId = user.info.nickname;
                }
                return {
                    name: user.info.name || user.info.id || user.info.nickname || 'unknown',
                    nickname: user.info.nickname || 'unknown',
                    department: user.info.department || '',
                    userType: user.type
                };
            }
            return {
                name: 'unknown',
                nickname: 'unknown',
                department: '',
                userType: 'unknown'
            };
        } catch (error) {
            this.logger.error('获取用户信息失败:', error);
            return {
                name: 'unknown',
                nickname: 'unknown',
                department: '',
                userType: 'unknown'
            };
        }
    }

    /**
     * 从文档和位置信息构建作用域信息
     */
    private buildScope(document: vscode.TextDocument, position: vscode.Position): Scope {
        const filePath = document.uri.fsPath;
        const language = document.languageId;

        // 尝试获取函数名和类名（简化实现）
        let functionName: string | undefined;
        let className: string | undefined;
        let moduleName: string | undefined;

        try {
            // 获取当前行的文本
            const lineText = document.lineAt(position.line).text;

            // 简单的函数名提取（可以根据需要扩展）
            const functionMatch = lineText.match(/(?:function|def|func)\s+(\w+)/);
            if (functionMatch) {
                functionName = functionMatch[1];
            }

            // 简单的类名提取
            const classMatch = lineText.match(/class\s+(\w+)/);
            if (classMatch) {
                className = classMatch[1];
            }

            // 模块名可以从文件路径提取
            const pathParts = filePath.split('/');
            if (pathParts.length > 1) {
                moduleName = pathParts[pathParts.length - 2];
            }
        } catch (error) {
            this.logger.warn('提取作用域信息失败:', error);
        }

        return {
            filePath,
            language,
            functionName,
            className,
            moduleName
        };
    }

    /**
     * 构建上下文信息
     */
    private buildContext(document: vscode.TextDocument, position: vscode.Position): Context {
        let beforeCursor = '';
        let afterCursor = '';

        try {
            // 获取光标前的内容
            const beforeRange = new vscode.Range(new vscode.Position(0, 0), position);
            beforeCursor = document.getText(beforeRange);

            // 获取光标后的内容
            const afterRange = new vscode.Range(position, new vscode.Position(document.lineCount - 1, document.lineAt(document.lineCount - 1).text.length));
            afterCursor = document.getText(afterRange);

            return {
                beforeCursor,
                afterCursor,
                relatedCodeSnippets: [],
                similarCodeSnippets: [],
                environments: []
            };
        } catch (error) {
            this.logger.warn('构建上下文信息失败:', error);
            return {
                beforeCursor: '',
                afterCursor: '',
                relatedCodeSnippets: [],
                similarCodeSnippets: [],
                environments: []
            };
        }
    }

    /**
     * 异步上报事件到后端
     */
    private async reportEventInternal(event: StatisticsEvent): Promise<StatisticsResponse> {
        try {
            this.logger.info(`上报事件: ${event.eventType}`, {
                sessionId: event.sessionId,
                timestamp: event.timestamp
            });

            const response = await axios({
                method: 'POST',
                url: this.endpoint,
                headers: {
                    'Content-Type': 'application/json'
                } as AxiosRequestHeaders,
                data: event
            });

            const responseData = response.data as StatisticsResponse;
            this.logger.info('事件上报成功', responseData);
            return responseData;
        } catch (error) {
            this.logger.error('事件上报失败:', error);
            // 返回错误响应
            return {
                success: false,
                message: error instanceof Error ? error.message : 'Unknown error',
                request_id: `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                timestamp: new Date().toISOString(),
                error_code: 'NETWORK_ERROR',
                error_details: { error: error instanceof Error ? error.message : 'Unknown error' }
            };
        }
    }

    /**
     * 上报统计事件
     */
    public async reportEvent(event: StatisticsEvent): Promise<StatisticsResponse> {
        return this.reportEventInternal(event);
    }

    /**
     * 上报续写触发事件
     */
    public async reportCompletionTriggered(
        scope: Scope,
        context: Context,
        prompt?: string,
        response?: string,
        config?: any
    ): Promise<void> {
        const userInfo = await this.getCurrentUserInfo();

        const eventData: StatisticsEventData & { userId: string } = {
            userId: userInfo.name,
            userInfo,
            scope,
            context,
            prompt,
            response,
            config
        };

        const event: StatisticsEvent = {
            userId: userInfo.name,
            eventType: StatisticsEventType.COMPLETION_TRIGGERED,
            timestamp: Date.now(),
            sessionId: this.sessionId,
            data: eventData
        };

        await this.reportEventInternal(event);
    }

    /**
     * 上报续写响应事件（新增）
     */
    public async reportCompletionResponse(
        scope: Scope,
        context: Context,
        prompt: string,
        response: string,
        config: any
    ): Promise<void> {
        const userInfo = await this.getCurrentUserInfo();

        const eventData: StatisticsEventData & { userId: string } = {
            userId: userInfo.name,
            userInfo,
            scope,
            context,
            prompt,
            response,
            config
        };

        const event: StatisticsEvent = {
            userId: userInfo.name,
            eventType: StatisticsEventType.COMPLETION_TRIGGERED, // 使用相同的事件类型，但包含响应内容
            timestamp: Date.now(),
            sessionId: this.sessionId,
            data: eventData
        };

        await this.reportEventInternal(event);
    }

    /**
     * 上报续写反馈事件
     */
    public async reportCompletionFeedback(
        eventType: StatisticsEventType.COMPLETION_ACCEPTED | StatisticsEventType.COMPLETION_REJECTED,
        scope: Scope,
        context: Context,
        suggestionText: string,
        originalText?: string,
        feedback?: string
    ): Promise<void> {
        const userInfo = await this.getCurrentUserInfo();

        const eventData: StatisticsEventData & { userId: string } = {
            userId: userInfo.name,
            userInfo,
            scope,
            context,
            suggestionText,
            originalText,
            feedback
        };

        const event: StatisticsEvent = {
            userId: userInfo.name,
            eventType: eventType,
            timestamp: Date.now(),
            sessionId: this.sessionId,
            data: eventData
        };

        await this.reportEventInternal(event);
    }

    /**
     * 便捷方法：从当前编辑器状态上报续写触发事件
     */
    public async reportCurrentCompletionTriggered(
        prompt?: string,
        response?: string,
        config?: any
    ): Promise<void> {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            this.logger.warn('没有活动的编辑器');
            return;
        }

        const document = editor.document;
        const position = editor.selection.active;

        const scope = this.buildScope(document, position);
        const context = this.buildContext(document, position);

        await this.reportCompletionTriggered(scope, context, prompt, response, config);
    }

    /**
     * 便捷方法：从当前编辑器状态上报续写反馈事件
     */
    public async reportCurrentCompletionFeedback(
        eventType: StatisticsEventType.COMPLETION_ACCEPTED | StatisticsEventType.COMPLETION_REJECTED,
        suggestionText: string,
        originalText?: string,
        feedback?: string
    ): Promise<void> {
        try {
            const editor = vscode.window.activeTextEditor;
            if (!editor) {
                this.logger.warn('没有活动的编辑器');
                return;
            }

            const document = editor.document;
            const position = editor.selection.active;

            const scope = this.buildScope(document, position);
            const context = this.buildContext(document, position);

            await this.reportCompletionFeedback(
                eventType,
                scope,
                context,
                suggestionText,
                originalText,
                feedback
            );
        } catch (error) {
            this.logger.error('上报补全反馈失败:', error);
        }
    }
} 