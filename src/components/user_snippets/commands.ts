import * as fs from 'fs';
import * as path from 'path';
import * as vscode from 'vscode';
import { Logger } from '../../common/log/logger';
import { UserCodeSnippet, UserRule } from '../types/user_snippets';
import { UserCustomContentManager } from './UserCustomContentManager';

const logger = new Logger('UserSnippetsCommands');

/**
 * 显示用户自定义内容管理面板
 */
export async function showUserCustomContentPanel(context: vscode.ExtensionContext): Promise<void> {
    try {
        const manager = UserCustomContentManager.getInstance();
        const snippets = await manager.getAllSnippets();
        const rules = await manager.getAllRules();

        // 创建WebView面板
        const panel = vscode.window.createWebviewPanel(
            'userCustomContent',
            '用户自定义内容管理',
            vscode.ViewColumn.One,
            {
                enableScripts: true,
                retainContextWhenHidden: true
            }
        );

        // 生成HTML内容
        panel.webview.html = generateWebviewContent(panel.webview, context, snippets, rules);

        // 处理消息
        panel.webview.onDidReceiveMessage(
            async message => {
                try {
                    switch (message.command) {
                        case 'addSnippet':
                            await handleAddSnippet(manager, message.data);
                            break;
                        case 'updateSnippet':
                            await handleUpdateSnippet(manager, message.data);
                            break;
                        case 'confirmDeleteSnippet':
                            await handleConfirmDeleteSnippet(manager, message.data);
                            break;
                        case 'deleteSnippet':
                            await handleDeleteSnippet(manager, message.data);
                            break;
                        case 'addRule':
                            await handleAddRule(manager, message.data);
                            break;
                        case 'updateRule':
                            await handleUpdateRule(manager, message.data);
                            break;
                        case 'confirmDeleteRule':
                            await handleConfirmDeleteRule(manager, message.data);
                            break;
                        case 'deleteRule':
                            await handleDeleteRule(manager, message.data);
                            break;
                        case 'exportConfig':
                            await handleExportConfig(manager);
                            break;
                        case 'importConfig':
                            await handleImportConfig(manager);
                            break;
                    }
                    // 刷新面板内容
                    const updatedSnippets = await manager.getAllSnippets();
                    const updatedRules = await manager.getAllRules();
                    panel.webview.postMessage({
                        command: 'refresh',
                        snippets: updatedSnippets,
                        rules: updatedRules
                    });
                } catch (error) {
                    logger.error('处理消息失败:', error);
                    vscode.window.showErrorMessage(`操作失败: ${error}`);
                }
            }
        );
    } catch (error) {
        logger.error('显示用户自定义内容面板失败:', error);
        vscode.window.showErrorMessage('显示面板失败');
    }
}

/**
 * 快速添加代码片段
 */
export async function quickAddSnippet(): Promise<void> {
    try {
        const name = await vscode.window.showInputBox({
            prompt: '请输入代码片段名称',
            placeHolder: '例如：快速排序函数'
        });

        if (!name) {return;}

        const description = await vscode.window.showInputBox({
            prompt: '请输入代码片段描述（可选）',
            placeHolder: '描述这个代码片段的用途'
        });

        const language = await vscode.window.showQuickPick(
            ['javascript', 'typescript', 'python', 'java', 'c', 'cpp', 'csharp', 'php', 'go', 'rust', '*'],
            {
                placeHolder: '选择适用语言（*表示通用）'
            }
        );

        if (!language) {return;}

        const tags = await vscode.window.showInputBox({
            prompt: '请输入标签（用逗号分隔）',
            placeHolder: '例如：排序,算法,快速排序'
        });

        const priority = await vscode.window.showQuickPick(
            ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10'],
            {
                placeHolder: '选择优先级（1-10，10最高）'
            }
        );

        if (!priority) {return;}

        // 打开临时文件让用户编辑代码
        const document = await vscode.workspace.openTextDocument({
            content: '// 请在此处编写代码片段\n// 完成后保存并关闭此文件',
            language: language === '*' ? 'plaintext' : language
        });

        const editor = await vscode.window.showTextDocument(document);

        // 监听文件保存事件
        const disposable = vscode.workspace.onDidSaveTextDocument(async (savedDoc) => {
            if (savedDoc === document) {
                disposable.dispose();
                
                const code = document.getText();
                if (code.trim()) {
                    const manager = UserCustomContentManager.getInstance();
                    await manager.addSnippet({
                        name,
                        description: description || undefined,
                        code,
                        language,
                        tags: tags ? tags.split(',').map(t => t.trim()) : [],
                        priority: parseInt(priority),
                        isEnabled: true
                    });

                    vscode.window.showInformationMessage(`✅ 代码片段 "${name}" 添加成功`);
                    
                    // 关闭临时文件
                    await vscode.commands.executeCommand('workbench.action.closeActiveEditor');
                }
            }
        });
    } catch (error) {
        logger.error('快速添加代码片段失败:', error);
        vscode.window.showErrorMessage('添加代码片段失败');
    }
}

/**
 * 快速添加规则
 */
export async function quickAddRule(): Promise<void> {
    try {
        const name = await vscode.window.showInputBox({
            prompt: '请输入规则名称',
            placeHolder: '例如：代码风格规范'
        });

        if (!name) {return;}

        const description = await vscode.window.showInputBox({
            prompt: '请输入规则描述（可选）',
            placeHolder: '描述这个规则的用途'
        });

        const language = await vscode.window.showQuickPick(
            ['javascript', 'typescript', 'python', 'java', 'c', 'cpp', 'csharp', 'php', 'go', 'rust', '*'],
            {
                placeHolder: '选择适用语言（*表示通用）'
            }
        );

        if (!language) {return;}

        const priority = await vscode.window.showQuickPick(
            ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10'],
            {
                placeHolder: '选择优先级（1-10，10最高）'
            }
        );

        if (!priority) {return;}

        const content = await vscode.window.showInputBox({
            prompt: '请输入规则内容',
            placeHolder: '例如：使用有意义的变量名，避免使用单字母变量'
        });

        if (!content) {return;}

        const manager = UserCustomContentManager.getInstance();
        await manager.addRule({
            name,
            description: description || undefined,
            content,
            language: language === '*' ? undefined : language,
            priority: parseInt(priority),
            isEnabled: true
        });

        vscode.window.showInformationMessage(`✅ 规则 "${name}" 添加成功`);
    } catch (error) {
        logger.error('快速添加规则失败:', error);
        vscode.window.showErrorMessage('添加规则失败');
    }
}

/**
 * 导出配置
 */
export async function exportUserCustomConfig(): Promise<void> {
    try {
        const manager = UserCustomContentManager.getInstance();
        const configJson = await manager.exportConfig();
        
        // 保存到文件
        const uri = await vscode.window.showSaveDialog({
            filters: {
                'JSON Files': ['json']
            }
        });

        if (uri) {
            await vscode.workspace.fs.writeFile(uri, Buffer.from(configJson, 'utf-8'));
            vscode.window.showInformationMessage('✅ 配置导出成功');
        }
    } catch (error) {
        logger.error('导出配置失败:', error);
        vscode.window.showErrorMessage('导出配置失败');
    }
}

/**
 * 导入配置
 */
export async function importUserCustomConfig(): Promise<void> {
    try {
        const uris = await vscode.window.showOpenDialog({
            canSelectFiles: true,
            canSelectFolders: false,
            canSelectMany: false,
            filters: {
                'JSON Files': ['json']
            }
        });

        if (uris && uris.length > 0) {
            const fileContent = await vscode.workspace.fs.readFile(uris[0]);
            const configJson = fileContent.toString();
            
            const manager = UserCustomContentManager.getInstance();
            await manager.importConfig(configJson);
            
            vscode.window.showInformationMessage('✅ 配置导入成功');
        }
    } catch (error) {
        logger.error('导入配置失败:', error);
        vscode.window.showErrorMessage('导入配置失败');
    }
}

// ==================== 私有辅助函数 ====================

function generateWebviewContent(
    webview: vscode.Webview,
    context: vscode.ExtensionContext,
    snippets: UserCodeSnippet[],
    rules: UserRule[]
): string {
    // 读取HTML模板文件
    const templatePath = path.join(context.extensionPath, 'src', 'components', 'user_snippets', 'ui', 'index.html');
    let htmlContent = fs.readFileSync(templatePath, 'utf-8');
    
    // 生成代码片段HTML
    const snippetsContent = snippets.length === 0 ? `
        <div class="empty-state">
            <h3>暂无代码片段</h3>
            <p>点击"添加代码片段"按钮开始创建您的第一个代码片段</p>
        </div>
    ` : snippets.map(snippet => `
        <div class="item">
            <div class="item-header">
                <div>
                    <div class="item-title">${snippet.name}</div>
                    <div class="item-meta">
                        语言: ${snippet.language} | 优先级: ${snippet.priority} | 
                        标签: ${snippet.tags.join(', ') || '无'} | 
                        状态: ${snippet.isEnabled ? '启用' : '禁用'}
                    </div>
                </div>
                <div>
                    <button class="btn btn-primary edit-snippet-btn" data-id="${snippet.id}">编辑</button>
                    <button class="btn btn-danger delete-snippet-btn" data-id="${snippet.id}">删除</button>
                </div>
            </div>
            ${snippet.description ? `<div style="margin-bottom: 10px; color: #6c757d;">${snippet.description}</div>` : ''}
            <div class="item-content">${escapeHtml(snippet.code || '')}</div>
        </div>
    `).join('');
    
    // 生成规则HTML
    const rulesContent = rules.length === 0 ? `
        <div class="empty-state">
            <h3>暂无规则</h3>
            <p>点击"添加规则"按钮开始创建您的第一个规则</p>
        </div>
    ` : rules.map(rule => `
        <div class="item">
            <div class="item-header">
                <div>
                    <div class="item-title">${rule.name}</div>
                    <div class="item-meta">
                        语言: ${rule.language || '*'} | 优先级: ${rule.priority} | 
                        状态: ${rule.isEnabled ? '启用' : '禁用'}
                    </div>
                </div>
                <div>
                    <button class="btn btn-primary edit-rule-btn" data-id="${rule.id}">编辑</button>
                    <button class="btn btn-danger delete-rule-btn" data-id="${rule.id}">删除</button>
                </div>
            </div>
            ${rule.description ? `<div style="margin-bottom: 10px; color: #6c757d;">${rule.description}</div>` : ''}
            <div class="item-content">${escapeHtml(rule.content || '')}</div>
        </div>
    `).join('');
    
    // 替换模板中的占位符
    htmlContent = htmlContent
        .replace('{{snippetsCount}}', snippets.length.toString())
        .replace('{{rulesCount}}', rules.length.toString())
        .replace('{{snippetsContent}}', snippetsContent)
        .replace('{{rulesContent}}', rulesContent)
        .replace('{{snippetsData}}', JSON.stringify(snippets).replace(/\\/g, '\\\\').replace(/'/g, "\\'").replace(/\n/g, '\\n').replace(/\r/g, '\\r').replace(/\t/g, '\\t'))
        .replace('{{rulesData}}', JSON.stringify(rules).replace(/\\/g, '\\\\').replace(/'/g, "\\'").replace(/\n/g, '\\n').replace(/\r/g, '\\r').replace(/\t/g, '\\t'));
    
    return htmlContent;
}

function escapeHtml(text: string): string {
    return text
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#39;');
}

async function handleAddSnippet(manager: UserCustomContentManager, data: any): Promise<void> {
    await manager.addSnippet(data);
    vscode.window.showInformationMessage('✅ 代码片段添加成功');
}

async function handleUpdateSnippet(manager: UserCustomContentManager, data: any): Promise<void> {
    await manager.updateSnippet(data.id, data);
    vscode.window.showInformationMessage('✅ 代码片段更新成功');
}

async function handleConfirmDeleteSnippet(manager: UserCustomContentManager, data: any): Promise<void> {
    const result = await vscode.window.showWarningMessage(
        `确定要删除代码片段 "${data.name}" 吗？`,
        { modal: true },
        '确定删除',
        '取消'
    );
    
    if (result === '确定删除') {
        logger.info(`用户确认删除代码片段，ID: ${data.id}`);
        const deleteResult = await manager.deleteSnippet(data.id);
        if (deleteResult) {
            vscode.window.showInformationMessage('✅ 代码片段删除成功');
        } else {
            vscode.window.showErrorMessage('❌ 代码片段删除失败：未找到指定片段');
        }
    } else {
        logger.info('用户取消删除代码片段');
    }
}

async function handleDeleteSnippet(manager: UserCustomContentManager, data: any): Promise<void> {
    logger.info(`收到删除代码片段请求，ID: ${data.id}`);
    const result = await manager.deleteSnippet(data.id);
    if (result) {
        vscode.window.showInformationMessage('✅ 代码片段删除成功');
    } else {
        vscode.window.showErrorMessage('❌ 代码片段删除失败：未找到指定片段');
    }
}

async function handleAddRule(manager: UserCustomContentManager, data: any): Promise<void> {
    await manager.addRule(data);
    vscode.window.showInformationMessage('✅ 规则添加成功');
}

async function handleUpdateRule(manager: UserCustomContentManager, data: any): Promise<void> {
    await manager.updateRule(data.id, data);
    vscode.window.showInformationMessage('✅ 规则更新成功');
}

async function handleConfirmDeleteRule(manager: UserCustomContentManager, data: any): Promise<void> {
    const result = await vscode.window.showWarningMessage(
        `确定要删除规则 "${data.name}" 吗？`,
        { modal: true },
        '确定删除',
        '取消'
    );
    
    if (result === '确定删除') {
        logger.info(`用户确认删除规则，ID: ${data.id}`);
        const deleteResult = await manager.deleteRule(data.id);
        if (deleteResult) {
            vscode.window.showInformationMessage('✅ 规则删除成功');
        } else {
            vscode.window.showErrorMessage('❌ 规则删除失败：未找到指定规则');
        }
    } else {
        logger.info('用户取消删除规则');
    }
}

async function handleDeleteRule(manager: UserCustomContentManager, data: any): Promise<void> {
    logger.info(`收到删除规则请求，ID: ${data.id}`);
    const result = await manager.deleteRule(data.id);
    if (result) {
        vscode.window.showInformationMessage('✅ 规则删除成功');
    } else {
        vscode.window.showErrorMessage('❌ 规则删除失败：未找到指定规则');
    }
}

async function handleExportConfig(manager: UserCustomContentManager): Promise<void> {
    await exportUserCustomConfig();
}

async function handleImportConfig(manager: UserCustomContentManager): Promise<void> {
    await importUserCustomConfig();
} 