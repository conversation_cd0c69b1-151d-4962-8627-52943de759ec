import * as fs from 'fs/promises';
import * as path from 'path';
import * as vscode from 'vscode';
import { Logger } from '../../common/log/logger';
import {
    IUserCustomContentManager,
    RuleMatchResult,
    SnippetMatchResult,
    UserCodeSnippet,
    UserCustomConfig,
    UserRule
} from '../types/user_snippets';

/**
 * 用户自定义内容管理器实现
 */
export class UserCustomContentManager implements IUserCustomContentManager {
  private static instance: UserCustomContentManager;
  private logger: Logger;
  private configPath: string;
  private config: UserCustomConfig;
  private readonly CONFIG_VERSION = '1.0.0';

  private constructor() {
    this.logger = new Logger('UserCustomContentManager');
    this.configPath = this.getConfigPath();
    this.config = this.getDefaultConfig();
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): UserCustomContentManager {
    if (!UserCustomContentManager.instance) {
      UserCustomContentManager.instance = new UserCustomContentManager();
    }
    return UserCustomContentManager.instance;
  }

  /**
   * 获取配置文件路径
   */
  private getConfigPath(): string {
    const workspaceFolders = vscode.workspace.workspaceFolders;
    if (workspaceFolders && workspaceFolders.length > 0) {
      return path.join(workspaceFolders[0].uri.fsPath, '.vscode', 'user-snippets.json');
    }
    // 如果没有工作区，使用全局配置
    return path.join(vscode.env.appRoot, 'user-snippets.json');
  }

  /**
   * 获取默认配置
   */
  private getDefaultConfig(): UserCustomConfig {
    return {
      snippets: [],
      rules: [],
      version: this.CONFIG_VERSION,
      lastUpdated: Date.now()
    };
  }

  /**
   * 生成唯一ID
   */
  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  /**
   * 计算文本相似度（简单的Jaccard相似度）
   */
  private calculateSimilarity(text1: string, text2: string): number {
    const words1 = new Set(text1.toLowerCase().split(/\s+/));
    const words2 = new Set(text2.toLowerCase().split(/\s+/));
    
    const intersection = new Set([...words1].filter(x => words2.has(x)));
    const union = new Set([...words1, ...words2]);
    
    return intersection.size / union.size;
  }

  // ==================== 代码片段管理 ====================

  async addSnippet(snippet: Omit<UserCodeSnippet, 'id' | 'createdAt' | 'updatedAt'>): Promise<UserCodeSnippet> {
    const newSnippet: UserCodeSnippet = {
      ...snippet,
      id: this.generateId(),
      createdAt: Date.now(),
      updatedAt: Date.now()
    };

    await this.loadConfig();
    this.config.snippets.push(newSnippet);
    this.config.lastUpdated = Date.now();
    await this.saveConfig(this.config);

    this.logger.info(`添加代码片段: ${newSnippet.name}`);
    return newSnippet;
  }

  async updateSnippet(id: string, updates: Partial<UserCodeSnippet>): Promise<UserCodeSnippet> {
    await this.loadConfig();
    const index = this.config.snippets.findIndex(s => s.id === id);
    
    if (index === -1) {
      throw new Error(`代码片段不存在: ${id}`);
    }

    this.config.snippets[index] = {
      ...this.config.snippets[index],
      ...updates,
      updatedAt: Date.now()
    };

    this.config.lastUpdated = Date.now();
    await this.saveConfig(this.config);

    this.logger.info(`更新代码片段: ${this.config.snippets[index].name}`);
    return this.config.snippets[index];
  }

  async deleteSnippet(id: string): Promise<boolean> {
    await this.loadConfig();
    const index = this.config.snippets.findIndex(s => s.id === id);
    
    if (index === -1) {
      return false;
    }

    const deletedSnippet = this.config.snippets.splice(index, 1)[0];
    this.config.lastUpdated = Date.now();
    await this.saveConfig(this.config);

    this.logger.info(`删除代码片段: ${deletedSnippet.name}`);
    return true;
  }

  async getSnippet(id: string): Promise<UserCodeSnippet | null> {
    await this.loadConfig();
    return this.config.snippets.find(s => s.id === id) || null;
  }

  async getAllSnippets(): Promise<UserCodeSnippet[]> {
    await this.loadConfig();
    return [...this.config.snippets];
  }

  async getEnabledSnippets(): Promise<UserCodeSnippet[]> {
    await this.loadConfig();
    return this.config.snippets.filter(s => s.isEnabled);
  }

  // ==================== 规则管理 ====================

  async addRule(rule: Omit<UserRule, 'id' | 'createdAt' | 'updatedAt'>): Promise<UserRule> {
    const newRule: UserRule = {
      ...rule,
      id: this.generateId(),
      createdAt: Date.now(),
      updatedAt: Date.now()
    };

    await this.loadConfig();
    this.config.rules.push(newRule);
    this.config.lastUpdated = Date.now();
    await this.saveConfig(this.config);

    this.logger.info(`添加规则: ${newRule.name}`);
    return newRule;
  }

  async updateRule(id: string, updates: Partial<UserRule>): Promise<UserRule> {
    await this.loadConfig();
    const index = this.config.rules.findIndex(r => r.id === id);
    
    if (index === -1) {
      throw new Error(`规则不存在: ${id}`);
    }

    this.config.rules[index] = {
      ...this.config.rules[index],
      ...updates,
      updatedAt: Date.now()
    };

    this.config.lastUpdated = Date.now();
    await this.saveConfig(this.config);

    this.logger.info(`更新规则: ${this.config.rules[index].name}`);
    return this.config.rules[index];
  }

  async deleteRule(id: string): Promise<boolean> {
    await this.loadConfig();
    const index = this.config.rules.findIndex(r => r.id === id);
    
    if (index === -1) {
      return false;
    }

    const deletedRule = this.config.rules.splice(index, 1)[0];
    this.config.lastUpdated = Date.now();
    await this.saveConfig(this.config);

    this.logger.info(`删除规则: ${deletedRule.name}`);
    return true;
  }

  async getRule(id: string): Promise<UserRule | null> {
    await this.loadConfig();
    return this.config.rules.find(r => r.id === id) || null;
  }

  async getAllRules(): Promise<UserRule[]> {
    await this.loadConfig();
    return [...this.config.rules];
  }

  async getEnabledRules(): Promise<UserRule[]> {
    await this.loadConfig();
    return this.config.rules.filter(r => r.isEnabled);
  }

  // ==================== 匹配和检索 ====================

  async findMatchingSnippets(
    code: string, 
    language: string, 
    maxResults: number = 5
  ): Promise<SnippetMatchResult[]> {
    const enabledSnippets = await this.getEnabledSnippets();
    const languageSnippets = enabledSnippets.filter(s => 
      s.language === language || s.language === '*'
    );

    const results: SnippetMatchResult[] = [];

    for (const snippet of languageSnippets) {
      // 计算相似度
      const codeSimilarity = this.calculateSimilarity(code, snippet.code);
      
      // 计算标签匹配度
      let tagSimilarity = 0;
      if (snippet.tags.length > 0) {
        const codeWords = new Set(code.toLowerCase().split(/\s+/));
        const matchingTags = snippet.tags.filter(tag => 
          codeWords.has(tag.toLowerCase())
        );
        tagSimilarity = matchingTags.length / snippet.tags.length;
      }

      // 综合相似度
      const similarity = Math.max(codeSimilarity, tagSimilarity);
      
      if (similarity > 0) {
        const score = similarity * (snippet.priority / 10);
        results.push({
          snippet,
          similarity,
          score
        });
      }
    }

    // 按得分排序并限制结果数量
    return results
      .sort((a, b) => b.score - a.score)
      .slice(0, maxResults);
  }

  async getApplicableRules(language: string): Promise<RuleMatchResult[]> {
    const enabledRules = await this.getEnabledRules();
    
    return enabledRules.map(rule => ({
      rule,
      isApplicable: !rule.language || rule.language === language || rule.language === '*'
    }));
  }

  // ==================== 配置管理 ====================

  async loadConfig(): Promise<UserCustomConfig> {
    try {
      const configDir = path.dirname(this.configPath);
      await fs.mkdir(configDir, { recursive: true });

      const configData = await fs.readFile(this.configPath, 'utf-8');
      this.config = JSON.parse(configData);
      
      // 版本兼容性检查
      if (this.config.version !== this.CONFIG_VERSION) {
        this.logger.warn(`配置版本不匹配，当前: ${this.config.version}，期望: ${this.CONFIG_VERSION}`);
      }
      
      return this.config;
    } catch (error) {
      if ((error as any).code === 'ENOENT') {
        // 配置文件不存在，使用默认配置
        this.config = this.getDefaultConfig();
        await this.saveConfig(this.config);
        return this.config;
      }
      throw error;
    }
  }

  async saveConfig(config: UserCustomConfig): Promise<void> {
    try {
      const configDir = path.dirname(this.configPath);
      await fs.mkdir(configDir, { recursive: true });
      
      await fs.writeFile(this.configPath, JSON.stringify(config, null, 2), 'utf-8');
      this.config = config;
    } catch (error) {
      this.logger.error('保存配置失败:', error);
      throw error;
    }
  }

  async exportConfig(): Promise<string> {
    await this.loadConfig();
    return JSON.stringify(this.config, null, 2);
  }

  async importConfig(configJson: string): Promise<void> {
    try {
      const importedConfig = JSON.parse(configJson) as UserCustomConfig;
      
      // 验证配置格式
      if (!importedConfig.snippets || !importedConfig.rules) {
        throw new Error('配置格式无效');
      }

      // 更新版本和时间戳
      importedConfig.version = this.CONFIG_VERSION;
      importedConfig.lastUpdated = Date.now();

      await this.saveConfig(importedConfig);
      this.logger.info('配置导入成功');
    } catch (error) {
      this.logger.error('配置导入失败:', error);
      throw error;
    }
  }

  async resetToDefaults(): Promise<void> {
    this.config = this.getDefaultConfig();
    await this.saveConfig(this.config);
    this.logger.info('配置已重置为默认值');
  }
} 