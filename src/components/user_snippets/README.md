# 用户自定义内容管理组件

## 概述

用户自定义内容管理组件提供了代码片段和规则的管理功能，允许用户创建、编辑、删除和导入/导出自定义内容。

## 组件结构

```
src/components/user_snippets/
├── index.ts                    # 组件入口文件，导出所有公共接口
├── commands.ts                 # 命令实现文件，包含所有VSCode命令
├── UserCustomContentManager.ts # 核心管理器，处理数据持久化
├── ui/                         # UI相关文件
│   └── index.html             # HTML模板文件
└── README.md                  # 本文档
```

## 主要功能

### 1. 代码片段管理
- 添加、编辑、删除代码片段
- 支持多种编程语言
- 标签系统
- 优先级设置
- 启用/禁用状态

### 2. 规则管理
- 添加、编辑、删除规则
- 语言特定规则
- 优先级设置
- 启用/禁用状态

### 3. 配置管理
- 导出配置到JSON文件
- 从JSON文件导入配置
- 重置到默认配置

## 命令列表

| 命令 | 描述 |
|------|------|
| `showUserCustomContentPanel` | 显示用户自定义内容管理面板 |
| `quickAddSnippet` | 快速添加代码片段 |
| `quickAddRule` | 快速添加规则 |
| `exportUserCustomConfig` | 导出配置 |
| `importUserCustomConfig` | 导入配置 |

## 使用方法

### 在VSCode扩展中使用

```typescript
import { 
    showUserCustomContentPanel,
    quickAddSnippet,
    UserCustomContentManager 
} from '../components/user_snippets';

// 显示管理面板
await showUserCustomContentPanel();

// 快速添加代码片段
await quickAddSnippet();

// 直接使用管理器
const manager = UserCustomContentManager.getInstance();
const snippets = await manager.getAllSnippets();
```

### 注册命令

在`register.ts`中注册命令：

```typescript
import { 
    showUserCustomContentPanel,
    quickAddSnippet,
    quickAddRule,
    exportUserCustomConfig,
    importUserCustomConfig 
} from '../components/user_snippets';

// 注册命令
context.subscriptions.push(
    vscode.commands.registerCommand('code-partner.showUserCustomContent', showUserCustomContentPanel),
    vscode.commands.registerCommand('code-partner.quickAddSnippet', quickAddSnippet),
    vscode.commands.registerCommand('code-partner.quickAddRule', quickAddRule),
    vscode.commands.registerCommand('code-partner.exportUserCustomConfig', exportUserCustomConfig),
    vscode.commands.registerCommand('code-partner.importUserCustomConfig', importUserCustomConfig)
);
```

## 技术实现

### 1. 模板系统
- 使用独立的HTML文件作为模板
- 支持模板变量替换
- 动态内容生成

### 2. WebView通信
- 使用VSCode WebView API
- 消息传递机制
- 实时数据更新

### 3. 数据持久化
- JSON文件存储
- 单例模式管理器
- 异步操作支持

### 4. 错误处理
- 完整的错误处理机制
- 用户友好的错误提示
- 日志记录

## 开发指南

### 添加新功能

1. 在`commands.ts`中添加新的命令函数
2. 在`index.ts`中导出新命令
3. 在`register.ts`中注册新命令
4. 更新HTML模板（如需要）

### 修改UI

1. 直接编辑`ui/index.html`文件
2. 使用模板变量`{{variableName}}`进行动态内容替换
3. 在`commands.ts`的`generateWebviewContent`函数中处理模板变量

### 添加新的数据字段

1. 更新`types/user_snippets.ts`中的类型定义
2. 更新`UserCustomContentManager.ts`中的相关方法
3. 更新HTML模板中的表单字段
4. 更新命令处理函数

## 测试

运行测试：

```bash
npm test -- --testPathPatterns=UserCustomContentManager.test.ts
```

## 注意事项

1. HTML模板使用模板变量语法，如`{{snippetsCount}}`
2. 所有用户输入都需要进行HTML转义
3. 文件操作使用异步方式
4. 错误处理要完整，避免应用崩溃
5. 保持向后兼容性 