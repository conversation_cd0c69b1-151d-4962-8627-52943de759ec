<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户自定义内容管理</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif; 
            padding: 20px; 
            background: #f5f5f5;
            color: #333;
        }
        
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 24px;
            font-weight: 600;
        }
        
        .tabs { 
            display: flex; 
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }
        
        .tab { 
            padding: 15px 25px; 
            cursor: pointer; 
            border: none;
            background: transparent;
            color: #6c757d;
            font-weight: 500;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .tab:hover {
            background: #e9ecef;
            color: #495057;
        }
        
        .tab.active { 
            background: white;
            color: #667eea;
            border-bottom: 3px solid #667eea;
            font-weight: 600;
        }
        
        .tab-content { 
            display: none; 
            padding: 20px;
        }
        
        .tab-content.active { 
            display: block; 
        }
        
        .section {
            margin-bottom: 30px;
        }
        
        .section h2 { 
            color: #495057; 
            border-bottom: 2px solid #667eea; 
            padding-bottom: 10px; 
            margin-bottom: 20px;
            font-size: 20px;
        }
        
        .btn { 
            padding: 8px 16px; 
            margin: 4px; 
            border: none; 
            border-radius: 6px; 
            cursor: pointer; 
            font-weight: 500;
            transition: all 0.2s ease;
        }
        
        .btn-primary { 
            background: #667eea; 
            color: white; 
        }
        
        .btn-primary:hover {
            background: #5a6fd8;
            transform: translateY(-1px);
        }
        
        .btn-danger { 
            background: #dc3545; 
            color: white; 
        }
        
        .btn-danger:hover {
            background: #c82333;
        }
        
        .btn-success { 
            background: #28a745; 
            color: white; 
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .item { 
            border: 1px solid #e9ecef; 
            margin: 15px 0; 
            padding: 20px; 
            border-radius: 8px;
            background: white;
            transition: all 0.2s ease;
        }
        
        .item:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        
        .item-header { 
            display: flex; 
            justify-content: space-between; 
            align-items: center; 
            margin-bottom: 15px; 
        }
        
        .item-title { 
            font-weight: 600; 
            color: #667eea; 
            font-size: 18px;
        }
        
        .item-meta { 
            font-size: 13px; 
            color: #6c757d; 
            margin-top: 5px;
        }
        
        .item-content { 
            background: #f8f9fa; 
            padding: 15px; 
            border-radius: 6px; 
            font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.5;
            border-left: 4px solid #667eea;
            white-space: pre-wrap;
            word-wrap: break-word;
            overflow-x: auto;
        }
        
        .form-group { 
            margin: 15px 0; 
        }
        
        .form-group label { 
            display: block; 
            margin-bottom: 8px; 
            font-weight: 600;
            color: #495057;
        }
        
        .form-group input, .form-group textarea, .form-group select { 
            width: 100%; 
            padding: 12px; 
            border: 2px solid #e9ecef; 
            border-radius: 6px; 
            font-size: 14px;
            transition: border-color 0.2s ease;
        }
        
        .form-group input:focus, .form-group textarea:focus, .form-group select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .form-group textarea { 
            height: 120px; 
            resize: vertical; 
            font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
        }
        
        .form-row {
            display: flex;
            gap: 15px;
        }
        
        .form-row .form-group {
            flex: 1;
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        
        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 8px;
            width: 80%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #e9ecef;
        }
        
        .modal-title {
            font-size: 20px;
            font-weight: 600;
            color: #495057;
        }
        
        .close {
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            line-height: 1;
        }
        
        .close:hover {
            color: #000;
        }
        
        .tag-input {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            padding: 8px;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            min-height: 45px;
        }
        
        .tag {
            background: #667eea;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .tag-remove {
            cursor: pointer;
            font-weight: bold;
        }
        
        .tag-input input {
            border: none;
            outline: none;
            flex: 1;
            min-width: 100px;
        }
        
        .empty-state {
            text-align: center;
            padding: 40px;
            color: #6c757d;
        }
        
        .empty-state h3 {
            margin-bottom: 10px;
            color: #495057;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>用户自定义内容管理</h1>
        </div>
        
        <div class="tabs">
            <div class="tab active" onclick="switchTab('snippets')">代码片段 ({{snippetsCount}})</div>
            <div class="tab" onclick="switchTab('rules')">规则 ({{rulesCount}})</div>
            <div class="tab" onclick="switchTab('config')">配置</div>
        </div>
        
        <div id="snippets" class="tab-content active">
            <div class="section">
                <h2>代码片段管理</h2>
                <button class="btn btn-primary" onclick="showAddSnippetForm()">添加代码片段</button>
                
                <div id="snippets-list">
                    {{snippetsContent}}
                </div>
            </div>
        </div>
        
        <div id="rules" class="tab-content">
            <div class="section">
                <h2>规则管理</h2>
                <button class="btn btn-primary" onclick="showAddRuleForm()">添加规则</button>
                
                <div id="rules-list">
                    {{rulesContent}}
                </div>
            </div>
        </div>
        
        <div id="config" class="tab-content">
            <div class="section">
                <h2>配置管理</h2>
                <button class="btn btn-success" onclick="exportConfig()">导出配置</button>
                <button class="btn btn-primary" onclick="importConfig()">导入配置</button>
                <button class="btn btn-danger" onclick="resetConfig()">重置配置</button>
            </div>
        </div>
    </div>
    
    <!-- 添加代码片段模态框 -->
    <div id="addSnippetModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title">添加代码片段</div>
                <span class="close" onclick="closeModal('addSnippetModal')">&times;</span>
            </div>
            <form id="addSnippetForm">
                <div class="form-row">
                    <div class="form-group">
                        <label for="snippetName">名称 *</label>
                        <input type="text" id="snippetName" required placeholder="例如：React函数组件模板">
                    </div>
                    <div class="form-group">
                        <label for="snippetLanguage">语言 *</label>
                        <select id="snippetLanguage" required>
                            <option value="">选择语言</option>
                            <option value="javascript">JavaScript</option>
                            <option value="typescript">TypeScript</option>
                            <option value="python">Python</option>
                            <option value="java">Java</option>
                            <option value="c">C</option>
                            <option value="cpp">C++</option>
                            <option value="csharp">C#</option>
                            <option value="php">PHP</option>
                            <option value="go">Go</option>
                            <option value="rust">Rust</option>
                            <option value="*">通用</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="snippetDescription">描述</label>
                    <input type="text" id="snippetDescription" placeholder="描述这个代码片段的用途">
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="snippetPriority">优先级</label>
                        <select id="snippetPriority">
                            <option value="1">1 - 最低</option>
                            <option value="2">2</option>
                            <option value="3">3</option>
                            <option value="4">4</option>
                            <option value="5" selected>5 - 默认</option>
                            <option value="6">6</option>
                            <option value="7">7</option>
                            <option value="8">8</option>
                            <option value="9">9</option>
                            <option value="10">10 - 最高</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="snippetEnabled">状态</label>
                        <select id="snippetEnabled">
                            <option value="true" selected>启用</option>
                            <option value="false">禁用</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="snippetTags">标签</label>
                    <div class="tag-input" id="snippetTags">
                        <input type="text" placeholder="输入标签后按回车添加" onkeydown="addTag(event, 'snippetTags')">
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="snippetCode">代码内容 *</label>
                    <textarea id="snippetCode" required placeholder="在此输入代码片段内容..."></textarea>
                </div>
                
                <div style="text-align: right; margin-top: 20px;">
                    <button type="button" class="btn btn-secondary" onclick="closeModal('addSnippetModal')">取消</button>
                    <button type="submit" class="btn btn-primary">保存</button>
                </div>
            </form>
        </div>
    </div>
    
    <!-- 编辑代码片段模态框 -->
    <div id="editSnippetModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title">编辑代码片段</div>
                <span class="close" onclick="closeModal('editSnippetModal')">&times;</span>
            </div>
            <form id="editSnippetForm">
                <input type="hidden" id="editSnippetId">
                <div class="form-row">
                    <div class="form-group">
                        <label for="editSnippetName">名称 *</label>
                        <input type="text" id="editSnippetName" required placeholder="例如：React函数组件模板">
                    </div>
                    <div class="form-group">
                        <label for="editSnippetLanguage">语言 *</label>
                        <select id="editSnippetLanguage" required>
                            <option value="">选择语言</option>
                            <option value="javascript">JavaScript</option>
                            <option value="typescript">TypeScript</option>
                            <option value="python">Python</option>
                            <option value="java">Java</option>
                            <option value="c">C</option>
                            <option value="cpp">C++</option>
                            <option value="csharp">C#</option>
                            <option value="php">PHP</option>
                            <option value="go">Go</option>
                            <option value="rust">Rust</option>
                            <option value="*">通用</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="editSnippetDescription">描述</label>
                    <input type="text" id="editSnippetDescription" placeholder="描述这个代码片段的用途">
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="editSnippetPriority">优先级</label>
                        <select id="editSnippetPriority">
                            <option value="1">1 - 最低</option>
                            <option value="2">2</option>
                            <option value="3">3</option>
                            <option value="4">4</option>
                            <option value="5" selected>5 - 默认</option>
                            <option value="6">6</option>
                            <option value="7">7</option>
                            <option value="8">8</option>
                            <option value="9">9</option>
                            <option value="10">10 - 最高</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="editSnippetEnabled">状态</label>
                        <select id="editSnippetEnabled">
                            <option value="true" selected>启用</option>
                            <option value="false">禁用</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="editSnippetTags">标签</label>
                    <div class="tag-input" id="editSnippetTags">
                        <input type="text" placeholder="输入标签后按回车添加" onkeydown="addTag(event, 'editSnippetTags')">
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="editSnippetCode">代码内容 *</label>
                    <textarea id="editSnippetCode" required placeholder="在此输入代码片段内容..."></textarea>
                </div>
                
                <div style="text-align: right; margin-top: 20px;">
                    <button type="button" class="btn btn-secondary" onclick="closeModal('editSnippetModal')">取消</button>
                    <button type="submit" class="btn btn-primary">保存</button>
                </div>
            </form>
        </div>
    </div>
    
    <!-- 添加规则模态框 -->
    <div id="addRuleModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title">添加规则</div>
                <span class="close" onclick="closeModal('addRuleModal')">&times;</span>
            </div>
            <form id="addRuleForm">
                <div class="form-row">
                    <div class="form-group">
                        <label for="ruleName">名称 *</label>
                        <input type="text" id="ruleName" required placeholder="例如：JavaScript代码风格">
                    </div>
                    <div class="form-group">
                        <label for="ruleLanguage">语言</label>
                        <select id="ruleLanguage">
                            <option value="">通用</option>
                            <option value="javascript">JavaScript</option>
                            <option value="typescript">TypeScript</option>
                            <option value="python">Python</option>
                            <option value="java">Java</option>
                            <option value="c">C</option>
                            <option value="cpp">C++</option>
                            <option value="csharp">C#</option>
                            <option value="php">PHP</option>
                            <option value="go">Go</option>
                            <option value="rust">Rust</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="ruleDescription">描述</label>
                    <input type="text" id="ruleDescription" placeholder="描述这个规则的用途">
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="rulePriority">优先级</label>
                        <select id="rulePriority">
                            <option value="1">1 - 最低</option>
                            <option value="2">2</option>
                            <option value="3">3</option>
                            <option value="4">4</option>
                            <option value="5" selected>5 - 默认</option>
                            <option value="6">6</option>
                            <option value="7">7</option>
                            <option value="8">8</option>
                            <option value="9">9</option>
                            <option value="10">10 - 最高</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="ruleEnabled">状态</label>
                        <select id="ruleEnabled">
                            <option value="true" selected>启用</option>
                            <option value="false">禁用</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="ruleContent">规则内容 *</label>
                    <textarea id="ruleContent" required placeholder="在此输入规则内容，这些内容会被加入到代码续写的提示词中..."></textarea>
                </div>
                
                <div style="text-align: right; margin-top: 20px;">
                    <button type="button" class="btn btn-secondary" onclick="closeModal('addRuleModal')">取消</button>
                    <button type="submit" class="btn btn-primary">保存</button>
                </div>
            </form>
        </div>
    </div>
    
    <!-- 编辑规则模态框 -->
    <div id="editRuleModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title">编辑规则</div>
                <span class="close" onclick="closeModal('editRuleModal')">&times;</span>
            </div>
            <form id="editRuleForm">
                <input type="hidden" id="editRuleId">
                <div class="form-row">
                    <div class="form-group">
                        <label for="editRuleName">名称 *</label>
                        <input type="text" id="editRuleName" required placeholder="例如：JavaScript代码风格">
                    </div>
                    <div class="form-group">
                        <label for="editRuleLanguage">语言</label>
                        <select id="editRuleLanguage">
                            <option value="">通用</option>
                            <option value="javascript">JavaScript</option>
                            <option value="typescript">TypeScript</option>
                            <option value="python">Python</option>
                            <option value="java">Java</option>
                            <option value="c">C</option>
                            <option value="cpp">C++</option>
                            <option value="csharp">C#</option>
                            <option value="php">PHP</option>
                            <option value="go">Go</option>
                            <option value="rust">Rust</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="editRuleDescription">描述</label>
                    <input type="text" id="editRuleDescription" placeholder="描述这个规则的用途">
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="editRulePriority">优先级</label>
                        <select id="editRulePriority">
                            <option value="1">1 - 最低</option>
                            <option value="2">2</option>
                            <option value="3">3</option>
                            <option value="4">4</option>
                            <option value="5" selected>5 - 默认</option>
                            <option value="6">6</option>
                            <option value="7">7</option>
                            <option value="8">8</option>
                            <option value="9">9</option>
                            <option value="10">10 - 最高</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="editRuleEnabled">状态</label>
                        <select id="editRuleEnabled">
                            <option value="true" selected>启用</option>
                            <option value="false">禁用</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="editRuleContent">规则内容 *</label>
                    <textarea id="editRuleContent" required placeholder="在此输入规则内容，这些内容会被加入到代码续写的提示词中..."></textarea>
                </div>
                
                <div style="text-align: right; margin-top: 20px;">
                    <button type="button" class="btn btn-secondary" onclick="closeModal('editRuleModal')">取消</button>
                    <button type="submit" class="btn btn-primary">保存</button>
                </div>
            </form>
        </div>
    </div>
    
    <script>
        const vscode = acquireVsCodeApi();
        let currentTags = [];
        let editTags = [];
        let snippetsData = JSON.parse('{{snippetsData}}');
        let rulesData = JSON.parse('{{rulesData}}');
        
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
        
        function switchTab(tabName) {
            // 隐藏所有标签内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 显示选中的标签内容
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');
        }
        
        function showModal(modalId) {
            document.getElementById(modalId).style.display = 'block';
        }
        
        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
            // 重置表单
            document.getElementById(modalId).querySelector('form').reset();
            currentTags = [];
            editTags = [];
            updateTagDisplay('snippetTags');
            updateTagDisplay('editSnippetTags');
        }
        
        function showAddSnippetForm() {
            showModal('addSnippetModal');
        }
        
        function showAddRuleForm() {
            showModal('addRuleModal');
        }
        
        function addTag(event, containerId) {
            if (event.key === 'Enter') {
                event.preventDefault();
                const input = event.target;
                const tag = input.value.trim();
                
                if (tag) {
                    if (containerId === 'editSnippetTags' || containerId === 'editRuleTags') {
                        if (!editTags.includes(tag)) {
                            editTags.push(tag);
                        }
                    } else {
                        if (!currentTags.includes(tag)) {
                            currentTags.push(tag);
                        }
                    }
                    updateTagDisplay(containerId);
                }
                
                input.value = '';
            }
        }
        
        function removeTag(tag, containerId) {
            if (containerId === 'editSnippetTags' || containerId === 'editRuleTags') {
                editTags = editTags.filter(t => t !== tag);
            } else {
                currentTags = currentTags.filter(t => t !== tag);
            }
            updateTagDisplay(containerId);
        }
        
        function updateTagDisplay(containerId) {
            const container = document.getElementById(containerId);
            const input = container.querySelector('input');
            const tags = containerId === 'editSnippetTags' || containerId === 'editRuleTags' ? editTags : currentTags;
            
            // 清除现有标签
            container.innerHTML = '';
            
            // 添加标签
            tags.forEach(tag => {
                const tagElement = document.createElement('span');
                tagElement.className = 'tag';
                tagElement.innerHTML = tag + ' <span class="tag-remove" onclick="removeTag(\'' + tag + '\', \'' + containerId + '\')">&times;</span>';
                container.appendChild(tagElement);
            });
            
            // 重新添加输入框
            container.appendChild(input);
        }
        
        function editSnippet(id) {
            const snippet = snippetsData.find(s => s.id === id);
            if (snippet) {
                // 填充表单
                document.getElementById('editSnippetId').value = snippet.id;
                document.getElementById('editSnippetName').value = snippet.name;
                document.getElementById('editSnippetDescription').value = snippet.description || '';
                document.getElementById('editSnippetCode').value = snippet.code;
                document.getElementById('editSnippetLanguage').value = snippet.language;
                document.getElementById('editSnippetPriority').value = snippet.priority;
                document.getElementById('editSnippetEnabled').value = snippet.isEnabled.toString();
                
                // 设置标签
                editTags = [...snippet.tags];
                updateTagDisplay('editSnippetTags');
                
                showModal('editSnippetModal');
            }
        }
        
        function deleteSnippet(id) {
            console.log('删除代码片段，ID:', id);
            // 使用VSCode的确认对话框
            vscode.postMessage({ 
                command: 'confirmDeleteSnippet', 
                data: { id, name: snippetsData.find(s => s.id === id)?.name || '未知片段' } 
            });
        }
        
        function editRule(id) {
            const rule = rulesData.find(r => r.id === id);
            if (rule) {
                // 填充表单
                document.getElementById('editRuleId').value = rule.id;
                document.getElementById('editRuleName').value = rule.name;
                document.getElementById('editRuleDescription').value = rule.description || '';
                document.getElementById('editRuleContent').value = rule.content;
                document.getElementById('editRuleLanguage').value = rule.language || '';
                document.getElementById('editRulePriority').value = rule.priority;
                document.getElementById('editRuleEnabled').value = rule.isEnabled.toString();
                
                showModal('editRuleModal');
            }
        }
        
        function deleteRule(id) {
            console.log('删除规则，ID:', id);
            // 使用VSCode的确认对话框
            vscode.postMessage({ 
                command: 'confirmDeleteRule', 
                data: { id, name: rulesData.find(r => r.id === id)?.name || '未知规则' } 
            });
        }
        
        function exportConfig() {
            vscode.postMessage({ command: 'exportConfig' });
        }
        
        function importConfig() {
            vscode.postMessage({ command: 'importConfig' });
        }
        
        function resetConfig() {
            if (confirm('确定要重置所有配置吗？此操作不可撤销。')) {
                vscode.postMessage({ command: 'resetConfig' });
            }
        }
        
        // 表单提交处理
        document.getElementById('addSnippetForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = {
                name: document.getElementById('snippetName').value,
                description: document.getElementById('snippetDescription').value,
                code: document.getElementById('snippetCode').value,
                language: document.getElementById('snippetLanguage').value,
                tags: currentTags,
                priority: parseInt(document.getElementById('snippetPriority').value),
                isEnabled: document.getElementById('snippetEnabled').value === 'true'
            };
            
            vscode.postMessage({ command: 'addSnippet', data: formData });
            closeModal('addSnippetModal');
        });
        
        document.getElementById('editSnippetForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = {
                id: document.getElementById('editSnippetId').value,
                name: document.getElementById('editSnippetName').value,
                description: document.getElementById('editSnippetDescription').value,
                code: document.getElementById('editSnippetCode').value,
                language: document.getElementById('editSnippetLanguage').value,
                tags: editTags,
                priority: parseInt(document.getElementById('editSnippetPriority').value),
                isEnabled: document.getElementById('editSnippetEnabled').value === 'true'
            };
            
            vscode.postMessage({ command: 'updateSnippet', data: formData });
            closeModal('editSnippetModal');
        });
        
        document.getElementById('addRuleForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = {
                name: document.getElementById('ruleName').value,
                description: document.getElementById('ruleDescription').value,
                content: document.getElementById('ruleContent').value,
                language: document.getElementById('ruleLanguage').value || undefined,
                priority: parseInt(document.getElementById('rulePriority').value),
                isEnabled: document.getElementById('ruleEnabled').value === 'true'
            };
            
            vscode.postMessage({ command: 'addRule', data: formData });
            closeModal('addRuleModal');
        });
        
        document.getElementById('editRuleForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = {
                id: document.getElementById('editRuleId').value,
                name: document.getElementById('editRuleName').value,
                description: document.getElementById('editRuleDescription').value,
                content: document.getElementById('editRuleContent').value,
                language: document.getElementById('editRuleLanguage').value || undefined,
                priority: parseInt(document.getElementById('editRulePriority').value),
                isEnabled: document.getElementById('editRuleEnabled').value === 'true'
            };
            
            vscode.postMessage({ command: 'updateRule', data: formData });
            closeModal('editRuleModal');
        });
        
        // 监听来自扩展的消息
        window.addEventListener('message', event => {
            const message = event.data;
            switch (message.command) {
                case 'refresh':
                    // 更新数据并刷新显示
                    if (message.snippets) {
                        snippetsData = message.snippets;
                        updateSnippetsDisplay();
                    }
                    if (message.rules) {
                        rulesData = message.rules;
                        updateRulesDisplay();
                    }
                    break;
            }
        });
        
        function updateSnippetsDisplay() {
            const container = document.getElementById('snippets-list');
            if (snippetsData.length === 0) {
                container.innerHTML = '<div class="empty-state"><h3>暂无代码片段</h3><p>点击"添加代码片段"按钮开始创建您的第一个代码片段</p></div>';
            } else {
                container.innerHTML = snippetsData.map(snippet => 
                    '<div class="item">' +
                        '<div class="item-header">' +
                            '<div>' +
                                '<div class="item-title">' + snippet.name + '</div>' +
                                '<div class="item-meta">语言: ' + snippet.language + ' | 优先级: ' + snippet.priority + ' | 标签: ' + (snippet.tags.join(', ') || '无') + ' | 状态: ' + (snippet.isEnabled ? '启用' : '禁用') + '</div>' +
                            '</div>' +
                            '<div>' +
                                '<button class="btn btn-primary edit-snippet-btn" data-id="' + snippet.id + '">编辑</button>' +
                                '<button class="btn btn-danger delete-snippet-btn" data-id="' + snippet.id + '">删除</button>' +
                            '</div>' +
                        '</div>' +
                        (snippet.description ? '<div style="margin-bottom: 10px; color: #6c757d;">' + snippet.description + '</div>' : '') +
                        '<div class="item-content">' + (snippet.code || '').replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;').replace(/"/g, '&quot;').replace(/'/g, '&#39;') + '</div>' +
                    '</div>'
                ).join('');
                
                // 重新绑定事件
                container.querySelectorAll('.edit-snippet-btn').forEach(btn => {
                    btn.addEventListener('click', function() {
                        editSnippet(this.getAttribute('data-id'));
                    });
                });
                
                container.querySelectorAll('.delete-snippet-btn').forEach(btn => {
                    btn.addEventListener('click', function() {
                        deleteSnippet(this.getAttribute('data-id'));
                    });
                });
            }
            
            // 更新标签页计数
            const snippetsTab = document.querySelector('.tab[onclick="switchTab(\'snippets\')"]');
            if (snippetsTab) {
                snippetsTab.textContent = '代码片段 (' + snippetsData.length + ')';
            }
        }
        
        function updateRulesDisplay() {
            const container = document.getElementById('rules-list');
            if (rulesData.length === 0) {
                container.innerHTML = '<div class="empty-state"><h3>暂无规则</h3><p>点击"添加规则"按钮开始创建您的第一个规则</p></div>';
            } else {
                container.innerHTML = rulesData.map(rule => 
                    '<div class="item">' +
                        '<div class="item-header">' +
                            '<div>' +
                                '<div class="item-title">' + rule.name + '</div>' +
                                '<div class="item-meta">语言: ' + (rule.language || '*') + ' | 优先级: ' + rule.priority + ' | 状态: ' + (rule.isEnabled ? '启用' : '禁用') + '</div>' +
                            '</div>' +
                            '<div>' +
                                '<button class="btn btn-primary edit-rule-btn" data-id="' + rule.id + '">编辑</button>' +
                                '<button class="btn btn-danger delete-rule-btn" data-id="' + rule.id + '">删除</button>' +
                            '</div>' +
                        '</div>' +
                        (rule.description ? '<div style="margin-bottom: 10px; color: #6c757d;">' + rule.description + '</div>' : '') +
                        '<div class="item-content">' + (rule.content || '').replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;').replace(/"/g, '&quot;').replace(/'/g, '&#39;') + '</div>' +
                    '</div>'
                ).join('');
                
                // 重新绑定事件
                container.querySelectorAll('.edit-rule-btn').forEach(btn => {
                    btn.addEventListener('click', function() {
                        editRule(this.getAttribute('data-id'));
                    });
                });
                
                container.querySelectorAll('.delete-rule-btn').forEach(btn => {
                    btn.addEventListener('click', function() {
                        deleteRule(this.getAttribute('data-id'));
                    });
                });
            }
            
            // 更新标签页计数
            const rulesTab = document.querySelector('.tab[onclick="switchTab(\'rules\')"]');
            if (rulesTab) {
                rulesTab.textContent = '规则 (' + rulesData.length + ')';
            }
        }
        
        // 初始事件绑定
        function bindInitialEvents() {
            // 绑定代码片段按钮事件
            document.querySelectorAll('.edit-snippet-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    editSnippet(this.getAttribute('data-id'));
                });
            });
            
            document.querySelectorAll('.delete-snippet-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    deleteSnippet(this.getAttribute('data-id'));
                });
            });
            
            // 绑定规则按钮事件
            document.querySelectorAll('.edit-rule-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    editRule(this.getAttribute('data-id'));
                });
            });
            
            document.querySelectorAll('.delete-rule-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    deleteRule(this.getAttribute('data-id'));
                });
            });
        }
        
        // 页面加载完成后绑定事件
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', bindInitialEvents);
        } else {
            bindInitialEvents();
        }
        
        // 点击模态框外部关闭
        window.onclick = function(event) {
            if (event.target.classList.contains('modal')) {
                event.target.style.display = 'none';
            }
        }
    </script>
</body>
</html> 