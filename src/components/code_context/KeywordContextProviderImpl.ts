import { ContextItem, KeywordContextProvider } from './types';

// Import wink-bm25-text-search and related libraries
const bm25 = require('wink-bm25-text-search');
const winkNLP = require('wink-nlp');
const model = require('wink-eng-lite-web-model');

/**
 * 关键字检索（BM25）Provider
 * 使用开源 wink-bm25-text-search 库实现
 * 需求参考：docs/requirements.md#4.1
 */
export class KeywordContextProviderImpl implements KeywordContextProvider {
    private contextItems: ContextItem[] = [];
    private engine: any;
    private nlp: any;
    private isInitialized: boolean = false;

    constructor() {
        this.initializeBM25Engine();
    }

    /**
     * 初始化BM25搜索引擎
     */
    private initializeBM25Engine(): void {
        try {
            // Create search engine instance
            this.engine = bm25();

            // Initialize NLP
            this.nlp = winkNLP(model);
            const its = this.nlp.its;

            // Define preprocessing task for tokenization
            const prepTask = (text: string) => {
                const tokens: string[] = [];
                this.nlp.readDoc(text)
                    .tokens()
                    // Use only words ignoring punctuations etc and from them remove stop words
                    .filter((t: any) => (t.out(its.type) === 'word' && !t.out(its.stopWordFlag)))
                    // Handle negation and extract stem of the word
                    .each((t: any) => tokens.push((t.out(its.negationFlag)) ? '!' + t.out(its.stem) : t.out(its.stem)));

                return tokens;
            };

            // Configure the engine
            this.engine.defineConfig({ fldWeights: { content: 1 } });
            this.engine.definePrepTasks([prepTask]);

            this.isInitialized = true;
            console.log('✅ BM25 搜索引擎初始化成功');
        } catch (error) {
            console.error('❌ BM25 搜索引擎初始化失败:', error);
            this.isInitialized = false;
        }
    }

    /**
     * 设置可搜索的文档集合
     */
    setDocuments(documents: ContextItem[]): void {
        if (!this.isInitialized) {
            console.warn('⚠️ BM25 引擎未初始化，无法设置文档');
            return;
        }

        this.contextItems = documents;

        // Clear existing documents and add new ones
        this.engine = bm25();
        this.initializeBM25Engine();

        // Add documents to the engine
        documents.forEach((doc, index) => {
            this.engine.addDoc({ content: doc.content }, index);
        });

        // Consolidate the engine for searching
        this.engine.consolidate();

        console.log(`📚 已加载 ${documents.length} 个文档到BM25搜索引擎`);
    }

    /**
     * 异步BM25检索，实现接口
     */
    async searchByKeywords(code: string): Promise<ContextItem[]> {
        if (!this.isInitialized) {
            console.warn('⚠️ BM25 引擎未初始化，返回空结果');
            return [];
        }

        if (this.contextItems.length === 0) {
            console.warn('⚠️ 没有可搜索的文档，请先调用 setDocuments()');
            return [];
        }

        return this.search(code, this.contextItems);
    }

    /**
     * 使用开源 wink-bm25-text-search 进行关键字检索
     * @param query 检索关键字
     * @param contextItems 可检索上下文项
     * @returns 匹配的上下文项列表，按相关性降序排序
     */
    async search(query: string, contextItems: ContextItem[]): Promise<ContextItem[]> {
        if (!this.isInitialized) {
            console.warn('⚠️ BM25 引擎未初始化，返回空结果');
            return [];
        }

        const trimmedQuery = query.trim();
        if (!trimmedQuery) {
            return [];
        }

        try {
            // Use wink-bm25 to search
            const results = this.engine.search(trimmedQuery);

            // Convert results back to ContextItem format
            const contextResults: ContextItem[] = results.map((result: any) => {
                const [docIndex, score] = result;
                const originalItem = contextItems[docIndex];

                if (!originalItem) {
                    return null;
                }

                // Extract keywords from query for structured data
                const keywords = this.extractKeywords(trimmedQuery);

                return {
                    ...originalItem,
                    score: score,
                    structuredData: {
                        codeSnippet: {
                            language: 'unknown', // TODO: 从上下文推断语言
                            filePath: originalItem.location?.file || '[unknown]',
                            startLine: originalItem.location?.start.line || 0,
                            endLine: originalItem.location?.end.line || 0,
                            keywords: keywords
                        }
                    }
                };
            }).filter((item:any): item is ContextItem => item !== null);

            console.log(`🔍 BM25搜索完成: 查询"${trimmedQuery}"，找到 ${contextResults.length} 个结果`);

            return contextResults;
        } catch (error) {
            console.error('❌ BM25搜索失败:', error);
            return [];
        }
    }

    /**
     * 从查询文本中提取关键词
     */
    private extractKeywords(query: string): string[] {
        // 简单的关键词提取：移除字符串，提取标识符
        // 保留注释
        return query
            .replace(/\/\/.*$/gm, '') // 移除行注释
            .replace(/"[^"]*"/g, '') // 移除双引号字符串
            .replace(/'[^']*'/g, '') // 移除单引号字符串
            .match(/\b[a-zA-Z_][a-zA-Z0-9_]*\b/g) || [];
    }
}