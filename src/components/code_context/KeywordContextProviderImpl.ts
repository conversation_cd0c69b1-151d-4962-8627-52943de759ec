import { ContextItem, KeywordContextProvider } from './types';

/**
 * 关键字检索（BM25）Provider
 * 需求参考：docs/requirements.md#4.1
 */
export class KeywordContextProviderImpl implements KeywordContextProvider {
    private contextItems: ContextItem[] = [];

    /**
     * 异步BM25检索，实现接口
     */
    async searchByKeywords(code: string): Promise<ContextItem[]> {
        // 这里假设 contextItems 数据源为单个code文本
        // 实际可扩展为多文档检索
        this.contextItems = [
            { source: 'bm25', contextType: 'similar', score: 0, content: code }
        ];
        return this.search(code, this.contextItems);
    }

    /**
     * 使用BM25算法进行关键字检索
     * @param query 检索关键字
     * @param contextItems 可检索上下文项
     * @returns 匹配的上下文项列表，按相关性降序排序
     */
    async search(query: string, contextItems: ContextItem[]): Promise<ContextItem[]> {
        // 简单分词（按空格分隔）
        const trimmedQuery = query.trim();
        if (!trimmedQuery) { return []; }
        const keywords = trimmedQuery.toLowerCase().split(/\s+/);
        if (keywords.length === 0) { return []; }

        // 统计文档长度 (helper function for tokenization)
        const tokenize = (text: string) => {
            return text.toLowerCase()
                .split(/[\s\(\),\{\};\.]+/)  // Split on whitespace and common punctuation
                .filter(word => word.length > 0 && /\w/.test(word));  // Keep only words with letters/numbers
        };
        const docLengths = contextItems.map(item => tokenize(item.content).length);
        const avgDocLength = docLengths.reduce((a, b) => a + b, 0) / (docLengths.length || 1);
        const N = contextItems.length;

        // 构建倒排索引和文档频率
        const df: Record<string, number> = {};
        contextItems.forEach(item => {
            const words = new Set(tokenize(item.content));
            keywords.forEach(k => {
                if (words.has(k)) {
                    df[k] = (df[k] || 0) + 1;
                }
            });
        });

        // BM25参数
        const k1 = 1.5;
        const b = 0.75;

        // 计算每个文档的BM25得分
        const results = contextItems.map((item, idx) => {
            const words = tokenize(item.content);
            let score = 0;
            keywords.forEach(k => {
                // 词频
                const tf = words.filter(w => w === k).length;
                if (tf === 0) { return; }
                // 文档频率
                const dfi = df[k] || 1;
                // IDF
                const idf = Math.log(1 + (N - dfi + 0.5) / (dfi + 0.5));
                // BM25公式
                score += idf * (tf * (k1 + 1)) / (tf + k1 * (1 - b + b * (docLengths[idx] / avgDocLength)));
            });
            return { ...item, score };
        });

        // 过滤无关项并按得分排序
        return results
            .filter(item => item.score > 0)
            .sort((a, b) => b.score - a.score);
    }
}