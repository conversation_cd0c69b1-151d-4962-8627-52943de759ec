import { RepoIndexer } from '../code_graph/repoIndexer/repoIndexer';
import { ASTNode } from '../code_graph/types/ast';
import { GraphCodeNode } from '../code_graph/types/graph';

export interface RelatedContextOptions {
  mode: 'completion' | 'check';
  includeEnclosingClass?: boolean;
  includeHiddenVariables?: boolean;
  maxCallChainDepth?: number;
  maxHiddenVariables?: number; // 新增
}

export interface HiddenVariable {
  name: string;
  source: 'function_param' | 'class_property' | 'global' | 'imported' | 'constant' | 'register' | 'local' | 'unknown';
  type: string;
  definitionLocation?: string;
  description?: string;
}

export interface RelatedContextData {
  currentFunction: {
    signature: string;
    body?: string;
  };
  enclosingClass?: {
    name: string;
    definition: string;
  };
  calledBy: Array<{
    signature: string;
    location: string;
    code?: string;
  }>;
  calls: Array<{
    signature: string;
    location: string;
    code?: string;
  }>;
  hiddenVariables: HiddenVariable[];
}

/**
 * 调用链上下文构建器
 * 根据设计规范构建调用链相关的上下文信息，包括：
 * - 上游函数（调用当前函数的函数）
 * - 下游函数（当前函数调用的函数）
 * - 当前函数使用的变量、结构体、宏等
 * - 所属类信息（如果适用）
 */
export class RelatedContextBuilder {
  constructor(private repoIndexer: RepoIndexer) {}

  /**
   * 构建完整的调用链上下文文本
   */
  async buildRelatedContext(
    functionNode: ASTNode,
    textBefore: string,
    textAfter: string,
    options: RelatedContextOptions = { mode: 'completion' }
  ): Promise<string> {
    const data = await this.extractRelatedContextData(functionNode, textBefore, textAfter, options);
    return this.formatRelatedContext(data, options);
  }

  /**
   * 提取调用链上下文数据
   * 包括上游函数、下游函数、使用的变量等
   */
  private async extractRelatedContextData(
    functionNode: ASTNode,
    textBefore: string,
    textAfter: string,
    options: RelatedContextOptions
  ): Promise<RelatedContextData> {
    // 查找当前函数的 CodeNode
    console.log(`=== 调用链上下文构建调试 ===`);
    console.log(`functionNode.file: "${functionNode.file}"`);
    console.log(`functionNode.name: "${functionNode.name}"`);
    console.log(`functionNode.originalType: "${functionNode.originalType}"`);

    const codeNode = this.repoIndexer.findNodeInFile(
      functionNode.file || '',
      functionNode.name || '',
      functionNode.originalType || 'function'
    );

    if (!codeNode) {
      console.log(`❌ 未找到对应的 CodeNode，返回空的调用链上下文`);
      return {
        currentFunction: { signature: functionNode.name || 'unknown' },
        calledBy: [],
        calls: [],
        hiddenVariables: []
      };
    }

    console.log(`✅ 找到 CodeNode: ${codeNode.name} (${codeNode.type})`);

    // 提取当前函数信息
    const currentFunction = this.extractCurrentFunction(codeNode, textBefore, textAfter);

    // 提取所属类信息
    const enclosingClass = options.includeEnclosingClass 
      ? this.extractEnclosingClass(codeNode)
      : undefined;

    // 提取调用链信息
    const calledBy = this.extractCalledBy(codeNode, options);
    const calls = this.extractCalls(codeNode, options);

    // 提取隐藏变量，传递maxHiddenVariables
    const hiddenVariables = options.includeHiddenVariables
      ? this.extractHiddenVariables(codeNode, textBefore, textAfter, options.maxHiddenVariables)
      : [];

    return {
      currentFunction,
      enclosingClass,
      calledBy,
      calls,
      hiddenVariables
    };
  }

  /**
   * 提取当前函数信息
   */
  private extractCurrentFunction(codeNode: GraphCodeNode, textBefore: string, textAfter: string) {
    const signature = codeNode.definition || `${codeNode.name}()`;
    
    // 对于代码检查模式，可能需要函数体
    let body: string | undefined;
    if (codeNode.definition && codeNode.definition.length > signature.length) {
      body = codeNode.definition.substring(signature.length).trim();
    }

    return { signature, body };
  }

  /**
   * 提取所属类信息
   */
  private extractEnclosingClass(codeNode: GraphCodeNode) {
    // TODO: 实现类信息提取逻辑
    // 需要从 AST 中查找当前函数所属的类
    return undefined;
  }

  /**
   * 提取调用当前函数的函数
   */
  private extractCalledBy(codeNode: GraphCodeNode, options: RelatedContextOptions) {
    const callers = this.repoIndexer.findCallersByName(codeNode.name, codeNode.type) || [];
    const maxDepth = options.maxCallChainDepth || 1;

    return callers.slice(0, maxDepth).map(caller => {
      const signature = caller.definition || `${caller.name}()`;
      const location = `${caller.file}:${caller.location?.start.line || '?'}`;
      
      let code: string | undefined;
      if (options.mode === 'check' && caller.definition) {
        // 对于代码检查模式，提取关键代码片段
        code = this.extractKeyCodeSnippet(caller.definition);
      }

      return { signature, location, code };
    });
  }

  /**
   * 提取当前函数调用的函数
   */
  private extractCalls(codeNode: GraphCodeNode, options: RelatedContextOptions) {
    const callees = this.repoIndexer.findCalleesByName(codeNode.name, codeNode.type) || [];
    const maxDepth = options.maxCallChainDepth || 1;

    return callees.slice(0, maxDepth).map(callee => {
      const signature = callee.definition || `${callee.name}()`;
      const location = `${callee.file}:${callee.location?.start.line || '?'}`;
      
      let code: string | undefined;
      if (options.mode === 'check' && callee.definition) {
        // 对于代码检查模式，提取关键代码片段
        code = this.extractKeyCodeSnippet(callee.definition);
      }

      return { signature, location, code };
    });
  }

  /**
   * 提取隐藏变量（增强版，支持粒度控制）
   */
  private extractHiddenVariables(
    codeNode: GraphCodeNode,
    textBefore: string,
    textAfter: string,
    maxHiddenVariables: number = 10
  ): HiddenVariable[] {
    const variables: HiddenVariable[] = [];
    const visibleText = textBefore + textAfter;
    const extractedVars = new Set<string>(); // 用于去重

    // 提取函数参数
    if (codeNode.metadata?.parameters) {
      for (const param of codeNode.metadata.parameters) {
        const paramName = this.extractTopLevelVariable(param.name);
        if (!this.isVariableVisible(paramName, visibleText) && !extractedVars.has(paramName)) {
          extractedVars.add(paramName);
          variables.push({
            name: paramName,
            source: 'function_param',
            type: param.type || 'unknown',
            description: 'function parameter'
          });
          if (variables.length >= maxHiddenVariables) {{return variables;};};
        }
      }
    }

    // 提取类属性（self.xxx）
    this.extractClassProperties(codeNode, visibleText, variables, extractedVars, maxHiddenVariables);
    if (variables.length >= maxHiddenVariables) {return variables;};

    // 提取全局变量
    this.extractGlobalVariables(codeNode, visibleText, variables, extractedVars, maxHiddenVariables);
    if (variables.length >= maxHiddenVariables) {return variables;};

    // 提取导入变量
    this.extractImportedVariables(codeNode, visibleText, variables, extractedVars, maxHiddenVariables);
    if (variables.length >= maxHiddenVariables) {return variables;};

    // 提取常量
    this.extractConstants(codeNode, visibleText, variables, extractedVars, maxHiddenVariables);
    if (variables.length >= maxHiddenVariables) {return variables;};

    // 提取寄存器变量（NP语言专用）
    this.extractRegisterVariables(codeNode, visibleText, variables, extractedVars, maxHiddenVariables);
    if (variables.length >= maxHiddenVariables) {return variables;};

    // 提取局部变量
    this.extractLocalVariables(codeNode, visibleText, variables, extractedVars, maxHiddenVariables);
    if (variables.length >= maxHiddenVariables) {return variables;};

    // 提取未知变量（其他未分类的变量）
    this.extractUnknownVariables(codeNode, visibleText, variables, extractedVars, maxHiddenVariables);
    if (variables.length >= maxHiddenVariables) {return variables;};

    {return variables;};
  }

  /**
   * 提取未知变量
   */
  private extractUnknownVariables(codeNode: GraphCodeNode, visibleText: string, variables: HiddenVariable[], extractedVars: Set<string>, maxHiddenVariables: number) {
    if (codeNode.definition) {
      // 匹配所有可能的变量名（字母开头，包含字母数字下划线）
      const varRegex = /\b([a-zA-Z_][a-zA-Z0-9_]*)\b/g;
      let match;
      
      while ((match = varRegex.exec(codeNode.definition)) !== null) {
        const varName = match[1];
        
        // 跳过关键字和已提取的变量
        if (this.isKeyword(varName) || extractedVars.has(varName) || this.isVariableVisible(varName, visibleText)) {
          continue;
        }
        
        // 跳过函数名和类名
        if (varName === codeNode.name || this.isFunctionOrClass(varName)) {
          continue;
        }
        
        // 跳过函数调用（通常以括号结尾）
        if (this.isFunctionCall(varName, codeNode.definition, match.index)) {
          continue;
        }
        
        // 跳过方法调用（包含点号的）
        if (this.isMethodCall(varName, codeNode.definition, match.index)) {
          continue;
        }
        
        extractedVars.add(varName);
        const { source, type } = this.analyzeVariableSource(varName);
        variables.push({
          name: varName,
          source,
          type,
          description: 'unknown variable'
        });
        if (variables.length >= maxHiddenVariables) {return;}
      }
    }
  }

  /**
   * 判断是否为关键字
   */
  private isKeyword(word: string): boolean {
    const keywords = [
      'def', 'class', 'if', 'else', 'elif', 'for', 'while', 'try', 'except', 'finally',
      'with', 'as', 'import', 'from', 'return', 'yield', 'break', 'continue', 'pass',
      'True', 'False', 'None', 'and', 'or', 'not', 'in', 'is', 'lambda', 'del',
      'global', 'nonlocal', 'assert', 'raise'
    ];
    return keywords.includes(word);
  }

  /**
   * 判断是否为函数或类名
   */
  private isFunctionOrClass(word: string): boolean {
    // 简单的启发式判断：首字母大写的可能是类名
    return /^[A-Z]/.test(word);
  }

  /**
   * 判断是否为函数调用
   */
  private isFunctionCall(varName: string, definition: string, index: number): boolean {
    // 检查变量名后面是否有括号
    const afterMatch = definition.substring(index + varName.length);
    return /^\s*\(/.test(afterMatch);
  }

  /**
   * 判断是否为方法调用
   */
  private isMethodCall(varName: string, definition: string, index: number): boolean {
    // 检查变量名前面是否有点号
    const beforeMatch = definition.substring(0, index);
    return /\.\s*$/.test(beforeMatch);
  }

  /**
   * 提取顶级变量名（避免变量爆炸）
   */
  private extractTopLevelVariable(variableName: string): string {
    // 如果是属性访问，只保留顶级变量
    const parts = variableName.split('.');
    return parts[0];
  }

  /**
   * 检查变量是否在可见文本中
   */
  private isVariableVisible(variableName: string, visibleText: string): boolean {
    // 使用词边界匹配，避免部分匹配
    const regex = new RegExp(`\\b${this.escapeRegex(variableName)}\\b`, 'g');
    return regex.test(visibleText);
  }

  /**
   * 转义正则表达式特殊字符
   */
  private escapeRegex(string: string): string {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }

  /**
   * 分析变量来源和类型
   */
  private analyzeVariableSource(variableName: string): { source: HiddenVariable['source'], type: string } {
    const name = variableName.toLowerCase();
    
    // 类属性
    if (name.startsWith('self.') || name.startsWith('this.')) {
      return { source: 'class_property', type: 'unknown' };
    }

    // 全局变量（以g_开头）
    if (name.startsWith('g_')) {
      return { source: 'global', type: 'unknown' };
    }

    // 常量（全大写且包含_）
    if (variableName === variableName.toUpperCase() && variableName.includes('_')) {
      return { source: 'constant', type: 'unknown' };
    }

    // 寄存器变量（NP语言专用）
    if (this.isRegisterVariable(variableName)) {
      return { source: 'register', type: 'unknown' };
    }

    // 局部变量（包含特定关键词）
    if (this.isLocalVariable(variableName)) {
      return { source: 'local', type: 'unknown' };
    }

    // 导入变量（通常来自import语句）
    if (this.isImportedVariable(variableName)) {
      return { source: 'imported', type: 'unknown' };
    }

    return { source: 'unknown', type: 'unknown' };
  }

  /**
   * 判断是否为寄存器变量
   */
  private isRegisterVariable(variableName: string): boolean {
    const registerPrefixes = ['lw', 'rb', 'rh', 'cc', 'rs', 'qs', 'q'];
    const prefix = variableName.toLowerCase().replace(/\d+.*$/, '');
    return registerPrefixes.includes(prefix);
  }

  /**
   * 判断是否为局部变量
   */
  private isLocalVariable(variableName: string): boolean {
    const localKeywords = ['temp', 'tmp', 'local', 'var', 'buf', 'buffer'];
    const name = variableName.toLowerCase();
    // 只有当变量名完全匹配局部变量模式时才认为是局部变量
    return localKeywords.some(keyword => name.startsWith(keyword));
  }

  /**
   * 判断是否为导入变量
   */
  private isImportedVariable(variableName: string): boolean {
    // 常见的导入模块名
    const commonImports = ['np', 'numpy', 'pd', 'pandas', 'plt', 'matplotlib', 'tf', 'tensorflow', 'torch', 'pytorch'];
    return commonImports.includes(variableName.toLowerCase());
  }

  /**
   * 提取类属性
   */
  private extractClassProperties(codeNode: GraphCodeNode, visibleText: string, variables: HiddenVariable[], extractedVars: Set<string>, maxHiddenVariables: number) {
    // 从函数定义中提取 self.xxx 形式的类属性
    if (codeNode.definition) {
      const selfPropertyRegex = /\b(self\.\w+)\b/g;
      let match;
      
      while ((match = selfPropertyRegex.exec(codeNode.definition)) !== null) {
        const propertyName = match[1]; // 保持完整的 self.xxx 形式
        if (!this.isVariableVisible(propertyName, visibleText) && !extractedVars.has(propertyName)) {
          extractedVars.add(propertyName);
          const { source, type } = this.analyzeVariableSource(propertyName);
          variables.push({
            name: propertyName,
            source,
            type,
            description: 'class property'
          });
          if (variables.length >= maxHiddenVariables) {return;}
        }
      }
    }
  }

  /**
   * 提取全局变量
   */
  private extractGlobalVariables(codeNode: GraphCodeNode, visibleText: string, variables: HiddenVariable[], extractedVars: Set<string>, maxHiddenVariables: number) {
    // 从函数定义中提取全局变量
    if (codeNode.definition) {
      // 匹配以 g_ 开头的全局变量
      const globalVarRegex = /\b(g_\w+)\b/g;
      let match;
      
      while ((match = globalVarRegex.exec(codeNode.definition)) !== null) {
        const varName = match[1];
        if (!this.isVariableVisible(varName, visibleText) && !extractedVars.has(varName)) {
          extractedVars.add(varName);
          const { source, type } = this.analyzeVariableSource(varName);
          variables.push({
            name: varName,
            source,
            type,
            description: 'global variable'
          });
          if (variables.length >= maxHiddenVariables) {return;}
        }
      }

      // 匹配其他可能的全局变量模式
      const otherGlobalRegex = /\b([A-Z][A-Z0-9_]*)\b/g;
      while ((match = otherGlobalRegex.exec(codeNode.definition)) !== null) {
        const varName = match[1];
        if (!this.isVariableVisible(varName, visibleText) && !extractedVars.has(varName)) {
          extractedVars.add(varName);
          const { source, type } = this.analyzeVariableSource(varName);
          if (source === 'constant' || source === 'global') {
            variables.push({
              name: varName,
              source,
              type,
              description: 'global variable or constant'
            });
            if (variables.length >= maxHiddenVariables) {return;}
          }
        }
      }
    }
  }

  /**
   * 提取导入变量
   */
  private extractImportedVariables(codeNode: GraphCodeNode, visibleText: string, variables: HiddenVariable[], extractedVars: Set<string>, maxHiddenVariables: number) {
    // 从函数定义中提取导入的模块和变量
    if (codeNode.definition) {
      // 匹配常见的导入模块使用
      const importUsageRegex = /\b(np|pd|plt|tf|torch|numpy|pandas|matplotlib|tensorflow|pytorch)\b/g;
      let match;
      
      while ((match = importUsageRegex.exec(codeNode.definition)) !== null) {
        const varName = match[1];
        if (!this.isVariableVisible(varName, visibleText) && !extractedVars.has(varName)) {
          extractedVars.add(varName);
          const { source, type } = this.analyzeVariableSource(varName);
          variables.push({
            name: varName,
            source,
            type,
            description: 'imported module'
          });
          if (variables.length >= maxHiddenVariables) {return;}
        }
      }
    }
  }

  /**
   * 提取常量
   */
  private extractConstants(codeNode: GraphCodeNode, visibleText: string, variables: HiddenVariable[], extractedVars: Set<string>, maxHiddenVariables: number) {
    // 从函数定义中提取常量
    if (codeNode.definition) {
      // 匹配全大写且包含下划线的常量
      const constantRegex = /\b([A-Z][A-Z0-9_]*)\b/g;
      let match;
      
      while ((match = constantRegex.exec(codeNode.definition)) !== null) {
        const varName = match[1];
        if (!this.isVariableVisible(varName, visibleText) && !extractedVars.has(varName)) {
          extractedVars.add(varName);
          const { source, type } = this.analyzeVariableSource(varName);
          if (source === 'constant') {
            variables.push({
              name: varName,
              source,
              type,
              description: 'constant'
            });
            if (variables.length >= maxHiddenVariables) {return;}
          }
        }
      }
    }
  }

  /**
   * 提取寄存器变量
   */
  private extractRegisterVariables(codeNode: GraphCodeNode, visibleText: string, variables: HiddenVariable[], extractedVars: Set<string>, maxHiddenVariables: number) {
    // 从函数定义中提取寄存器变量（NP语言专用）
    if (codeNode.definition) {
      // 匹配寄存器变量模式
      const registerRegex = /\b(lw\d+|rb\d+|rh\d+|cc\d+|rs\d+|qs\d+|q\d+)\b/gi;
      let match;
      
      while ((match = registerRegex.exec(codeNode.definition)) !== null) {
        const varName = match[1];
        if (!this.isVariableVisible(varName, visibleText) && !extractedVars.has(varName)) {
          extractedVars.add(varName);
          const { source, type } = this.analyzeVariableSource(varName);
          variables.push({
            name: varName,
            source,
            type,
            description: 'register variable'
          });
          if (variables.length >= maxHiddenVariables) {return;}
        }
      }
    }
  }

  /**
   * 提取局部变量
   */
  private extractLocalVariables(codeNode: GraphCodeNode, visibleText: string, variables: HiddenVariable[], extractedVars: Set<string>, maxHiddenVariables: number) {
    // 从函数定义中提取局部变量
    if (codeNode.definition) {
      // 匹配局部变量模式
      const localVarRegex = /\b(temp\w+|tmp\w+|local\w+|var\w+|buf\w+|buffer\w+)\b/gi;
      let match;
      
      while ((match = localVarRegex.exec(codeNode.definition)) !== null) {
        const varName = match[1];
        if (!this.isVariableVisible(varName, visibleText) && !extractedVars.has(varName)) {
          extractedVars.add(varName);
          const { source, type } = this.analyzeVariableSource(varName);
          if (source === 'local') {
            variables.push({
              name: varName,
              source,
              type,
              description: 'local variable'
            });
            if (variables.length >= maxHiddenVariables) {return;}
          }
        }
      }
    }
  }

  /**
   * 提取关键代码片段
   */
  private extractKeyCodeSnippet(definition: string): string {
    // 简单的代码片段提取逻辑
    const lines = definition.split('\n');
    if (lines.length <= 3) {
      return definition;
    }
    
    // 返回前几行作为关键片段
    return lines.slice(0, 3).join('\n') + '...';
  }

  /**
   * 格式化调用链上下文文本
   */
  private formatRelatedContext(data: RelatedContextData, options: RelatedContextOptions): string {
    const sections: string[] = [];

    // 添加标题
    sections.push('### Call Chain Context ###');
    sections.push('');

    // Current Function
    sections.push('# Current Function:');
    sections.push(data.currentFunction.signature);
    if (data.currentFunction.body && options.mode === 'check') {
      sections.push(data.currentFunction.body);
    }

    // Enclosing Class
    if (data.enclosingClass) {
      sections.push('');
      sections.push('# Enclosing Class:');
      sections.push(data.enclosingClass.definition);
    }

    // Called By
    if (data.calledBy.length > 0) {
      sections.push('');
      sections.push('# Called By:');
      data.calledBy.forEach(caller => {
        sections.push(`- ${caller.location} -> ${caller.signature}`);
        if (caller.code && options.mode === 'check') {
          sections.push(`  ${caller.code}`);
        }
      });
    }

    // Calls
    if (data.calls.length > 0) {
      sections.push('');
      sections.push('# Calls:');
      data.calls.forEach(callee => {
        sections.push(`- ${callee.location} -> ${callee.signature}`);
        if (callee.code && options.mode === 'check') {
          sections.push(`  ${callee.code}`);
        }
      });
    }

    // Hidden Variables
    if (data.hiddenVariables.length > 0) {
      sections.push('');
      sections.push('# Hidden Variables Model Might Need:');
      data.hiddenVariables.forEach(variable => {
        let desc = `- ${variable.name}: ${variable.source}, type=${variable.type}`;
        if (variable.definitionLocation) {
          desc += ` (from ${variable.definitionLocation})`;
        }
        if (variable.description) {
          desc += ` # ${variable.description}`;
        }
        sections.push(desc);
      });
      // 如果数量达到上限，提示省略
      if (options.maxHiddenVariables && data.hiddenVariables.length >= options.maxHiddenVariables) {
        sections.push('...（已省略部分变量，详见代码）');
      }
    }

    return sections.join('\n');
  }
} 