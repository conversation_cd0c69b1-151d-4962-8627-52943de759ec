import { ASTNode } from '../code_graph/types/ast';
import { RelatedContextOptions } from './RelatedContextBuilder';

export type ContextItem = {
  source: 'graph' | 'bm25' | 'embedding' | 'user-snippets';  // 新增 user-snippets 源
  contextType: 'related' | 'similar';  // 新增：区分相关上下文和相似上下文
  score: number;
  content: string;
  location?: SourceLocation;
  // 新增：调用链相关的 ASTNode 信息
  astNode?: ASTNode;  // 对于 graph 源的 related 类型，包含调用链中的 ASTNode
  // 新增：用户自定义内容相关字段
  userSnippet?: {
    id: string;
    name: string;
    priority: number;
    similarity: number;
  };
};

export type SourceLocation = {
  file: string;
  start: { line: number; column: number };
  end: { line: number; column: number };
};

export type RetrievalFusionConfig = {
  graphWeight: number;
  bm25Weight: number;
  embeddingWeight: number;
};

/**
 * 上下文Provider基础接口
 */
export interface ContextProvider {
  /**
   * 基于输入文本进行上下文检索
   */
  search(query: string, contextItems: ContextItem[]): Promise<ContextItem[]>;
}

/**
 * 关键字检索Provider接口
 */
export interface KeywordContextProvider extends ContextProvider {
  /**
   * 基于关键字的BM25检索
   */
  searchByKeywords(code: string): Promise<ContextItem[]>;
}

/**
 * 调用链检索Provider接口
 */
export interface GraphContextProvider extends ContextProvider {
  /**
   * 获取调用链上下文
   * 包括：
   * - 上游函数（调用当前函数的函数）
   * - 下游函数（当前函数调用的函数）
   * - 当前函数使用的变量、结构体、宏等
   * - 所属类信息（如果适用）
   * @param functionNode 函数节点
   * @param textBefore 光标前的文本
   * @param textAfter 光标后的文本
   * @param options 调用链上下文选项
   */
  getRelatedContext(
    functionNode: ASTNode,
    textBefore?: string,
    textAfter?: string,
    options?: RelatedContextOptions
  ): Promise<ContextItem[]>;

  /**
   * 获取相似上下文（相似函数）
   * 基于函数名、签名等查找相似的函数实现
   */
  getSimilarContext(functionNode: ASTNode): Promise<ContextItem[]>;
}

/**
 * 向量检索Provider接口
 */
export interface EmbeddingContextProvider extends ContextProvider {
  /**
   * 基于向量相似度的语义检索
   */
  searchByEmbedding(code: string): Promise<ContextItem[]>;
}

/**
 * 用户自定义内容Provider接口
 */
export interface UserCustomContextProvider extends ContextProvider {
  /**
   * 获取匹配的用户自定义代码片段
   */
  searchByUserSnippets(code: string, language: string): Promise<ContextItem[]>;
} 