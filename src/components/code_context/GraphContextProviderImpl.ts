import { RepoIndexer } from '../code_graph/repoIndexer/repoIndexer';
import { ASTNode } from '../code_graph/types/ast';
import { RelatedContextOptions } from './RelatedContextBuilder';
import { ContextItem, GraphContextProvider } from './types';

/**
 * 调用链检索Provider
 * 需求参考：docs/requirements.md#4.2
 */
export class GraphContextProviderImpl implements GraphContextProvider {
  constructor(private repoIndexer: RepoIndexer) {
  }

  /**
   * 获取调用链上下文
   * 包括：
   * - 上游函数（调用当前函数的函数）
   * - 下游函数（当前函数调用的函数）
   * - 当前函数使用的变量、结构体、宏等
   * - 所属类信息（如果适用）
   * 直接返回调用链中的 ASTNode 对象，而不是构建字符串
   */
  async getRelatedContext(functionNode: ASTNode, textBefore: string = '', textAfter: string = '', options?: RelatedContextOptions): Promise<ContextItem[]> {
    // 查找当前函数的 CodeNode
    const codeNode = this.repoIndexer.findNodeInFile(
      functionNode.file || '',
      functionNode.name || '',
      functionNode.originalType || 'function'
    );

    if (!codeNode) {
      console.log(`❌ 未找到对应的 CodeNode，无法获取调用链上下文`);
      return [];
    }

    console.log(`✅ 找到 CodeNode: ${codeNode.name} (${codeNode.type})`);

    const contextItems: ContextItem[] = [];

    // 获取上游函数（调用当前函数的函数）
    const callers = this.repoIndexer.findCallersByName(codeNode.name, codeNode.type) || [];
    callers.forEach(caller => {
      const callerASTNode = this.codeNodeToASTNode(caller, 'caller');
      contextItems.push({
        source: 'graph' as const,
        contextType: 'related' as const,
        score: 0.9,
        content: caller.definition || caller.name,
        location: caller.location ? {
          file: caller.file || '',
          start: caller.location.start,
          end: caller.location.end,
        } : undefined,
        astNode: callerASTNode
      });
    });

    // 获取下游函数（当前函数调用的函数）
    const callees = this.repoIndexer.findCalleesByName(codeNode.name, codeNode.type) || [];
    callees.forEach(callee => {
      const calleeASTNode = this.codeNodeToASTNode(callee, 'callee');
      contextItems.push({
        source: 'graph' as const,
        contextType: 'related' as const,
        score: 0.8,
        content: callee.definition || callee.name,
        location: callee.location ? {
          file: callee.file || '',
          start: callee.location.start,
          end: callee.location.end,
        } : undefined,
        astNode: calleeASTNode
      });
    });

    console.log(`📊 调用链上下文统计: ${callers.length} 个上游函数, ${callees.length} 个下游函数`);

    return contextItems;
  }

  /**
   * 获取相似上下文（相似函数）
   */
  async getSimilarContext(functionNode: ASTNode): Promise<ContextItem[]> {
    // to-do 目前图不具备搜索相似内容的能力
    console.warn("目前图没有实现检索相似上下文的功能");
    return [];
  }

  /**
   * 基于输入文本进行上下文检索
   */
  async search(query: string, contextItems: ContextItem[]): Promise<ContextItem[]> {
    // TODO: 实现基于调用链的文本检索
    return [];
  }

  /**
   * 将 GraphCodeNode 转换为 ASTNode
   */
  private codeNodeToASTNode(codeNode: any, role: 'caller' | 'callee'): ASTNode {
    return {
      name: codeNode.name,
      kind: 'function' as any, // 使用字符串避免导入问题
      originalType: codeNode.type,
      text: codeNode.definition,
      children: [],
      location: codeNode.location,
      language: codeNode.language,
      file: codeNode.file,
      metadata: {
        role: role, // 标记是调用者还是被调用者
        ...codeNode.metadata
      }
    };
  }
}