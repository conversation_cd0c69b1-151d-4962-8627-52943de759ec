import { RepoIndexer } from '../code_graph/repoIndexer/repoIndexer';
import { ASTNode } from '../code_graph/types/ast';
import { RelatedContextOptions } from './RelatedContextBuilder';
import { ContextItem, GraphContextProvider } from './types';

/**
 * 调用链检索Provider
 * 需求参考：docs/requirements.md#4.2
 */
export class GraphContextProviderImpl implements GraphContextProvider {
  constructor(private repoIndexer: RepoIndexer) {
  }

  /**
   * 获取调用链上下文
   * 包括：
   * - 上游函数（调用当前函数的函数）
   * - 下游函数（当前函数调用的函数）
   * - 当前函数使用的变量、结构体、宏等
   * - 所属类信息（如果适用）
   * 直接返回调用链中的 ASTNode 对象，而不是构建字符串
   */
  async getRelatedContext(functionNode: ASTNode, textBefore: string = '', textAfter: string = '', options?: RelatedContextOptions): Promise<ContextItem[]> {
    // 查找当前函数的 CodeNode
    const codeNode = this.repoIndexer.findNodeInFile(
      functionNode.file || '',
      functionNode.name || '',
      functionNode.originalType || 'function'
    );

    if (!codeNode) {
      console.log(`❌ 未找到对应的 CodeNode，无法获取调用链上下文`);
      return [];
    }

    console.log(`✅ 找到 CodeNode: ${codeNode.name} (${codeNode.type})`);

    const contextItems: ContextItem[] = [];

    // 获取上游函数（调用当前函数的函数）
    const callers = this.repoIndexer.findCallersByName(codeNode.name, codeNode.type) || [];
    callers.forEach(caller => {
      const callerASTNode = this.codeNodeToASTNode(caller, 'caller');
      contextItems.push({
        source: 'graph' as const,
        contextType: 'related' as const,
        score: 0.9,
        content: caller.definition || caller.name, // 保留作为降级选项
        location: caller.location ? {
          file: caller.file || '',
          start: caller.location.start,
          end: caller.location.end,
        } : undefined,
        structuredData: {
          callChain: {
            astNode: callerASTNode,
            role: 'caller',
            relationship: `调用当前函数 ${functionNode.name}`
          }
        }
      });
    });

    // 获取下游函数（当前函数调用的函数）
    const callees = this.repoIndexer.findCalleesByName(codeNode.name, codeNode.type) || [];
    callees.forEach(callee => {
      const calleeASTNode = this.codeNodeToASTNode(callee, 'callee');
      contextItems.push({
        source: 'graph' as const,
        contextType: 'related' as const,
        score: 0.8,
        content: callee.definition || callee.name, // 保留作为降级选项
        location: callee.location ? {
          file: callee.file || '',
          start: callee.location.start,
          end: callee.location.end,
        } : undefined,
        structuredData: {
          callChain: {
            astNode: calleeASTNode,
            role: 'callee',
            relationship: `被当前函数 ${functionNode.name} 调用`
          }
        }
      });
    });

    console.log(`📊 调用链上下文统计: ${callers.length} 个上游函数, ${callees.length} 个下游函数`);

    return contextItems;
  }

  /**
   * 获取相似上下文（相似函数）
   * 使用分层检索策略：本文件 > 本模块 > 全局
   */
  async getSimilarContext(functionNode: ASTNode): Promise<ContextItem[]> {
    const contextItems: ContextItem[] = [];
    const currentFile = functionNode.file || '';
    const query = this.buildSearchQuery(functionNode);

    if (!query.trim()) {
      console.log('无法构建搜索查询，跳过相似上下文检索');
      return [];
    }

    // 1. 本文件上下文（最高优先级）
    const fileContexts = await this.searchInFile(currentFile, query);
    contextItems.push(...fileContexts.map(item => ({ ...item, score: item.score * 1.0 })));

    // 2. 本模块可见上下文（import 关系）
    const moduleContexts = await this.searchInModule(currentFile, query);
    contextItems.push(...moduleContexts.map(item => ({ ...item, score: item.score * 0.8 })));

    // 3. 全局上下文（最低优先级）
    const globalContexts = await this.searchGlobal(query, [currentFile]);
    contextItems.push(...globalContexts.map(item => ({ ...item, score: item.score * 0.6 })));

    // 按得分排序并去重
    const uniqueContexts = this.deduplicateContexts(contextItems);
    return uniqueContexts.sort((a, b) => b.score - a.score).slice(0, 10); // 限制返回数量
  }

  /**
   * 基于输入文本进行上下文检索
   */
  async search(query: string, contextItems: ContextItem[]): Promise<ContextItem[]> {
    // TODO: 实现基于调用链的文本检索
    return [];
  }

  /**
   * 构建搜索查询字符串
   */
  private buildSearchQuery(functionNode: ASTNode): string {
    const parts: string[] = [];

    // 函数名
    if (functionNode.name) {
      parts.push(functionNode.name);
    }

    // 函数类型
    if (functionNode.originalType) {
      parts.push(functionNode.originalType);
    }

    // 从函数文本中提取关键词
    if (functionNode.text) {
      const keywords = this.extractKeywords(functionNode.text);
      parts.push(...keywords.slice(0, 5)); // 限制关键词数量
    }

    return parts.join(' ');
  }

  /**
   * 从代码文本中提取关键词
   */
  private extractKeywords(text: string): string[] {
    // 简单的关键词提取：移除注释、字符串，提取标识符
    const cleanText = text
      .replace(/\/\*[\s\S]*?\*\//g, '') // 移除块注释
      .replace(/\/\/.*$/gm, '') // 移除行注释
      .replace(/"[^"]*"/g, '') // 移除双引号字符串
      .replace(/'[^']*'/g, '') // 移除单引号字符串
      .replace(/`[^`]*`/g, ''); // 移除模板字符串

    // 提取标识符（字母开头，包含字母数字下划线）
    const identifiers = cleanText.match(/\b[a-zA-Z_][a-zA-Z0-9_]*\b/g) || [];

    // 过滤常见关键字和短词
    const commonKeywords = new Set(['function', 'const', 'let', 'var', 'if', 'else', 'for', 'while', 'return', 'true', 'false', 'null', 'undefined']);

    return identifiers
      .filter(word => word.length > 2 && !commonKeywords.has(word.toLowerCase()))
      .slice(0, 10); // 限制数量
  }

  /**
   * 在指定文件中搜索相似上下文
   */
  private async searchInFile(filePath: string, query: string): Promise<ContextItem[]> {
    // 获取文件中的所有函数节点
    const fileNodes = this.getNodesByFile(filePath);
    return this.performBM25Search(query, fileNodes, 'file');
  }

  /**
   * 在模块范围内搜索相似上下文（基于 import 关系）
   */
  private async searchInModule(currentFile: string, query: string): Promise<ContextItem[]> {
    // 使用 RepoIndexer 的模块相关节点查询
    const moduleNodes = this.repoIndexer.getModuleRelatedNodesByName('', currentFile) || [];
    return this.performBM25Search(query, moduleNodes, 'module');
  }

  /**
   * 在全局范围内搜索相似上下文
   */
  private async searchGlobal(query: string, excludeFiles: string[]): Promise<ContextItem[]> {
    // 获取所有节点，排除指定文件
    const allNodes = this.getAllNodes().filter(node => !excludeFiles.includes(node.file || ''));
    return this.performBM25Search(query, allNodes, 'global');
  }

  /**
   * 获取指定文件的所有节点
   */
  private getNodesByFile(filePath: string): any[] {
    const graph = this.repoIndexer.getGraph();
    const nodes: any[] = [];

    for (const [_, node] of Array.from(graph.nodes)) {
      if (node.file === filePath || node.file === filePath.replace(/\\/g, '/')) {
        nodes.push(node);
      }
    }

    return nodes;
  }

  /**
   * 获取所有节点
   */
  private getAllNodes(): any[] {
    const graph = this.repoIndexer.getGraph();
    return Array.from(graph.nodes.values());
  }

  /**
   * 执行 BM25 搜索
   */
  private performBM25Search(query: string, nodes: any[], scope: 'file' | 'module' | 'global'): ContextItem[] {
    if (!nodes || nodes.length === 0) {
      return [];
    }

    // 分词
    const queryKeywords = this.tokenize(query);
    if (queryKeywords.length === 0) {
      return [];
    }

    // 计算文档长度
    const docLengths = nodes.map(node => this.tokenize(node.definition || node.name || '').length);
    const avgDocLength = docLengths.reduce((a, b) => a + b, 0) / (docLengths.length || 1);
    const N = nodes.length;

    // 构建文档频率
    const df: Record<string, number> = {};
    nodes.forEach(node => {
      const words = new Set(this.tokenize(node.definition || node.name || ''));
      queryKeywords.forEach(keyword => {
        if (words.has(keyword)) {
          df[keyword] = (df[keyword] || 0) + 1;
        }
      });
    });

    // BM25 参数
    const k1 = 1.5;
    const b = 0.75;

    // 计算每个节点的 BM25 得分
    const results = nodes.map((node, idx) => {
      const words = this.tokenize(node.definition || node.name || '');
      let score = 0;
      const matchedKeywords: string[] = [];

      queryKeywords.forEach(keyword => {
        const tf = words.filter(w => w === keyword).length;
        if (tf === 0) {
          return;
        }

        matchedKeywords.push(keyword);
        const dfi = df[keyword] || 1;
        const idf = Math.log(1 + (N - dfi + 0.5) / (dfi + 0.5));
        score += idf * (tf * (k1 + 1)) / (tf + k1 * (1 - b + b * (docLengths[idx] / avgDocLength)));
      });

      if (score === 0) {
        return null;
      }

      return {
        source: 'bm25' as const,
        contextType: 'similar' as const,
        score,
        content: node.definition || node.name || '',
        location: node.location ? {
          file: node.file || '',
          start: node.location.start,
          end: node.location.end,
        } : undefined,
        structuredData: {
          codeSnippet: {
            language: node.language || 'unknown',
            filePath: node.file || '[unknown]',
            startLine: node.location?.start.line || 0,
            endLine: node.location?.end.line || 0,
            keywords: matchedKeywords
          }
        }
      };
    }).filter(item => item !== null) as ContextItem[];

    return results.sort((a, b) => b.score - a.score);
  }

  /**
   * 改进的分词方法
   */
  private tokenize(text: string): string[] {
    return text.toLowerCase()
      .replace(/\/\*[\s\S]*?\*\//g, '') // 移除块注释
      .replace(/\/\/.*$/gm, '') // 移除行注释
      .replace(/"[^"]*"/g, '') // 移除字符串
      .replace(/'[^']*'/g, '') // 移除字符串
      .split(/[\s\(\),\{\};\.!@#$%^&*+=\[\]\\|`~<>?:]+/) // 按标点符号分割
      .filter(word => word.length > 1 && /[a-zA-Z]/.test(word)) // 保留有意义的词
      .filter(word => !this.isCommonKeyword(word)); // 过滤常见关键字
  }

  /**
   * 判断是否为常见关键字
   */
  private isCommonKeyword(word: string): boolean {
    const commonKeywords = new Set([
      'function', 'const', 'let', 'var', 'if', 'else', 'for', 'while', 'return',
      'true', 'false', 'null', 'undefined', 'this', 'new', 'class', 'extends',
      'import', 'export', 'from', 'default', 'async', 'await', 'try', 'catch',
      'finally', 'throw', 'typeof', 'instanceof', 'in', 'of', 'do', 'switch',
      'case', 'break', 'continue', 'with', 'delete', 'void'
    ]);
    return commonKeywords.has(word.toLowerCase());
  }

  /**
   * 去重上下文项
   */
  private deduplicateContexts(contexts: ContextItem[]): ContextItem[] {
    const seen = new Set<string>();
    return contexts.filter(item => {
      const key = `${item.location?.file || ''}:${item.location?.start.line || 0}:${item.content.substring(0, 50)}`;
      if (seen.has(key)) {
        return false;
      }
      seen.add(key);
      return true;
    });
  }

  /**
   * 将 GraphCodeNode 转换为 ASTNode
   */
  private codeNodeToASTNode(codeNode: any, role: 'caller' | 'callee'): ASTNode {
    return {
      name: codeNode.name,
      kind: 'function' as any, // 使用字符串避免导入问题
      originalType: codeNode.type,
      text: codeNode.definition,
      children: [],
      location: codeNode.location,
      language: codeNode.language,
      file: codeNode.file,
      metadata: {
        role: role, // 标记是调用者还是被调用者
        ...codeNode.metadata
      }
    };
  }
}