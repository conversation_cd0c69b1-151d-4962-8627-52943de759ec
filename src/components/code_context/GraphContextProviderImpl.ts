import { RepoIndexer } from '../code_graph/repoIndexer/repoIndexer';
import { ASTNode, ASTNodeKind } from '../code_graph/types/ast';
import { RelatedContextOptions } from './RelatedContextBuilder';
import { ContextItem, GraphContextProvider } from './types';

/**
 * 调用链检索Provider
 * 需求参考：docs/requirements.md#4.2
 */
export class GraphContextProviderImpl implements GraphContextProvider {
  private keywordProvider: any; // KeywordContextProvider

  constructor(private repoIndexer: RepoIndexer) {
    // Import KeywordContextProviderImpl dynamically to avoid circular dependencies
    const { KeywordContextProviderImpl } = require('./KeywordContextProviderImpl');
    this.keywordProvider = new KeywordContextProviderImpl();
  }

  /**
   * 获取相关上下文（调用链信息 + 潜在可用上下文）
   * 包括两大部分：
   * 1. 真实已经发生的调用链：
   *    - 上游函数（调用当前函数的函数）
   *    - 下游函数（当前函数调用的函数）
   *    - 相关变量、结构体、宏定义
   * 2. 潜在可用上下文（通过BM25搜索）：
   *    - 当前模块可见范围内的符号
   *    - 全局范围内的相关符号
   */
  async getRelatedContext(functionNode: ASTNode, textBefore: string = '', textAfter: string = '', options?: RelatedContextOptions): Promise<ContextItem[]> {
    const contextItems: ContextItem[] = [];

    // 第一部分：真实已经发生的调用链
    const actualCallChainItems = await this.getActualCallChainContext(functionNode);
    contextItems.push(...actualCallChainItems);

    // 第二部分：潜在可用上下文（BM25搜索）
    const potentialContextItems = await this.getPotentialRelatedContext(functionNode);
    contextItems.push(...potentialContextItems);

    console.log(`📊 相关上下文统计: ${actualCallChainItems.length} 个调用链项, ${potentialContextItems.length} 个潜在相关项`);

    return contextItems;
  }

  /**
   * 获取真实已经发生的调用链上下文
   */
  private async getActualCallChainContext(functionNode: ASTNode): Promise<ContextItem[]> {
    // 查找当前函数的 CodeNode
    const codeNode = this.repoIndexer.findNodeInFile(
      functionNode.file || '',
      functionNode.name || '',
      functionNode.originalType || 'function'
    );

    if (!codeNode) {
      console.log(`❌ 未找到对应的 CodeNode，无法获取调用链上下文`);
      return [];
    }

    console.log(`✅ 找到 CodeNode: ${codeNode.name} (${codeNode.type})`);

    const contextItems: ContextItem[] = [];

    // 获取上游函数（调用当前函数的函数）
    const callers = this.repoIndexer.findCallersByName(codeNode.name, codeNode.type) || [];
    callers.forEach(caller => {
      const callerASTNode = this.codeNodeToASTNode(caller, 'caller');
      contextItems.push({
        source: 'graph' as const,
        contextType: 'related' as const,
        score: 0.9,
        content: caller.definition || caller.name, // 保留作为降级选项
        location: caller.location ? {
          file: caller.file || '',
          start: caller.location.start,
          end: caller.location.end,
        } : undefined,
        structuredData: {
          callChain: {
            astNode: callerASTNode,
            role: 'caller',
            relationship: `调用当前函数 ${functionNode.name}`
          }
        }
      });
    });

    // 获取下游函数（当前函数调用的函数）
    const callees = this.repoIndexer.findCalleesByName(codeNode.name, codeNode.type) || [];
    callees.forEach(callee => {
      const calleeASTNode = this.codeNodeToASTNode(callee, 'callee');
      contextItems.push({
        source: 'graph' as const,
        contextType: 'related' as const,
        score: 0.8,
        content: callee.definition || callee.name, // 保留作为降级选项
        location: callee.location ? {
          file: callee.file || '',
          start: callee.location.start,
          end: callee.location.end,
        } : undefined,
        structuredData: {
          callChain: {
            astNode: calleeASTNode,
            role: 'callee',
            relationship: `被当前函数 ${functionNode.name} 调用`
          }
        }
      });
    });

    return contextItems;
  }

  /**
   * 获取潜在相关上下文（通过BM25搜索）
   * 包括：模块可见范围 + 全局范围
   */
  private async getPotentialRelatedContext(functionNode: ASTNode): Promise<ContextItem[]> {
    const query = this.buildSearchQuery(functionNode);
    const currentFile = functionNode.file || '';

    const contextItems: ContextItem[] = [];

    // 1. 搜索当前模块可见范围（导入的模块）
    const moduleContextItems = await this.searchInModuleScope(currentFile, query);
    contextItems.push(...moduleContextItems.map(item => ({
      ...item,
      score: item.score * 0.7, // 模块范围相关性中等
      structuredData: {
        ...item.structuredData,
        callChain: {
          astNode: this.createASTNodeFromContextItem(item),
          role: 'related' as const,
          relationship: '模块可见范围内的相关符号'
        }
      }
    })));

    // 2. 搜索全局范围（排除当前文件和已搜索的模块文件）
    const searchedFiles = [currentFile, ...this.getModuleFiles(currentFile)];
    const globalContextItems = await this.searchGlobal(query, searchedFiles);
    contextItems.push(...globalContextItems.map(item => ({
      ...item,
      score: item.score * 0.5, // 全局范围相关性较低
      structuredData: {
        ...item.structuredData,
        callChain: {
          astNode: this.createASTNodeFromContextItem(item),
          role: 'related' as const,
          relationship: '全局范围内的相关符号'
        }
      }
    })));

    return contextItems;
  }

  /**
   * 在模块范围内搜索相关上下文
   */
  private async searchInModuleScope(currentFile: string, query: string): Promise<ContextItem[]> {
    // 使用 RepoIndexer 的模块相关节点查询
    const moduleNodes = this.repoIndexer.getModuleRelatedNodesByName('', currentFile, ASTNodeKind.MODULE) || [];

    // 将节点转换为 ContextItem 格式
    const contextItems = this.convertNodesToContextItems(moduleNodes, 'module');

    // 使用 KeywordContextProvider 进行 BM25 搜索
    this.keywordProvider.setDocuments(contextItems);
    return await this.keywordProvider.search(query, contextItems);
  }

  /**
   * 获取模块相关的文件列表
   */
  private getModuleFiles(currentFile: string): string[] {
    // 简化实现：通过图获取相关文件
    const graph = this.repoIndexer.getGraph();
    const relatedFiles: string[] = [];

    // 查找与当前文件有导入关系的文件
    for (const [_, node] of Array.from(graph.nodes)) {
      if (node.file && node.file !== currentFile) {
        // 简单的启发式：如果节点类型是 import 或 include，认为是相关文件
        if (node.type === 'import' || node.type === 'include' || node.type === 'require') {
          relatedFiles.push(node.file);
        }
      }
    }

    return [...new Set(relatedFiles)]; // 去重
  }

  /**
   * 从 ContextItem 创建 ASTNode
   */
  private createASTNodeFromContextItem(item: ContextItem): ASTNode {
    return {
      name: this.extractNameFromContent(item.content),
      kind: 'function' as any,
      originalType: 'unknown',
      text: item.content,
      children: [],
      location: item.location ? {
        start: item.location.start,
        end: item.location.end
      } : undefined,
      language: 'unknown',
      file: item.location?.file || '',
      metadata: {}
    };
  }

  /**
   * 从内容中提取名称
   */
  private extractNameFromContent(content: string): string {
    // 简单的名称提取：查找第一个标识符
    const match = content.match(/\b[a-zA-Z_][a-zA-Z0-9_]*\b/);
    return match ? match[0] : 'unknown';
  }

  /**
   * 将图节点转换为 ContextItem 格式
   */
  private convertNodesToContextItems(nodes: any[], scope: 'file' | 'module' | 'global'): ContextItem[] {
    return nodes.map(node => ({
      source: 'graph' as const,
      contextType: 'related' as const,
      score: 0, // 初始分数，将由BM25算法计算
      content: node.definition || node.name || '',
      location: node.location ? {
        file: node.file || '',
        start: node.location.start,
        end: node.location.end,
      } : undefined,
      structuredData: {
        codeSnippet: {
          language: node.language || 'unknown',
          filePath: node.file || '',
          startLine: node.location?.start.line || 0,
          endLine: node.location?.end.line || 0
        }
      }
    }));
  }

  /**
   * 获取相似上下文（相似函数）
   * 现在专注于真正的相似性搜索，不再包含BM25关键词搜索
   * 基于函数结构、参数类型、返回类型等进行相似性判断
   */
  async getSimilarContext(functionNode: ASTNode): Promise<ContextItem[]> {
    const contextItems: ContextItem[] = [];

    // 查找结构相似的函数
    const similarFunctions = await this.findStructurallySimilarFunctions(functionNode);

    similarFunctions.forEach(similarFunc => {
      const astNode = this.createASTNodeFromCodeNode(similarFunc);
      contextItems.push({
        source: 'graph' as const,
        contextType: 'similar' as const,
        score: similarFunc.similarityScore || 0.6,
        content: similarFunc.definition || similarFunc.name,
        location: similarFunc.location ? {
          file: similarFunc.file || '',
          start: similarFunc.location.start,
          end: similarFunc.location.end,
        } : undefined,
        structuredData: {
          codeSnippet: {
            astNode,
            language: similarFunc.language || 'unknown',
            filePath: similarFunc.file || '',
            startLine: similarFunc.location?.start.line || 0,
            endLine: similarFunc.location?.end.line || 0
          }
        }
      });
    });

    console.log(`📊 相似上下文统计: 找到 ${contextItems.length} 个结构相似的函数`);

    return contextItems.sort((a, b) => b.score - a.score).slice(0, 5); // 限制返回数量
  }

  /**
   * 查找结构相似的函数
   */
  private async findStructurallySimilarFunctions(functionNode: ASTNode): Promise<any[]> {
    const graph = this.repoIndexer.getGraph();
    const similarFunctions: any[] = [];

    // 获取当前函数的特征
    const currentFeatures = this.extractFunctionFeatures(functionNode);

    // 遍历所有函数节点，计算相似度
    for (const [_, node] of Array.from(graph.nodes)) {
      if (node.type === 'function' && node.name !== functionNode.name) {
        const nodeFeatures = this.extractFunctionFeatures({
          name: node.name,
          text: node.definition || '',
          originalType: node.type,
          file: node.file
        } as ASTNode);

        const similarity = this.calculateFunctionSimilarity(currentFeatures, nodeFeatures);

        if (similarity > 0.3) { // 相似度阈值
          similarFunctions.push({
            ...node,
            similarityScore: similarity
          });
        }
      }
    }

    return similarFunctions;
  }

  /**
   * 提取函数特征
   */
  private extractFunctionFeatures(functionNode: ASTNode): any {
    const text = functionNode.text || '';

    return {
      // 参数数量
      paramCount: (text.match(/\w+\s*:/g) || []).length,
      // 返回类型
      hasReturn: text.includes('return'),
      // 函数长度（行数）
      lineCount: text.split('\n').length,
      // 关键词
      keywords: this.extractKeywords(text),
      // 复杂度指标
      complexity: this.calculateComplexity(text)
    };
  }

  /**
   * 计算函数相似度
   */
  private calculateFunctionSimilarity(features1: any, features2: any): number {
    let similarity = 0;
    let totalWeight = 0;

    // 参数数量相似度
    const paramWeight = 0.3;
    const paramSim = 1 - Math.abs(features1.paramCount - features2.paramCount) / Math.max(features1.paramCount, features2.paramCount, 1);
    similarity += paramSim * paramWeight;
    totalWeight += paramWeight;

    // 返回类型相似度
    const returnWeight = 0.2;
    const returnSim = features1.hasReturn === features2.hasReturn ? 1 : 0;
    similarity += returnSim * returnWeight;
    totalWeight += returnWeight;

    // 长度相似度
    const lengthWeight = 0.2;
    const lengthSim = 1 - Math.abs(features1.lineCount - features2.lineCount) / Math.max(features1.lineCount, features2.lineCount, 1);
    similarity += lengthSim * lengthWeight;
    totalWeight += lengthWeight;

    // 关键词相似度（Jaccard相似度）
    const keywordWeight = 0.3;
    const keywordSim = this.calculateJaccardSimilarity(features1.keywords, features2.keywords);
    similarity += keywordSim * keywordWeight;
    totalWeight += keywordWeight;

    return totalWeight > 0 ? similarity / totalWeight : 0;
  }

  /**
   * 计算Jaccard相似度
   */
  private calculateJaccardSimilarity(set1: string[], set2: string[]): number {
    const s1 = new Set(set1);
    const s2 = new Set(set2);
    const intersection = new Set([...s1].filter(x => s2.has(x)));
    const union = new Set([...s1, ...s2]);

    return union.size > 0 ? intersection.size / union.size : 0;
  }

  /**
   * 计算代码复杂度
   */
  private calculateComplexity(text: string): number {
    // 简单的复杂度计算：控制结构数量
    const controlStructures = (text.match(/\b(if|for|while|switch|try)\b/g) || []).length;
    const nestedLevel = Math.max(...(text.match(/\{/g) || []).map((_, i, arr) =>
      arr.slice(0, i + 1).length - (text.substring(0, text.indexOf(arr[i])).match(/\}/g) || []).length
    ));

    return controlStructures + nestedLevel * 0.5;
  }

  /**
   * 基于输入文本进行上下文检索
   */
  async search(query: string, contextItems: ContextItem[]): Promise<ContextItem[]> {
    // TODO: 实现基于调用链的文本检索
    return [];
  }

  /**
   * 构建搜索查询字符串
   */
  private buildSearchQuery(functionNode: ASTNode): string {
    const parts: string[] = [];

    // 函数名
    if (functionNode.name) {
      parts.push(functionNode.name);
    }

    // 函数类型
    if (functionNode.originalType) {
      parts.push(functionNode.originalType);
    }

    // 从函数文本中提取关键词
    if (functionNode.text) {
      const keywords = this.extractKeywords(functionNode.text);
      parts.push(...keywords.slice(0, 5)); // 限制关键词数量
    }

    return parts.join(' ');
  }

  /**
   * 从代码文本中提取关键词
   */
  private extractKeywords(text: string): string[] {
    // 简单的关键词提取：移除字符串，提取标识符
    // 保留注释
    const cleanText = text
      .replace(/\/\/.*$/gm, '') // 移除行注释
      .replace(/"[^"]*"/g, '') // 移除双引号字符串
      .replace(/'[^']*'/g, '') // 移除单引号字符串
      .replace(/`[^`]*`/g, ''); // 移除模板字符串

    // 提取标识符（字母开头，包含字母数字下划线）
    const identifiers = cleanText.match(/\b[a-zA-Z_][a-zA-Z0-9_]*\b/g) || [];

    // 过滤常见关键字和短词
    const commonKeywords = new Set(['function', 'const', 'let', 'var', 'if', 'else', 'for', 'while', 'return', 'true', 'false', 'null', 'undefined']);

    return identifiers
      .filter(word => word.length > 2 && !commonKeywords.has(word.toLowerCase()))
      .slice(0, 10); // 限制数量
  }

  /**
   * 在指定文件中搜索相似上下文
   */
  private async searchInFile(filePath: string, query: string): Promise<ContextItem[]> {
    // 获取文件中的所有函数节点
    const fileNodes = this.getNodesByFile(filePath);

    // 将节点转换为 ContextItem 格式
    const contextItems = this.convertNodesToContextItems(fileNodes, 'file');

    // 使用 KeywordContextProvider 进行 BM25 搜索
    this.keywordProvider.setDocuments(contextItems);
    return await this.keywordProvider.search(query, contextItems);
  }

  /**
   * 在模块范围内搜索相似上下文（基于 import 关系）
   */
  private async searchInModule(currentFile: string, query: string): Promise<ContextItem[]> {
    // 使用 RepoIndexer 的模块相关节点查询
    const moduleNodes = this.repoIndexer.getModuleRelatedNodesByName('', currentFile) || [];

    // 将节点转换为 ContextItem 格式
    const contextItems = this.convertNodesToContextItems(moduleNodes, 'module');

    // 使用 KeywordContextProvider 进行 BM25 搜索
    this.keywordProvider.setDocuments(contextItems);
    return await this.keywordProvider.search(query, contextItems);
  }

  /**
   * 在全局范围内搜索相关上下文
   */
  private async searchGlobal(query: string, excludeFiles: string[]): Promise<ContextItem[]> {
    // 获取所有节点，排除指定文件
    const allNodes = this.getAllNodes().filter(node => !excludeFiles.includes(node.file || ''));

    // 将节点转换为 ContextItem 格式
    const contextItems = this.convertNodesToContextItems(allNodes, 'global');

    // 使用 KeywordContextProvider 进行 BM25 搜索
    this.keywordProvider.setDocuments(contextItems);
    return await this.keywordProvider.search(query, contextItems);
  }

  /**
   * 获取指定文件的所有节点
   */
  private getNodesByFile(filePath: string): any[] {
    const graph = this.repoIndexer.getGraph();
    const nodes: any[] = [];

    for (const [_, node] of Array.from(graph.nodes)) {
      if (node.file === filePath || node.file === filePath.replace(/\\/g, '/')) {
        nodes.push(node);
      }
    }

    return nodes;
  }

  /**
   * 获取所有节点
   */
  private getAllNodes(): any[] {
    const graph = this.repoIndexer.getGraph();
    return Array.from(graph.nodes.values());
  }

  // Note: performBM25Search method removed - now using KeywordContextProvider

  // Note: tokenize and isCommonKeyword methods removed - now using KeywordContextProvider

  /**
   * 去重上下文项
   */
  private deduplicateContexts(contexts: ContextItem[]): ContextItem[] {
    const seen = new Set<string>();
    return contexts.filter(item => {
      const key = `${item.location?.file || ''}:${item.location?.start.line || 0}:${item.content.substring(0, 50)}`;
      if (seen.has(key)) {
        return false;
      }
      seen.add(key);
      return true;
    });
  }

  /**
   * 从 CodeNode 创建 ASTNode（用于相似函数）
   */
  private createASTNodeFromCodeNode(codeNode: any): ASTNode {
    return {
      name: codeNode.name,
      kind: 'function' as any,
      originalType: codeNode.type,
      text: codeNode.definition,
      children: [],
      location: codeNode.location,
      language: codeNode.language,
      file: codeNode.file,
      metadata: {}
    };
  }

  /**
   * 将 GraphCodeNode 转换为 ASTNode
   */
  private codeNodeToASTNode(codeNode: any, role: 'caller' | 'callee'): ASTNode {
    return {
      name: codeNode.name,
      kind: 'function' as any, // 使用字符串避免导入问题
      originalType: codeNode.type,
      text: codeNode.definition,
      children: [],
      location: codeNode.location,
      language: codeNode.language,
      file: codeNode.file,
      metadata: {
        role: role, // 标记是调用者还是被调用者
        ...codeNode.metadata
      }
    };
  }
}