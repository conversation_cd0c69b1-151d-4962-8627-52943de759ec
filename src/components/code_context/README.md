# 代码上下文模块

本模块提供了代码补全提示词的构建功能，包括相关上下文提取和结构化提示词生成。

## 主要组件

### 1. RelatedContextBuilder

负责提取与当前代码相关的上下文信息，包括：
- 当前函数信息
- 所属类信息
- 调用链关系
- 隐藏变量（不可见但对模型有用的变量）

### 2. CompletionPromptBuilder

负责构建结构化的代码补全提示词，按照设计文档的格式组织各个部分，使用模型原始支持的补全token。

## 使用示例

### 基本用法

```typescript
import { CompletionPromptBuilder } from './CompletionPromptBuilder';
import { RelatedContextBuilder } from './RelatedContextBuilder';
import { RepoIndexer } from '../code_graph/repoIndexer/repoIndexer';

// 初始化
const repoIndexer = new RepoIndexer();
const relatedContextBuilder = new RelatedContextBuilder(repoIndexer);
const promptBuilder = new CompletionPromptBuilder(relatedContextBuilder);

// 构建提示词
const functionNode = {
  name: 'process_data',
  file: 'main.py',
  originalType: 'function',
  kind: 'function_definition',
  children: []
};

const textBefore = 'def process_data(items):\n    result = []\n    for item in items:';
const textAfter = '\n    return result';

const prompt = await promptBuilder.buildCompletionPrompt(
  functionNode,
  textBefore,
  textAfter
);

console.log(prompt);
```

### 自定义选项

```typescript
const options = {
  system: 'You are an expert Python developer.',
  systemRules: `
1. 使用Python 3.8+语法
2. 遵循PEP 8代码规范
3. 添加适当的类型注解
  `.trim(),
  environment: `
- 项目类型: Web应用
- 框架: FastAPI
- 数据库: PostgreSQL
  `.trim(),
  userRules: `
- 优先使用异步函数
- 添加详细的错误处理
  `.trim(),
  completionTokens: {
    start: '{{complete_code_here}}',
    fill: '{{complete_code_here}}',
    end: '{{complete_code_here}}'
  },
  includeRelatedContext: true,
  relatedContextOptions: {
    mode: 'completion',
    maxHiddenVariables: 5
  }
};

const prompt = await promptBuilder.buildCompletionPrompt(
  functionNode,
  textBefore,
  textAfter,
  options
);
```

### 模型特定提示词

```typescript
// DeepSeek模型 - 使用REDACTED_SPECIAL_TOKEN
const deepseekPrompt = await promptBuilder.buildModelSpecificPrompt(
  functionNode,
  textBefore,
  textAfter,
  'deepseek'
);

// Qwen模型 - 使用<|fim_prefix|>、<|fim_suffix|>、<|fim_middle|>
const qwenPrompt = await promptBuilder.buildModelSpecificPrompt(
  functionNode,
  textBefore,
  textAfter,
  'qwen'
);
```

## 提示词结构

生成的提示词按照以下结构组织：

```
You are a code completion assistant.

请结合上下文补全代码

<system_rules>
系统规则...
</system_rules>

<env>
环境信息...
</env>

<user_rules>
用户自定义规则...
</user_rules>

<related_context>
相关上下文...
</related_context>

<similar_context>
相似代码片段...
</similar_context>

## 补全如下代码:

REDACTED_SPECIAL_TOKEN
def process_data(items):
    result = []
    for item in items:
REDACTED_SPECIAL_TOKEN
        if item.is_valid():
            result.append(item.process())
    return result
REDACTED_SPECIAL_TOKEN
```

## 配置选项

### CompletionPromptOptions

| 选项 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `system` | `string` | `"You are a code completion assistant."` | 系统提示词 |
| `instruction` | `string` | `"请结合上下文补全代码"` | 指令 |
| `systemRules` | `string` | - | 系统规则 |
| `environment` | `string` | - | 环境信息 |
| `userRules` | `string` | - | 用户自定义规则 |
| `includeRelatedContext` | `boolean` | `true` | 是否包含相关上下文 |
| `includeSimilarContext` | `boolean` | `false` | 是否包含相似上下文 |
| `relatedContextOptions` | `RelatedContextOptions` | - | 相关上下文选项 |
| `similarContext` | `string` | - | 相似上下文内容 |
| `completionToken` | `string` | - | 补全标记（兼容性） |
| `completionTokens` | `object` | - | 补全token对象 |

### completionTokens 对象

```typescript
{
  start: string;   // 开始token
  fill: string;    // 填充token
  end: string;     // 结束token
}
```

### RelatedContextOptions

| 选项 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `mode` | `'completion' \| 'check'` | `'completion'` | 上下文模式 |
| `includeEnclosingClass` | `boolean` | `false` | 是否包含所属类 |
| `includeHiddenVariables` | `boolean` | `true` | 是否包含隐藏变量 |
| `maxCallChainDepth` | `number` | `1` | 调用链深度 |
| `maxHiddenVariables` | `number` | `10` | 隐藏变量数量限制 |

## 补全标记

支持不同模型的补全标记：

### DeepSeek Coder
```typescript
{
  start: 'REDACTED_SPECIAL_TOKEN',
  fill: 'REDACTED_SPECIAL_TOKEN',
  end: 'REDACTED_SPECIAL_TOKEN'
}
```

### Qwen Coder
```typescript
{
  start: '<|fim_prefix|>',
  fill: '<|fim_suffix|>',
  end: '<|fim_middle|>'
}
```

### 自定义
```typescript
{
  start: '{{complete_code_here}}',
  fill: '{{complete_code_here}}',
  end: '{{complete_code_here}}'
}
```

## 隐藏变量分类

提取的隐藏变量按以下类型分类：

- `function_param`: 函数参数
- `class_property`: 类属性
- `global`: 全局变量
- `imported`: 导入的模块
- `constant`: 常量
- `register`: 寄存器变量（NP语言）
- `local`: 局部变量
- `unknown`: 未知类型

## 设计原则

1. **使用模型原始支持的补全token**：充分利用模型的原有能力
2. **结构化提示词**：使用XML标签组织不同部分，便于后端处理
3. **变量提取粒度控制**：只提取顶级变量标识符，避免变量爆炸
4. **数量限制**：通过 `maxHiddenVariables` 参数控制隐藏变量数量
5. **智能分类**：基于命名规则和上下文自动分类变量

## 注意事项

1. 补全token使用模型原始支持的特殊token，不是自定义标记
2. 指令中不包含具体的补全标记，使用通用描述
3. 用户输入部分使用完整的start/fill/end token结构
4. 相关上下文使用 XML 标签包裹，便于后端处理
5. 变量提取只提取顶级变量标识符，避免变量爆炸 