import * as vscode from 'vscode';
import { CodeGraphBuilder } from '../code_graph/builder/CodeGraphBuilder';
import { ParserManager } from '../code_graph/parser/ParserManager';
import { RepoIndexerManager } from '../code_graph/repoIndexer/repoIndexerManager';
import { ASTNode } from '../code_graph/types/ast';
import { GraphCodeNode } from '../code_graph/types/graph';
import { DEFAULT_CONTEXT_FUSION_CONFIG } from './config';
import { ContextCollector } from './ContextCollector';
import { ContextFusionEngine, ExtendedRetrievalFusionConfig } from './ContextFusionEngine';
import { EmbeddingContextProviderImpl } from './EmbeddingContextProviderImpl';
import { GraphContextProviderImpl } from './GraphContextProviderImpl';
import { KeywordContextProviderImpl } from './KeywordContextProviderImpl';
import { ContextItem, GraphContextProvider } from './types';
import { UserCustomContextProviderImpl } from './UserCustomContextProviderImpl';

// 默认的检索融合配置
const defaultRetrievalFusionConfig: ExtendedRetrievalFusionConfig = DEFAULT_CONTEXT_FUSION_CONFIG;

/**
 * 空实现，用于没有索引时的降级处理
 */
class EmptyGraphContextProvider implements GraphContextProvider {
  async getRelatedContext(functionNode: ASTNode): Promise<ContextItem[]> {
    return [];
  }

  async getSimilarContext(functionNode: ASTNode): Promise<ContextItem[]> {
    return [];
  }

  async search(query: string, contextItems: ContextItem[]): Promise<ContextItem[]> {
    return [];
  }
}

/**
 * 获取RepoIndexer实例，如果未初始化则返回null
 */
function getRepoIndexer() {
  return RepoIndexerManager.getInstance().getRepoIndexer();
}

/**
 * 获取或创建ContextCollector实例
 */
function getContextCollector(): ContextCollector {
  const repoIndexer = getRepoIndexer();
  
  if (!repoIndexer) {
    // 没有索引时，使用空实现
    const emptyGraphProvider = new EmptyGraphContextProvider();
    const keywordProvider = new KeywordContextProviderImpl();
    const embeddingProvider = new EmbeddingContextProviderImpl();
    const userCustomProvider = new UserCustomContextProviderImpl();
    const fusionEngine = new ContextFusionEngine(defaultRetrievalFusionConfig);
    
    return new ContextCollector(
      emptyGraphProvider,
      keywordProvider,
      embeddingProvider,
      userCustomProvider,
      fusionEngine
    );
  }

  // 有索引时，使用完整实现
  const graphProvider = new GraphContextProviderImpl(repoIndexer);
  const keywordProvider = new KeywordContextProviderImpl();
  const embeddingProvider = new EmbeddingContextProviderImpl();
  const userCustomProvider = new UserCustomContextProviderImpl();
  const fusionEngine = new ContextFusionEngine(defaultRetrievalFusionConfig);

  return new ContextCollector(
    graphProvider,
    keywordProvider,
    embeddingProvider,
    userCustomProvider,
    fusionEngine
  );
}



/**
 * 获取当前编辑函数的上下文信息（动态解析，适用于代码续写场景）
 * 这个方法会动态解析当前文件内容，获取实时的函数位置信息
 * 使用增强的多维度上下文检索能力，包括图谱检索、BM25关键词搜索、向量搜索和用户自定义片段
 */
export async function getCurrentFunctionContextDynamic(
  config?: ExtendedRetrievalFusionConfig
): Promise<{
  currentFunction: GraphCodeNode | null;
  relatedContext: ContextItem[];
  similarContext: ContextItem[];
  allContext: ContextItem[];
  contextSummary: string;
}> {
  const editor = vscode.window.activeTextEditor;
  if (!editor) {
    return {
      currentFunction: null,
      relatedContext: [],
      similarContext: [],
      allContext: [],
      contextSummary: ''
    };
  }

  const document = editor.document;
  const position = editor.selection.active;
  const parserManager = new ParserManager();

  try {
    // 动态解析当前文件内容，获取实时的AST
    const astNode = await parserManager.findFunctionAtPosition(document, position);
    if (!astNode) {
      return {
        currentFunction: null,
        relatedContext: [],
        similarContext: [],
        allContext: [],
        contextSummary: ''
      };
    }

    // 将AST节点转换为GraphCodeNode，使用实时的位置信息
    const currentFunction = astNodeToGraphCodeNode(astNode, document.uri.fsPath);

    // 获取当前函数的代码内容用于检索
    const functionCode = currentFunction.definition || currentFunction.name;

    // 使用配置或默认配置
    const fusionConfig = config || defaultRetrievalFusionConfig;

    // 获取ContextCollector实例
    const contextCollector = getContextCollector();

    // 使用增强的上下文收集能力
    const { relatedContext, similarContext } = await contextCollector.collectContextsSeparately(
      astNode,
      functionCode,
      fusionConfig
    );
    // 合并所有上下文
    const allContext = [...relatedContext, ...similarContext];

    // 构建上下文摘要
    const contextSummary = buildEnhancedContextSummary(currentFunction, relatedContext, similarContext);

    return {
      currentFunction,
      relatedContext,
      similarContext,
      allContext,
      contextSummary
    };

  } catch (error) {
    console.error('获取动态函数上下文失败:', error);
    return {
      currentFunction: null,
      relatedContext: [],
      similarContext: [],
      allContext: [],
      contextSummary: ''
    };
  }
}

// ASTNode 转 GraphCodeNode 辅助函数
function astNodeToGraphCodeNode(astNode: any, filePath: string): GraphCodeNode {
  return new CodeGraphBuilder().parserASTNodeToGraphNode(astNode, filePath);
}

/**
 * 续写主流程：获取融合后的上下文
 * @param code 当前函数源码
 * @param position 光标位置
 * @param config 检索融合权重配置
 * @returns 融合后的 ContextItem[]
 */
export async function collectContextForCompletion(
  code: string,
  position: vscode.Position,
  config: ExtendedRetrievalFusionConfig = defaultRetrievalFusionConfig
): Promise<ContextItem[]> {
  // 1. 用 ParserManager 定位当前函数 ASTNode
  const document = vscode.window.activeTextEditor?.document;
  if (!document) { return []; }
  const parserManager = new ParserManager();
  const astNode: any | null = await parserManager.findFunctionAtPosition(document, position);

  if (!astNode) { return []; }

  // 2. 使用 ContextCollector 收集融合上下文
  const contextCollector = getContextCollector();
  const contextItems = await contextCollector.collectSimilarContext(astNode, code, config);

  return contextItems;
}

/**
 * 构建增强的上下文摘要
 * @param currentFunction 当前函数
 * @param relatedContext 相关上下文
 * @param similarContext 相似上下文
 * @returns 上下文摘要字符串
 */
function buildEnhancedContextSummary(
  currentFunction: GraphCodeNode,
  relatedContext: ContextItem[],
  similarContext: ContextItem[]
): string {
  const contextParts: string[] = [];

  // 当前函数信息
  contextParts.push(`当前函数: ${currentFunction.name}`);
  if (currentFunction.file) {
    contextParts.push(`文件: ${currentFunction.file}`);
  }

  // 相关上下文统计
  if (relatedContext.length > 0) {
    const sourceStats = getContextSourceStats(relatedContext);
    contextParts.push(`\n相关上下文 (${relatedContext.length}项):`);
    for (const [source, count] of Object.entries(sourceStats)) {
      contextParts.push(`  ${source}: ${count}项`);
    }

    // 显示前几个高分项目
    const topItems = relatedContext.slice(0, 3);
    for (const item of topItems) {
      contextParts.push(`  - [${item.source}] 评分:${item.score.toFixed(2)} ${item.content.substring(0, 50)}...`);
    }
  }

  // 相似上下文统计
  if (similarContext.length > 0) {
    const sourceStats = getContextSourceStats(similarContext);
    contextParts.push(`\n相似上下文 (${similarContext.length}项):`);
    for (const [source, count] of Object.entries(sourceStats)) {
      contextParts.push(`  ${source}: ${count}项`);
    }

    // 显示前几个高分项目
    const topItems = similarContext.slice(0, 3);
    for (const item of topItems) {
      contextParts.push(`  - [${item.source}] 评分:${item.score.toFixed(2)} ${item.content.substring(0, 50)}...`);
    }
  }

  return contextParts.join('\n');
}

/**
 * 获取上下文来源统计
 * @param contextItems 上下文项目列表
 * @returns 来源统计对象
 */
function getContextSourceStats(contextItems: ContextItem[]): Record<string, number> {
  const stats: Record<string, number> = {};
  for (const item of contextItems) {
    stats[item.source] = (stats[item.source] || 0) + 1;
  }
  return stats;
}



/**
 * 调试函数：检查RepoIndexer状态
 */
export function debugRepoIndexerStatus(): {
  hasRepoIndexer: boolean;
  repoIndexerType: string;
  nodeCount: number;
  workspacePath: string | null;
} {
  const repoIndexer = getRepoIndexer();

  if (!repoIndexer) {
    return {
      hasRepoIndexer: false,
      repoIndexerType: 'null',
      nodeCount: 0,
      workspacePath: null
    };
  }

  try {
    const graph = repoIndexer.getGraph();
    return {
      hasRepoIndexer: true,
      repoIndexerType: repoIndexer.constructor.name,
      nodeCount: graph.nodes.size,
      workspacePath: repoIndexer.workspacePath
    };
  } catch (error) {
    return {
      hasRepoIndexer: true,
      repoIndexerType: repoIndexer.constructor.name,
      nodeCount: -1, // 表示获取图数据失败
      workspacePath: repoIndexer.workspacePath
    };
  }
}
