import { ASTNode } from '../code_graph/types/ast';
import { ContextFusionEngine, ExtendedRetrievalFusionConfig } from './ContextFusionEngine';
import { RelatedContextOptions } from './RelatedContextBuilder';
import { ContextItem, EmbeddingContextProvider, GraphContextProvider, KeywordContextProvider, UserCustomContextProvider } from './types';

export class ContextCollector {
  constructor(
    private graphProvider: GraphContextProvider,
    private keywordProvider: KeywordContextProvider,
    private embeddingProvider: EmbeddingContextProvider,
    private userCustomProvider: UserCustomContextProvider,
    private fusionEngine: ContextFusionEngine
  ) { }

  async collectContextForCompletion(functionNode: ASTNode, code: string): Promise<ContextItem[]> {
    const [relatedItems, similarItems, bm25Items, embeddingItems, userSnippetsItems] = await Promise.all([
      this.graphProvider.getRelatedContext(functionNode),
      this.graphProvider.getSimilarContext(functionNode),
      this.keywordProvider.searchByKeywords(code),
      this.embeddingProvider.searchByEmbedding(code),
      this.userCustomProvider.searchByUserSnippets(code, functionNode.language || 'javascript'),
    ]);
    
    const graphItems = [...relatedItems, ...similarItems];
    return this.fusionEngine.fuse(graphItems, bm25Items, embeddingItems, userSnippetsItems);
  }

  /**
   * 新增：主流程统一入口
   */
  async collectContext(
    astNode: ASTNode,
    code: string,
    config: ExtendedRetrievalFusionConfig
  ): Promise<ContextItem[]> {
    const fusionEngine = new ContextFusionEngine(config);

    // 收集相关上下文（调用链）
    const relatedGraphItems = await this.graphProvider.getRelatedContext(astNode);

    // 收集相似上下文（相似函数 + BM25关键词搜索 + 向量搜索 + 用户自定义片段）
    const [similarGraphItems, bm25Items, embeddingItems, userSnippetsItems] = await Promise.all([
      this.graphProvider.getSimilarContext(astNode),
      this.keywordProvider.searchByKeywords(code),
      this.embeddingProvider.searchByEmbedding(code),
      this.userCustomProvider.searchByUserSnippets(code, astNode.language || 'javascript'),
    ]);

    // 融合所有上下文
    const relatedContext = fusionEngine.fuse(relatedGraphItems, [], []);
    const similarContext = fusionEngine.fuse(similarGraphItems, bm25Items, embeddingItems, userSnippetsItems);

    // 合并相关和相似上下文
    return [...relatedContext, ...similarContext];
  }

  /**
   * 分别收集相关上下文和相似上下文
   */
  async collectContextsSeparately(
    astNode: ASTNode,
    code: string,
    config: ExtendedRetrievalFusionConfig
  ): Promise<{
    relatedContext: ContextItem[];
    similarContext: ContextItem[];
  }> {
    // 收集相关上下文（调用链）
    const relatedGraphItems = await this.graphProvider.getRelatedContext(astNode);

    // 收集相似上下文（相似函数 + BM25关键词搜索 + 向量搜索 + 用户自定义片段）
    const [similarGraphItems, bm25Items, embeddingItems, userSnippetsItems] = await Promise.all([
      this.graphProvider.getSimilarContext(astNode),
      this.keywordProvider.searchByKeywords(code),
      this.embeddingProvider.searchByEmbedding(code),
      this.userCustomProvider.searchByUserSnippets(code, astNode.language || 'c'),
    ]);

    // 分别融合相关上下文和相似上下文
    const relatedContext = this.fusionEngine.fuse(relatedGraphItems, [], []);
    const similarContext = this.fusionEngine.fuse(similarGraphItems, bm25Items, embeddingItems, userSnippetsItems);

    return {
      relatedContext,
      similarContext
    };
  }

  /**
   * 只收集相关上下文（调用链）
   */
  async collectRelatedContext(
    astNode: ASTNode,
    code: string,
    config: ExtendedRetrievalFusionConfig,
    textBefore?: string,
    textAfter?: string,
    options?: RelatedContextOptions
  ): Promise<ContextItem[]> {
    const relatedGraphItems = await this.graphProvider.getRelatedContext(astNode, textBefore, textAfter, options);
    return this.fusionEngine.fuse(relatedGraphItems, [], []);
  }

  /**
   * 只收集相似上下文（相似函数 + BM25关键词搜索 + 向量搜索 + 用户自定义片段）
   */
  async collectSimilarContext(
    astNode: ASTNode,
    code: string,
    config: ExtendedRetrievalFusionConfig
  ): Promise<ContextItem[]> {
    const [similarGraphItems, bm25Items, embeddingItems, userSnippetsItems] = await Promise.all([
      this.graphProvider.getSimilarContext(astNode),
      this.keywordProvider.searchByKeywords(code),
      this.embeddingProvider.searchByEmbedding(code),
      this.userCustomProvider.searchByUserSnippets(code, astNode.language || 'javascript'),
    ]);

    return this.fusionEngine.fuse(similarGraphItems, bm25Items, embeddingItems, userSnippetsItems);
  }
} 