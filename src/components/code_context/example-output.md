# 提示词输出示例

## DeepSeek Coder 模型示例

```
You are an AI programming assistant, utilizing the DeepSeek Coder model, developed by DeepSeek Company, and you only answer questions related to computer science.

请结合上下文补全代码

<system_rules>
1. 使用Python 3.8+语法
2. 遵循PEP 8代码规范
3. 添加适当的类型注解
</system_rules>

<env>
- 项目类型: Web应用
- 框架: FastAPI
- 数据库: PostgreSQL
</env>

<user_rules>
- 优先使用异步函数
- 添加详细的错误处理
- 使用日志记录关键操作
</user_rules>

<related_context>
### Related Context ###

# Current Function:
def process_data(items: List[Dict]) -> List[Dict]:

# Hidden Variables Model Might Need:
- items: function_param, type=List[Dict]
- result: unknown, type=unknown
- self.cache: class_property, type=Dict
- g_config: global, type=Config
- np: imported, type=module (numpy)
</related_context>

<similar_context>
def similar_function(data):
    result = []
    for item in data:
        if item.is_valid():
            result.append(item.process())
    return result
</similar_context>

## 补全如下代码:

REDACTED_SPECIAL_TOKEN
def process_data(items: List[Dict]) -> List[Dict]:
    result = []
    for item in items:
REDACTED_SPECIAL_TOKEN
        if item.get('valid', False):
            processed = item.copy()
            processed['processed'] = True
            result.append(processed)
    return result
REDACTED_SPECIAL_TOKEN
```

## Qwen Coder 模型示例

```
You are a code completion assistant.

请结合上下文补全代码

<system_rules>
1. 使用Python 3.8+语法
2. 遵循PEP 8代码规范
3. 添加适当的类型注解
</system_rules>

<env>
- 项目类型: Web应用
- 框架: FastAPI
- 数据库: PostgreSQL
</env>

<user_rules>
- 优先使用异步函数
- 添加详细的错误处理
- 使用日志记录关键操作
</user_rules>

<related_context>
### Related Context ###

# Current Function:
def process_data(items: List[Dict]) -> List[Dict]:

# Hidden Variables Model Might Need:
- items: function_param, type=List[Dict]
- result: unknown, type=unknown
- self.cache: class_property, type=Dict
- g_config: global, type=Config
- np: imported, type=module (numpy)
</related_context>

<similar_context>
def similar_function(data):
    result = []
    for item in data:
        if item.is_valid():
            result.append(item.process())
    return result
</similar_context>

## 补全如下代码:

<|fim_prefix|>
def process_data(items: List[Dict]) -> List[Dict]:
    result = []
    for item in items:
<|fim_suffix|>
        if item.get('valid', False):
            processed = item.copy()
            processed['processed'] = True
            result.append(processed)
    return result
<|fim_middle|>
```

## 自定义标记示例

```
You are a code completion assistant.

请结合上下文补全代码

<system_rules>
1. 使用Python 3.8+语法
2. 遵循PEP 8代码规范
3. 添加适当的类型注解
</system_rules>

<env>
- 项目类型: Web应用
- 框架: FastAPI
- 数据库: PostgreSQL
</env>

<user_rules>
- 优先使用异步函数
- 添加详细的错误处理
- 使用日志记录关键操作
</user_rules>

<related_context>
### Related Context ###

# Current Function:
def process_data(items: List[Dict]) -> List[Dict]:

# Hidden Variables Model Might Need:
- items: function_param, type=List[Dict]
- result: unknown, type=unknown
- self.cache: class_property, type=Dict
- g_config: global, type=Config
- np: imported, type=module (numpy)
</related_context>

<similar_context>
def similar_function(data):
    result = []
    for item in data:
        if item.is_valid():
            result.append(item.process())
    return result
</similar_context>

## 补全如下代码:

{{complete_code_here}}
def process_data(items: List[Dict]) -> List[Dict]:
    result = []
    for item in items:
{{complete_code_here}}
        if item.get('valid', False):
            processed = item.copy()
            processed['processed'] = True
            result.append(processed)
    return result
{{complete_code_here}}
```

## 关键特点

1. **使用模型原始支持的补全token**：DeepSeek使用`REDACTED_SPECIAL_TOKEN`，Qwen使用`<|fim_prefix|>`、`<|fim_suffix|>`、`<|fim_middle|>`

2. **指令简洁**：指令中不包含具体的补全标记，使用通用描述"请结合上下文补全代码"

3. **完整的token结构**：用户输入部分使用start/fill/end三个token，符合模型训练格式

4. **结构化上下文**：使用XML标签组织不同部分，便于后端处理

5. **智能变量提取**：只提取有用的隐藏变量，避免冗余，支持数量限制 