import { ExtendedRetrievalFusionConfig } from './ContextFusionEngine';

/**
 * 默认的上下文融合配置
 */
export const DEFAULT_CONTEXT_FUSION_CONFIG: ExtendedRetrievalFusionConfig = {
  // 基础权重配置
  graphWeight: 0.4,           // 图谱相似度权重
  bm25Weight: 0.3,            // BM25关键词搜索权重
  embeddingWeight: 0.3,       // 向量搜索权重
  
  // 用户自定义片段权重配置
  userSnippetsWeight: 0.2,    // 用户片段基础权重
  userSnippetsPriorityMultiplier: 1.5  // 用户片段优先级倍数
};

/**
 * 高优先级用户片段配置
 */
export const HIGH_PRIORITY_USER_SNIPPETS_CONFIG: ExtendedRetrievalFusionConfig = {
  ...DEFAULT_CONTEXT_FUSION_CONFIG,
  userSnippetsWeight: 0.4,    // 提高用户片段权重
  userSnippetsPriorityMultiplier: 2.0  // 提高优先级倍数
};

/**
 * 低优先级用户片段配置
 */
export const LOW_PRIORITY_USER_SNIPPETS_CONFIG: ExtendedRetrievalFusionConfig = {
  ...DEFAULT_CONTEXT_FUSION_CONFIG,
  userSnippetsWeight: 0.1,    // 降低用户片段权重
  userSnippetsPriorityMultiplier: 1.2  // 降低优先级倍数
};

/**
 * 获取上下文融合配置
 * @param configName 配置名称
 * @returns 融合配置
 */
export function getContextFusionConfig(configName: string = 'default'): ExtendedRetrievalFusionConfig {
  switch (configName) {
    case 'high-priority':
      return HIGH_PRIORITY_USER_SNIPPETS_CONFIG;
    case 'low-priority':
      return LOW_PRIORITY_USER_SNIPPETS_CONFIG;
    case 'default':
    default:
      return DEFAULT_CONTEXT_FUSION_CONFIG;
  }
} 