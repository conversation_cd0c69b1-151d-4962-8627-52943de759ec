import { UserCustomContentManager } from '../user_snippets/UserCustomContentManager';
import { ContextItem, UserCustomContextProvider } from './types';

export class UserCustomContextProviderImpl implements UserCustomContextProvider {
  private userContentManager = UserCustomContentManager.getInstance();

  /**
   * 基于输入文本进行上下文检索
   */
  async search(query: string, contextItems: ContextItem[]): Promise<ContextItem[]> {
    // 这里可以添加基于查询的用户自定义内容搜索逻辑
    // 目前主要使用 searchByUserSnippets 方法
    return [];
  }

  /**
   * 获取匹配的用户自定义代码片段
   */
  async searchByUserSnippets(code: string, language: string): Promise<ContextItem[]> {
    try {
      // 获取匹配的用户代码片段
      const matchingSnippets = await this.userContentManager.findMatchingSnippets(code, language, 5);
      
      // 转换为 ContextItem 格式
      const contextItems: ContextItem[] = matchingSnippets.map(result => ({
        source: 'user-snippets',
        contextType: 'similar', // 用户片段属于相似上下文
        score: result.score, // 使用计算好的综合得分
        content: result.snippet.code,
        userSnippet: {
          id: result.snippet.id,
          name: result.snippet.name,
          priority: result.snippet.priority,
          similarity: result.similarity
        }
      }));

      return contextItems;
    } catch (error) {
      console.error('获取用户自定义代码片段失败:', error);
      return [];
    }
  }
} 