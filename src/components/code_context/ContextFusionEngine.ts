import { ContextItem, RetrievalFusionConfig } from './types';

export interface ExtendedRetrievalFusionConfig extends RetrievalFusionConfig {
  userSnippetsWeight?: number;  // 用户自定义片段权重
  userSnippetsPriorityMultiplier?: number;  // 用户片段优先级倍数
}

export class ContextFusionEngine {
  constructor(private config: ExtendedRetrievalFusionConfig) {
    // 设置默认值
    this.config.userSnippetsWeight = this.config.userSnippetsWeight ?? 0.2;
    this.config.userSnippetsPriorityMultiplier = this.config.userSnippetsPriorityMultiplier ?? 1.5;
  }

  fuse(
    graphItems: ContextItem[],
    bm25Items: ContextItem[],
    embeddingItems: ContextItem[],
    userSnippetsItems: ContextItem[] = []
  ): ContextItem[] {
    // 简单加权融合，去重，排序
    const all: ContextItem[] = [];
    const map = new Map<string, ContextItem>();
    
    // 处理图谱项目
    for (const item of graphItems) {
      map.set(item.content, { ...item, score: item.score * this.config.graphWeight });
    }
    
    // 处理BM25项目
    for (const item of bm25Items) {
      if (map.has(item.content)) {
        map.set(item.content, {
          ...item,
          score: map.get(item.content)!.score + item.score * this.config.bm25Weight,
        });
      } else {
        map.set(item.content, { ...item, score: item.score * this.config.bm25Weight });
      }
    }
    
    // 处理向量搜索项目
    for (const item of embeddingItems) {
      if (map.has(item.content)) {
        map.set(item.content, {
          ...item,
          score: map.get(item.content)!.score + item.score * this.config.embeddingWeight,
        });
      } else {
        map.set(item.content, { ...item, score: item.score * this.config.embeddingWeight });
      }
    }
    
    // 处理用户自定义片段项目
    for (const item of userSnippetsItems) {
      // 应用优先级倍数
      const adjustedScore = item.score * (this.config.userSnippetsPriorityMultiplier || 1.5);
      
      if (map.has(item.content)) {
        map.set(item.content, {
          ...item,
          score: map.get(item.content)!.score + adjustedScore * (this.config.userSnippetsWeight || 0.2),
        });
      } else {
        map.set(item.content, { 
          ...item, 
          score: adjustedScore * (this.config.userSnippetsWeight || 0.2) 
        });
      }
    }
    
    all.push(...Array.from(map.values()));
    all.sort((a, b) => b.score - a.score);
    return all;
  }
} 