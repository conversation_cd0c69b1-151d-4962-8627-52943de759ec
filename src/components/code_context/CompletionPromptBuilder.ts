import { ASTNode } from '../code_graph/types/ast';
import { RelatedContextBuilder, RelatedContextOptions } from './RelatedContextBuilder';

export interface CompletionPromptOptions {
  // 系统提示词
  system?: string;
  // 指令
  instruction?: string;
  // 系统规则
  systemRules?: string;
  // 环境信息
  environment?: string;
  // 用户自定义规则
  userRules?: string;
  // 是否包含相关上下文
  includeRelatedContext?: boolean;
  // 是否包含相似上下文
  includeSimilarContext?: boolean;
  // 相关上下文选项
  relatedContextOptions?: RelatedContextOptions;
  // 相似上下文
  similarContext?: string;
  // 补全标记（兼容性）
  completionToken?: string;
  // 针对特定模型的补全token
  completionTokens?: {
    start: string;
    fill: string;
    end: string;
  };
}

export interface CompletionPromptData {
  system: string;
  instruction: string;
  systemRules?: string;
  environment?: string;
  userRules?: string;
  relatedContext?: string;
  similarContext?: string;
  userInput: string;
  completionToken: string;
}

/**
 * 代码补全提示词构建器
 * 按照设计文档实现结构化提示词模版
 */
export class CompletionPromptBuilder {
  private relatedContextBuilder: RelatedContextBuilder;

  constructor(relatedContextBuilder: RelatedContextBuilder) {
    this.relatedContextBuilder = relatedContextBuilder;
  }

  /**
   * 构建完整的代码补全提示词
   */
  async buildCompletionPrompt(
    functionNode: ASTNode,
    textBefore: string,
    textAfter: string,
    options: CompletionPromptOptions = {}
  ): Promise<string> {
    const data = await this.buildPromptData(functionNode, textBefore, textAfter, options);
    return this.formatPrompt(data);
  }

  /**
   * 构建提示词数据
   */
  private async buildPromptData(
    functionNode: ASTNode,
    textBefore: string,
    textAfter: string,
    options: CompletionPromptOptions
  ): Promise<CompletionPromptData> {
    // 默认值
    const system = options.system || this.getDefaultSystem();
    const instruction = options.instruction || this.getDefaultInstruction();
    
    // 获取补全token
    const completionTokens = options.completionTokens || 
      CompletionPromptBuilder.getCompletionTokens('custom');

    // 构建用户输入
    const userInput = this.buildUserInput(textBefore, textAfter, completionTokens);

    // 构建相关上下文
    let relatedContext: string | undefined;
    if (options.includeRelatedContext !== false) {
      const relatedContextOptions = {
        mode: 'completion' as const,
        includeHiddenVariables: true,
        maxHiddenVariables: 10,
        ...options.relatedContextOptions
      };
      relatedContext = await this.relatedContextBuilder.buildRelatedContext(
        functionNode,
        textBefore,
        textAfter,
        relatedContextOptions
      );
    }

    return {
      system,
      instruction,
      systemRules: options.systemRules,
      environment: options.environment,
      userRules: options.userRules,
      relatedContext,
      similarContext: options.similarContext,
      userInput,
      completionToken: completionTokens.fill // 保持兼容性
    };
  }

  /**
   * 格式化提示词
   */
  private formatPrompt(data: CompletionPromptData): string {
    const sections: string[] = [];

    // 系统提示词
    sections.push(data.system);
    sections.push('');

    // 指令
    sections.push(data.instruction);
    sections.push('');

    // 系统规则
    if (data.systemRules) {
      sections.push('<system_rules>');
      sections.push(data.systemRules);
      sections.push('</system_rules>');
      sections.push('');
    }

    // 环境信息
    if (data.environment) {
      sections.push('<env>');
      sections.push(data.environment);
      sections.push('</env>');
      sections.push('');
    }

    // 用户自定义规则
    if (data.userRules) {
      sections.push('<user_rules>');
      sections.push(data.userRules);
      sections.push('</user_rules>');
      sections.push('');
    }

    // 相关上下文
    if (data.relatedContext) {
      sections.push('<related_context>');
      sections.push(data.relatedContext);
      sections.push('</related_context>');
      sections.push('');
    }

    // 相似上下文
    if (data.similarContext) {
      sections.push('<similar_context>');
      sections.push(data.similarContext);
      sections.push('</similar_context>');
      sections.push('');
    }

    // 用户输入
    sections.push('## 补全如下代码:');
    sections.push('');
    sections.push(data.userInput);

    return sections.join('\n');
  }

  /**
   * 构建用户输入部分
   */
  private buildUserInput(textBefore: string, textAfter: string, completionTokens: {
    start: string;
    fill: string;
    end: string;
  }): string {
    const lines: string[] = [];

    // 添加开始token
    lines.push(completionTokens.start);

    // 添加前置代码
    if (textBefore.trim()) {
      lines.push(textBefore);
    }

    // 添加填充token
    lines.push(completionTokens.fill);

    // 添加后置代码
    if (textAfter.trim()) {
      lines.push(textAfter);
    }

    // 添加结束token
    lines.push(completionTokens.end);

    return lines.join('\n');
  }

  /**
   * 获取默认系统提示词
   */
  private getDefaultSystem(): string {
    return `You are a code completion assistant.`;
  }

  /**
   * 获取默认指令
   */
  private getDefaultInstruction(): string {
    return `请结合上下文补全代码`;
  }

  /**
   * 获取针对不同模型的补全标记
   */
  static getCompletionTokens(modelType: 'deepseek' | 'qwen' | 'custom' = 'custom'): {
    start: string;
    fill: string;
    end: string;
  } {
    switch (modelType) {
      case 'deepseek':
        return {
          start: 'REDACTED_SPECIAL_TOKEN',
          fill: 'REDACTED_SPECIAL_TOKEN',
          end: 'REDACTED_SPECIAL_TOKEN'
        };
      case 'qwen':
        return {
          start: '<|fim_prefix|>',
          fill: '<|fim_suffix|>',
          end: '<|fim_middle|>'
        };
      case 'custom':
      default:
        return {
          start: '{{complete_code_here}}',
          fill: '{{complete_code_here}}',
          end: '{{complete_code_here}}'
        };
    }
  }

  /**
   * 构建针对特定模型的提示词
   */
  async buildModelSpecificPrompt(
    functionNode: ASTNode,
    textBefore: string,
    textAfter: string,
    modelType: 'deepseek' | 'qwen' | 'custom',
    options: CompletionPromptOptions = {}
  ): Promise<string> {
    const tokens = CompletionPromptBuilder.getCompletionTokens(modelType);
    
    // 根据模型类型调整选项
    const modelOptions: CompletionPromptOptions = {
      ...options,
      completionTokens: tokens
    };

    // 对于特定模型，可能需要调整系统提示词
    if (modelType === 'deepseek') {
      modelOptions.system = modelOptions.system || 
        `You are an AI programming assistant, utilizing the DeepSeek Coder model, developed by DeepSeek Company, and you only answer questions related to computer science.`;
    }

    return this.buildCompletionPrompt(functionNode, textBefore, textAfter, modelOptions);
  }
} 