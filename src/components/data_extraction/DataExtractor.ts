import * as vscode from 'vscode';
import { Logger } from '../../common/log/logger';
import { ASTStatementLevelExtractionStrategy } from './ASTStatementLevelExtractionStrategy';
import { FunctionLevelExtractionStrategy } from './FunctionLevelExtractionStrategy';
import { FunctionWithContextExtractionStrategy } from './FunctionWithContextExtractionStrategy';
import { DataExtractionStrategy, SFTSample } from './types';

export class DataExtractor {
  private strategies: Map<string, DataExtractionStrategy> = new Map();
  private logger = new Logger('DataExtractor');

  constructor() {
    this.registerStrategies();
  }

  private registerStrategies(): void {
    this.strategies.set('statement', new ASTStatementLevelExtractionStrategy());
    this.strategies.set('function', new FunctionLevelExtractionStrategy());
    this.strategies.set('function_with_context', new FunctionWithContextExtractionStrategy());
  }

  /**
   * 从单个文件提取数据
   */
  async extractFromFile(filePath: string, strategies: string[] = ['statement', 'function', 'function_with_context']): Promise<{
    filePath: string;
    success: boolean;
    error: string | null;
    samples: {
    statement: SFTSample[];
    function: SFTSample[];
    function_with_context: SFTSample[];
    };
  }> {
    const result = {
      filePath,
      success: false,
      error: null as string | null,
      samples: {
        statement: [] as SFTSample[],
        function: [] as SFTSample[],
        function_with_context: [] as SFTSample[]
      }
    };

    try {
      for (const strategyName of strategies) {
        const strategy = this.strategies.get(strategyName);
        if (!strategy) {
          this.logger.warn(`未知的提取策略: ${strategyName}`);
          continue;
        }

        try {
          const samples = await strategy.extractSamples(filePath);
          result.samples[strategyName as keyof typeof result.samples] = samples;
        } catch (error) {
          this.logger.error(`策略 ${strategyName} 提取失败:`, error as Error);
        }
      }

      result.success = true;

      // 使用Logger输出到outputChannel
      this.logger.info(`文件: ${filePath}`, { toOutput: true });
      this.logger.info(`语句级样本: ${result.samples.statement.length}`, { toOutput: true });
      this.logger.info(`函数级样本: ${result.samples.function.length}`, { toOutput: true });
      this.logger.info(`带上下文的函数样本: ${result.samples.function_with_context.length}`, { toOutput: true });

    } catch (error) {
      result.success = false;
      result.error = error instanceof Error ? error.message : String(error);
      this.logger.error(`文件提取失败: ${filePath}`, error as Error);
    }

    return result;
    }

  /**
   * 从工作区提取数据
   */
  async extractFromWorkspace(workspacePath: string, filePatterns: string[] = ['**/*.{js,ts,jsx,tsx,py,java,cpp,c,cs}']): Promise<{
    totalFiles: number;
    successCount: number;
    failedFiles: string[];
    totalSamples: number;
    fileResults: any[];
  }> {
    const result = {
      totalFiles: 0,
      successCount: 0,
      failedFiles: [] as string[],
      totalSamples: 0,
      fileResults: [] as any[]
    };

    try {
      const files = await this.findFiles(workspacePath, filePatterns);
      result.totalFiles = files.length;

      this.logger.info(`开始从 ${files.length} 个文件提取数据...`);

      for (const file of files) {
        try {
          const fileResult = await this.extractFromFile(file);
          result.fileResults.push(fileResult);

          if (fileResult.success) {
            result.successCount++;
            result.totalSamples += this.countTotalSamples(fileResult.samples);
          } else {
            result.failedFiles.push(file);
          }
        } catch (error) {
          this.logger.error(`处理文件失败: ${file}`, error as Error);
          result.failedFiles.push(file);
        }
      }

      // 使用Logger输出到outputChannel
      this.logger.info('=== 数据提取测试结果 ===', { toOutput: true });
      this.logger.info(`总文件数: ${result.totalFiles}`, { toOutput: true });
      this.logger.info(`成功提取: ${result.successCount}`, { toOutput: true });
      this.logger.info(`失败文件: ${result.failedFiles.length}`, { toOutput: true });
      this.logger.info(`总样本数: ${result.totalSamples}`, { toOutput: true });

      if (result.failedFiles.length > 0) {
        this.logger.info('\n失败的文件:', { toOutput: true });
        for (const file of result.failedFiles) {
          this.logger.info(`  - ${file}`, { toOutput: true });
        }
      }

      this.logger.info('\n=== 测试完成 ===', { toOutput: true });

    } catch (error) {
      this.logger.error('工作区提取失败:', error as Error);
    }

    return result;
  }

  /**
   * 查找匹配的文件
   */
  private async findFiles(workspacePath: string, patterns: string[]): Promise<string[]> {
    const files: string[] = [];

    for (const pattern of patterns) {
    try {
        const matches = await vscode.workspace.findFiles(
          new vscode.RelativePattern(workspacePath, pattern),
          '**/node_modules/**'
        );
        files.push(...matches.map(uri => uri.fsPath));
      } catch (error) {
        this.logger.warn(`模式 ${pattern} 查找失败:`, error as Error);
      }
    }

    return [...new Set(files)]; // 去重
  }

  /**
   * 统计总样本数
   */
  private countTotalSamples(samples: any): number {
    return Object.values(samples).reduce((total: number, sampleArray: any) => {
      return total + (Array.isArray(sampleArray) ? sampleArray.length : 0);
    }, 0);
  }

  /**
   * 提取SFT数据（兼容旧接口）
   */
  async extractSFTData(filePath: string, strategy: string = 'statement'): Promise<SFTSample[]> {
    // 检查策略是否有效
    if (!this.strategies.has(strategy)) {
      throw new Error('不支持的提取策略');
    }

    try {
      const result = await this.extractFromFile(filePath, [strategy]);
      return result.samples[strategy as keyof typeof result.samples] || [];
    } catch (error) {
      this.logger.error(`提取SFT数据失败: ${filePath}`, error as Error);
      return [];
    }
  }

  /**
   * 使用所有策略提取数据（兼容旧接口）
   */
  async extractAllStrategies(filePath: string): Promise<{
    statement: SFTSample[];
    function: SFTSample[];
    function_with_context: SFTSample[];
  }> {
    try {
      const result = await this.extractFromFile(filePath);
      return result.samples;
    } catch (error) {
      this.logger.error(`使用所有策略提取数据失败: ${filePath}`, error as Error);
      return {
        statement: [],
        function: [],
        function_with_context: []
      };
    }
  }

  /**
   * 从工作区提取SFT数据（兼容旧接口）
   */
  async extractSFTDataFromWorkspace(): Promise<SFTSample[]> {
    try {
      const workspaceFolders = (global as any).vscode?.workspace?.workspaceFolders;
      if (!workspaceFolders || workspaceFolders.length === 0) {
        this.logger.warn('未找到工作区');
        return [];
      }

      const workspacePath = workspaceFolders[0].uri.fsPath;
      const result = await this.extractFromWorkspace(workspacePath);
      
      // 收集所有样本
      const allSamples: SFTSample[] = [];
      for (const fileResult of result.fileResults) {
        if (fileResult.success) {
          Object.values(fileResult.samples).forEach((samples: any) => {
            if (Array.isArray(samples)) {
              allSamples.push(...samples);
            }
          });
        }
      }

      return allSamples;
    } catch (error) {
      this.logger.error('从工作区提取SFT数据失败:', error as Error);
      return [];
    }
  }
} 