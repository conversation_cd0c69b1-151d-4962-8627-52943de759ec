import * as vscode from 'vscode';
import { ContextCollector } from '../code_context/ContextCollector';
import { ContextFusionEngine } from '../code_context/ContextFusionEngine';
import { EmbeddingContextProviderImpl } from '../code_context/EmbeddingContextProviderImpl';
import { GraphContextProviderImpl } from '../code_context/GraphContextProviderImpl';
import { KeywordContextProviderImpl } from '../code_context/KeywordContextProviderImpl';
import { UserCustomContextProviderImpl } from '../code_context/UserCustomContextProviderImpl';
import { ParserManager } from '../code_graph/parser/ParserManager';
import { RepoIndexerManager } from '../code_graph/repoIndexer/repoIndexerManager';
import { ASTNode } from '../code_graph/types/ast';
import { SFTSample } from './types';

export class FunctionWithContextExtractionStrategy {
  private parserManager: ParserManager;
  private contextCollector: ContextCollector;

  constructor() {
    this.parserManager = new ParserManager();
    
    // 创建ContextCollector所需的依赖
    const repoIndexerManager = RepoIndexerManager.getInstance();
    const repoIndexer = repoIndexerManager.getRepoIndexer();
    
    // 如果repoIndexer为null，创建一个简单的mock实现
    const mockRepoIndexer = {
      findNodeInFile: () => null,
      findCallersByName: () => [],
      findCalleesByName: () => [],
      findRelatedFunctionsByName: () => []
    };
    
    const graphProvider = new GraphContextProviderImpl(repoIndexer || mockRepoIndexer as any);
    const keywordProvider = new KeywordContextProviderImpl();
    const embeddingProvider = new EmbeddingContextProviderImpl();
    const userCustomProvider = new UserCustomContextProviderImpl();
    const fusionEngine = new ContextFusionEngine({
      graphWeight: 0.4,
      bm25Weight: 0.3,
      embeddingWeight: 0.3
    });
    
    this.contextCollector = new ContextCollector(
      graphProvider,
      keywordProvider,
      embeddingProvider,
      userCustomProvider,
      fusionEngine
    );
  }

  async extractSamples(filePath: string): Promise<SFTSample[]> {
    try {
      const content = await vscode.workspace.fs.readFile(vscode.Uri.file(filePath));
      const fileContent = content.toString();
      
      // 获取语言类型
      const language = ParserManager.getLanguageFromFile(filePath);
      const parser = await this.parserManager.getParser(language);
      
      if (!parser) {
        console.warn(`不支持的语言类型: ${language}`);
        return [];
      }

      // 解析AST
      const ast = parser.parse(fileContent);
      
      // 提取函数+上下文样本
      const samples: SFTSample[] = [];
      await this.extractFunctionsWithContextFromAST(ast, fileContent, filePath, samples);
      
      return samples;
    } catch (error) {
      console.error('AST函数+上下文提取失败:', error);
      return [];
    }
  }

  private async extractFunctionsWithContextFromAST(
    node: ASTNode, 
    fileContent: string, 
    filePath: string, 
    samples: SFTSample[]
  ): Promise<void> {
    // 检查当前节点是否为函数定义
    if (this.isFunctionNode(node)) {
      const functionText = this.extractTextFromLocation(node.location, fileContent);
      if (functionText.trim() && node.name) {
        // 收集函数上下文
        const contexts = await this.collectFunctionContexts(node, filePath, fileContent);
        
        samples.push({
          id: `${filePath}::${node.name}::with_context`,
          file: filePath,
          functionName: node.name,
          language: ParserManager.getLanguageFromFile(filePath),
          expected: functionText.trim(),
          above_cursor: contexts.aboveContext || '',
          below_cursor: contexts.belowContext || '',
          contexts: contexts.relatedContexts || [],
          meta: {
            type: 'function_with_context',
            nodeType: node.originalType || 'unknown',
            line: node.location?.start.line,
            column: node.location?.start.column,
            parameters: node.metadata?.parameters,
            returnType: node.metadata?.returnType,
            isStatic: node.metadata?.isStatic,
            isExtern: node.metadata?.isExtern,
            isInline: node.metadata?.isInline,
            contextCount: contexts.relatedContexts?.length || 0
          }
        });
      }
    }

    // 递归处理子节点
    for (const child of node.children) {
      await this.extractFunctionsWithContextFromAST(child, fileContent, filePath, samples);
    }
  }

  private isFunctionNode(node: ASTNode): boolean {
    // 检查是否为函数定义节点
    const functionTypes = [
      'function_definition',  // C语言
      'statement_function_def', // NP语言
      'function_declaration', // 通用
      'method_definition',    // 类方法
      'arrow_function',       // 箭头函数
      'function_expression'   // 函数表达式
    ];

    return functionTypes.includes(node.originalType || '') || node.kind === 'function';
  }

  private async collectFunctionContexts(
    functionNode: ASTNode, 
    filePath: string, 
    fileContent: string
  ): Promise<{
    aboveContext: string;
    belowContext: string;
    relatedContexts: string[];
  }> {
    try {
      // 获取函数位置
      const functionStart = functionNode.location?.start.line || 0;
      const functionEnd = functionNode.location?.end.line || 0;

      // 提取函数上文的代码
      const aboveContext = this.extractAboveContext(fileContent, functionStart);
      
      // 提取函数下文的代码
      const belowContext = this.extractBelowContext(fileContent, functionEnd);

      // 收集相关上下文（调用关系、依赖等）
      const relatedContexts = await this.collectRelatedContexts(functionNode, filePath);

      return {
        aboveContext,
        belowContext,
        relatedContexts
      };
    } catch (error) {
      console.error('收集函数上下文失败:', error);
      return {
        aboveContext: '',
        belowContext: '',
        relatedContexts: []
      };
    }
  }

  private extractAboveContext(fileContent: string, functionStartLine: number): string {
    const lines = fileContent.split('\n');
    const contextLines = Math.min(10, functionStartLine); // 最多取10行上文
    const startLine = Math.max(0, functionStartLine - contextLines);
    
    return lines.slice(startLine, functionStartLine).join('\n');
  }

  private extractBelowContext(fileContent: string, functionEndLine: number): string {
    const lines = fileContent.split('\n');
    const contextLines = Math.min(10, lines.length - functionEndLine); // 最多取10行下文
    const endLine = Math.min(lines.length, functionEndLine + contextLines);
    
    return lines.slice(functionEndLine, endLine).join('\n');
  }

  private async collectRelatedContexts(functionNode: ASTNode, filePath: string): Promise<string[]> {
    try {
      const contexts: string[] = [];
      
      // 收集函数调用关系
      if (functionNode.name) {
        const callContexts = await this.contextCollector.collectContext(
          functionNode,
          functionNode.text || '',
          {
            graphWeight: 0.4,
            bm25Weight: 0.3,
            embeddingWeight: 0.3
          }
        );
        
        contexts.push(...callContexts.map((ctx: any) => ctx.content));
      }

      // 收集函数参数相关的上下文
      if (functionNode.metadata?.parameters) {
        for (const param of functionNode.metadata.parameters) {
          const paramContexts = await this.contextCollector.collectContext(
            functionNode,
            param.name,
            {
              graphWeight: 0.2,
              bm25Weight: 0.6,
              embeddingWeight: 0.2
            }
          );
          
          contexts.push(...paramContexts.map((ctx: any) => ctx.content));
        }
      }

      return contexts;
    } catch (error) {
      console.error('收集相关上下文失败:', error);
      return [];
    }
  }

  private extractTextFromLocation(location: any, fileContent: string): string {
    if (!location || !location.start || !location.end) {
      return '';
    }

    const lines = fileContent.split('\n');
    const startLine = location.start.line;
    const endLine = location.end.line;
    const startColumn = location.start.column;
    const endColumn = location.end.column;

    if (startLine === endLine) {
      // 单行函数
      const line = lines[startLine] || '';
      return line.substring(startColumn, endColumn);
    } else {
      // 多行函数
      const result: string[] = [];
      
      // 第一行
      if (lines[startLine]) {
        result.push(lines[startLine].substring(startColumn));
      }
      
      // 中间行
      for (let i = startLine + 1; i < endLine; i++) {
        if (lines[i]) {
          result.push(lines[i]);
        }
      }
      
      // 最后一行
      if (lines[endLine]) {
        result.push(lines[endLine].substring(0, endColumn));
      }
      
      return result.join('\n');
    }
  }
} 