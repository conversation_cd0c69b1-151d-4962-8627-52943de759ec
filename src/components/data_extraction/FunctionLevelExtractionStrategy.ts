import * as vscode from 'vscode';
import { ParserManager } from '../code_graph/parser/ParserManager';
import { ASTNode } from '../code_graph/types/ast';
import { SFTSample } from './types';

export class FunctionLevelExtractionStrategy {
  private parserManager: ParserManager;

  constructor() {
    this.parserManager = new ParserManager();
  }

  async extractSamples(filePath: string): Promise<SFTSample[]> {
    try {
      const content = await vscode.workspace.fs.readFile(vscode.Uri.file(filePath));
      const fileContent = content.toString();
      
      // 获取语言类型
      const language = ParserManager.getLanguageFromFile(filePath);
      const parser = await this.parserManager.getParser(language);
      
      if (!parser) {
        console.warn(`不支持的语言类型: ${language}`);
        return [];
      }

      // 解析AST
      const ast = parser.parse(fileContent);
      
      // 提取函数级样本
      const samples: SFTSample[] = [];
      this.extractFunctionsFromAST(ast, fileContent, filePath, samples);
      
      return samples;
    } catch (error) {
      console.error('AST函数级提取失败:', error);
      return [];
    }
  }

  private extractFunctionsFromAST(
    node: ASTNode, 
    fileContent: string, 
    filePath: string, 
    samples: SFTSample[]
  ): void {
    // 检查当前节点是否为函数定义
    if (this.isFunctionNode(node)) {
      const functionText = this.extractTextFromLocation(node.location, fileContent);
      if (functionText.trim() && node.name) {
        samples.push({
          id: `${filePath}::${node.name}`,
          file: filePath,
          functionName: node.name,
          language: ParserManager.getLanguageFromFile(filePath),
          expected: functionText.trim(),
          above_cursor: '',
          below_cursor: '',
          meta: {
            type: 'function',
            nodeType: node.originalType || 'unknown',
            line: node.location?.start.line,
            column: node.location?.start.column,
            parameters: node.metadata?.parameters,
            returnType: node.metadata?.returnType,
            isStatic: node.metadata?.isStatic,
            isExtern: node.metadata?.isExtern,
            isInline: node.metadata?.isInline
          }
        });
      }
    }

    // 递归处理子节点
    for (const child of node.children) {
      this.extractFunctionsFromAST(child, fileContent, filePath, samples);
    }
  }

  private isFunctionNode(node: ASTNode): boolean {
    // 检查是否为函数定义节点
    const functionTypes = [
      'function_definition',  // C语言
      'statement_function_def', // NP语言
      'function_declaration', // 通用
      'method_definition',    // 类方法
      'arrow_function',       // 箭头函数
      'function_expression'   // 函数表达式
    ];

    return functionTypes.includes(node.originalType || '') || node.kind === 'function';
  }

  private extractTextFromLocation(location: any, fileContent: string): string {
    if (!location || !location.start || !location.end) {
      return '';
    }

    const lines = fileContent.split('\n');
    const startLine = location.start.line;
    const endLine = location.end.line;
    const startColumn = location.start.column;
    const endColumn = location.end.column;

    if (startLine === endLine) {
      // 单行函数
      const line = lines[startLine] || '';
      return line.substring(startColumn, endColumn);
    } else {
      // 多行函数
      const result: string[] = [];
      
      // 第一行
      if (lines[startLine]) {
        result.push(lines[startLine].substring(startColumn));
      }
      
      // 中间行
      for (let i = startLine + 1; i < endLine; i++) {
        if (lines[i]) {
          result.push(lines[i]);
        }
      }
      
      // 最后一行
      if (lines[endLine]) {
        result.push(lines[endLine].substring(0, endColumn));
      }
      
      return result.join('\n');
    }
  }
} 