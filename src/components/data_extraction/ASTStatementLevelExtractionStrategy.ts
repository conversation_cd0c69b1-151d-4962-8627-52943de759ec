import * as vscode from 'vscode';
import { ParserManager } from '../code_graph/parser/ParserManager';
import { ASTNode } from '../code_graph/types/ast';
import { SFTSample } from './types';

export class ASTStatementLevelExtractionStrategy {
  private parserManager: ParserManager;

  constructor() {
    this.parserManager = new ParserManager();
  }

  async extractSamples(filePath: string): Promise<SFTSample[]> {
    try {
      const content = await vscode.workspace.fs.readFile(vscode.Uri.file(filePath));
      const fileContent = content.toString();
      
      // 获取语言类型
      const language = ParserManager.getLanguageFromFile(filePath);
      const parser = await this.parserManager.getParser(language);
      
      if (!parser) {
        console.warn(`不支持的语言类型: ${language}`);
        return [];
      }

      // 解析AST
      const ast = parser.parse(fileContent);
      
      // 提取语句级样本
      const samples: SFTSample[] = [];
      this.extractStatementsFromAST(ast, fileContent, filePath, samples);
      
      return samples;
    } catch (error) {
      console.error('AST语句级提取失败:', error);
      return [];
    }
  }

  private extractStatementsFromAST(
    node: ASTNode, 
    fileContent: string, 
    filePath: string, 
    samples: SFTSample[]
  ): void {
    // 根据语言类型提取语句
    const language = ParserManager.getLanguageFromFile(filePath);
    
    switch (language) {
      case 'c':
        this.extractCStatements(node, fileContent, filePath, samples);
        break;
      case 'np':
        this.extractNPStatements(node, fileContent, filePath, samples);
        break;
      default:
        // 通用语句提取
        this.extractGenericStatements(node, fileContent, filePath, samples);
    }

    // 递归处理子节点
    for (const child of node.children) {
      this.extractStatementsFromAST(child, fileContent, filePath, samples);
    }
  }

  private extractCStatements(
    node: ASTNode, 
    fileContent: string, 
    filePath: string, 
    samples: SFTSample[]
  ): void {
    // C语言语句类型
    const statementTypes = [
      'expression_statement',
      'declaration',
      'if_statement',
      'for_statement',
      'while_statement',
      'do_statement',
      'switch_statement',
      'case_statement',
      'return_statement',
      'break_statement',
      'continue_statement',
      'goto_statement',
      'labeled_statement',
      'compound_statement'
    ];

    if (statementTypes.includes(node.originalType || '') && node.location) {
      const statementText = this.extractTextFromLocation(node.location, fileContent);
      if (statementText.trim()) {
        samples.push({
          id: `${filePath}::${node.originalType || 'unknown'}::${node.location.start.line}`,
          file: filePath,
          language: 'c',
          expected: statementText.trim(),
          above_cursor: '',
          below_cursor: '',
          meta: {
            type: 'statement',
            nodeType: node.originalType || 'unknown',
            line: node.location.start.line,
            column: node.location.start.column
          }
        });
      }
    }
  }

  private extractNPStatements(
    node: ASTNode, 
    fileContent: string, 
    filePath: string, 
    samples: SFTSample[]
  ): void {
    // NP语言语句类型
    const statementTypes = [
      'statement',
      'statement_function_def',
      'statement_function_call',
      'statement_if',
      'statement_for',
      'statement_while',
      'statement_return',
      'statement_break',
      'statement_continue'
    ];

    if (statementTypes.includes(node.originalType || '') && node.location) {
      const statementText = this.extractTextFromLocation(node.location, fileContent);
      if (statementText.trim()) {
        samples.push({
          id: `${filePath}::${node.originalType || 'unknown'}::${node.location.start.line}`,
          file: filePath,
          language: 'np',
          expected: statementText.trim(),
          above_cursor: '',
          below_cursor: '',
          meta: {
            type: 'statement',
            nodeType: node.originalType || 'unknown',
            line: node.location.start.line,
            column: node.location.start.column
          }
        });
      }
    }
  }

  private extractGenericStatements(
    node: ASTNode, 
    fileContent: string, 
    filePath: string, 
    samples: SFTSample[]
  ): void {
    // 通用语句提取逻辑
    const genericStatementTypes = [
      'statement',
      'expression',
      'declaration',
      'assignment',
      'call',
      'return',
      'if',
      'for',
      'while',
      'switch',
      'case'
    ];

    if (genericStatementTypes.some(type => (node.originalType || '').includes(type)) && node.location) {
      const statementText = this.extractTextFromLocation(node.location, fileContent);
      if (statementText.trim()) {
        samples.push({
          id: `${filePath}::${node.originalType || 'unknown'}::${node.location.start.line}`,
          file: filePath,
          language: ParserManager.getLanguageFromFile(filePath),
          expected: statementText.trim(),
          above_cursor: '',
          below_cursor: '',
          meta: {
            type: 'statement',
            nodeType: node.originalType || 'unknown',
            line: node.location.start.line,
            column: node.location.start.column
          }
        });
      }
    }
  }

  private extractTextFromLocation(location: any, fileContent: string): string {
    if (!location || !location.start || !location.end) {
      return '';
    }

    const lines = fileContent.split('\n');
    const startLine = location.start.line;
    const endLine = location.end.line;
    const startColumn = location.start.column;
    const endColumn = location.end.column;

    if (startLine === endLine) {
      // 单行语句
      const line = lines[startLine] || '';
      return line.substring(startColumn, endColumn);
    } else {
      // 多行语句
      const result: string[] = [];
      
      // 第一行
      if (lines[startLine]) {
        result.push(lines[startLine].substring(startColumn));
      }
      
      // 中间行
      for (let i = startLine + 1; i < endLine; i++) {
        if (lines[i]) {
          result.push(lines[i]);
        }
      }
      
      // 最后一行
      if (lines[endLine]) {
        result.push(lines[endLine].substring(0, endColumn));
      }
      
      return result.join('\n');
    }
  }
} 