# Components Types Directory

这个目录包含了所有组件的类型定义，统一管理以避免组件间的循环依赖。

## 目录结构

```
src/components/types/
├── index.ts              # 统一导出所有类型
├── code_completion.ts    # 代码补全相关类型
├── user.ts              # 用户相关类型
├── statistics.ts        # 统计相关类型
├── code_graph.ts        # 代码图相关类型
├── data_extraction.ts   # 数据提取相关类型
└── code_context.ts      # 代码上下文相关类型
```

## 类型定义说明

### code_completion.ts
- `CompletionRequestPayload`: 补全请求载荷
- `CompletionMeta`: 补全元数据
- `EnvironmentInfo`: 环境信息
- `ContextSnippet`: 上下文代码片段
- `CompletionScope`: 补全作用域

### user.ts
- `IUserInfo`: 用户信息接口
- `UserType`: 用户类型枚举
- `IUser`: 用户接口
- `IUserFactory`: 用户工厂接口

### statistics.ts
- `StatisticsEventType`: 统计事件类型
- `StatisticsCompletionScope`: 统计用补全作用域
- `CompletionContext`: 补全上下文
- `CompletionPosition`: 补全位置
- `UserInfo`: 统计用用户信息

### code_graph.ts
- `GraphCodeNode`: 代码图节点
- `CodeEdge`: 代码图边
- `ICodeGraph`: 代码图接口
- `ASTNode`: AST节点接口

### data_extraction.ts
- `SFTSample`: SFT样本
- `DataExtractionStrategy`: 数据提取策略

### code_context.ts
- `ContextItem`: 上下文项
- `ContextProvider`: 上下文提供者接口
- `KeywordContextProvider`: 关键字上下文提供者
- `GraphContextProvider`: 图上下文提供者
- `EmbeddingContextProvider`: 向量上下文提供者

## 使用方式

### 导入单个组件的类型
```typescript
import { CompletionRequestPayload } from '../types/code_completion';
import { IUserInfo } from '../types/user';
```

### 导入所有类型
```typescript
import * as Types from '../types';
```

## 优势

1. **避免循环依赖**: 所有组件都依赖统一的types目录，避免了组件间的直接依赖
2. **类型复用**: 公共类型可以在多个组件间共享
3. **维护性**: 类型定义集中管理，便于维护和更新
4. **清晰性**: 每个组件的类型定义都有独立的文件，结构清晰

## 迁移说明

原有的组件内部types目录已迁移到此统一目录：
- `src/components/code_completion/types.ts` → `src/components/types/code_completion.ts`
- `src/components/user/types/user.ts` → `src/components/types/user.ts`
- `src/components/statistics/types.ts` → `src/components/types/statistics.ts`
- `src/components/code_graph/types/graph.ts` → `src/components/types/code_graph.ts`
- `src/components/data_extraction/types.ts` → `src/components/types/data_extraction.ts`
- `src/components/code_context/types.ts` → `src/components/types/code_context.ts`

所有相关的导入路径已更新为新的路径。 