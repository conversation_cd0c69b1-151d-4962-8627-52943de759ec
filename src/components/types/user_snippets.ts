/**
 * 用户自定义代码片段和规则类型定义
 */

/**
 * 用户自定义代码片段
 */
export interface UserCodeSnippet {
  id: string;                    // 唯一标识符
  name: string;                  // 片段名称
  description?: string;          // 片段描述
  code: string;                  // 代码内容
  language: string;              // 适用语言
  tags: string[];                // 标签，用于匹配
  priority: number;              // 优先级 (1-10, 10最高)
  createdAt: number;             // 创建时间戳
  updatedAt: number;             // 更新时间戳
  isEnabled: boolean;            // 是否启用
}

/**
 * 用户自定义规则
 */
export interface UserRule {
  id: string;                    // 唯一标识符
  name: string;                  // 规则名称
  description?: string;          // 规则描述
  content: string;               // 规则内容（会被加入到提示词中）
  language?: string;             // 适用语言（可选，为空表示通用）
  priority: number;              // 优先级 (1-10, 10最高)
  createdAt: number;             // 创建时间戳
  updatedAt: number;             // 更新时间戳
  isEnabled: boolean;            // 是否启用
}

/**
 * 用户自定义配置
 */
export interface UserCustomConfig {
  snippets: UserCodeSnippet[];   // 代码片段列表
  rules: UserRule[];             // 规则列表
  version: string;               // 配置版本
  lastUpdated: number;           // 最后更新时间
}

/**
 * 片段匹配结果
 */
export interface SnippetMatchResult {
  snippet: UserCodeSnippet;
  similarity: number;            // 相似度 (0-1)
  score: number;                 // 综合得分 (相似度 * 优先级)
}

/**
 * 规则匹配结果
 */
export interface RuleMatchResult {
  rule: UserRule;
  isApplicable: boolean;         // 是否适用于当前语言
}

/**
 * 用户自定义内容管理器接口
 */
export interface IUserCustomContentManager {
  // 代码片段管理
  addSnippet(snippet: Omit<UserCodeSnippet, 'id' | 'createdAt' | 'updatedAt'>): Promise<UserCodeSnippet>;
  updateSnippet(id: string, updates: Partial<UserCodeSnippet>): Promise<UserCodeSnippet>;
  deleteSnippet(id: string): Promise<boolean>;
  getSnippet(id: string): Promise<UserCodeSnippet | null>;
  getAllSnippets(): Promise<UserCodeSnippet[]>;
  getEnabledSnippets(): Promise<UserCodeSnippet[]>;
  
  // 规则管理
  addRule(rule: Omit<UserRule, 'id' | 'createdAt' | 'updatedAt'>): Promise<UserRule>;
  updateRule(id: string, updates: Partial<UserRule>): Promise<UserRule>;
  deleteRule(id: string): Promise<boolean>;
  getRule(id: string): Promise<UserRule | null>;
  getAllRules(): Promise<UserRule[]>;
  getEnabledRules(): Promise<UserRule[]>;
  
  // 匹配和检索
  findMatchingSnippets(
    code: string, 
    language: string, 
    maxResults?: number
  ): Promise<SnippetMatchResult[]>;
  
  getApplicableRules(language: string): Promise<RuleMatchResult[]>;
  
  // 配置管理
  loadConfig(): Promise<UserCustomConfig>;
  saveConfig(config: UserCustomConfig): Promise<void>;
  exportConfig(): Promise<string>;
  importConfig(configJson: string): Promise<void>;
  resetToDefaults(): Promise<void>;
} 