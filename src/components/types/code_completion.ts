import { IUserInfo } from './user';

export type ContextType = 'similar' | 'semantic';

export type CodeSnippetType =
  | 'function'
  | 'variable'
  | 'struct'
  | 'class'
  | 'interface'
  | 'enum'
  | 'comment'
  | 'import'
  | 'typealias'
  | 'other';

export interface ContextSnippet {
  contextType: ContextType;
  codeSnippetType: CodeSnippetType;
  fileName: string;
  content: string;
  startLine: number;
  startColumn: number;
  endLine: number;
  endColumn: number;
  label?: string;
}

export interface CompletionScope {
  scopeType: 'function' | 'class' | 'method' | 'global' | 'block' | 'other';
  name?: string;
  startLine: number;
  startColumn: number;
  endLine: number;
  endColumn: number;
}

/**
 * 环境信息接口
 */
export interface EnvironmentInfo {
  /** 编码平台信息 */
  platform: string;
  /** 设备信息 */
  deviceInfo: string;
  /** 操作系统信息 */
  osInfo: string;
  /** VSCode版本 */
  vscodeVersion: string;
  /** 插件版本 */
  extensionVersion: string;
  /** 工作区信息 */
  workspaceInfo?: {
    name?: string;
    uri?: string;
    folders?: string[];
  };
}

export interface CompletionMeta {
  fileContent?: string;
  rawContextList?: ContextSnippet[];
  selectedContext?: ContextSnippet[];
  fileName?: string;
  languageId?: string;
  cursorLine?: number;
  cursorColumn?: number;
  scope?: CompletionScope;
  /** 环境信息 */
  environment?: EnvironmentInfo;
  /** 用户信息 */
  user?: IUserInfo;
  /** 会话信息 */
  session?: {
    sessionId: string;
    startTime: number;
    duration: number;
  };
  /** 请求上下文 */
  requestContext?: {
    requestId: string;
    timestamp: number;
    source: 'inline' | 'diff-highlight' | 'manual' | 'auto';
  };
}

export interface ModelParams {
  model?: string;
  max_tokens?: number;
  temperature?: number;
  stream?: boolean;
  [key: string]: any;
}

export interface CompletionRequestPayload {
  prompt: string;
  modelConfig: ModelParams;
  meta: CompletionMeta;
  /** 请求优先级 */
  priority?: 'low' | 'normal' | 'high' | 'urgent';
  /** 请求标签 */
  tags?: string[];
  /** 自定义参数 */
  customParams?: Record<string, any>;
} 