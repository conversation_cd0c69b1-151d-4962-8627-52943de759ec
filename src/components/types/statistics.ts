/**
 * 统计事件类型
 */
export enum StatisticsEventType {
    COMPLETION_TRIGGERED = 'COMPLETION_TRIGGERED',    // 续写触发
    COMPLETION_ACCEPTED = 'COMPLETION_ACCEPTED',      // 续写接受
    COMPLETION_REJECTED = 'COMPLETION_REJECTED',      // 续写拒绝
    COMPLETION_FEEDBACK = 'COMPLETION_FEEDBACK',      // 续写反馈
    BATCH_STATISTICS = 'BATCH_STATISTICS',           // 批量统计数据
    CUSTOM_EVENT = 'CUSTOM_EVENT'                    // 自定义事件
}

/**
 * 用户信息
 */
export interface UserInfo {
    name: string;               // 用户名
    nickname: string;           // 用户昵称
    department?: string;        // 部门（可选）
    userType: string;           // 用户类型
}

/**
 * 作用域信息
 */
export interface Scope {
    filePath: string;           // 文件路径
    language: string;           // 编程语言
    functionName?: string;      // 函数名（如果有）
    className?: string;         // 类名（如果有）
    moduleName?: string;        // 模块名（如果有）
}

/**
 * 代码片段
 */
export interface CodeSnippet {
    filePath: string;           // 文件路径
    language: string;           // 编程语言
    code: string;               // 代码内容
    sourceType?: string;        // 来源类型（codeGraph/keyword/vector等）
}

/**
 * 环境信息
 */
export interface Environment {
    name: string;               // 环境变量名
    value: string;              // 环境变量值
}

/**
 * 上下文信息
 */
export interface Context {
    beforeCursor: string;       // 光标前的内容
    afterCursor?: string;       // 光标后的内容
    relatedCodeSnippets?: CodeSnippet[];  // 相关代码片段
    similarCodeSnippets?: CodeSnippet[];  // 相似代码片段
    environments?: Environment[];         // 环境信息
}

/**
 * 模型配置
 */
export interface ModelConfig {
    model: string;              // 模型名称
    provider: string;           // 提供商
    temperature: number;        // 温度参数
    max_tokens: number;         // 最大token数
    top_p?: number;             // top_p参数
}

/**
 * 统计事件数据
 */
export interface StatisticsEventData {
    // 基础信息
    userInfo?: UserInfo;
    scope?: Scope;
    context?: Context;
    
    // 续写相关信息
    prompt?: string;            // 提示词
    response?: string;          // 响应内容
    config?: ModelConfig;       // 模型配置
    
    // 反馈相关信息
    suggestionText?: string;    // 建议文本
    originalText?: string;      // 原始文本
    feedback?: string;          // 用户反馈
    
    // 自定义数据
    [key: string]: any;         // 允许任意额外字段
}

/**
 * 统计事件
 */
export interface StatisticsEvent {
    eventType: StatisticsEventType;  // 事件类型
    timestamp: number;               // 时间戳
    sessionId?: string;              // 会话ID（可选）
    userId?: string;                 // 用户ID（可选）
    data: StatisticsEventData;       // 事件数据
}

/**
 * 统计响应
 */
export interface StatisticsResponse {
    success: boolean;           // 请求是否成功
    message: string;            // 响应消息
    request_id: string;         // 请求唯一标识
    timestamp: string;          // 响应时间戳
    data?: any;                 // 响应数据
    error_code?: string;        // 错误代码
    error_details?: any;        // 错误详情
}

/**
 * 统计服务接口
 */
export interface IStatisticsService {
    /**
     * 上报统计事件
     */
    reportEvent(event: StatisticsEvent): Promise<StatisticsResponse>;

    /**
     * 上报续写触发事件
     */
    reportCompletionTriggered(
        scope: Scope,
        context: Context,
        prompt?: string,
        response?: string,
        config?: ModelConfig
    ): Promise<void>;

    /**
     * 上报续写反馈事件
     */
    reportCompletionFeedback(
        eventType: StatisticsEventType.COMPLETION_ACCEPTED | StatisticsEventType.COMPLETION_REJECTED,
        scope: Scope,
        context: Context,
        suggestionText: string,
        originalText?: string,
        feedback?: string
    ): Promise<void>;

    /**
     * 上报续写响应事件
     */
    reportCompletionResponse(
        scope: Scope,
        context: Context,
        prompt: string,
        response: string,
        config: any
    ): Promise<void>;
} 