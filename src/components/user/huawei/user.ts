import * as vscode from 'vscode';



interface HuaweiUserInfoBase {
    id: string;
    name: string;
    nickName: string;
    department: string;
    cloudDragonToken: string;
    accessToken: string;
    codeCommitToken: string;
    codeHubGreenAccessToken: string;
    codeHubYellowAccessToken: string;
    gitLabGreenAccessToken: string;
    gitLabYellowAccessToken: string;
    cancel?: boolean;
}

export const HUAWEI_USER_DEPARTMENT_PREFIX = 'hwDepartName';

interface HuaweiUserDepartment {
    hwDepartName1?: string;
    hwDepartName2?: string;
    hwDepartName3?: string;
    hwDepartName4?: string;
    hwDepartName5?: string;
    hwDepartName6?: string;
}

type HuaweiUserInfo = HuaweiUserInfoBase & HuaweiUserDepartment;

export class HuaweiUser {
    private static _instance: HuaweiUser;

    private _info: HuaweiUserInfo | undefined;
    private constructor() {
    }

    public static getInstance(): HuaweiUser {
        if (!this._instance) {
            this._instance = new HuaweiUser();
        }
        return this._instance;
    }

    /**
     * 获取当前用户信息
     * 
     * @return 返回用户信息，未登录的返回undefined
     */
    public get info(): HuaweiUserInfo | undefined {
        return this._info;
    }

    /**
     * 初始化用户信息，并且监听用户切换登录时，重新获取用户信息
     * 
     */
    public async init(): Promise<void> {
        await this.getUserInfo();
        if ((vscode.workspace as any).onDidChangeLoginStatus) {
                (vscode.workspace as any).onDidChangeLoginStatus(async (status: boolean) => {
                    if (status) {
                        await this.getUserInfo(); // 重新登录后，重新获取一次用户信息
                    }
                });
        }
    }
    
    private async getUserInfo(): Promise<HuaweiUserInfo | undefined> { 
        const res = await vscode.commands.executeCommand<HuaweiUserInfo>('vscode.getUserInfo');
        if (res.cancel) {
            return;
        }
        this._info = res;
        return res;
    }
}