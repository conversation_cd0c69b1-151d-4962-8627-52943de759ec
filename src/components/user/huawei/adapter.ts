import { IUser, UserType } from '../types/user';
import { HuaweiUser } from './user';

/**
 * 华为用户适配器 - 将华为用户适配到通用用户接口
 */
export class <PERSON>aweiUserAdapter implements IUser {
    private _huaweiUser: <PERSON>aw<PERSON>User;
    private _rawData: any;

    constructor() {
        this._huaweiUser = HuaweiUser.getInstance();
    }

    public get type(): UserType {
        return UserType.HUAWEI;
    }

    public get info() {
        const rawInfo = this._huaweiUser.info;
        if (!rawInfo) {
            return undefined;
        }

        return {
            id: rawInfo.id,
            name: rawInfo.name,
            nickname: rawInfo.nickName,
            department: rawInfo.department
        };
    }

    public getRaw(): any {
        return this._rawData || this._huaweiUser.info;
    }

    public async init(): Promise<void> {
        await this._huaweiUser.init();
        this._rawData = this._huaweiUser.info;
    }

    public isLoggedIn(): boolean {
        return this._huaweiUser.info !== undefined;
    }
} 