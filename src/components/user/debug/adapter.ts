import { IUser, IUserInfo, UserType } from '../types/user';

/**
 * 本地调试用户适配器
 */
export class DebugUserAdapter implements IUser {
    private _info: IUserInfo = {
        id: 'debug-user',
        name: process.env.DEBUG_USER_NAME || 'Debug User',
        nickname: process.env.DEBUG_USER_NICKNAME || 'debugger',
        department: process.env.DEBUG_USER_DEPARTMENT || 'DebugDept'
    };
    private _raw: any = this._info;

    public get type() {
        return UserType.DEBUG;
    }

    public get info() {
        return this._info;
    }

    public getRaw() {
        return this._raw;
    }

    public async init(): Promise<void> {
        // 可扩展为读取本地配置或环境变量
    }

    public isLoggedIn(): boolean {
        return true;
    }
} 