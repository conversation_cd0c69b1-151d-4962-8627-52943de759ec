/**
 * 通用用户信息接口
 */
export interface IUserInfo {
    /** 用户ID */
    id: string;
    /** 用户姓名 */
    name: string;
    /** 用户昵称 */
    nickname: string;
    /** 用户部门 */
    department: string;
}

/**
 * 用户类型枚举
 */
export enum UserType {
    HUAWEI = 'huawei',
    GITHUB = 'github',
    MICROSOFT = 'microsoft',
    GITLAB = 'gitlab',
    DEBUG = 'debug'
}

/**
 * 通用用户接口
 */
export interface IUser {
    /** 获取用户类型 */
    readonly type: UserType;
    
    /** 获取通用用户信息 */
    readonly info: IUserInfo | undefined;
    
    /** 获取原始用户数据 */
    getRaw(): any;
    
    /** 初始化用户信息 */
    init(): Promise<void>;
    
    /** 检查用户是否已登录 */
    isLoggedIn(): boolean;
}

/**
 * 用户工厂接口
 */
export interface IUserFactory {
    /** 创建指定类型的用户实例 */
    createUser(type: UserType): IUser;
    
    /** 获取当前活跃的用户 */
    getCurrentUser(): IUser | undefined;
    
    /** 设置当前活跃的用户类型 */
    setCurrentUserType(type: UserType): void;
} 