import * as vscode from 'vscode';
import { Logger } from '../../common/log/logger';
import { UserFactory } from './UserFactory';
import { IUser, UserType } from './types/user';

/**
 * 用户服务类
 * 提供统一的用户管理接口
 */
export class UserService {
    private static _instance: UserService;
    private userFactory: UserFactory;
    private _initialized = false;
    private logger = new Logger('UserService');

    constructor() {
        this.userFactory = UserFactory.getInstance();
    }

    public static getInstance(): UserService {
        if (!UserService._instance) {
            UserService._instance = new UserService();
        }
        return UserService._instance;
    }

    /**
     * 初始化用户系统
     */
    public async initialize(): Promise<void> {
        if (this._initialized) {
            return;
        }

        try {
            const user = this.userFactory.getCurrentUser();
        if (user) {
            await user.init();
        }
        this._initialized = true;
            console.log('UserService 初始化完成');
        } catch (error) {
            console.error('UserService 初始化失败:', error);
            throw error;
        }
    }

    /**
     * 获取当前用户
     */
    public async getCurrentUser(): Promise<IUser | undefined> {
            await this.initialize();
        return this.userFactory.getCurrentUser();
    }

    /**
     * 切换用户类型
     */
    public async switchUserType(type: UserType): Promise<IUser> {
        this.userFactory.setCurrentUserType(type);
        const user = this.userFactory.getCurrentUser();
        if (user) {
            await user.init();
        }
        return user!;
    }

    /**
     * 获取指定类型的用户
     */
    public async getUser(type: UserType): Promise<IUser> {
        const user = this.userFactory.createUser(type);
        await user.init();
        return user;
    }

    /**
     * 检查当前用户是否已登录
     */
    public async isCurrentUserLoggedIn(): Promise<boolean> {
        const user = await this.getCurrentUser();
        return user?.isLoggedIn() || false;
    }

    /**
     * 获取当前用户信息
     */
    public async getCurrentUserInfo() {
        const user = await this.getCurrentUser();
        return user?.info;
    }

    /**
     * 获取当前用户的原始数据
     */
    public async getCurrentUserRaw(): Promise<any> {
        const user = await this.getCurrentUser();
        return user?.getRaw();
    }

    /**
     * 获取当前用户类型
     */
    public getCurrentUserType(): UserType {
        return this.userFactory.getCurrentUserType();
    }

    /**
     * 检查是否已初始化
     */
    public isInitialized(): boolean {
        return this._initialized;
    }

    /**
     * 获取并显示用户信息（命令入口）
     */
    public async showUserInfo(): Promise<void> {
        try {
            const userInfo = await this.getCurrentUserInfo();
            vscode.window.showInformationMessage(`当前用户: ${userInfo?.name || '未知'}`);
        } catch (error) {
            console.error('获取用户信息失败', error);
            vscode.window.showErrorMessage('获取用户信息失败: ' + error);
            throw error;
        }
    }

    /**
     * 调试用户信息（命令入口）
     */
    public async debugUserInfo(): Promise<void> {
        try {
            console.log('开始调试用户信息...');

            const userInfo = await this.getCurrentUserInfo();

            // 使用Logger输出到outputChannel
            this.logger.info('=== 用户信息调试 ===', { toOutput: true });
            this.logger.info(`原始用户信息: ${JSON.stringify(userInfo, null, 2)}`, { toOutput: true });
            this.logger.info(`VSCode 环境信息:`, { toOutput: true });
            this.logger.info(`- 版本: ${vscode.version}`, { toOutput: true });
            this.logger.info(`- 语言: ${vscode.env.language}`, { toOutput: true });
            this.logger.info(`- 机器ID: ${vscode.env.machineId}`, { toOutput: true });
            this.logger.info(`- 会话ID: ${vscode.env.sessionId}`, { toOutput: true });

            console.log('用户信息调试完成');
        } catch (error) {
            console.error('调试用户信息失败', error);
            throw error;
        }
    }
}

// 全局用户服务实例，方便在任何地方访问
export const globalUserService = UserService.getInstance();

// 便捷的全局访问函数
export async function getCurrentUser(): Promise<IUser | undefined> {
    return globalUserService.getCurrentUser();
}

export async function getCurrentUserInfo() {
    return globalUserService.getCurrentUserInfo();
}

export async function getCurrentUserRaw(): Promise<any> {
    return globalUserService.getCurrentUserRaw();
}

export async function isUserLoggedIn(): Promise<boolean> {
    return globalUserService.isCurrentUserLoggedIn();
}

export function getUserType(): UserType {
    return globalUserService.getCurrentUserType();
}

// 向后兼容的函数导出
export interface IUserVscodeResult {
    fileName: string;
    languageId: string;
    contentPreview: string;
}

export interface IGitHubUserInfo {
    username: string;
    email?: string;
    accessToken: string;
}

export interface IGitUserInfo {
    name?: string;
    email?: string;
}

export interface IWorkspaceInfo {
    name: string;
    uri?: string;
    folders: string[];
}

export interface IVSCodeUserInfo {
    github?: IGitHubUserInfo;
    git?: IGitUserInfo;
    workspace?: IWorkspaceInfo;
    activeEditor?: IUserVscodeResult;
    vscodeVersion: string;
}

/**
 * 获取当前激活编辑器的基本信息
 */
export async function getActiveEditorInfo(): Promise<IUserVscodeResult | null> {
    const editor = vscode.window.activeTextEditor;
    if (!editor) { return null; }
    const doc = editor.document;
    return {
        fileName: doc.fileName,
        languageId: doc.languageId,
        contentPreview: doc.getText().slice(0, 100)
    };
}

/**
 * 获取工作区信息
 */
export function getWorkspaceInfo(): IWorkspaceInfo | null {
    try {
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (!workspaceFolders) {
            return null;
        }

        const folders = workspaceFolders.map(folder => folder.uri.fsPath);
        const workspaceFile = vscode.workspace.workspaceFile;

        return {
            name: vscode.workspace.name || 'Unknown',
            uri: workspaceFile?.toString(),
            folders: folders
        };
    } catch (error) {
        console.log('获取工作区信息失败:', error);
        return null;
    }
}

/**
 * 获取完整的VSCode用户信息
 */
export async function getVSCodeUserInfo(): Promise<IVSCodeUserInfo> {
    try {
        const [activeEditor] = await Promise.all([
            getActiveEditorInfo()
        ]);

        const workspace = getWorkspaceInfo();

        return {
            workspace: workspace || undefined,
            activeEditor: activeEditor || undefined,
            vscodeVersion: vscode.version
        };
    } catch (error) {
        console.error('获取VSCode用户信息失败:', error);
        throw error;
    }
} 