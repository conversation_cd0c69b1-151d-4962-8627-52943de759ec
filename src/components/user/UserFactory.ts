import { DebugUserAdapter } from './debug/adapter';
import { HuaweiUserAdapter } from './huawei/adapter';
import { IUser, IUserFactory, UserType } from './types/user';

/**
 * 用户工厂实现
 */
export class UserFactory implements IUserFactory {
    private static _instance: UserFactory;
    private _currentUserType: UserType = UserType.DEBUG; // 默认调试用户
    private _currentUser: IUser | undefined;

    private constructor() {}

    public static getInstance(): UserFactory {
        if (!this._instance) {
            this._instance = new UserFactory();
        }
        return this._instance;
    }

    /**
     * 创建指定类型的用户实例
     */
    public createUser(type: UserType): IUser {
        switch (type) {
            case UserType.HUAWEI:
                return new HuaweiUserAdapter();
            case UserType.DEBUG:
                return new DebugUserAdapter();
            case UserType.GITHUB:
                // TODO: 实现 GitHub 用户适配器
                throw new Error(`用户类型 ${type} 暂未实现`);
            case UserType.MICROSOFT:
                // TODO: 实现 Microsoft 用户适配器
                throw new Error(`用户类型 ${type} 暂未实现`);
            case UserType.GITLAB:
                // TODO: 实现 GitLab 用户适配器
                throw new Error(`用户类型 ${type} 暂未实现`);
            default:
                throw new Error(`不支持的用户类型: ${type}`);
        }
    }

    /**
     * 获取当前活跃的用户
     */
    public getCurrentUser(): IUser | undefined {
        if (!this._currentUser) {
            this._currentUser = this.createUser(this._currentUserType);
        }
        return this._currentUser;
    }

    /**
     * 设置当前活跃的用户类型
     */
    public setCurrentUserType(type: UserType): void {
        this._currentUserType = type;
        this._currentUser = undefined; // 重置当前用户，下次获取时重新创建
    }

    /**
     * 获取当前用户类型
     */
    public getCurrentUserType(): UserType {
        return this._currentUserType;
    }
} 