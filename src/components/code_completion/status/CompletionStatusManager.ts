import * as vscode from 'vscode';
import { Logger } from '../../../common/log/logger';
import { CompletionRequestPayload } from '../../types/code_completion';
import { CompletionStatus, CompletionStatusBar } from './CompletionStatusBar';
import { CompletionEventManager, CompletionEventType } from './events/CompletionEventManager';

/**
 * 续写状态管理器
 * 集成状态栏显示和事件系统，提供统一的状态管理接口
 */
export class CompletionStatusManager {
  private static instance: CompletionStatusManager;
  private logger: Logger;
  private statusBar: CompletionStatusBar;
  private eventManager: CompletionEventManager;
  private disposables: vscode.Disposable[] = [];

  private constructor() {
    this.logger = new Logger('CompletionStatusManager');
    this.statusBar = CompletionStatusBar.getInstance();
    this.eventManager = CompletionEventManager.getInstance();

    this.setupEventListeners();
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): CompletionStatusManager {
    if (!CompletionStatusManager.instance) {
      CompletionStatusManager.instance = new CompletionStatusManager();
    }
    return CompletionStatusManager.instance;
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 监听所有续写事件，自动更新状态栏
    const disposable = this.eventManager.subscribe([
      CompletionEventType.STARTED,
      CompletionEventType.THINKING,
      CompletionEventType.PROGRESS,
      CompletionEventType.SUCCESS,
      CompletionEventType.FAILED,
      CompletionEventType.CANCELLED,
      CompletionEventType.ACCEPTED,
      CompletionEventType.REJECTED
    ], (event) => {
      this.handleCompletionEvent(event);
    });

    this.disposables.push(disposable);
  }

  /**
   * 处理续写事件
   */
  private handleCompletionEvent(event: any): void {
    switch (event.eventType) {
      case CompletionEventType.STARTED:
        this.statusBar.updateStatus(CompletionStatus.THINKING, event.message);
        break;

      case CompletionEventType.THINKING:
        this.statusBar.updateStatus(CompletionStatus.THINKING, event.message);
        break;

      case CompletionEventType.PROGRESS:
        this.statusBar.showProgressStatus(event.message || '正在生成', event.progress);
        break;

      case CompletionEventType.SUCCESS:
        this.statusBar.showTemporaryStatus(CompletionStatus.SUCCESS, event.message, 2000);
        break;

      case CompletionEventType.FAILED:
        this.statusBar.showTemporaryStatus(CompletionStatus.FAILED, event.message, 5000);
        break;

      case CompletionEventType.CANCELLED:
        this.statusBar.showTemporaryStatus(CompletionStatus.CANCELLED, event.message, 2000);
        break;

      case CompletionEventType.ACCEPTED:
        this.statusBar.showTemporaryStatus(CompletionStatus.SUCCESS, event.message, 1500);
        break;

      case CompletionEventType.REJECTED:
        this.statusBar.showTemporaryStatus(CompletionStatus.CANCELLED, event.message, 1500);
        break;
    }
  }

  /**
   * 开始续写（使用现有payload）
   */
  public startCompletion(payload: CompletionRequestPayload): void {
    const metadata = {
      filePath: payload.meta.fileName,
      language: payload.meta.languageId,
      position: payload.meta.cursorLine && payload.meta.cursorColumn ? {
        line: payload.meta.cursorLine - 1,
        character: payload.meta.cursorColumn - 1
      } : undefined,
      scope: payload.meta.scope,
      environment: payload.meta.environment,
      user: payload.meta.user,
      contextSnippets: payload.meta.selectedContext?.length || 0
    };

    this.eventManager.publishStarted(metadata);
  }

  /**
   * 更新续写进度
   */
  public updateProgress(progress: number, message?: string, payload?: CompletionRequestPayload): void {
    const metadata = payload ? {
      filePath: payload.meta.fileName,
      language: payload.meta.languageId
    } : undefined;

    this.eventManager.publishProgress(progress, message, metadata);
  }

  /**
   * 续写成功
   */
  public completionSuccess(completionText: string, payload?: CompletionRequestPayload): void {
    const metadata = payload ? {
      filePath: payload.meta.fileName,
      language: payload.meta.languageId,
      suggestionText: completionText
    } : undefined;

    this.eventManager.publishSuccess(completionText, metadata);
  }

  /**
   * 续写失败
   */
  public completionFailed(error: Error, payload?: CompletionRequestPayload): void {
    const metadata = payload ? {
      filePath: payload.meta.fileName,
      language: payload.meta.languageId
    } : undefined;

    this.eventManager.publishFailed(error, metadata);
  }

  /**
   * 续写取消
   */
  public completionCancelled(payload?: CompletionRequestPayload): void {
    const metadata = payload ? {
      filePath: payload.meta.fileName,
      language: payload.meta.languageId
    } : undefined;

    this.eventManager.publishCancelled(metadata);
  }

  /**
   * 用户接受建议
   */
  public userAccepted(suggestionText: string, originalText?: string, payload?: CompletionRequestPayload): void {
    const metadata = payload ? {
      filePath: payload.meta.fileName,
      language: payload.meta.languageId,
      suggestionText,
      originalText
    } : undefined;

    this.eventManager.publishAccepted(suggestionText, originalText, metadata);
  }

  /**
   * 用户拒绝建议
   */
  public userRejected(suggestionText: string, payload?: CompletionRequestPayload): void {
    const metadata = payload ? {
      filePath: payload.meta.fileName,
      language: payload.meta.languageId,
      suggestionText
    } : undefined;

    this.eventManager.publishRejected(suggestionText, metadata);
  }

  /**
   * 获取当前状态
   */
  public getCurrentStatus(): CompletionStatus {
    return this.statusBar.getCurrentStatus();
  }

  /**
   * 订阅事件（代理到事件管理器）
   */
  public subscribe(eventType: CompletionEventType | CompletionEventType[], listener: any): vscode.Disposable {
    return this.eventManager.subscribe(eventType, listener);
  }

  /**
   * 发布自定义事件（代理到事件管理器）
   */
  public publish(event: any): void {
    this.eventManager.publish(event);
  }

  /**
   * 清理资源
   */
  public dispose(): void {
    this.disposables.forEach(disposable => disposable.dispose());
    this.disposables = [];
    this.statusBar.dispose();
    this.eventManager.dispose();
  }
} 