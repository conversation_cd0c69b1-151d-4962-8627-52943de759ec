import * as vscode from 'vscode';
import { Logger } from '../../../../common/log/logger';
import { CompletionStatus } from '../CompletionStatusBar';

/**
 * 续写事件类型
 */
export enum CompletionEventType {
  STARTED = 'completion_started',           // 续写开始
  THINKING = 'completion_thinking',         // 正在思考
  PROGRESS = 'completion_progress',         // 进度更新
  SUCCESS = 'completion_success',           // 续写成功
  FAILED = 'completion_failed',             // 续写失败
  CANCELLED = 'completion_cancelled',       // 续写取消
  ACCEPTED = 'completion_accepted',         // 用户接受
  REJECTED = 'completion_rejected',         // 用户拒绝
  STATUS_CHANGED = 'completion_status_changed' // 状态变化
}

/**
 * 续写事件数据接口
 */
export interface CompletionEventData {
  eventType: CompletionEventType;
  timestamp: number;
  status: CompletionStatus;
  message?: string;
  progress?: number;
  error?: Error;
  metadata?: {
    filePath?: string;
    language?: string;
    position?: vscode.Position | { line: number; character: number };
    suggestionText?: string;
    originalText?: string;
    scope?: any;
    environment?: any;
    user?: any;
    contextSnippets?: number;
    [key: string]: any;
  };
}

/**
 * 事件监听器类型
 */
export type CompletionEventListener = (event: CompletionEventData) => void | Promise<void>;

/**
 * 续写事件管理器
 * 提供事件发布和订阅机制，让其他组件可以监听续写状态变化
 */
export class CompletionEventManager {
  private static instance: CompletionEventManager;
  private logger: Logger;
  private eventEmitter: vscode.EventEmitter<CompletionEventData>;
  private listeners: Map<CompletionEventType, Set<CompletionEventListener>> = new Map();

  private constructor() {
    this.logger = new Logger('CompletionEventManager');
    this.eventEmitter = new vscode.EventEmitter<CompletionEventData>();
    
    // 设置事件发射器
    this.eventEmitter.event((event) => {
      this.handleEvent(event);
    });
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): CompletionEventManager {
    if (!CompletionEventManager.instance) {
      CompletionEventManager.instance = new CompletionEventManager();
    }
    return CompletionEventManager.instance;
  }

  /**
   * 订阅事件
   */
  public subscribe(
    eventType: CompletionEventType | CompletionEventType[],
    listener: CompletionEventListener
  ): vscode.Disposable {
    const eventTypes = Array.isArray(eventType) ? eventType : [eventType];
    
    eventTypes.forEach(type => {
      if (!this.listeners.has(type)) {
        this.listeners.set(type, new Set());
      }
      this.listeners.get(type)!.add(listener);
    });

    this.logger.info(`事件监听器已注册`, { eventTypes });

    // 返回取消订阅的disposable
    return {
      dispose: () => {
        eventTypes.forEach(type => {
          const listeners = this.listeners.get(type);
          if (listeners) {
            listeners.delete(listener);
            if (listeners.size === 0) {
              this.listeners.delete(type);
            }
          }
        });
        this.logger.info(`事件监听器已取消注册`, { eventTypes });
      }
    };
  }

  /**
   * 发布事件
   */
  public publish(event: CompletionEventData): void {
    this.logger.info(`发布续写事件`, { 
      eventType: event.eventType, 
      status: event.status,
      message: event.message 
    });
    
    this.eventEmitter.fire(event);
  }

  /**
   * 发布续写开始事件
   */
  public publishStarted(metadata?: CompletionEventData['metadata']): void {
    this.publish({
      eventType: CompletionEventType.STARTED,
      timestamp: Date.now(),
      status: CompletionStatus.THINKING,
      message: '续写开始',
      metadata
    });
  }

  /**
   * 发布续写思考事件
   */
  public publishThinking(message?: string, metadata?: CompletionEventData['metadata']): void {
    this.publish({
      eventType: CompletionEventType.THINKING,
      timestamp: Date.now(),
      status: CompletionStatus.THINKING,
      message: message || '正在分析代码并生成建议',
      metadata
    });
  }

  /**
   * 发布进度更新事件
   */
  public publishProgress(progress: number, message?: string, metadata?: CompletionEventData['metadata']): void {
    this.publish({
      eventType: CompletionEventType.PROGRESS,
      timestamp: Date.now(),
      status: CompletionStatus.THINKING,
      message: message || `生成进度: ${Math.round(progress * 100)}%`,
      progress,
      metadata
    });
  }

  /**
   * 发布续写成功事件
   */
  public publishSuccess(suggestionText?: string, metadata?: CompletionEventData['metadata']): void {
    this.publish({
      eventType: CompletionEventType.SUCCESS,
      timestamp: Date.now(),
      status: CompletionStatus.SUCCESS,
      message: '续写完成',
      metadata: {
        ...metadata,
        suggestionText
      }
    });
  }

  /**
   * 发布续写失败事件
   */
  public publishFailed(error: Error, metadata?: CompletionEventData['metadata']): void {
    this.publish({
      eventType: CompletionEventType.FAILED,
      timestamp: Date.now(),
      status: CompletionStatus.FAILED,
      message: error.message || '续写失败',
      error,
      metadata
    });
  }

  /**
   * 发布续写取消事件
   */
  public publishCancelled(metadata?: CompletionEventData['metadata']): void {
    this.publish({
      eventType: CompletionEventType.CANCELLED,
      timestamp: Date.now(),
      status: CompletionStatus.CANCELLED,
      message: '续写已取消',
      metadata
    });
  }

  /**
   * 发布用户接受事件
   */
  public publishAccepted(suggestionText: string, originalText?: string, metadata?: CompletionEventData['metadata']): void {
    this.publish({
      eventType: CompletionEventType.ACCEPTED,
      timestamp: Date.now(),
      status: CompletionStatus.SUCCESS,
      message: '用户接受了续写建议',
      metadata: {
        ...metadata,
        suggestionText,
        originalText
      }
    });
  }

  /**
   * 发布用户拒绝事件
   */
  public publishRejected(suggestionText: string, metadata?: CompletionEventData['metadata']): void {
    this.publish({
      eventType: CompletionEventType.REJECTED,
      timestamp: Date.now(),
      status: CompletionStatus.IDLE,
      message: '用户拒绝了续写建议',
      metadata: {
        ...metadata,
        suggestionText
      }
    });
  }

  /**
   * 发布状态变化事件
   */
  public publishStatusChanged(status: CompletionStatus, message?: string, metadata?: CompletionEventData['metadata']): void {
    this.publish({
      eventType: CompletionEventType.STATUS_CHANGED,
      timestamp: Date.now(),
      status,
      message,
      metadata
    });
  }

  /**
   * 处理事件分发
   */
  private handleEvent(event: CompletionEventData): void {
    const listeners = this.listeners.get(event.eventType);
    if (listeners) {
      listeners.forEach(listener => {
        try {
          const result = listener(event);
          if (result instanceof Promise) {
            result.catch(error => {
              this.logger.error(`事件监听器执行失败`, { 
                eventType: event.eventType, 
                error: error.message 
              });
            });
          }
        } catch (error) {
          this.logger.error(`事件监听器执行失败`, { 
            eventType: event.eventType, 
            error: error instanceof Error ? error.message : String(error)
          });
        }
      });
    }
  }

  /**
   * 获取事件发射器（用于高级用法）
   */
  public get event(): vscode.Event<CompletionEventData> {
    return this.eventEmitter.event;
  }

  /**
   * 清理所有监听器
   */
  public dispose(): void {
    this.listeners.clear();
    this.eventEmitter.dispose();
  }
} 