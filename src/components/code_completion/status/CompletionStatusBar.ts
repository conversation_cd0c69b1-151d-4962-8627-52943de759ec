import * as vscode from 'vscode';
import { Logger } from '../../../common/log/logger';

/**
 * 续写状态枚举
 */
export enum CompletionStatus {
  IDLE = 'idle',           // 空闲状态
  THINKING = 'thinking',   // 正在思考/生成
  SUCCESS = 'success',     // 续写成功
  FAILED = 'failed',       // 续写失败
  CANCELLED = 'cancelled'  // 续写取消
}

/**
 * 续写状态栏管理器
 * 负责在VSCode状态栏显示续写状态
 */
export class CompletionStatusBar {
  private static instance: CompletionStatusBar;
  private statusBarItem: vscode.StatusBarItem;
  private logger: Logger;
  private currentStatus: CompletionStatus = CompletionStatus.IDLE;
  private statusTimer?: NodeJS.Timeout;

  private constructor() {
    this.logger = new Logger('CompletionStatusBar');
    this.statusBarItem = vscode.window.createStatusBarItem(
      vscode.StatusBarAlignment.Right,
      100 // 优先级
    );
    this.updateStatus(CompletionStatus.IDLE);
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): CompletionStatusBar {
    if (!CompletionStatusBar.instance) {
      CompletionStatusBar.instance = new CompletionStatusBar();
    }
    return CompletionStatusBar.instance;
  }

  /**
   * 更新续写状态
   */
  public updateStatus(status: CompletionStatus, message?: string): void {
    this.currentStatus = status;

    const statusConfig = this.getStatusConfig(status, message);

    this.statusBarItem.text = statusConfig.text;
    this.statusBarItem.tooltip = statusConfig.tooltip;
    this.statusBarItem.backgroundColor = statusConfig.backgroundColor;

    // 根据状态决定是否显示状态栏
    if (status === CompletionStatus.IDLE) {
      this.statusBarItem.hide();
    } else {
      this.statusBarItem.show();
    }

    this.logger.info(`续写状态更新: ${status}`, { message });
  }

  /**
   * 显示临时状态（带自动隐藏）
   */
  public showTemporaryStatus(status: CompletionStatus, message?: string, duration: number = 3000): void {
    this.updateStatus(status, message);

    // 清除之前的定时器
    if (this.statusTimer) {
      clearTimeout(this.statusTimer);
      this.statusTimer = undefined;
    }

    // 设置自动隐藏
    this.statusTimer = setTimeout(() => {
      this.updateStatus(CompletionStatus.IDLE);
      this.statusTimer = undefined;
    }, duration);
  }

  /**
   * 显示进度状态（带进度指示）
   */
  public showProgressStatus(message: string, progress?: number): void {
    let text = `$(sync~spin) ${message}`;
    if (progress !== undefined) {
      text += ` (${Math.round(progress * 100)}%)`;
    }

    this.statusBarItem.text = text;
    this.statusBarItem.tooltip = `正在续写: ${message}`;
    this.statusBarItem.backgroundColor = new vscode.ThemeColor('statusBarItem.prominentBackground');
    this.statusBarItem.show();
  }

  /**
   * 获取状态配置
   */
  private getStatusConfig(status: CompletionStatus, message?: string) {
    const baseMessage = message || this.getDefaultMessage(status);

    switch (status) {
      case CompletionStatus.THINKING:
        return {
          text: `$(sync~spin) 续写中...`,
          tooltip: `正在生成代码: ${baseMessage}`,
          backgroundColor: new vscode.ThemeColor('statusBarItem.prominentBackground')
        };

      case CompletionStatus.SUCCESS:
        return {
          text: `$(check) 续写完成`,
          tooltip: `续写成功: ${baseMessage}`,
          backgroundColor: new vscode.ThemeColor('statusBarItem.successBackground')
        };

      case CompletionStatus.FAILED:
        return {
          text: `$(error) 续写失败`,
          tooltip: `续写失败: ${baseMessage}`,
          backgroundColor: new vscode.ThemeColor('statusBarItem.errorBackground')
        };

      case CompletionStatus.CANCELLED:
        return {
          text: `$(close) 续写取消`,
          tooltip: `续写已取消: ${baseMessage}`,
          backgroundColor: new vscode.ThemeColor('statusBarItem.warningBackground')
        };

      default:
        return {
          text: '',
          tooltip: '',
          backgroundColor: undefined
        };
    }
  }

  /**
   * 获取默认状态消息
   */
  private getDefaultMessage(status: CompletionStatus): string {
    switch (status) {
      case CompletionStatus.THINKING:
        return '正在分析代码并生成建议';
      case CompletionStatus.SUCCESS:
        return '代码续写完成';
      case CompletionStatus.FAILED:
        return '生成失败，请重试';
      case CompletionStatus.CANCELLED:
        return '用户取消了续写';
      default:
        return '';
    }
  }

  /**
   * 获取当前状态
   */
  public getCurrentStatus(): CompletionStatus {
    return this.currentStatus;
  }

  /**
   * 销毁状态栏
   */
  public dispose(): void {
    if (this.statusTimer) {
      clearTimeout(this.statusTimer);
      this.statusTimer = undefined;
    }
    this.statusBarItem.dispose();
  }
} 