import * as vscode from 'vscode';
import { Suggestion, suggestionStore } from '../../../common/store/store';

import { getSharedCompletionClient } from '../../../common/client/llm/clientFactory';
import { collectContextForCompletion, getCurrentFunctionContextDynamic } from '../../code_context/collect';
import { StatisticsEventType, StatisticsService } from '../../statistics';
import { CodeSnippetType, CompletionMeta, CompletionRequestPayload, ContextSnippet } from '../../types/code_completion';
import { GraphCodeNode } from '../../types/code_graph';
import { extractCodeFromResp } from '../prompt/completionPrompt';
import { buildStructuredPromptFromMeta } from '../prompt/structuredPrompt';
import { CompletionStatusManager } from '../status/CompletionStatusManager';
import { fireCodeLensChange } from '../ui/codeLensProvider';
import { CompletionRequestManager } from '../utils/completionRequestManager';
import { CompletionState, debounce, DEBOUNCE_DELAY } from '../utils/debounce';
import { buildEnvironmentAndUserInfo } from '../utils/environmentBuilder';


// 补全建议被接受的回调
function onSuggestionAccepted() {
    // 补全被接受时，保持缓存
}

// 补全建议被拒绝的回调
function onSuggestionRejected() {
    // 补全被拒绝时，清除缓存
    CompletionState.getInstance().clearCompletionState();
}

// 注册补全建议的回调
export function registerInlineCompletionCallbacks() {
    // 监听文档内容变化，处理补全建议的接受/拒绝和清除
    vscode.workspace.onDidChangeTextDocument((event) => {
        const docUri = event.document.uri.toString();
        const suggestion = suggestionStore.get(docUri);

        if (suggestion && !suggestion.ongoing) {
            // 检查是否有内容变化
            if (event.contentChanges.length > 0) {
                // 检查补全是否被接受
                const text = event.document.getText(suggestion.range);
                if (text === suggestion.text) {
                    onSuggestionAccepted();
                } else {
                    onSuggestionRejected();
                }

                // 清除建议状态
                suggestionStore.delete(docUri);
                fireCodeLensChange();
                vscode.commands.executeCommand('editor.action.inlineSuggest.hide');
            }
        }
    });

    // 监听光标位置变化，清除建议
    vscode.window.onDidChangeTextEditorSelection((event) => {
        const editor = event.textEditor;
        const docUri = editor.document.uri.toString();
        const suggestion = suggestionStore.get(docUri);

        if (suggestion && !suggestion.ongoing && suggestion.range) {
            const currentPosition = event.selections[0].active;
            // 如果光标移到建议范围之外，则拒绝建议
            if (!suggestion.range.contains(currentPosition)) {
                rejectSuggestion();
            }
        }
    });

    // 监听编辑器切换，清除建议
    vscode.window.onDidChangeActiveTextEditor(() => {
        suggestionStore.clear();
        fireCodeLensChange();
        vscode.commands.executeCommand('editor.action.inlineSuggest.hide');
    });

    // ESC键命令已在 package.json 中通过 keybindings 绑定，无需重复注册
    // 当用户按ESC键时，VSCode会自动调用 code-partner.rejectSuggestion 命令
}

// 更新建议状态
function updateSuggestionState(docUri: string, state: Partial<Suggestion>) {
    const currentState = suggestionStore.get(docUri) || {};
    const newState = { ...currentState, ...state };
    suggestionStore.set(docUri, newState);
    suggestionStore.fireCodeLensChange();
}



// 辅助函数：ContextItem => ContextSnippet（新的增强版本）
function contextItemToContextSnippet(contextItem: any, contextType: 'semantic' | 'similar' = 'semantic') {
    const validTypes: CodeSnippetType[] = ['function', 'variable', 'struct', 'class', 'interface', 'enum', 'comment', 'import', 'typealias', 'other'];

    // 根据来源确定代码片段类型
    let codeSnippetType: CodeSnippetType = 'other';
    if (contextItem.source === 'graph') {
        codeSnippetType = 'function'; // 图谱来源通常是函数
    } else if (contextItem.source === 'user-snippets') {
        codeSnippetType = 'other'; // 用户片段
    }

    return {
        contextType,
        codeSnippetType,
        fileName: contextItem.location?.file || contextItem.userSnippet?.name || `[${contextItem.source}]`,
        content: contextItem.content || '',
        startLine: contextItem.location?.start?.line ?? 1,
        startColumn: contextItem.location?.start?.column ?? 1,
        endLine: contextItem.location?.end?.line ?? 1,
        endColumn: contextItem.location?.end?.column ?? 1,
        label: contextItem.userSnippet?.name || extractLabelFromContent(contextItem.content) || `[${contextItem.source}]`
    };
}

// 辅助函数：从内容中提取标签
function extractLabelFromContent(content: string): string {
    if (!content) {
        return '';
    }

    // 尝试提取函数名
    const functionMatch = content.match(/(?:function\s+|def\s+|fn\s+)(\w+)/);
    if (functionMatch) {
        return functionMatch[1];
    }

    // 尝试提取类名
    const classMatch = content.match(/(?:class\s+)(\w+)/);
    if (classMatch) {
        return classMatch[1];
    }

    // 返回内容的前几个词
    const words = content.trim().split(/\s+/).slice(0, 3);
    return words.join(' ');
}

// 辅助函数：GraphCodeNode => CompletionScope
function graphNodeToCompletionScope(node: GraphCodeNode) {
    const validScopeTypes = ['function', 'class', 'method', 'global', 'block', 'other'] as const;
    const type = (node.type && validScopeTypes.includes(node.type as typeof validScopeTypes[number])) ? node.type as typeof validScopeTypes[number] : 'function';
    return {
        scopeType: type,
        name: node.name,
        startLine: node.location?.start?.line ?? 1,
        startColumn: node.location?.start?.column ?? 1,
        endLine: node.location?.end?.line ?? 1,
        endColumn: node.location?.end?.column ?? 1,
    };
}

// 获取文档文本内容
function getDocumentTextContent(document: vscode.TextDocument, position: vscode.Position, currentFunction?: any) {
    let textBefore: string;
    let textAfter: string;

    try {
        if (currentFunction && currentFunction.location) {
            // 如果在函数内部，我们需要更准确地处理位置映射
            // tree-sitter位置已经是从0开始的，不需要再减1
            const astStartLine = currentFunction.location.start.line;
            const astStartColumn = currentFunction.location.start.column || 0;
            const astEndLine = currentFunction.location.end.line;
            const astEndColumn = currentFunction.location.end.column || 0;

            // tree-sitter位置已经是从0开始的，直接使用
            const functionStartLine = Math.max(0, astStartLine);
            const functionStartColumn = Math.max(0, astStartColumn);
            const functionEndLine = Math.max(0, astEndLine);
            const functionEndColumn = Math.max(0, astEndColumn);

            // 确保位置在文档范围内
            const maxLine = document.lineCount - 1;
            const clampedStartLine = Math.min(functionStartLine, maxLine);
            const clampedEndLine = Math.min(functionEndLine, maxLine);

            // 获取当前行的最大列数
            const startLineMaxColumn = document.lineAt(clampedStartLine).text.length;
            const endLineMaxColumn = document.lineAt(clampedEndLine).text.length;

            const clampedStartColumn = Math.min(functionStartColumn, startLineMaxColumn);
            const clampedEndColumn = Math.min(functionEndColumn, endLineMaxColumn);

            const functionStart = new vscode.Position(clampedStartLine, clampedStartColumn);
            const functionEnd = new vscode.Position(clampedEndLine, clampedEndColumn);

            // 检查光标是否在函数范围内
            const isCursorInFunction = (
                (position.line > functionStart.line || (position.line === functionStart.line && position.character >= functionStart.character)) &&
                (position.line < functionEnd.line || (position.line === functionEnd.line && position.character <= functionEnd.character))
            );

            let finalFunctionStart = functionStart;
            let finalFunctionEnd = functionEnd;

            // // 调试：显示位置比较的详细信息
            // console.log(`=== 位置调试信息 ===`);
            // console.log(`函数开始位置: ${functionStart.line}:${functionStart.character}`);
            // console.log(`函数结束位置: ${functionEnd.line}:${functionEnd.character}`);
            // console.log(`光标位置: ${position.line}:${position.character}`);
            // console.log(`光标在函数范围内: ${isCursorInFunction}`);

            if (!isCursorInFunction) {
                console.log('警告：光标不在函数范围内，这可能是AST解析位置不准确导致的');
                console.log('建议检查AST解析器的位置信息是否正确');
            }

            if (!isCursorInFunction) {
                console.log('光标不在函数范围内，但暂时不进行智能扩展，专注于调试位置信息');
            }

            // 确保光标位置在函数范围内
            const clampedPosition = new vscode.Position(
                Math.max(finalFunctionStart.line, Math.min(position.line, finalFunctionEnd.line)),
                position.character
            );

            // 获取函数开始到光标位置的文本
            textBefore = document.getText(new vscode.Range(finalFunctionStart, clampedPosition));

            // 获取光标位置到函数结束的文本
            textAfter = document.getText(new vscode.Range(clampedPosition, finalFunctionEnd));

            // console.log(`在函数内部续写: ${currentFunction.name}`);
            // console.log(`AST位置: ${astStartLine}:${astStartColumn} - ${astEndLine}:${astEndColumn}`);
            // console.log(`最终位置: ${finalFunctionStart.line}:${finalFunctionStart.character} - ${finalFunctionEnd.line}:${finalFunctionEnd.character}`);
            // console.log(`光标位置: ${position.line}:${position.character} -> 调整后: ${clampedPosition.line}:${clampedPosition.character}`);
            // console.log(`textBefore长度: ${textBefore.length}, textAfter长度: ${textAfter.length}`);
        } else {
            // 如果不在函数内部，获取光标上下5行
            const linesBefore = Math.max(0, position.line - 5);
            const linesAfter = Math.min(document.lineCount - 1, position.line + 5);

            const startPosition = new vscode.Position(linesBefore, 0);
            const endPosition = new vscode.Position(linesAfter, document.lineAt(linesAfter).text.length);

            // 获取光标前5行到光标位置的文本
            textBefore = document.getText(new vscode.Range(startPosition, position));

            // 获取光标位置到光标后5行的文本
            textAfter = document.getText(new vscode.Range(position, endPosition));

            console.log(`不在函数内部续写，获取光标上下5行: ${linesBefore}-${linesAfter}`);
        }
    } catch (error) {
        console.error('获取文档文本内容失败，使用默认值:', error);
        // 如果出现任何错误，使用安全的默认值
        textBefore = '';
        textAfter = '';
    }
    return { textBefore, textAfter };

}

// 处理正在进行的补全
function handleOngoingCompletion(document: vscode.TextDocument) {
    const requestManager = CompletionRequestManager.getInstance();
    const docUri = document.uri.toString();

    // 使用请求管理器取消之前的请求
    requestManager.cancelPreviousRequests(docUri);

    console.log(`[handleOngoingCompletion] 已取消之前的补全请求 for ${docUri}`);
}

// 构建补全上下文
// 使用动态解析获取实时函数上下文，确保在代码续写场景中获取准确的位置信息
// 避免使用索引中的旧数据导致的位置错位问题
async function buildCompletionContext(document: vscode.TextDocument, position: vscode.Position) {
    // console.log('=== 开始构建补全上下文 ===');
    // console.log('文档:', document.fileName);
    // console.log('位置:', `${position.line}:${position.character}`);
    // console.log('使用动态解析获取实时函数上下文...');

    // 1. 获取当前函数上下文（使用增强的多维度检索）
    console.log('1. 获取当前函数上下文（增强版）...');
    const functionContextResult = await getCurrentFunctionContextDynamic();
    const {
        currentFunction,
        relatedContext,
        similarContext,
        allContext,
        contextSummary,
    } = functionContextResult;

    console.log('当前函数:', currentFunction?.name || '无');
    console.log('相关上下文数量:', relatedContext?.length || 0);
    console.log('相似上下文数量:', similarContext?.length || 0);
    console.log('总上下文数量:', allContext?.length || 0);
    console.log('上下文摘要:', contextSummary);

    // 2. 构建上下文代码片段列表（使用增强的ContextItem格式）
    console.log('2. 构建上下文代码片段列表（增强版）...');
    const contextSnippets = [];

    // 添加相关上下文（调用链分析结果）
    if (relatedContext && relatedContext.length > 0) {
        // 限制数量以控制上下文长度
        const limitedRelatedContext = relatedContext.slice(0, 2);
        for (const contextItem of limitedRelatedContext) {
            const relatedSnippet = contextItemToContextSnippet(contextItem, 'semantic');
            contextSnippets.push(relatedSnippet);
            console.log(`添加相关上下文片段: [${contextItem.source}] 评分:${contextItem.score.toFixed(2)}`);
        }
    }

    // 添加相似上下文（多维度相似性检索结果）
    if (similarContext && similarContext.length > 0) {
        // 限制数量以控制上下文长度
        const limitedSimilarContext = similarContext.slice(0, 3);
        for (const contextItem of limitedSimilarContext) {
            const similarSnippet = contextItemToContextSnippet(contextItem, 'similar');
            contextSnippets.push(similarSnippet);
            console.log(`添加相似上下文片段: [${contextItem.source}] 评分:${contextItem.score.toFixed(2)}`);
        }
    }

    // 3. 获取用户自定义片段（通过统一流程管理）
    console.log('3. 获取用户自定义片段...');
    const currentCode = getDocumentTextContent(document, position, currentFunction);
    const userSnippetsItems = await collectContextForCompletion(currentCode.textBefore + currentCode.textAfter, position);

    // 将用户自定义片段添加到similar_context中
    if (userSnippetsItems && userSnippetsItems.length > 0) {
        for (const userSnippetItem of userSnippetsItems) {
            if (userSnippetItem.source === 'user-snippets') {
                const userSnippet = {
                    label: `[用户片段] ${userSnippetItem.userSnippet?.name || '未命名'}`,
                    content: userSnippetItem.content,
                    contextType: 'similar',
                    fileName: '用户自定义片段',
                    score: userSnippetItem.score,
                    userSnippet: userSnippetItem.userSnippet
                };
                contextSnippets.push(userSnippet);
                console.log('添加用户自定义片段:', userSnippet.label);
            }
        }
    }

    console.log('总上下文片段数量:', contextSnippets.length);

    // 4. 获取环境和用户信息
    console.log('4. 获取环境和用户信息...');
    const { environment, user } = await buildEnvironmentAndUserInfo();
    // console.log('环境信息:', {
    //     platform: environment.platform,
    //     vscodeVersion: environment.vscodeVersion,
    //     extensionVersion: environment.extensionVersion
    // });
    // console.log('用户信息:', {
    //     id: user?.id,
    //     name: user?.name
    // });

    // 5. 组装最终结果
    const result = {
        currentFunction,
        selectedContext: contextSnippets,
        environment,
        user,
        // 添加原始上下文数据用于新的结构化提示词
        relatedContext,
        similarContext,
        allContext,
        contextSummary
    };

    console.log('=== 补全上下文构建完成 ===');
    // console.log('结果摘要:', {
    //     hasCurrentFunction: !!currentFunction,
    //     contextSnippetsCount: contextSnippets.length,
    //     hasEnvironment: !!environment,
    //     hasUser: !!user
    // });

    return result;
}

// 构建CompletionMeta
function buildCompletionMeta(
    document: vscode.TextDocument,
    position: vscode.Position,
    contextData: ReturnType<typeof buildCompletionContext> extends Promise<infer T> ? T : never
): CompletionMeta {
    console.log('=== 开始构建CompletionMeta ===');

    const { currentFunction, selectedContext, environment, user } = contextData;

    // 构建 ContextSnippet - 将selectedContext转换成符合ContextSnippet类型的数组
    const contextSnippets: ContextSnippet[] = selectedContext.map((ctx: any) => {
        // 如果是用户自定义片段（来自user-snippets）
        if ('userSnippet' in ctx) {
            return {
                contextType: ctx.contextType as 'semantic' | 'similar',
                codeSnippetType: 'other' as CodeSnippetType,
                fileName: ctx.fileName || '用户自定义片段',
                content: ctx.content,
                startLine: 1,
                startColumn: 1,
                endLine: 1,
                endColumn: 1,
                label: ctx.label || '用户片段'
            };
        }

        // 如果是GraphCodeNode转换的片段（已经有完整的ContextSnippet结构）
        if ('codeSnippetType' in ctx && 'startLine' in ctx && 'startColumn' in ctx && 'endLine' in ctx && 'endColumn' in ctx) {
            return ctx as ContextSnippet;
        }

        // 默认情况，创建一个基本的ContextSnippet
        return {
            contextType: ctx.contextType as 'semantic' | 'similar',
            codeSnippetType: 'other' as CodeSnippetType,
            fileName: ctx.fileName || '',
            content: ctx.content || '',
            startLine: 1,
            startColumn: 1,
            endLine: 1,
            endColumn: 1,
            label: ctx.label || '未知片段'
        };
    });

    // 1. 构建基础元数据
    const baseMeta = {
        fileContent: document.getText(),
        fileName: document.fileName,
        languageId: document.languageId,
        cursorLine: position.line + 1,    // VSCode从0开始，显示时+1
        cursorColumn: position.character + 1,
        rawContextList: contextSnippets,
        selectedContext: contextSnippets,
        environment,
        user
    };

    // 2. 构建作用域信息
    let scope = undefined;
    if (currentFunction) {
        scope = graphNodeToCompletionScope(currentFunction);
        console.log('构建作用域信息:', {
            scopeType: scope.scopeType,
            name: scope.name,
            startLine: scope.startLine,
            endLine: scope.endLine
        });
    } else {
        console.log('无当前函数，跳过作用域构建');
    }

    const result: CompletionMeta = {
        ...baseMeta,
        scope
    };

    console.log('=== CompletionMeta构建完成 ===');
    console.log('元数据摘要:', {
        fileName: result.fileName,
        languageId: result.languageId,
        cursorPosition: `${result.cursorLine}:${result.cursorColumn}`,
        contextSnippetsCount: result.selectedContext?.length || 0,
        hasScope: !!result.scope,
        hasEnvironment: !!result.environment,
        hasUser: !!result.user
    });

    return result;
}

// 构建统计上报的上下文信息
function buildStatisticsContext(
    document: vscode.TextDocument,
    position: vscode.Position,
    textBefore: string,
    textAfter: string,
    contextData: ReturnType<typeof buildCompletionContext> extends Promise<infer T> ? T : never
) {
    console.log('=== 开始构建统计上下文 ===');

    const { currentFunction, selectedContext, environment } = contextData;

    // 1. 构建作用域信息
    const scope = {
        filePath: document.fileName,
        language: document.languageId,
        functionName: currentFunction?.name,
        className: currentFunction?.type === 'class' ? currentFunction.name : undefined,
        moduleName: document.fileName.split('/').pop()?.split('.')[0]
    };

    console.log('作用域信息:', {
        filePath: scope.filePath,
        language: scope.language,
        functionName: scope.functionName,
        className: scope.className,
        moduleName: scope.moduleName
    });

    // 2. 构建上下文信息
    const context = {
        beforeCursor: textBefore,
        afterCursor: textAfter,
        relatedCodeSnippets: selectedContext
            .filter(ctx => ctx.contextType === 'semantic')
            .map(ctx => ({
                filePath: ctx.fileName,
                language: document.languageId,
                code: ctx.content,
                sourceType: ctx.contextType
            })),
        similarCodeSnippets: selectedContext
            .filter(ctx => ctx.contextType === 'similar')
            .map(ctx => ({
                filePath: ctx.fileName,
                language: document.languageId,
                code: ctx.content,
                sourceType: ctx.contextType
            })),
        environments: [
            { name: 'platform', value: environment.platform },
            { name: 'deviceInfo', value: environment.deviceInfo },
            { name: 'osInfo', value: environment.osInfo },
            { name: 'vscodeVersion', value: environment.vscodeVersion },
            { name: 'extensionVersion', value: environment.extensionVersion }
        ]
    };

    console.log('上下文信息:', {
        beforeCursorLength: textBefore.length,
        afterCursorLength: textAfter.length,
        relatedSnippetsCount: context.relatedCodeSnippets.length,
        similarSnippetsCount: context.similarCodeSnippets.length,
        environmentsCount: context.environments.length
    });

    const result = { scope, context };

    console.log('=== 统计上下文构建完成 ===');
    console.log('统计摘要:', {
        hasFunctionName: !!scope.functionName,
        hasClassName: !!scope.className,
        relatedSnippetsCount: context.relatedCodeSnippets.length,
        similarSnippetsCount: context.similarCodeSnippets.length,
        environmentsCount: context.environments.length
    });

    return result;
}

// 获取模型配置
function getModelConfig() {
    const modelConfigRaw = getSharedCompletionClient().getDefaultParams();
    return {
        ...modelConfigRaw,
        max_tokens: modelConfigRaw.max_tokens ?? 256,
        temperature: modelConfigRaw.temperature ?? 0.2,
        stream: modelConfigRaw.stream ?? false,
    };
}

// 处理补全响应
async function processCompletionResponse(completionResult: string | ReadableStream): Promise<string> {
    let completionText = '';
    if (typeof completionResult === 'string') {
        completionText = completionResult;
    } else if (completionResult instanceof ReadableStream) {
        // 如有流式返回，简单收集全部内容
        const reader = completionResult.getReader();
        let collected = '';
        const decoder = new TextDecoder();
        while (true) {
            const { done, value } = await reader.read();
            if (done) { break; }
            collected += decoder.decode(value, { stream: true });
        }
        completionText = collected;
    }

    console.log("===========补全响应===============");
    console.log("Response (formatted):");
    console.log("----------------------------------------");
    console.log(completionText);
    console.log("----------------------------------------");

    console.log("===========提取的续写内容=============");
    completionText = extractCodeFromResp(completionText);

    // 统一换行符处理：将\r\n转换为\n，确保跨平台一致性
    completionText = completionText.replace(/\r\n/g, '\n');

    console.log("Extracted content (formatted):");
    console.log("----------------------------------------");
    console.log(completionText);
    console.log("----------------------------------------");

    return completionText;
}

// 判断内容是否为"全是空白或换行符"
function isEmptyOrOnlyNewlines(text: string): boolean {
    // 统一换行符处理，确保跨平台一致性
    const normalizedText = text.replace(/\r\n/g, '\n');
    return !normalizedText || normalizedText.replace(/[\r\n\s]/g, '') === '';
}

// 处理空补全建议
function handleEmptyCompletion(document: vscode.TextDocument, payload: CompletionRequestPayload) {
    console.log('收到空补全建议');
    const statusManager = CompletionStatusManager.getInstance();
    statusManager.completionSuccess('', payload);

    // 清除建议状态
    suggestionStore.delete(document.uri.toString());
    fireCodeLensChange();

    console.log('空补全处理完成，返回空结果');
    return [];
}

// 创建补全项
function createCompletionItem(completionText: string, position: vscode.Position) {
    // 统一换行符处理：将\r\n转换为\n，确保跨平台一致性
    const normalizedText = completionText.replace(/\r\n/g, '\n');

    // 计算补全内容的行数（使用标准化后的文本）
    const lines = normalizedText.split('\n');
    let endPosition: vscode.Position;

    if (lines.length === 1) {
        // 单行内容：只需要计算字符偏移
        endPosition = new vscode.Position(position.line, position.character + normalizedText.length);
    } else {
        // 多行内容：计算最终行和列位置
        const lastLineIndex = lines.length - 1;
        const lastLineLength = lines[lastLineIndex].length;
        endPosition = new vscode.Position(
            position.line + lastLineIndex,
            lastLineIndex === 0 ? position.character + lastLineLength : lastLineLength
        );
    }

    const completionRange = new vscode.Range(position, endPosition);

    console.log('[createCompletionItem] 补全内容:');
    console.log('----------------------------------------');
    console.log(normalizedText);
    console.log('----------------------------------------');
    console.log('[createCompletionItem] 原始文本长度:', completionText.length);
    console.log('[createCompletionItem] 标准化后长度:', normalizedText.length);
    console.log('[createCompletionItem] 行数:', lines.length);
    console.log('[createCompletionItem] 补全范围:', position.line, position.character, '->', endPosition.line, endPosition.character);

    // 使用对象字面量替代构造函数，解决 Windows 兼容性问题
    // 记录一下绕过的方案
        // TODO: Windows VSCode 环境兼容性问题
        // 按照 VSCode API 文档，第二个参数应该是 vscode.Range 类型
        // 但在 Windows 环境中使用 completionRange 会导致补全建议无法显示
        // 这可能与 Windows 系统的换行符处理或 VSCode 渲染机制有关
        // 临时使用 undefined 绕过此问题，详细信息请参考 docs/windows-vscode-completion-issue.md
    const completionItem = {
        insertText: normalizedText,
        range: completionRange, // 使用正确的范围
        command: {
            title: "接受建议",
            command: "code-partner.acceptSuggestion"
        }
    } as vscode.InlineCompletionItem;

    return { completionItem, completionRange };
}

// 处理补全成功
async function handleCompletionSuccess(
    completionText: string,
    payload: CompletionRequestPayload,
    scope: any,
    context: any,
    prompt: string,
    modelConfig: any
) {
    const statusManager = CompletionStatusManager.getInstance();
    statusManager.completionSuccess(completionText, payload);

    // 上报续写响应事件（包含实际的响应内容）
    const statisticsService = StatisticsService.getInstance();
    await statisticsService.reportCompletionResponse(
        scope,
        context,
        prompt,
        completionText,
        modelConfig
    );
}

// 处理补全失败
function handleCompletionError(error: any, payload: CompletionRequestPayload | null) {
    console.error('补全处理错误:', error);
    const statusManager = CompletionStatusManager.getInstance();
    if (payload) {
        statusManager.completionFailed(error as Error, payload);
    } else {
        console.warn('补全失败但没有payload，跳过状态管理');
    }

    // 清除建议状态
    suggestionStore.clear();
    fireCodeLensChange();

    console.log('补全错误处理完成，返回空结果');
    return [];
}

// 创建内联补全提供器
export function createInlineCompletionProvider(): vscode.InlineCompletionItemProvider {
    const completionState = CompletionState.getInstance();
    const statisticsService = StatisticsService.getInstance();

    // 使用防抖处理补全请求
    const debouncedProvideInlineCompletionItems = debounce(
        async (
            document: vscode.TextDocument,
            position: vscode.Position,
            context: vscode.InlineCompletionContext,
            token: vscode.CancellationToken
        ) => {
            console.log('处理补全请求:', {
                document: document.uri.toString(),
                position: position.line + ':' + position.character,
                triggerKind: context.triggerKind
            });

            const languageId = document.languageId;
            console.log('当前编辑器文件类型:', languageId);

            // 检查是否应该触发补全
            if (!completionState.canTriggerCompletion(document, position)) {
                return [];
            }

            let payload: CompletionRequestPayload | null = null;

            try {
                completionState.setCompleting(true);

                // 使用请求管理器处理连续请求
                const requestManager = CompletionRequestManager.getInstance();
                const { requestId, abortController } = requestManager.startNewRequest(document, position);
                const signal = abortController.signal;

                console.log(`[补全请求] 开始处理请求 #${requestId}`);

                // 更新补全状态
                updateSuggestionState(document.uri.toString(), {
                    ongoing: true,
                    abortController
                });

                // 构建补全上下文
                const contextData = await buildCompletionContext(document, position);

                // 获取文档文本内容（根据是否在函数内部决定范围）
                const { textBefore, textAfter } = getDocumentTextContent(document, position, contextData.currentFunction);

                // 构建CompletionMeta
                const meta = buildCompletionMeta(document, position, contextData);

                // 使用新的结构化提示词构建
                const prompt = buildStructuredPromptFromMeta(
                    meta,
                    textBefore,
                    textAfter,
                    contextData.relatedContext,
                    contextData.similarContext
                );

                // 获取模型配置
                const modelConfig = getModelConfig();

                // 组装 CompletionRequestPayload
                payload = {
                    prompt,
                    modelConfig,
                    meta,
                };

                // 开始续写状态管理
                const statusManager = CompletionStatusManager.getInstance();
                statusManager.startCompletion(payload);

                // 构建统计上报的上下文信息
                const { scope, context: statisticsContext } = buildStatisticsContext(
                    document, position, textBefore, textAfter, contextData
                );

                // 上报续写触发事件（在真正开始续写时）
                await statisticsService.reportCompletionTriggered(
                    scope,
                    statisticsContext,
                    prompt,
                    undefined, // 响应内容稍后填充
                    modelConfig
                );

                // 端到端调用新补全服务
                const client = getSharedCompletionClient();
                console.log("===========payload===============");
                console.log("Payload (formatted):");
                console.log("----------------------------------------");
                console.log(JSON.stringify(payload, null, 2));
                console.log("----------------------------------------");
                let completionResult = await client.getCompletion(payload, signal);

                // 检查请求是否仍然有效
                if (!requestManager.isRequestValid(document.uri.toString(), requestId)) {
                    console.log(`[补全请求] 请求 #${requestId} 已被中止`);
                    return [];
                }

                // 处理补全响应
                const completionText = await processCompletionResponse(completionResult);

                // 再次检查请求是否仍然有效
                if (!requestManager.isRequestValid(document.uri.toString(), requestId)) {
                    console.log(`[补全请求] 请求 #${requestId} 在响应处理后已被中止`);
                    return [];
                }

                // 过滤只包含换行符和空白的内容
                if (isEmptyOrOnlyNewlines(completionText)) {
                    console.log('补全内容为空或仅包含换行符，返回空结果');
                    return handleEmptyCompletion(document, payload);
                }

                if (!completionText || !completionText.trim()) {
                    console.log('补全内容为空，返回空结果');
                    return handleEmptyCompletion(document, payload);
                }

                // 处理补全成功
                await handleCompletionSuccess(completionText, payload, scope, statisticsContext, prompt, modelConfig);

                // 创建补全项
                const { completionItem, completionRange } = createCompletionItem(completionText, position);

                // 只有在有实际内容时才更新补全状态
                updateSuggestionState(document.uri.toString(), {
                    ongoing: false,
                    text: completionText,
                    range: completionRange
                });
                fireCodeLensChange();

                // 完成请求
                requestManager.completeRequest(document.uri.toString());

                console.log(`[补全请求] 请求 #${requestId} 成功完成，返回结果:`, completionText.substring(0, 50) + '...');
                // 返回 InlineCompletionItem 数组
                return [completionItem];
            } catch (error) {
                console.error('补全处理错误:', error);
                return handleCompletionError(error, payload);
            } finally {
                completionState.setCompleting(false);
            }
        },
        DEBOUNCE_DELAY
    );

    return {
        provideInlineCompletionItems: debouncedProvideInlineCompletionItems
    };
}

export async function triggerInlineCompletion() {
    console.log('triggerInlineCompletion 被调用');
    const editor = vscode.window.activeTextEditor;
    if (!editor) {
        console.log('没有活动的编辑器');
        return;
    }
    await vscode.commands.executeCommand('editor.action.inlineSuggest.trigger');
}

export async function acceptSuggestion() {
    console.log("accept suggestion triggered");
    const editor = vscode.window.activeTextEditor;
    if (!editor) {
        return;
    }

    const docUri = editor.document.uri.toString();
    const suggestion = suggestionStore.get(docUri);
    if (!suggestion || !suggestion.text || !suggestion.range) {
        return;
    }

    // 获取原始文本（如果有）
    const originalText = editor.document.getText(suggestion.range);

    await editor.edit(editBuilder => {
        editBuilder.replace(suggestion.range!, suggestion.text as string);
    });

    // 构建更完整的统计上下文
    const document = editor.document;
    const position = editor.selection.active;

    const scope = {
        filePath: document.fileName,
        language: document.languageId,
        functionName: undefined, // 可以从代码图中获取
        className: undefined,
        moduleName: document.fileName.split('/').pop()?.split('.')[0]
    };

    const context = {
        beforeCursor: document.getText(new vscode.Range(new vscode.Position(0, 0), position)),
        afterCursor: document.getText(new vscode.Range(position, new vscode.Position(document.lineCount - 1, document.lineAt(document.lineCount - 1).text.length))),
        relatedCodeSnippets: [],
        similarCodeSnippets: [],
        environments: []
    };

    // 上报接受事件
    const statisticsService = StatisticsService.getInstance();
    await statisticsService.reportCompletionFeedback(
        StatisticsEventType.COMPLETION_ACCEPTED,
        scope,
        context,
        suggestion.text as string,
        originalText
    );

    // 状态管理：用户接受建议
    const statusManager = CompletionStatusManager.getInstance();
    statusManager.userAccepted(suggestion.text as string, originalText);

    suggestionStore.delete(docUri);
    vscode.commands.executeCommand('editor.action.inlineSuggest.hide');
}

export async function rejectSuggestion() {
    console.log("reject suggestion triggered - ESC键被按下");
    const editor = vscode.window.activeTextEditor;
    if (!editor) {
        console.log("没有活动的编辑器，无法拒绝建议");
        return;
    }

    const docUri = editor.document.uri.toString();
    const suggestion = suggestionStore.get(docUri);

    console.log(`检查建议: docUri=${docUri}, suggestion=${suggestion ? '存在' : '不存在'}`);

    // 上报拒绝事件（如果有建议）
    if (suggestion && suggestion.text) {
        // 构建更完整的统计上下文
        const document = editor.document;
        const position = editor.selection.active;

        const scope = {
            filePath: document.fileName,
            language: document.languageId,
            functionName: undefined,
            className: undefined,
            moduleName: document.fileName.split('/').pop()?.split('.')[0]
        };

        const context = {
            beforeCursor: document.getText(new vscode.Range(new vscode.Position(0, 0), position)),
            afterCursor: document.getText(new vscode.Range(position, new vscode.Position(document.lineCount - 1, document.lineAt(document.lineCount - 1).text.length))),
            relatedCodeSnippets: [],
            similarCodeSnippets: [],
            environments: []
        };

        const statisticsService = StatisticsService.getInstance();
        await statisticsService.reportCompletionFeedback(
            StatisticsEventType.COMPLETION_REJECTED,
            scope,
            context,
            suggestion.text as string
        );

        // 状态管理：用户拒绝建议
        const statusManager = CompletionStatusManager.getInstance();
        statusManager.userRejected(suggestion.text as string);
    }

    // 同步清除状态
    suggestionStore.delete(docUri);


    // 微调时序，fireCodeLensChnage 是发送事件，前端需要时间渲染，所以视觉上总是会慢一点
    fireCodeLensChange();
    await new Promise(resolve => setTimeout(resolve, 400));
    await vscode.commands.executeCommand('editor.action.inlineSuggest.hide');
}
