// import * as vscode from 'vscode';
// import { getSharedCompletionClient } from '../../../common/client/llm/clientFactory';
// import { ICodeLensManager } from '../../../common/register/interface';
// import { suggestionStore } from '../../../common/store/store';
// import { StatisticsEventType } from '../../statistics';
// import { StatisticsService } from '../../statistics/StatisticsService';
// import { applyAddedHighlight, applyDeletedHighlight, clearAllDiffHighlights } from '../ui/diffHighlightManager';

// let _codeLensManager: ICodeLensManager | undefined;

// export function initializeModule(manager: ICodeLensManager) {
//     _codeLensManager = manager;
// }

// /**
//  * 触发带有红绿差异高亮的代码补全。
//  * 补全文本会直接插入到编辑器中。
//  */
// export async function triggerHighlightDiffSuggestion() {
//     const editor = vscode.window.activeTextEditor;
//     if (!editor) { return; }

//     // 清除之前可能存在的任何差异高亮和之前的建议状态
//     clearAllDiffHighlights();
//     suggestionStore.delete(editor.document.uri.toString());
//     if (_codeLensManager) {
//         _codeLensManager.fireCodeLensChange();
//     }

//     const document = editor.document;
//     const position = editor.selection.active;
//     const textBefore = document.getText(new vscode.Range(new vscode.Position(0, 0), position));

//     // 确定将被替换的范围 (originalRange)
//     let originalRange: vscode.Range;
//     const lineText = document.lineAt(position.line).text;
//     const rewriteComment = '// INSERT YOUR REWRITE HERE';
//     if (lineText.trim() === rewriteComment && position.character === lineText.length) {
//         originalRange = document.lineAt(position.line).range; // 替换整行
//     } else {
//         originalRange = new vscode.Range(position, position); // 在光标处插入
//     }
//     const originalText = document.getText(originalRange); // 获取将被替换的原始文本

//     // 获取补全建议
//     const client = getSharedCompletionClient();
//     let suggestionText = '';
//     try {
//         const completionResult = await client.getCompletion(textBefore, new AbortController().signal);

//         if (typeof completionResult === 'string') {
//             suggestionText = completionResult;
//         } else if (completionResult instanceof ReadableStream) {
//             const reader = completionResult.getReader();
//             let collectedText = '';
//             const textDecoder = new TextDecoder();
//             while (true) {
//                 const { done, value } = await reader.read();
//                 if (done) { break; }
//                 collectedText += textDecoder.decode(value, { stream: true });
//             }
//             collectedText += textDecoder.decode();
//             suggestionText = collectedText;
//         } else {
//             console.error('Unexpected completion result type:', completionResult);
//             return;
//         }

//     } catch (error) {
//         console.error('获取补全失败:', error);
//         return;
//     }

//     if (!suggestionText.trim()) {
//         console.log('收到空补全建议，不应用差异高亮');
//         return;
//     }

//     // 应用补全文本到编辑器
//     const success = await editor.edit(editBuilder => {
//         editBuilder.replace(originalRange, suggestionText);
//     });

//     if (success) {
//         // 计算新插入文本的范围 (newRange)
//         const newRangeEndLine = originalRange.start.line + suggestionText.split('\n').length - 1;
//         const newRangeEndCharacter = (suggestionText.includes('\n') ? suggestionText.substring(suggestionText.lastIndexOf('\n') + 1).length : originalRange.start.character + suggestionText.length);
//         const newRange = new vscode.Range(originalRange.start, new vscode.Position(newRangeEndLine, newRangeEndCharacter));

//         // 应用高亮
//         if (!originalRange.isEmpty) {
//             applyDeletedHighlight(editor, originalRange);
//         }
//         applyAddedHighlight(editor, newRange);

//         // 存储当前建议信息到 suggestionStore，以便 CodeLens 可以访问
//         suggestionStore.set(document.uri.toString(), {
//             range: newRange,
//             text: suggestionText, // 存入建议文本，CodeLens 可能需要
//             ongoing: false, // 标记为非持续性，因为是手动触发
//             originalText: originalText,
//             originalRange: originalRange,
//             editor: editor
//         });

//         // 触发 CodeLens 刷新
//         if (_codeLensManager) {
//             _codeLensManager.fireCodeLensChange();
//         }

//     } else {
//         console.error('应用补全文本失败');
//     }
// }

// /**
//  * 接受当前的差异补全建议。
//  * 清除高亮，并清除内部存储的状态。
//  */
// export async function acceptHighlightDiffSuggestion() {
//     const editor = vscode.window.activeTextEditor;
//     if (!editor) { return; }

//     const documentUri = editor.document.uri.toString();
//     const suggestion = suggestionStore.get(documentUri);

//     if (suggestion) {
//         // 上报接受事件
//         if (suggestion.text) {
//             const statisticsService = StatisticsService.getInstance();
//             await statisticsService.reportCurrentCompletionFeedback(
//                 StatisticsEventType.COMPLETION_ACCEPTED,
//                 suggestion.text,
//                 suggestion.originalText
//             );
//         }

//         clearAllDiffHighlights();
//         suggestionStore.delete(documentUri);
//         if (_codeLensManager) {
//             _codeLensManager.fireCodeLensChange();
//         }
//     }
// }

// /**
//  * 拒绝当前的差异补全建议。
//  * 撤销已插入的文本，清除高亮，并清除内部存储的状态。
//  */
// export async function rejectHighlightDiffSuggestion() {
//     const editor = vscode.window.activeTextEditor;
//     if (!editor) { return; }

//     const documentUri = editor.document.uri.toString();
//     const suggestion = suggestionStore.get(documentUri);

//     if (suggestion && suggestion.editor && suggestion.originalRange && suggestion.originalText && suggestion.range) {
//         // 上报拒绝事件
//         if (suggestion.text) {
//             const statisticsService = StatisticsService.getInstance();
//             await statisticsService.reportCurrentCompletionFeedback(
//                 StatisticsEventType.COMPLETION_REJECTED,
//                 suggestion.text,
//                 suggestion.originalText
//             );
//         }

//         // 撤销文本更改：将新插入的文本替换回原始文本
//         await suggestion.editor.edit(editBuilder => {
//             editBuilder.replace(suggestion.range!, suggestion.originalText!); // Use ! for non-null assertion
//         });

//         clearAllDiffHighlights();
//         suggestionStore.delete(documentUri);
//         if (_codeLensManager) {
//             _codeLensManager.fireCodeLensChange();
//         }
//     }
// } 