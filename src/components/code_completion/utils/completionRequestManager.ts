import * as vscode from 'vscode';
import { suggestionStore } from '../../../common/store/store';

/**
 * 补全请求管理器
 * 负责管理连续的补全请求，确保只保留最新的请求
 */
export class CompletionRequestManager {
    private static instance: CompletionRequestManager;
    private currentRequestId = 0;
    private pendingRequests = new Map<string, AbortController>();

    private constructor() {}

    public static getInstance(): CompletionRequestManager {
        if (!CompletionRequestManager.instance) {
            CompletionRequestManager.instance = new CompletionRequestManager();
        }
        return CompletionRequestManager.instance;
    }

    /**
     * 开始新的补全请求
     * @param document 文档
     * @param position 位置
     * @returns 请求ID和AbortController
     */
    public startNewRequest(document: vscode.TextDocument, position: vscode.Position): {
        requestId: number;
        abortController: AbortController;
    } {
        const docUri = document.uri.toString();
        const requestId = ++this.currentRequestId;
        
        // 终止之前的请求
        this.cancelPreviousRequests(docUri);
        
        // 创建新的AbortController
        const abortController = new AbortController();
        this.pendingRequests.set(docUri, abortController);
        
        console.log(`[CompletionRequestManager] 开始新请求 #${requestId} for ${docUri}`);
        
        return { requestId, abortController };
    }

    /**
     * 取消指定文档的所有待处理请求
     * @param docUri 文档URI
     */
    public cancelPreviousRequests(docUri: string): void {
        const existingController = this.pendingRequests.get(docUri);
        if (existingController) {
            console.log(`[CompletionRequestManager] 取消之前的请求 for ${docUri}`);
            existingController.abort();
            this.pendingRequests.delete(docUri);
        }

        // 同时取消suggestionStore中的ongoing请求
        const currentSuggestion = suggestionStore.get(docUri);
        if (currentSuggestion?.ongoing && currentSuggestion.abortController) {
            console.log(`[CompletionRequestManager] 取消suggestionStore中的ongoing请求 for ${docUri}`);
            currentSuggestion.abortController.abort();
        }
    }

    /**
     * 完成请求
     * @param docUri 文档URI
     */
    public completeRequest(docUri: string): void {
        this.pendingRequests.delete(docUri);
        console.log(`[CompletionRequestManager] 完成请求 for ${docUri}`);
    }

    /**
     * 检查请求是否仍然有效
     * @param docUri 文档URI
     * @param requestId 请求ID
     * @returns 是否有效
     */
    public isRequestValid(docUri: string, requestId: number): boolean {
        const controller = this.pendingRequests.get(docUri);
        if (!controller) {
            console.log(`[CompletionRequestManager] 请求已不存在 for ${docUri}`);
            return false;
        }
        
        if (controller.signal.aborted) {
            console.log(`[CompletionRequestManager] 请求已被中止 for ${docUri}`);
            return false;
        }
        
        return true;
    }

    /**
     * 获取当前活跃的请求数量
     */
    public getActiveRequestCount(): number {
        return this.pendingRequests.size;
    }

    /**
     * 清理所有请求
     */
    public clearAllRequests(): void {
        console.log(`[CompletionRequestManager] 清理所有请求 (${this.pendingRequests.size} 个)`);
        for (const [docUri, controller] of this.pendingRequests) {
            controller.abort();
        }
        this.pendingRequests.clear();
    }

    /**
     * 检查是否有请求正在进行
     * @param docUri 文档URI
     */
    public hasActiveRequest(docUri: string): boolean {
        return this.pendingRequests.has(docUri);
    }
}

/**
 * 补全请求包装器
 * 用于包装补全函数，自动处理请求的取消和验证
 */
export function withRequestManagement<T extends any[], R>(
    fn: (requestId: number, abortController: AbortController, ...args: T) => Promise<R>
) {
    return async (document: vscode.TextDocument, position: vscode.Position, ...args: T): Promise<R> => {
        const manager = CompletionRequestManager.getInstance();
        const { requestId, abortController } = manager.startNewRequest(document, position);
        
        try {
            const result = await fn(requestId, abortController, ...args);
            
            // 检查请求是否仍然有效
            if (manager.isRequestValid(document.uri.toString(), requestId)) {
                manager.completeRequest(document.uri.toString());
                return result;
            } else {
                throw new Error('Request was cancelled');
            }
        } catch (error) {
            // 如果是中止错误，不记录为错误
            if (error instanceof Error && error.name === 'AbortError') {
                console.log(`[CompletionRequestManager] 请求 #${requestId} 被中止`);
            } else {
                console.error(`[CompletionRequestManager] 请求 #${requestId} 失败:`, error);
            }
            throw error;
        }
    };
} 