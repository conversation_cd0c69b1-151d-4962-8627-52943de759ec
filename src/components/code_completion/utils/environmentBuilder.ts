import * as vscode from 'vscode';
import { EnvironmentInfo } from '../../types/code_completion';
import { IUserInfo } from '../../types/user';
import { getCurrentUserInfo } from '../../user/UserService';

/**
 * 环境信息构建器
 */
export class EnvironmentBuilder {
  private static instance: EnvironmentBuilder;
  private cachedEnvironmentInfo: EnvironmentInfo | null = null;
  private cachedUserInfo: IUserInfo | null = null;

  private constructor() {}

  public static getInstance(): EnvironmentBuilder {
    if (!EnvironmentBuilder.instance) {
      EnvironmentBuilder.instance = new EnvironmentBuilder();
    }
    return EnvironmentBuilder.instance;
  }

  /**
   * 构建环境信息
   */
  public async buildEnvironmentInfo(): Promise<EnvironmentInfo> {
    if (this.cachedEnvironmentInfo) {
      return this.cachedEnvironmentInfo;
    }

    const environmentInfo: EnvironmentInfo = {
      platform: this.getPlatformInfo(),
      deviceInfo: this.getDeviceInfo(),
      osInfo: this.getOSInfo(),
      vscodeVersion: vscode.version,
      extensionVersion: this.getExtensionVersion(),
      workspaceInfo: this.getWorkspaceInfo()
    };

    this.cachedEnvironmentInfo = environmentInfo;
    return environmentInfo;
  }

  /**
   * 构建用户信息
   */
  public async buildUserInfo(): Promise<IUserInfo> {
    if (this.cachedUserInfo) {
      return this.cachedUserInfo;
    }

    try {
      const vscodeUserInfo = await getCurrentUserInfo();
      if (vscodeUserInfo) {
        this.cachedUserInfo = vscodeUserInfo;
        return vscodeUserInfo;
      }
    } catch (error) {
      console.error('构建用户信息失败:', error);
    }

    // 返回默认用户信息
    const defaultUserInfo: IUserInfo = {
      id: 'unknown',
      name: 'Unknown User',
      nickname: 'Unknown',
      department: 'Unknown'
    };

    this.cachedUserInfo = defaultUserInfo;
    return defaultUserInfo;
  }

  /**
   * 获取平台信息
   */
  private getPlatformInfo(): string {
    const platform = process.platform;
    const arch = process.arch;
    return `${platform}-${arch}`;
  }

  /**
   * 获取设备信息
   */
  private getDeviceInfo(): string {
    const platform = process.platform;
    const arch = process.arch;
    const nodeVersion = process.version;
    return `${platform}-${arch}-node-${nodeVersion}`;
  }

  /**
   * 获取操作系统信息
   */
  private getOSInfo(): string {
    const platform = process.platform;
    const release = process.release?.name || 'unknown';
    const version = process.version;
    return `${platform}-${release}-${version}`;
  }

  /**
   * 获取插件版本
   */
  private getExtensionVersion(): string {
    try {
      const extension = vscode.extensions.getExtension('code-partner');
      return extension?.packageJSON?.version || 'unknown';
    } catch (error) {
      return 'unknown';
    }
  }

  /**
   * 获取工作区信息
   */
  private getWorkspaceInfo(): EnvironmentInfo['workspaceInfo'] {
    const workspaceFolders = vscode.workspace.workspaceFolders;
    const workspaceFile = vscode.workspace.workspaceFile;
    
    if (!workspaceFolders) {
      return undefined;
    }

    return {
      name: vscode.workspace.name || 'Unknown',
      uri: workspaceFile?.toString(),
      folders: workspaceFolders.map(folder => folder.uri.fsPath)
    };
  }

  /**
   * 清除缓存
   */
  public clearCache(): void {
    this.cachedEnvironmentInfo = null;
    this.cachedUserInfo = null;
  }

  /**
   * 刷新环境信息
   */
  public async refreshEnvironmentInfo(): Promise<EnvironmentInfo> {
    this.cachedEnvironmentInfo = null;
    return this.buildEnvironmentInfo();
  }

  /**
   * 刷新用户信息
   */
  public async refreshUserInfo(): Promise<IUserInfo> {
    this.cachedUserInfo = null;
    return this.buildUserInfo();
  }
}

/**
 * 便捷函数：构建完整的环境和用户信息
 */
export async function buildEnvironmentAndUserInfo(): Promise<{
  environment: EnvironmentInfo;
  user: IUserInfo;
}> {
  const builder = EnvironmentBuilder.getInstance();
  const [environment, user] = await Promise.all([
    builder.buildEnvironmentInfo(),
    builder.buildUserInfo()
  ]);

  return { environment, user };
} 