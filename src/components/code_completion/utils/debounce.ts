import * as vscode from 'vscode';

// 补全状态管理
export class CompletionState {
    private static instance: CompletionState;
    private constructor() {}

    // 防抖相关变量
    private debounceTimer: NodeJS.Timeout | undefined;
    private lastDebounceTime = 0;
    private lastCancellationTime = 0;

    // 补全状态变量
    private isCompleting = false;
    private lastTriggerTime = 0;
    private lastCompletionPosition: vscode.Position | undefined;
    private lastCompletionResult: vscode.InlineCompletionItem[] | undefined;
    private lastDocumentVersion: number | undefined;

    public static getInstance(): CompletionState {
        if (!CompletionState.instance) {
            CompletionState.instance = new CompletionState();
        }
        return CompletionState.instance;
    }

    // 检查是否可以触发补全
    public canTriggerCompletion(document: vscode.TextDocument, position: vscode.Position): boolean {
        const now = Date.now();
        
        // 检查触发间隔（减少限制，允许更频繁的触发）
        if (now - this.lastTriggerTime < 200) { // 从1000ms减少到200ms
            console.log('触发间隔太短，忽略此次触发');
            return false;
        }

        // 检查位置是否与上次补全位置相同（允许相同位置的重新触发）
        if (this.lastCompletionPosition && 
            this.lastCompletionPosition.line === position.line && 
            this.lastCompletionPosition.character === position.character &&
            now - this.lastTriggerTime < 1000) { // 相同位置1秒内不重复触发
            console.log('位置与上次补全位置相同且时间间隔太短，不触发');
            return false;
        }

        // 检查文档版本
        if (this.lastDocumentVersion !== document.version) {
            console.log('文档版本已变化:', this.lastDocumentVersion, '->', document.version);
            return true;
        }

        console.log('允许触发补全');
        return true;
    }

    // 设置补全状态
    public setCompleting(completing: boolean) {
        this.isCompleting = completing;
    }

    // 更新补全位置和结果
    public updateCompletionState(
        position: vscode.Position,
        result: vscode.InlineCompletionItem[],
        documentVersion: number
    ) {
        this.lastCompletionPosition = position;
        this.lastCompletionResult = result;
        this.lastDocumentVersion = documentVersion;
        this.lastTriggerTime = Date.now();
    }

    // 清除补全状态
    public clearCompletionState() {
        this.lastCompletionPosition = undefined;
        this.lastCompletionResult = undefined;
        this.lastDocumentVersion = undefined;
        this.isCompleting = false;
        console.log('补全状态已清除');
    }

    // 获取上次补全结果
    public getLastCompletionResult() {
        return this.lastCompletionResult;
    }
}

// 防抖函数 - 改进版本，支持取消之前的请求
export function debounce<T extends (...args: any[]) => vscode.ProviderResult<vscode.InlineCompletionItem[] | vscode.InlineCompletionList>>(
    func: T,
    wait: number
): (...args: Parameters<T>) => vscode.ProviderResult<vscode.InlineCompletionItem[] | vscode.InlineCompletionList> {
    let timeout: NodeJS.Timeout | undefined;
    let currentAbortController: AbortController | undefined;
    
    return (...args: Parameters<T>) => {
        return new Promise((resolve, reject) => {
            // 清除之前的定时器
            if (timeout) {
                clearTimeout(timeout);
            }
            
            // 中止之前的请求
            if (currentAbortController) {
                currentAbortController.abort();
            }
            
            // 创建新的AbortController
            currentAbortController = new AbortController();
            
            timeout = setTimeout(async () => {
                try {
                    // 检查是否被中止
                    if (currentAbortController?.signal.aborted) {
                        reject(new Error('Request was aborted'));
                        return;
                    }
                    
                    const result = await func(...args);
                    resolve(result);
                } catch (error) {
                    if (error instanceof Error && error.name === 'AbortError') {
                        reject(new Error('Request was aborted'));
                    } else {
                        reject(error);
                    }
                }
            }, wait);
        });
    };
}

// 防抖相关常量
export const DEBOUNCE_DELAY = 300; // 防抖延迟 300ms
export const RE_TRIGGER_COOLDOWN = 500; // 冷却时间 500ms
export const MIN_TRIGGER_INTERVAL = 1000; // 最小触发间隔 1 秒 