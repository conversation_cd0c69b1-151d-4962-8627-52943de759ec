
import * as vscode from "vscode";

export async function popupCompletionFeedbackInputBox(){
    const options: vscode.InputBoxOptions = {
        prompt: '请提供补全反馈', // 提示信息
        placeHolder: '在这里输入内容', // 占位符
        validateInput: (value) => {
            // 输入验证函数，若输入不符合要求，返回错误信息
            if (value.length < 3) {
                return '输入内容长度不能少于 3 个字符';
            }
            return null;
        }
    };

    // 弹出输入框并获取用户输入
    const input = await vscode.window.showInputBox(options);

    if (!input) {
        // 用户取消输入时，显示消息框
        vscode.window.showInformationMessage('你取消了输入');
        return;
    }   
        // 用户输入不为空时，显示消息框
    vscode.window.showInformationMessage(`你输入的内容是: ${input}`);
    
}