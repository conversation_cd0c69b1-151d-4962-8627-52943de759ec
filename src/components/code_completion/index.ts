// 代码续写模块统一入口
// 整合了原来的 code_completion、suggestion、completion 三个模块

// 核心续写功能
export { acceptSuggestion, createInlineCompletionProvider, rejectSuggestion, triggerInlineCompletion } from './providers/inlineCompletionProvider';

// 状态管理系统
export { CompletionStatus, CompletionStatusBar } from './status/CompletionStatusBar';
export { CompletionStatusManager } from './status/CompletionStatusManager';
export { CompletionEventManager, CompletionEventType } from './status/events/CompletionEventManager';

// 提示词和工具
export { buildPromptFromMeta, enhanceCompletionWithContext, extractCodeFromResp } from './prompt/completionPrompt';
export { buildEnvironmentAndUserInfo, EnvironmentBuilder } from './utils/environmentBuilder';

// UI组件
export { acceptInlineSuggestion, clearButtons, rejectInlineSuggestion, showAcceptRejectButtons } from './ui/acceptRejectButtons';
export { suggestionCodeLenses } from './ui/codeLens';
export { fireCodeLensChange, inlineCompletionCodeLensProvider, registerCodeLensEvents } from './ui/codeLensProvider';

// 工具函数
export { CompletionState, debounce, DEBOUNCE_DELAY } from './utils/debounce';
export { popupCompletionFeedbackInputBox } from './utils/feedback';

