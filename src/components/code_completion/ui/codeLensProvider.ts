import * as vscode from 'vscode';
import { suggestionStore } from '../../../common/store/store';
import { suggestionCodeLenses } from './codeLens';

// 事件触发器
const _codeLensRefreshEmitter = new vscode.EventEmitter<void>();

// 触发 CodeLens 刷新
export function fireCodeLensChange() {
    if (_codeLensRefreshEmitter) {
        _codeLensRefreshEmitter.fire();
    } else {
        console.warn('CodeLens 刷新触发器未初始化');
    }
}

// 检查建议是否有效
function isValidSuggestion(suggestion: any): boolean {
    return suggestion && 
           !suggestion.ongoing && 
           suggestion.text && 
           suggestion.range && 
           suggestion.range.start && 
           suggestion.range.end;
}

// 注册 CodeLens 相关的事件监听器
export function registerCodeLensEvents(): vscode.Disposable[] {
    console.log('注册 CodeLens 事件监听器');
    return [
        // 监听文档变更事件
        vscode.workspace.onDidChangeTextDocument(event => {
            console.log('文档变更事件触发');
            const docUri = event.document.uri.toString();
            const suggestion = suggestionStore.get(docUri);
            
            // 只有在文档内容真正改变且建议有效时才清除
            if (isValidSuggestion(suggestion) && 
                event.contentChanges.some(change => change.text !== '')) {
                console.log('文档内容变更，清除建议');
                suggestionStore.delete(docUri);
                fireCodeLensChange();
            }
        }),

        // 监听活动编辑器变更事件
        vscode.window.onDidChangeActiveTextEditor(() => {
            console.log('活动编辑器变更');
            const currentEditor = vscode.window.activeTextEditor;
            if (currentEditor) {
                const docUri = currentEditor.document.uri.toString();
                const suggestion = suggestionStore.get(docUri);
                if (isValidSuggestion(suggestion)) {
                    console.log('编辑器切换，清除建议');
                    suggestionStore.delete(docUri);
                    fireCodeLensChange();
                }
            }
        }),

        // 监听光标移动事件
        vscode.window.onDidChangeTextEditorSelection((event) => {
            const editor = event.textEditor;
            const docUri = editor.document.uri.toString();
            const suggestion = suggestionStore.get(docUri);
            
            // 如果建议无效，清理状态
            if (!isValidSuggestion(suggestion)) {
                // console.log('建议无效，清理状态');
                suggestionStore.delete(docUri);
                fireCodeLensChange();
                vscode.commands.executeCommand('editor.action.inlineSuggest.hide');
            }
        })
    ];
}

export const inlineCompletionCodeLensProvider = {
    onDidChangeCodeLenses: _codeLensRefreshEmitter.event,
    provideCodeLenses(document: vscode.TextDocument): vscode.CodeLens[] | null {
        return suggestionCodeLenses(document);
    }
} as vscode.CodeLensProvider; 
