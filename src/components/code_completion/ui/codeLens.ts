import * as vscode from 'vscode';
import { suggestionStore } from '../../../common/store/store';
// import { rejectHighlightDiffSuggestion } from '../providers/diffHighlightProvider';

// 检查建议是否有效
function isValidSuggestion(suggestion: any): boolean {
    return suggestion && 
           !suggestion.ongoing && 
           suggestion.text && 
           suggestion.range && 
           suggestion.range.start && 
           suggestion.range.end;
}

// 生成内联补全 CodeLens
export function suggestionCodeLenses(document: vscode.TextDocument): vscode.CodeLens[] {
    // console.log('生成 CodeLens，文档:', document.uri.toString());
    const docUri = document.uri.toString();
    const suggestion = suggestionStore.get(docUri);
    
    // 检查建议是否有效
    if (!isValidSuggestion(suggestion)) {
        // console.log('建议无效，不生成 CodeLens');
        return [];
    }
    
    // console.log('建议有效，生成 CodeLens');
    const line = suggestion!.range!.start.line;
    
    // 创建接受和拒绝按钮
    const acceptButton = new vscode.CodeLens(
        new vscode.Range(line, 0, line, 0),
        {
            title: "✅ 接受建议",
            command: "code-partner.acceptSuggestion",
            tooltip: "接受当前建议"
        }
    );
    
    const rejectButton = new vscode.CodeLens(
        new vscode.Range(line, 0, line, 0),
        {
            title: "❌ 拒绝建议",
            command: "code-partner.rejectSuggestion",
            tooltip: "拒绝当前建议"
        }
    );
    return [acceptButton, rejectButton];
}

// // 生成差异高亮 CodeLens
// export function diffHighlightCodeLenses(document: vscode.TextDocument): vscode.CodeLens[] {
//     const activeSuggestion = suggestionStore.get(document.uri.toString());

//     if (activeSuggestion && !activeSuggestion.ongoing && activeSuggestion.range && activeSuggestion.range.end) {
//         const rangeForButtons = new vscode.Range(activeSuggestion.range.end, activeSuggestion.range.end);

//         const acceptCommand: vscode.Command = {
//             title: '✓ 接受',
//             command: 'code-partner.acceptHighlightDiffSuggestion',
//             arguments: []
//         };

//         const rejectCommand: vscode.Command = {
//             title: '✗ 拒绝',
//             command: 'code-partner.rejectHighlightDiffSuggestion',
//             arguments: []
//         };

//         const acceptCodeLens = new vscode.CodeLens(rangeForButtons, acceptCommand);
//         const rejectCodeLens = new vscode.CodeLens(rangeForButtons, rejectCommand);

//         return [acceptCodeLens, rejectCodeLens];
//     }
//     return [];
// }

// // 差异高亮 CodeLens 事件处理
// export function diffHighlightCodeLensEvents(manager: ICodeLensManager): vscode.Disposable[] {
//     const disposables: vscode.Disposable[] = [];

//     // 当文本变化时，清除CodeLens
//     disposables.push(vscode.workspace.onDidChangeTextDocument(event => {
//         const docUri = event.document.uri.toString();
//         const suggestion = suggestionStore.get(docUri);
//         if (suggestion && !suggestion.ongoing && event.contentChanges.length > 0) {
//             suggestionStore.delete(docUri);
//             manager.fireCodeLensChange();
//         }
//     }));

//     // 当编辑器激活状态改变时，清除CodeLens
//     disposables.push(vscode.window.onDidChangeActiveTextEditor(editor => {
//         suggestionStore.clear();
//         manager.fireCodeLensChange();
//     }));

//     // 当光标移动时，清除CodeLens
//     disposables.push(vscode.window.onDidChangeTextEditorSelection(event => {
//         const docUri = event.textEditor.document.uri.toString();
//         const suggestion = suggestionStore.get(docUri);
//         if (suggestion && !suggestion.ongoing) {
//             const currentPosition = event.selections[0].active;
//             // 如果光标移到建议范围之外，则拒绝建议并清除CodeLens
//             if (suggestion.range && !suggestion.range.contains(currentPosition)) {
//                 rejectHighlightDiffSuggestion();
//                 suggestionStore.delete(docUri);
//                 manager.fireCodeLensChange();
//             }
//         }
//     }));
    
//     return disposables;
// }