import * as vscode from 'vscode';

// 延迟初始化装饰类型
let deletedDecorationType: vscode.TextEditorDecorationType | undefined;
let addedDecorationType: vscode.TextEditorDecorationType | undefined;

function getDeletedDecorationType(): vscode.TextEditorDecorationType {
    if (!deletedDecorationType) {
        deletedDecorationType = vscode.window.createTextEditorDecorationType({
            backgroundColor: 'rgba(255, 0, 0, 0.3)', // 红色背景，带透明度
        });
    }
    return deletedDecorationType;
}

function getAddedDecorationType(): vscode.TextEditorDecorationType {
    if (!addedDecorationType) {
        addedDecorationType = vscode.window.createTextEditorDecorationType({
            backgroundColor: 'rgba(0, 255, 0, 0.3)', // 绿色背景，带透明度
            isWholeLine: true, // 确保整行被绿色背景包裹
        });
    }
    return addedDecorationType;
}

// 用于存储当前活跃的删除装饰，以便在需要时清除
let activeDeletedDecorations: { [uri: string]: vscode.Range[] } = {};
// 用于存储当前活跃的新增装饰
let activeAddedDecorations: { [uri: string]: vscode.Range[] } = {};

/**
 * 将指定范围内的文本高亮显示为"已删除"。
 * 这适用于将被内联补全替换的现有内容。
 * @param editor 当前的文本编辑器。
 * @param range 要高亮显示为已删除的文本范围。
 */
export function applyDeletedHighlight(editor: vscode.TextEditor, range: vscode.Range) {
    const uri = editor.document.uri.toString();
    // 首先清除该编辑器之前的所有删除装饰
    clearDeletedHighlight(editor);

    // 应用新的删除装饰，使用精确范围
    editor.setDecorations(getDeletedDecorationType(), [range]);
    activeDeletedDecorations[uri] = [range];
}

/**
 * 将指定范围内的文本高亮显示为"已新增"。
 * @param editor 当前的文本编辑器。
 * @param range 要高亮显示为已新增的文本范围。
 */
export function applyAddedHighlight(editor: vscode.TextEditor, range: vscode.Range) {
    const uri = editor.document.uri.toString();
    clearAddedHighlight(editor);

    // 计算覆盖新文本所有行的范围，以便 isWholeLine 生效
    const startLine = range.start.line;
    const endLine = range.end.line;
    const wholeLinesRange = new vscode.Range(
        new vscode.Position(startLine, 0),
        editor.document.lineAt(endLine).range.end // End of the last line of the new text
    );

    editor.setDecorations(getAddedDecorationType(), [wholeLinesRange]);
    activeAddedDecorations[uri] = [wholeLinesRange];
}

/**
 * 清除指定编辑器上的"已删除"高亮。
 * @param editor 当前的文本编辑器。
 */
export function clearDeletedHighlight(editor: vscode.TextEditor) {
    if (deletedDecorationType) {
        editor.setDecorations(deletedDecorationType, []);
    }
    delete activeDeletedDecorations[editor.document.uri.toString()];
}

/**
 * 清除指定编辑器上的"已新增"高亮。
 * @param editor 当前的文本编辑器。
 */
export function clearAddedHighlight(editor: vscode.TextEditor) {
    if (addedDecorationType) {
        editor.setDecorations(addedDecorationType, []);
    }
    delete activeAddedDecorations[editor.document.uri.toString()];
}

/**
 * 清除所有可见编辑器上的所有"已删除"和"已新增"高亮。
 */
export function clearAllDiffHighlights() {
    vscode.window.visibleTextEditors.forEach(editor => {
        clearDeletedHighlight(editor);
        clearAddedHighlight(editor);
    });
} 