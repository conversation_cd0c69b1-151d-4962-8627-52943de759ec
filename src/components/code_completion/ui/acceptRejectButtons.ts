import * as vscode from 'vscode';
import { suggestionStore } from '../../../common/store/store';

let activeEditor: vscode.TextEditor | undefined;
let currentDecoration: vscode.TextEditorDecorationType | null = null;

export function showAcceptRejectButtons(range: vscode.Range) {
  if (!vscode.window.activeTextEditor) {return;}
  activeEditor = vscode.window.activeTextEditor;

  // 清理旧的
  clearButtons();

  const decorationType = vscode.window.createTextEditorDecorationType({
    after: {
      contentText: '✅ Accept   ❌ Reject',
      margin: '0 0 0 1em',
      color: 'gray',
      fontWeight: 'bold',
    },
    rangeBehavior: vscode.DecorationRangeBehavior.ClosedClosed,
  });

  activeEditor.setDecorations(decorationType, [range]);
  currentDecoration = decorationType;
}

export function clearButtons() {
  if (currentDecoration && activeEditor) {
    activeEditor.setDecorations(currentDecoration, []);
    currentDecoration.dispose();
    currentDecoration = null;
  }
}

export async function acceptInlineSuggestion(){
  const editor = vscode.window.activeTextEditor;
  if (!editor) {return;}

  const uri = editor.document.uri.toString();
  const suggestion = suggestionStore.get(uri);
  if (!suggestion) {return;}

  await editor.edit(builder => {
    builder.replace(suggestion.range!, suggestion.text as string);
  });

  suggestionStore.delete(uri);
  clearButtons();
}

export async function rejectInlineSuggestion(){
  const editor = vscode.window.activeTextEditor;
  if (!editor) {return;}

  suggestionStore.delete(editor.document.uri.toString());
  clearButtons();
}

export function clearEventsHandler(){
  return [
    vscode.window.onDidChangeTextEditorSelection(() => {
      clearButtons();
    }),

    vscode.workspace.onDidChangeTextDocument(() => {
      clearButtons();
    }),

    vscode.window.onDidChangeActiveTextEditor(() => {
      clearButtons();
    })];

}

