import { ContextItem } from '../../code_context/types';
import { CompletionMeta } from '../../types/code_completion';

/**
 * 模型特殊token配置
 */
export interface ModelTokenConfig {
  startToken: string;
  fillToken: string;
  endToken: string;
}

/**
 * 预定义的模型token配置
 */
export const MODEL_TOKENS = {
  DEEPSEEK_CODER: {
    startToken: '<｜fim▁begin｜>',
    fillToken: '<｜fim▁hole｜>',
    endToken: '<｜fim▁end｜>'
  },
  QWEN_CODER: {
    startToken: '<|fim_prefix|>',
    fillToken: '<|fim_suffix|>',
    endToken: '<|fim_middle|>'
  }
} as const;

/**
 * 提示词构建配置
 */
export interface PromptConfig {
  modelTokens: ModelTokenConfig;
  systemPrompt: string;
  instruction: string;
  systemRules: string[];
  environmentInfo: string;
  userRules: string[];
}

/**
 * 默认配置
 */
export const DEFAULT_PROMPT_CONFIG: PromptConfig = {
  modelTokens: MODEL_TOKENS.DEEPSEEK_CODER,
  systemPrompt: 'You are a code completion assistant.',
  instruction: '请结合上下文在<｜fim▁hole｜>处补全代码',
  systemRules: [
    '请根据上下文信息进行准确的代码补全',
    '保持代码风格与现有代码一致',
    '确保补全的代码语法正确',
    '优先考虑相关上下文中的函数和变量',
    '参考相似上下文中的代码模式'
  ],
  environmentInfo: '',
  userRules: []
};

/**
 * 构建相关上下文部分（调用链信息）
 * @param relatedContext 相关上下文项目
 * @returns 格式化的相关上下文字符串
 */
function buildRelatedContextSection(relatedContext: ContextItem[]): string {
  if (!relatedContext || relatedContext.length === 0) {
    return '';
  }

  const contextContent = relatedContext.map(item => {
    // 优先使用结构化的调用链信息
    if (item.structuredData?.callChain) {
      const { astNode, role, relationship } = item.structuredData.callChain;
      const filePath = astNode.file || '[unknown]';
      const functionName = astNode.name || 'unnamed';
      const functionDef = astNode.text || item.content;

      const roleLabel = role === 'caller' ? '调用当前函数' :
                       role === 'callee' ? '被当前函数调用' : '相关函数';

      return `// ${roleLabel}: ${functionName} (${filePath})
// 关系: ${relationship}
${functionDef}`;
    }

    // 降级到使用 content 字段
    const filePath = item.location?.file || '[unknown]';
    return `// filepath: ${filePath}
${item.content}`;
  }).join('\n\n');

  return `<related_context>
${contextContent}
</related_context>`;
}

/**
 * 构建相似上下文部分
 * @param similarContext 相似上下文项目
 * @returns 格式化的相似上下文字符串
 */
function buildSimilarContextSection(similarContext: ContextItem[]): string {
  if (!similarContext || similarContext.length === 0) {
    return '';
  }

  const contextContent = similarContext.map(item => {
    // 优先使用结构化的代码片段信息
    if (item.structuredData?.codeSnippet) {
      const { filePath, language, startLine, endLine, keywords } = item.structuredData.codeSnippet;
      const keywordInfo = keywords && keywords.length > 0 ? ` (匹配关键词: ${keywords.join(', ')})` : '';

      return `// 相似代码: ${filePath} (${language}, 行${startLine}-${endLine})${keywordInfo}
${item.content}`;
    }

    // 处理用户自定义片段
    if (item.structuredData?.userSnippet) {
      const { name, category, tags } = item.structuredData.userSnippet;
      const tagInfo = tags && tags.length > 0 ? ` [${tags.join(', ')}]` : '';
      const categoryInfo = category ? ` (${category})` : '';

      return `// 用户片段: ${name}${categoryInfo}${tagInfo}
${item.content}`;
    }

    // 降级到使用基本信息
    const filePath = item.location?.file || '[unknown]';
    const sourceLabel = item.source === 'bm25' ? 'BM25搜索' :
                       item.source === 'embedding' ? '向量搜索' :
                       item.source === 'user-snippets' ? '用户片段' : '相似代码';

    return `// ${sourceLabel}: ${filePath}
${item.content}`;
  }).join('\n\n');

  return `<similar_context>
${contextContent}
</similar_context>`;
}

/**
 * 构建系统规则部分
 * @param systemRules 系统规则数组
 * @returns 格式化的系统规则字符串
 */
function buildSystemRulesSection(systemRules: string[]): string {
  if (!systemRules || systemRules.length === 0) {
    return '';
  }

  const rulesContent = systemRules.map((rule, index) => `${index + 1}. ${rule}`).join('\n');
  return `<system_rules>
${rulesContent}
</system_rules>`;
}

/**
 * 构建环境信息部分
 * @param environmentInfo 环境信息
 * @returns 格式化的环境信息字符串
 */
function buildEnvironmentSection(environmentInfo: string): string {
  if (!environmentInfo) {
    return '';
  }

  return `<env>
${environmentInfo}
</env>`;
}

/**
 * 构建用户规则部分
 * @param userRules 用户规则数组
 * @returns 格式化的用户规则字符串
 */
function buildUserRulesSection(userRules: string[]): string {
  if (!userRules || userRules.length === 0) {
    return '';
  }

  const rulesContent = userRules.map((rule, index) => `${index + 1}. ${rule}`).join('\n');
  return `<user_rules>
${rulesContent}
</user_rules>`;
}

/**
 * 构建结构化的代码补全提示词
 * @param config 提示词配置
 * @param textBefore 光标前的代码
 * @param textAfter 光标后的代码
 * @param relatedContext 相关上下文
 * @param similarContext 相似上下文
 * @returns 完整的提示词
 */
export function buildStructuredPrompt(
  config: PromptConfig,
  textBefore: string,
  textAfter: string,
  relatedContext: ContextItem[] = [],
  similarContext: ContextItem[] = []
): string {
  const parts: string[] = [];

  // 1. 系统提示词
  parts.push(config.systemPrompt);
  parts.push('');

  // 2. 指令
  parts.push(config.instruction);
  parts.push('');

  // 3. 系统规则
  const systemRulesSection = buildSystemRulesSection(config.systemRules);
  if (systemRulesSection) {
    parts.push(systemRulesSection);
    parts.push('');
  }

  // 4. 环境信息
  const environmentSection = buildEnvironmentSection(config.environmentInfo);
  if (environmentSection) {
    parts.push(environmentSection);
    parts.push('');
  }

  // 5. 用户规则
  const userRulesSection = buildUserRulesSection(config.userRules);
  if (userRulesSection) {
    parts.push(userRulesSection);
    parts.push('');
  }

  // 6. 相关上下文
  const relatedContextSection = buildRelatedContextSection(relatedContext);
  if (relatedContextSection) {
    parts.push(relatedContextSection);
    parts.push('');
  }

  // 7. 相似上下文
  const similarContextSection = buildSimilarContextSection(similarContext);
  if (similarContextSection) {
    parts.push(similarContextSection);
    parts.push('');
  }

  // 8. 补全代码部分
  parts.push('## 补全如下代码:');
  parts.push('');
  parts.push(config.modelTokens.startToken);
  parts.push(textBefore);
  parts.push(config.modelTokens.fillToken);
  parts.push(textAfter);
  parts.push(config.modelTokens.endToken);

  return parts.join('\n');
}

/**
 * 从 CompletionMeta 构建结构化提示词
 * @param meta 补全元数据
 * @param textBefore 光标前的代码
 * @param textAfter 光标后的代码
 * @param relatedContext 相关上下文
 * @param similarContext 相似上下文
 * @param config 可选的配置覆盖
 * @returns 完整的提示词
 */
export function buildStructuredPromptFromMeta(
  meta: CompletionMeta,
  textBefore: string,
  textAfter: string,
  relatedContext: ContextItem[] = [],
  similarContext: ContextItem[] = [],
  config: Partial<PromptConfig> = {}
): string {
  // 合并配置
  const finalConfig: PromptConfig = {
    ...DEFAULT_PROMPT_CONFIG,
    ...config
  };

  // 根据模型类型调整指令中的token
  if (finalConfig.modelTokens === MODEL_TOKENS.QWEN_CODER) {
    finalConfig.instruction = '请结合上下文在<|fim_suffix|>处补全代码';
  }

  // 构建环境信息
  if (meta.environment) {
    const envParts = [
      `平台: ${meta.environment.platform}`,
      `语言: ${meta.languageId || 'unknown'}`
    ];
    
    if (meta.environment.vscodeVersion) {
      envParts.push(`VSCode版本: ${meta.environment.vscodeVersion}`);
    }
    
    finalConfig.environmentInfo = envParts.join('\n');
  }

  return buildStructuredPrompt(
    finalConfig,
    textBefore,
    textAfter,
    relatedContext,
    similarContext
  );
}

/**
 * 获取模型配置
 * @param modelName 模型名称
 * @returns 模型token配置
 */
export function getModelTokenConfig(modelName: string): ModelTokenConfig {
  const lowerModelName = modelName.toLowerCase();
  
  if (lowerModelName.includes('deepseek')) {
    return MODEL_TOKENS.DEEPSEEK_CODER;
  } else if (lowerModelName.includes('qwen')) {
    return MODEL_TOKENS.QWEN_CODER;
  }
  
  // 默认使用 DeepSeek 格式
  return MODEL_TOKENS.DEEPSEEK_CODER;
}
