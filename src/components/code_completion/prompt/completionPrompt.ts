import * as vscode from 'vscode';
import { getSharedCompletionClient } from '../../../common/client/llm/clientFactory';
import { CompletionMeta } from '../../types/code_completion';
import { GraphCodeNode } from '../../types/code_graph';
import { UserCustomContentManager } from '../../user_snippets/UserCustomContentManager';

const completion_flag = `{{complete_code_here}}`;
const default_context_line_radius = 3;

/**
 * 构建补全提示词的核心函数
 */
export function buildPromptCore(
    textBefore: string,
    textAfter: string,
    language: string,
    context: string = '',
    scopeInfo: string = '',
    userRules: string[] = [],
    relatedContext: string = ''
): string {
    const hints = [
        `${completion_flag}为续写标记，请在标记位置进行代码续写`,
        `当前文件使用的语言为${language}`
    ];
    
    if (scopeInfo) {
        hints.push(scopeInfo);
    }
    
    const hintsSection = hints.map((hint, index) => `${index + 1}.${hint}`).join('\n');
    
    // 构建用户规则部分
    let userRulesSection = '';
    if (userRules.length > 0) {
        userRulesSection = `\n<user-rules>
${userRules.map((rule, index) => `${index + 1}.${rule}`).join('\n')}
</user-rules>`;
    }

    // 构建相关上下文部分
    let relatedContextSection = '';
    if (relatedContext) {
        relatedContextSection = `\n<related_context>
${relatedContext}
</related_context>`;
    }
    
    return `请根据以下信息进行代码续写：

<system_rule>
${hintsSection}
</system_rule>

<context>
${context}
</context>${relatedContextSection}

${textBefore}

${completion_flag}

${textAfter}
${userRulesSection}`;
}

/**
 * 从 CompletionMeta 生成 prompt
 */
export function buildPromptFromMeta(meta: CompletionMeta, textBefore: string, textAfter: string): string {
    const language = meta.languageId || 'unknown';
    
    // 构建上下文字符串
    let contextStr = '';
    if (meta.selectedContext && meta.selectedContext.length > 0) {
        contextStr = meta.selectedContext.map(ctx => {
            return `【${ctx.codeSnippetType}】${ctx.label || ctx.fileName} (行${ctx.startLine}-${ctx.endLine}):\n${ctx.content}`;
        }).join('\n\n');
    }
    
    // 构建作用域信息
    let scopeStr = '';
    if (meta.scope) {
        scopeStr = `当前续写作用域: ${meta.scope.scopeType}${meta.scope.name ? ' ' + meta.scope.name : ''} (行${meta.scope.startLine}-${meta.scope.endLine})`;
    }
    
    return buildPromptCore(textBefore, textAfter, language, contextStr, scopeStr);
}

/**
 * 增强的提示词构建函数，集成用户自定义规则
 * 注意：用户自定义代码片段现在通过 similar_context 统一管理，不再直接添加到提示词
 */
export async function buildEnhancedPrompt(
    textBefore: string,
    textAfter: string,
    language: string,
    context: string = '',
    scopeInfo: string = '',
    relatedContext: string = ''
): Promise<string> {
    const userContentManager = UserCustomContentManager.getInstance();
    
    // 获取适用的用户规则（规则仍然直接添加到提示词中）
    const applicableRules = await userContentManager.getApplicableRules(language);
    const userRules = applicableRules
        .filter(result => result.isApplicable)
        .map(result => result.rule.content);
    
    // 注意：用户自定义代码片段现在通过 similar_context 统一管理
    // 不再直接添加到提示词中，而是通过 ContextCollector 的 collectSimilarContext 方法
    // 在 similar_context 中与其他相似代码片段一起进行权重计算和排序
    
    return buildPromptCore(textBefore, textAfter, language, context, scopeInfo, userRules, relatedContext);
}

/**
 * 使用上下文增强补全
 */
export async function enhanceCompletionWithContext(
    currentFunction: GraphCodeNode|null,
    textBefore: string,
    textAfter: string,
    context: string,
    cursorPosition: vscode.Position,
    language: string,
    signal?: AbortSignal
): Promise<string> {
    const client = getSharedCompletionClient();

    const document = vscode.window.activeTextEditor?.document;
    if (currentFunction) {
        // 如果 currentFunction 不为空，说明在函数内部续写
        // 需要以整个函数为全局，按光标位置切分
        // 获取函数的起止位置
        const funcLoc = currentFunction.location;
        if (funcLoc && funcLoc.start && funcLoc.end) {
            if (document && document.uri.fsPath === currentFunction.file) {
                // 计算函数起止的 vscode.Position
                const funcStart = new vscode.Position(funcLoc.start.line, funcLoc.start.column || 0);
                const funcEnd = new vscode.Position(funcLoc.end.line, funcLoc.end.column || 0);

                // 获取函数体文本
                const funcRange = new vscode.Range(funcStart, funcEnd);
                const funcText = document.getText(funcRange);

                // 计算光标在函数体内的偏移
                const cursorOffset = document.offsetAt(cursorPosition) - document.offsetAt(funcStart);

                // 按光标偏移切分函数体
                const beforeText = funcText.slice(0, cursorOffset);
                const afterText = funcText.slice(cursorOffset);

                textBefore = beforeText;
                textAfter = afterText;
            }
        }
    } else {
        // 如果当前不在函数内，则取以光标为中心，半径 default_context_line_radius 行的上下文
        if (document) {
            const totalLines = document.lineCount;
            const cursorLine = cursorPosition.line;
            const startLine = Math.max(0, cursorLine - default_context_line_radius);
            const endLine = Math.min(totalLines - 1, cursorLine + default_context_line_radius);

            const beforeRange = new vscode.Range(
                new vscode.Position(startLine, 0),
                cursorPosition
            );
            const afterRange = new vscode.Range(
                cursorPosition,
                new vscode.Position(endLine, document.lineAt(endLine).text.length)
            );

            textBefore = document.getText(beforeRange);
            textAfter = document.getText(afterRange);
        }
    }
    
    // 使用增强的提示词构建函数
    const prompt = await buildEnhancedPrompt(textBefore, textAfter, language, context);
    try {
        // 修复：getCompletion 需要传入 CompletionRequestPayload 类型参数
        const completionPayload = {
            prompt,
            modelConfig: {
                max_tokens: 256,
                temperature: 0.2,
                stream: false
            },
            meta: {
                languageId: language,
                selectedContext: [],
                environment: {
                    platform: 'vscode',
                    deviceInfo: 'unknown',
                    osInfo: 'unknown',
                    vscodeVersion: 'unknown',
                    extensionVersion: 'unknown'
                }
            }
        };
        const completion = await client.getCompletion(completionPayload, signal ?? new AbortController().signal);
        return completion instanceof ReadableStream ? '' : completion;
    } catch (error) {
        console.error('获取补全失败:', error);
        return '';
    }
}

/**
 * 从响应中提取出被```包裹的代码块，如果没有则直接返回原始响应
 * @param {string} resp - 补全响应内容
 * @returns {string} - 提取出的代码块或原始响应
 */
export function extractCodeFromResp(resp: string): string {
    if (!resp) {return '';}
    // 匹配第一个被```包裹的代码块（支持可选的语言标记）
    const match = resp.match(/```(?:[\w-]*)\s*([\s\S]*?)```/);
    if (match && match[1] !== undefined) {
        return match[1].trim();
    }
    return resp;
}