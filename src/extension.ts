// The module 'vscode' contains the VS Code extensibility API
// Import the module and reference it with the alias vscode in your code below
import * as vscode from 'vscode';
import { CommandRegister } from './commands/register';
import { IndexingStatus, RepoIndexerManager } from './components/code_graph/repoIndexer/repoIndexerManager';
import { globalUserService } from './components/user/UserService';

import { globalLogger as logger } from './common/log/logger';

export async function activate(context: vscode.ExtensionContext) {
	logger.info("Log in client", { toOutput: true });
	logger.info('🎉 Code Partner 扩展正在激活...', { toOutput: true });
	console.log('Congratulations, your extension "code-partner" is now active!');

	// 注册测试日志命令
	const testLogCommand = vscode.commands.registerCommand('code-partner.testLog', () => {
		logger.info('🧪 测试日志输出功能', { toOutput: true });
		logger.info('这是一条 INFO 级别的日志', { toOutput: true });
		logger.warn('这是一条 WARN 级别的日志', { toOutput: true });
		logger.error('这是一条 ERROR 级别的日志', { toOutput: true });
		vscode.window.showInformationMessage('日志测试完成，请查看输出面板');
	});
	context.subscriptions.push(testLogCommand);

	// 初始化用户系统
	logger.info('开始初始化用户系统...', { toOutput: true });
	console.log('开始初始化用户系统...');
	try {
		await globalUserService.initialize();
		logger.info('用户系统初始化完成', { toOutput: true });
		console.log('用户系统初始化完成');
		const user = await globalUserService.getCurrentUser();
		// 弹出欢迎用户的VSCode信息框
		vscode.window.showInformationMessage(`欢迎 ${user?.info?.nickname} ${user?.info?.name}`);
	} catch (error) {
		logger.error('用户系统初始化失败:', error, { toOutput: true });
		console.error('用户系统初始化失败:', error);
	}

	// 注册索引状态栏
	const indexingStatusBar = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Left, 100);
	indexingStatusBar.text = '$(sync~spin) 正在初始化索引...';
	indexingStatusBar.tooltip = '代码仓库索引状态';
	indexingStatusBar.show();
	context.subscriptions.push(indexingStatusBar);

	const repoIndexerManager = RepoIndexerManager.getInstance();
	repoIndexerManager.onIndexingStatusChange((status: IndexingStatus) => {
		switch (status) {
			case 'indexing':
				indexingStatusBar.text = '$(sync~spin) 正在索引...';
				indexingStatusBar.show();
				break;
			case 'success':
				indexingStatusBar.text = '$(check) 索引完成';
				setTimeout(() => indexingStatusBar.hide(), 3000);
				break;
			case 'error':
				indexingStatusBar.text = '$(error) 索引失败';
				indexingStatusBar.show();
				break;
			case 'idle':
				indexingStatusBar.hide();
				break;
		}
	});

	// 初始化RepoIndexerManager，启动自动索引
	logger.info('开始初始化RepoIndexerManager...', { toOutput: true });
	console.log('开始初始化RepoIndexerManager...');
	try {
		await repoIndexerManager.initialize(context, true); // 启用自动初始化索引
		logger.info('RepoIndexerManager初始化完成', { toOutput: true });
		console.log('RepoIndexerManager初始化完成');
	} catch (error) {
		logger.error('RepoIndexerManager初始化失败:', error, { toOutput: true });
		console.error('RepoIndexerManager初始化失败:', error);
	}

	// 注册所有命令和提供者
	logger.info('开始创建CommandRegister...', { toOutput: true });
	console.log('开始创建CommandRegister...');
	try {
		const commandRegister = new CommandRegister(context);
		logger.info('CommandRegister创建成功，开始注册命令...', { toOutput: true });
		console.log('CommandRegister创建成功，开始注册命令...');
		commandRegister.register(context);
		logger.info('CommandRegister注册完成', { toOutput: true });
		console.log('CommandRegister注册完成');
	} catch (error) {
		logger.error('CommandRegister创建或注册失败:', error, { toOutput: true });
		console.error('CommandRegister创建或注册失败:', error);
	}
	logger.info('✅ Extension activate完成', { toOutput: true });
	console.log('Extension activate完成');
}

// This method is called when your extension is deactivated
export function deactivate() { }
