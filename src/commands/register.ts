import * as vscode from 'vscode';
import { Logger } from '../common/log/logger';
import { IRegister } from '../common/register/interface';
import { suggestionStore } from '../common/store/store';
import { acceptSuggestion, createInlineCompletionProvider, registerInlineCompletionCallbacks, rejectSuggestion, triggerInlineCompletion } from '../components/code_completion/providers/inlineCompletionProvider';
import { inlineCompletionCodeLensProvider, registerCodeLensEvents } from '../components/code_completion/ui/codeLensProvider';
import { popupCompletionFeedbackInputBox } from '../components/code_completion/utils/feedback';
import { debugRepoIndexerStatus } from '../components/code_context/collect';
import { CodeGraphCommands } from '../components/code_graph/commands';
import { DataExtractor } from '../components/data_extraction/DataExtractor';
import { getVSCodeUserInfo } from '../components/user/UserService';
import { exportUserCustomConfig, importUserCustomConfig, quickAddRule, quickAddSnippet, showUserCustomContentPanel } from '../components/user_snippets';
import { configureCompletionParams, configureFusionWeights, configureLLMService, resetConfiguration, showConfig, validateConfiguration } from './config';

const logger = new Logger('CommandRegister');

/**
 * 命令注册器类
 * 负责注册所有命令、代码镜头和事件监听器
 */
export class CommandRegister implements IRegister {
    private codeLensRefreshEmitter = new vscode.EventEmitter<void>();
    private codeGraphCommands: CodeGraphCommands;
    private dataExtractor: DataExtractor;
    private userService: any; // UserService实例

    constructor(context: vscode.ExtensionContext) {
        this.codeGraphCommands = new CodeGraphCommands(context);
        this.dataExtractor = new DataExtractor();
        this.userService = getVSCodeUserInfo();
    }

    /**
     * 注册所有命令和提供器
     */
    register(context: vscode.ExtensionContext): void {
        logger.info('开始注册命令...');

        try {
            // 注册全局事件监听器
            logger.info('注册全局事件监听器...');
            this.registerGlobalListeners(context);

            // 按功能模块注册命令
            logger.info('注册基础命令...');
            this.registerBasicCommands(context);

            logger.info('注册代码补全相关命令...');
            this.registerSuggestionCommands(context);

            logger.info('注册代码图谱相关命令...');
            this.registerCodeGraphCommands(context);

            logger.info('注册数据提取相关命令...');
            this.registerDataExtractionCommands(context);

            logger.info('注册用户相关命令...');
            this.registerUserCommands(context);

            logger.info('注册配置管理命令...');
            this.registerConfigCommands(context);

            logger.info('注册用户自定义内容命令...');
            this.registerUserCustomContentCommands(context);

            logger.info('命令注册完成');
            logger.info('所有命令注册完成 - CommandRegister.register()');
        } catch (error) {
            logger.error('命令注册失败', error as Error);
            throw error;
        }
    }

    /**
     * 注册基础测试命令
     */
    private registerBasicCommands(context: vscode.ExtensionContext): void {
        context.subscriptions.push(
            vscode.commands.registerCommand('code-partner.helloWorld', () => {
                try {
                    vscode.window.showInformationMessage('Hello from Code Partner!');
                } catch (error) {
                    logger.error('Hello World命令执行失败', error as Error);
                }
            }),
            vscode.commands.registerCommand('code-partner.test', () => {
                try {
                    vscode.window.showInformationMessage('Code Partner test command executed!');
                } catch (error) {
                    logger.error('测试命令执行失败', error as Error);
                }
            })
        );
    }

    /**
     * 注册代码补全相关命令
     */
    private registerSuggestionCommands(context: vscode.ExtensionContext): void {
        context.subscriptions.push(
            vscode.commands.registerCommand('code-partner.acceptSuggestion', this.wrapWithErrorHandler(acceptSuggestion, '接受建议')),
            vscode.commands.registerCommand('code-partner.rejectSuggestion', this.wrapWithErrorHandler(rejectSuggestion, '拒绝建议')),
            vscode.commands.registerCommand('code-partner.triggerInlineCompletion', this.wrapWithErrorHandler(triggerInlineCompletion, '触发行内补全')),
            vscode.commands.registerCommand('code-partner.completionFeedback', this.wrapWithErrorHandler(popupCompletionFeedbackInputBox, '补全反馈')),

            // 注册事件处理器
            ...registerCodeLensEvents(),

            // 注册行内补全提供器
            vscode.languages.registerInlineCompletionItemProvider(
                { pattern: '**' },
                createInlineCompletionProvider()
            ),

            // 注册 CodeLens 提供器
            vscode.languages.registerCodeLensProvider(
                { pattern: '**' },
                inlineCompletionCodeLensProvider
            )
        );

        // 注册内联补全回调
        registerInlineCompletionCallbacks();
    }

    /**
     * 注册代码图谱相关命令
     */
    private registerCodeGraphCommands(context: vscode.ExtensionContext): void {
        logger.info('开始注册代码图谱相关命令...');

        context.subscriptions.push(
            vscode.commands.registerCommand('code-partner.reinitializeWorkspaceIndex', this.wrapWithErrorHandler(async () => {
                logger.info('执行命令: code-partner.reinitializeWorkspaceIndex');
                await this.codeGraphCommands.initializeWorkspaceIndex();
                logger.info('工作区索引初始化完成');
                vscode.window.showInformationMessage('工作区索引初始化完成');
            }, '重新初始化工作区索引')),

            vscode.commands.registerCommand('code-partner.resetIndex', this.wrapWithErrorHandler(async () => {
                logger.info('执行命令: code-partner.resetIndex');
                await this.codeGraphCommands.resetIndex();
                logger.info('索引重置完成');
                vscode.window.showInformationMessage('索引重置完成');
            }, '重置索引')),

            vscode.commands.registerCommand('code-partner.loadRepoIndex', this.wrapWithErrorHandler(async () => {
                logger.info('执行命令: code-partner.loadRepoIndex');
                await this.codeGraphCommands.initializeRepoIndex();
                logger.info('代码仓索引加载完成');
                vscode.window.showInformationMessage('代码仓索引加载完成');
            }, '加载代码仓索引')),

            vscode.commands.registerCommand('code-partner.cleanIndexData', this.wrapWithErrorHandler(async () => {
                logger.info('执行命令: code-partner.cleanIndexData');
                await this.codeGraphCommands.cleanIndexData();
                logger.info('索引数据清理完成');
                vscode.window.showInformationMessage('索引数据清理完成');
            }, '清理索引数据')),

            vscode.commands.registerCommand('code-partner.queryRelatedFunctions', this.wrapWithErrorHandler(() => {
                logger.info('执行命令: code-partner.queryRelatedFunctions');
                this.codeGraphCommands.queryRelatedFunctions();
            }, '查询相关函数')),

            vscode.commands.registerCommand('code-partner.updateRepoIndex', this.wrapWithErrorHandler(() => {
                logger.info('执行命令: code-partner.updateRepoIndex');
                this.codeGraphCommands.updateRepoIndex();
            }, '更新仓库索引')),

            vscode.commands.registerCommand('code-partner.showRepoIndex', this.wrapWithErrorHandler(() => {
                logger.info('执行命令: code-partner.showRepoIndex');
                this.codeGraphCommands.showRepoIndex();
            }, '显示仓库索引')),

            vscode.commands.registerCommand('code-partner.testWorkerDemo', this.wrapWithErrorHandler(async () => {
                logger.info('执行命令: code-partner.testWorkerDemo');
                await this.codeGraphCommands.testWorkerDemo();
            }, '测试 Worker Demo')),

            vscode.commands.registerCommand('code-partner.testWorkerPoolDemo', this.wrapWithErrorHandler(async () => {
                logger.info('执行命令: code-partner.testWorkerPoolDemo');
                await this.codeGraphCommands.testWorkerPoolDemo();
            }, '测试 Worker Pool Demo'))
        );

        logger.info('代码图谱相关命令注册完成');
    }

    /**
     * 注册数据提取相关命令
     */
    private registerDataExtractionCommands(context: vscode.ExtensionContext): void {
        context.subscriptions.push(
            vscode.commands.registerCommand(
                'code-partner.extractSFTData',
                this.wrapWithErrorHandler(async () => {
                    // 获取当前工作区路径
                    const workspaceFolders = vscode.workspace.workspaceFolders;
                    if (!workspaceFolders || workspaceFolders.length === 0) {
                        throw new Error('没有打开的工作区');
                    }
                    const workspacePath = workspaceFolders[0].uri.fsPath;

                    // 使用新的方法
                    await this.dataExtractor.extractFromWorkspace(workspacePath);
                    vscode.window.showInformationMessage('数据提取完成');
                }, '提取SFT数据')
            ),
            vscode.commands.registerCommand(
                'code-partner.runDataExtractionTests',
                this.wrapWithErrorHandler(async () => {
                    // 获取当前工作区路径
                    const workspaceFolders = vscode.workspace.workspaceFolders;
                    if (!workspaceFolders || workspaceFolders.length === 0) {
                        throw new Error('没有打开的工作区');
                    }
                    const workspacePath = workspaceFolders[0].uri.fsPath;

                    // 使用新的方法
                    await this.dataExtractor.extractFromWorkspace(workspacePath);
                    vscode.window.showInformationMessage('数据提取测试完成');
                }, '运行数据提取测试')
            ),
            vscode.commands.registerCommand(
                'code-partner.runProjectTests',
                this.wrapWithErrorHandler(async () => {
                    // 获取当前工作区路径
                    const workspaceFolders = vscode.workspace.workspaceFolders;
                    if (!workspaceFolders || workspaceFolders.length === 0) {
                        throw new Error('没有打开的工作区');
                    }
                    const workspacePath = workspaceFolders[0].uri.fsPath;

                    // 使用新的方法
                    await this.dataExtractor.extractFromWorkspace(workspacePath);
                    vscode.window.showInformationMessage('项目测试完成');
                }, '运行项目测试')
            )
        );
    }

    /**
     * 注册用户相关命令
     */
    private registerUserCommands(context: vscode.ExtensionContext): void {
        context.subscriptions.push(
            vscode.commands.registerCommand('code-partner.showUserInfo', this.wrapWithErrorHandler(async () => {
                await this.userService.showUserInfo();
            }, '显示用户信息')),

            vscode.commands.registerCommand('code-partner.debugUserInfo', this.wrapWithErrorHandler(async () => {
                await this.userService.debugUserInfo();
            }, '调试用户信息')),

            vscode.commands.registerCommand('code-partner.debugRepoIndexerStatus', this.wrapWithErrorHandler(async () => {
                await debugRepoIndexerStatus();
            }, '调试仓库索引状态'))
        );
    }

    /**
     * 注册配置管理命令
     */
    private registerConfigCommands(context: vscode.ExtensionContext): void {
        context.subscriptions.push(
            vscode.commands.registerCommand('code-partner.showConfiguration', this.wrapWithErrorHandler(showConfig, '显示配置')),
            vscode.commands.registerCommand('code-partner.validateConfiguration', this.wrapWithErrorHandler(validateConfiguration, '验证配置')),
            vscode.commands.registerCommand('code-partner.resetConfiguration', this.wrapWithErrorHandler(resetConfiguration, '重置配置')),
            vscode.commands.registerCommand('code-partner.configureLLMService', this.wrapWithErrorHandler(configureLLMService, '配置LLM服务')),
            vscode.commands.registerCommand('code-partner.configureCompletionParams', this.wrapWithErrorHandler(configureCompletionParams, '配置补全参数')),
            vscode.commands.registerCommand('code-partner.configureFusionWeights', this.wrapWithErrorHandler(configureFusionWeights, '配置融合权重')),
            vscode.commands.registerCommand('code-partner.openSettings', this.wrapWithErrorHandler(vscode.commands.executeCommand, '打开设置'))
        );
    }

    /**
     * 注册用户自定义内容命令
     */
    private registerUserCustomContentCommands(context: vscode.ExtensionContext): void {
        context.subscriptions.push(
            vscode.commands.registerCommand('code-partner.showUserCustomContentPanel', this.wrapWithErrorHandler(() => showUserCustomContentPanel(context), '显示用户自定义内容管理')),
            vscode.commands.registerCommand('code-partner.quickAddSnippet', this.wrapWithErrorHandler(quickAddSnippet, '快速添加代码片段')),
            vscode.commands.registerCommand('code-partner.quickAddRule', this.wrapWithErrorHandler(quickAddRule, '快速添加规则')),
            vscode.commands.registerCommand('code-partner.exportUserCustomConfig', this.wrapWithErrorHandler(exportUserCustomConfig, '导出用户自定义配置')),
            vscode.commands.registerCommand('code-partner.importUserCustomConfig', this.wrapWithErrorHandler(importUserCustomConfig, '导入用户自定义配置'))
        );
    }

    /**
     * 注册全局事件监听器
     */
    private registerGlobalListeners(context: vscode.ExtensionContext): void {
        // 监听文档变化，触发建议存储更新
        context.subscriptions.push(
            vscode.workspace.onDidChangeTextDocument((event) => {
                if (event.document === vscode.window.activeTextEditor?.document) {
                    suggestionStore.clear();
                }
            })
        );

        // 监听活动编辑器变化，触发建议存储清理
        context.subscriptions.push(
            vscode.window.onDidChangeActiveTextEditor(() => {
                suggestionStore.clear();
            })
        );

    }

    /**
     * 获取代码镜头变更事件
     */
    public onCodeLensChange(listener: () => any): vscode.Disposable {
        return this.codeLensRefreshEmitter.event(listener);
    }

    /**
     * 包装错误处理
     */
    private wrapWithErrorHandler<T extends (...args: any[]) => any>(
        fn: T,
        commandName: string
    ): (...args: Parameters<T>) => Promise<ReturnType<T>> {
        return async (...args: Parameters<T>): Promise<ReturnType<T>> => {
            try {
                return await fn(...args);
            } catch (error) {
                logger.error(`${commandName}命令执行失败`, error as Error);
                vscode.window.showErrorMessage(`${commandName}失败: ${error}`);
                throw error;
            }
        };
    }
}