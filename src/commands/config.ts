import * as vscode from 'vscode';
import { ConfigurationManager } from '../common/config/ConfigurationManager';
import { Logger } from '../common/log/logger';

const logger = new Logger('ConfigCommands');

export async function showConfig() {
    try {
    const configManager = ConfigurationManager.getInstance();
    const config = configManager.getConfiguration();
      
    // 使用Logger输出到outputChannel
    logger.info('Code Partner 详细配置:', { toOutput: true });
    logger.info('', { toOutput: true }); // 空行作为分隔符
    logger.info(JSON.stringify(config, null, 2), { toOutput: true });

    vscode.window.showInformationMessage('配置信息已显示在输出面板中');
    } catch (error) {
    logger.error('显示配置失败:', error as Error);
    vscode.window.showErrorMessage('显示配置失败');
    }
  }

  /**
   * 验证配置有效性
   */
export async function validateConfiguration(): Promise<void> {
    try {
      const validation = ConfigurationManager.getInstance().validateConfiguration();
      
      if (validation.isValid) {
        vscode.window.showInformationMessage('✅ 配置验证通过');
      } else {
        const errorMessage = '❌ 配置验证失败:\n' + validation.errors.join('\n');
        vscode.window.showErrorMessage(errorMessage);
      }
    } catch (error) {
    logger.error('验证配置失败', error as Error);
      vscode.window.showErrorMessage('验证配置失败: ' + error);
    }
  }

  /**
   * 重置配置到默认值
   */
export async function resetConfiguration(): Promise<void> {
    try {
      const result = await vscode.window.showWarningMessage(
        '确定要重置所有配置到默认值吗？此操作不可撤销。',
        '确定重置',
        '取消'
      );
      
      if (result === '确定重置') {
        await ConfigurationManager.getInstance().resetToDefaults();
        vscode.window.showInformationMessage('✅ 配置已重置到默认值');
      }
    } catch (error) {
    logger.error('重置配置失败', error as Error);
      vscode.window.showErrorMessage('重置配置失败: ' + error);
    }
  }

  /**
   * 快速配置LLM服务
   */
export async function configureLLMService(): Promise<void> {
    try {
      const serviceType = await vscode.window.showQuickPick(
        ['OpenAI', 'Python服务'],
        {
          placeHolder: '选择LLM服务类型'
        }
      );

    if (!serviceType) { return; }

      if (serviceType === 'OpenAI') {
        const apiKey = await vscode.window.showInputBox({
          prompt: '请输入OpenAI API密钥',
          password: true,
          placeHolder: 'sk-...'
        });

        if (apiKey) {
        await ConfigurationManager.getInstance().update('usePythonService', false);
        await ConfigurationManager.getInstance().update('openAIApiKey', apiKey);
          vscode.window.showInformationMessage('✅ OpenAI服务配置完成');
        }
      } else {
        const endpoint = await vscode.window.showInputBox({
          prompt: '请输入Python服务端点',
          placeHolder: 'http://127.0.0.1:5555/',
          value: 'http://127.0.0.1:5555/'
        });

        if (endpoint) {
        await ConfigurationManager.getInstance().update('usePythonService', true);
        await ConfigurationManager.getInstance().update('pythonEndpoint', endpoint);
          vscode.window.showInformationMessage('✅ Python服务配置完成');
        }
      }
    } catch (error) {
    logger.error('配置LLM服务失败', error as Error);
      vscode.window.showErrorMessage('配置LLM服务失败: ' + error);
    }
  }

  /**
   * 配置代码补全参数
   */
export async function configureCompletionParams(): Promise<void> {
    try {
      const maxTokens = await vscode.window.showInputBox({
        prompt: '请输入最大token数量',
        placeHolder: '256',
      value: ConfigurationManager.getInstance().get('maxTokens').toString(),
        validateInput: (value) => {
          const num = parseInt(value);
          if (isNaN(num) || num < 1 || num > 4000) {
            return '请输入1-4000之间的数字';
          }
          return null;
        }
      });

      if (maxTokens) {
      await ConfigurationManager.getInstance().update('maxTokens', parseInt(maxTokens));
      }

      const temperature = await vscode.window.showInputBox({
        prompt: '请输入创造性程度 (0-2)',
        placeHolder: '0.2',
      value: ConfigurationManager.getInstance().get('temperature').toString(),
        validateInput: (value) => {
          const num = parseFloat(value);
          if (isNaN(num) || num < 0 || num > 2) {
            return '请输入0-2之间的数字';
          }
          return null;
        }
      });

      if (temperature) {
      await ConfigurationManager.getInstance().update('temperature', parseFloat(temperature));
      }

      vscode.window.showInformationMessage('✅ 代码补全参数配置完成');
    } catch (error) {
    logger.error('配置代码补全参数失败', error as Error);
      vscode.window.showErrorMessage('配置代码补全参数失败: ' + error);
    }
  }

  /**
   * 配置上下文融合权重
   */
export async function configureFusionWeights(): Promise<void> {
    try {
      const graphWeight = await vscode.window.showInputBox({
        prompt: '请输入图谱检索权重 (0-1)',
        placeHolder: '0.4',
      value: ConfigurationManager.getInstance().get('graphWeight').toString(),
        validateInput: (value) => {
          const num = parseFloat(value);
          if (isNaN(num) || num < 0 || num > 1) {
            return '请输入0-1之间的数字';
          }
          return null;
        }
      });

      if (graphWeight) {
      await ConfigurationManager.getInstance().update('graphWeight', parseFloat(graphWeight));
      }

      const bm25Weight = await vscode.window.showInputBox({
        prompt: '请输入BM25检索权重 (0-1)',
        placeHolder: '0.3',
      value: ConfigurationManager.getInstance().get('bm25Weight').toString(),
        validateInput: (value) => {
          const num = parseFloat(value);
          if (isNaN(num) || num < 0 || num > 1) {
            return '请输入0-1之间的数字';
          }
          return null;
        }
      });

      if (bm25Weight) {
      await ConfigurationManager.getInstance().update('bm25Weight', parseFloat(bm25Weight));
      }

      const embeddingWeight = await vscode.window.showInputBox({
        prompt: '请输入向量检索权重 (0-1)',
        placeHolder: '0.3',
      value: ConfigurationManager.getInstance().get('embeddingWeight').toString(),
        validateInput: (value) => {
          const num = parseFloat(value);
          if (isNaN(num) || num < 0 || num > 1) {
            return '请输入0-1之间的数字';
          }
          return null;
        }
      });

      if (embeddingWeight) {
      await ConfigurationManager.getInstance().update('embeddingWeight', parseFloat(embeddingWeight));
      }

        vscode.window.showInformationMessage('✅ 上下文融合权重配置完成');
    } catch (error) {
    logger.error('配置上下文融合权重失败', error as Error);
    vscode.window.showErrorMessage('配置上下文融合权重失败: ' + error);
    }
  }

  /**
   * 打开设置页面
   */
export async function openSettings(): Promise<void> {
    try {
    await vscode.commands.executeCommand('workbench.action.openSettings', 'code-partner');
    } catch (error) {
    logger.error('打开设置失败', error as Error);
    vscode.window.showErrorMessage('打开设置失败: ' + error);
  }
} 