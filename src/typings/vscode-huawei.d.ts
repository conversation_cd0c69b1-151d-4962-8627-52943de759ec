declare module 'vscode' {
	export interface TreeItem {
		/**
		 * Describes the checked state of the checkbox in tree item.
		 */
		checkedState?: TreeItemCheckedState;

		/**
		 * Describes whether to hide the checkbox in tree item.
		 */
		hideCheckBox?: boolean;

		/**
		 * The [command](#Command) that should be executed when the tree item is double clicked.
		 */
		doubleClickCommand?: Command;

		/**
		 * Describes the font color of the label in tree item.
		 */
		labelColor?: string;
	}

	/**
	 * Describes the checked state of the checkbox in tree item.
	 */
	export enum TreeItemCheckedState {
		Unchecked = 0,
		Indeterminate = 1,
		Checked = 2
	}

	export interface TreeView<T> extends Disposable {
		/**
		 * Get all the checked children items for element or root when element is undefined.
		 *
		 * @param element The parent element which to get the checked children items.
		 */
		getCheckedItems(element?: T): Thenable<TreeItem[]>;

		/**
		 * Set the checked state of checkbox in tree items.
		 *
		 * @param elements The elements which to set the checked state.
		 * @param recursive Represents whether to set the children of elements.
		 * @param value The checked state value to set.
		 */
		setCheckedItems(elements: T[], recursive: boolean, value?: TreeItemCheckedState): Thenable<void>;

		/**
		 * Collapse all tree items.
		 */
		collapseAllItems(): Promise<void>;

		/**
		 * Expand all tree items.
		 */
		expandAllItems(): Promise<void>; // added by nextcode

		/**
		 * An event that is emitted when the checkbox in tree item checked.
		 */
		readonly onDidCheckItems: Event<any>;

		/**
		 * An event that is emitted when the checkbox in tree item unchecked.
		 */
		readonly onDidUnCheckItems: Event<any>;

		/**
		 * An event that is emitted when the filter input content is changed.
		 */
		readonly onDidFilterInputChange: Event<any>;

		/**
		 * Start the progress bar for filter tree view.
		 */
		progressBarStart(): void;

		/**
		 * Stop the progress bar for filter tree view.
		 */
		progressBarStop(): void;

		/**
		 * Focus the filter input of tree view.
		 */
		focusFilterInput(): void;

		/**
		 * Hide the filter input of tree view.
		 */
		hideFilterInput(): void;

		/**
		 * Show the filter input of tree view.
		 */
		showFilterInput(placeholder?: string): void;

		/**
		 * Clear the filter input of tree view.
		 */
		clearFilterInput(): void;
	}

	export namespace workspace {
		/**
		 * An event that is emitted when the login status changed.
		 */
		export const onDidChangeLoginStatus: Event<boolean>;

		/**
		 * An event that is emitted when the iSource access token changed.
		 */
		export const onDidChangeISourceAccessToken: Event<string>;

		/**
		 * An event that is emitted when the gitLab green access token changed.
		 */
		export const onDidChangeGitLabGreenAccessToken: Event<string>;

		/**
		 * An event that is emitted when the gitLab yellow access token changed.
		 */
		export const onDidChangeGitLabYellowAccessToken: Event<string>;

		/**
		 * An event that is emitted when the codeHub green access token changed.
		 */
		export const onDidChangeCodeHubGreenAccessToken: Event<string>;

		/**
		 * An event that is emitted when the codeHub yellow access token changed.
		 */
		export const onDidChangeCodeHubYellowAccessToken: Event<string>;

		/**
		 * An event that is emitted when the statistics point is triggered.
		 */
		export const onDidTriggerStatistics: Event<{ key: string, value: any }>;
	}

	export namespace window {
		/**
		 * Creates a new [output channel](#OutputChannel) with the given name and log type.
		 *
		 * @param name Human-readable string which will be used to represent the channel in the UI.
		 * @param log Whether the output is a log type
		 */
		export function createOutputChannel(name: string, log?: boolean): OutputChannel;

		/**
		 * An [event](#Event) which fires when click the text editor decoration.
		 */
		export const onDidClickTextEditorIconDecoration: Event<TextEditorIconDecorationClickEvent>;

		/**
		 * An [event](#Event) which fires when the text editor is double clicked.
		 */
		export const onDidDoubleClickTextEditor: Event<TextEditorDoubleClickEvent>;
	}

	/**
	 * Describes where to create the webview.
	 */
	export enum WebviewInsideType {
		/**
		 * Webview inside editor.
		 */
		EDITOR = 1,

		/**
		 * Webview inside panel.
		 */
		PANEL = 2,

		/**
		 * Webview inside modal.
		 */
		MODAL = 3
	}

	/**
	 * Describes where the webview modal is displayed.
	 */
	export enum WebviewModalPosition {
		TOP_LEFT = 1,
		TOP_CENTER = 2,
		TOP_RIGHT = 3,
		MIDDLE_LEFT = 4,
		MIDDLE_CENTER = 5,
		MIDDLE_RIGHT = 6,
		BOTTOM_LEFT = 7,
		BOTTOM_CENTER = 8,
		BOTTOM_RIGHT = 9
	}

	/**
	 * Describes the display options of webview modal.
	 */
	export interface WebviewModalOptions {
		/**
		 * Width of the webview modal.
		 */
		width?: number;

		/**
		 * Height of the webview modal.
		 */
		height?: number;

		/**
		 * Display position of the webview modal.
		 */
		position?: WebviewModalPosition;

		/**
		 * Content of the title added when the 'title' attribute is not undefined.
		 */
		title?: string;

		/**
		 * Indicates whether to hide the backdrop of the webview modal.
		 */
		hideBackdrop?: boolean;
	}

	/**
	 * Describes the display options of webview inside editor.
	 */
	export interface WebviewEditorOptions {
		/**
		 * The height by number of editor lines of the webview.
		 */
		heightInLines?: number;

		/**
		 * Controls if the webview is kept in the created text model.
		 */
		retainInTextModel?: boolean;
	}

	/**
	 * Content settings for a webview.
	 */
	export interface WebviewOptions {
		/**
		 * Where to create the webview.
		 */
		insideType?: WebviewInsideType;

		/**
		 * @deprecated
		 * The height by number of editor lines of the webview (only for webview inside editor).
		 */
		heightInLines?: number;

		/**
		 * The display options of the webview inside editor.
		 */
		editorOptions?: WebviewEditorOptions;

		/**
		 * The display options of the webview modal (only for webview inside modal).
		 */
		modalOptions?: WebviewModalOptions;
	}

	/**
	 * A panel that contains a webview.
	 */
	export interface WebviewPanel {
		/**
		 * Show the loading layer of webview panel.
		 *
		 * @param message The message displayed at the loading layer.
		 */
		showLoading(message?: string): void;

		/**
		 * Hide the loading layer of webview panel.
		 */
		hideLoading(): void;
	}

	/**
	 * Describes the font style of code lens.
	 */
	export interface CodeLensFontStyle {
		/**
		 * The font weight of code lens.
		 */
		weight?: string;

		/**
		 * The font color of code lens.
		 */
		color?: string;
	}

	/**
	 * Represents an event describing the click of a text editor decoration.
	 */
	export interface TextEditorIconDecorationClickEvent {
		/**
		 * The [text editor](#TextEditor) which is double clicked.
		 */
		readonly textEditor: TextEditor;

		/**
		 * Key of the clicked text editor decoration.
		 */
		readonly key: string;

		/**
		 * Line number of the clicked text editor decoration.
		 */
		readonly lineNumber: number;
	}

	/**
	 * Represents an event describing the double click of a text editor.
	 */
	export interface TextEditorDoubleClickEvent {
		/**
		 * The [text editor](#TextEditor) which is double clicked.
		 */
		readonly textEditor: TextEditor;

		/**
		 * The selection of text editor caused by double click event.
		 */
		readonly selection?: Selection;
	}

	/**
	 * Options to configure the behavior of the message.
	 */
	export interface MessageOptions {
		/**
		 * Timeout in milliseconds after which the message will be closed when `modal` is false or undefined.
		 */
		closeAfterTimeout?: number;

		/**
		 * Indicates whether to display messages using a custom dialog or an electron dialog when `modal` is true.
		 */
		useCustom?: boolean;
	}

	/**
	 * An event describing the select to the set of [breakpoints](#Breakpoint) on UI.
	 */
	export interface CallStackThreadSelectEvent {
		/**
		 * the id of thread.
		 */
		readonly id: string;

		/**
		 * the name of thread.
		 */
		readonly name: string;
	}

	export namespace debug {
		/**
		 * An [event](#Event) that is emitted when debugger stop, user select the thread of threads.
		 */
		export const onDidFocusThread: Event<CallStackThreadSelectEvent>;
	}

	export interface DebugSession {
		/**
		 * Add a extra information for the breakpoints, to show at the BreakPoints List
		 */
		addExtraData(id: string, extra: string): void;
	}
}