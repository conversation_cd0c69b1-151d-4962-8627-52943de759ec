import * as vscode from 'vscode';

export interface Suggestion {
  text?: string;
  range?: vscode.Range;
  ongoing?: boolean;
  originalText?: string;
  originalRange?: vscode.Range;
  editor?: vscode.TextEditor;
  abortController?: AbortController;
  meta?: import('../../components/types/code_completion').CompletionMeta;
}

class SuggestionStore {
  private store: Map<string, Suggestion> = new Map();
  private _codeLensRefreshEmitter = new vscode.EventEmitter<void>();

  get codeLensRefreshEmitter() {
    return this._codeLensRefreshEmitter;
  }

  get(docUri: string): Suggestion | undefined {
    return this.store.get(docUri);
  }

  set(docUri: string, suggestion: Suggestion): void {
    this.store.set(docUri, suggestion);
    this.updateContext();
  }

  delete(docUri: string): void {
    this.store.delete(docUri);
    this.updateContext();
  }

  clear(): void {
    this.store.clear();
    this.updateContext();
  }

  fireCodeLensChange(): void {
    this._codeLensRefreshEmitter.fire();
  }

  private updateContext(): void {
    const hasSuggestion = this.store.size > 0;
    // console.log(`更新上下文: code-partner.hasSuggestion = ${hasSuggestion}, 建议数量: ${this.store.size}`);
    vscode.commands.executeCommand('setContext', 'code-partner.hasSuggestion', hasSuggestion);
  }
}

export const suggestionStore = new SuggestionStore();