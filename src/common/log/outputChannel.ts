import * as vscode from 'vscode';

/**
 * 全局输出通道管理器
 * 提供统一的日志输出接口，避免多个输出窗口的混乱
 */
export class OutputChannelManager {
    private static instance: OutputChannelManager;
    private outputChannel: vscode.OutputChannel | null = null;

    private constructor() {
        try {
            // 在测试环境中，vscode API可能不可用
            if (typeof vscode !== 'undefined' && vscode.window && vscode.window.createOutputChannel) {
                this.outputChannel = vscode.window.createOutputChannel('Code Partner');
            }
        } catch (error) {
            console.warn('Failed to create output channel:', error);
        }
    }

    public static getInstance(): OutputChannelManager {
        if (!OutputChannelManager.instance) {
            OutputChannelManager.instance = new OutputChannelManager();
        }
        return OutputChannelManager.instance;
    }

    /**
     * 获取输出通道实例
     */
    public getOutputChannel(): vscode.OutputChannel | null {
        return this.outputChannel;
    }

    /**
     * 显示输出通道
     */
    public show(): void {
        if (this.outputChannel) {
            this.outputChannel.show();
        }
    }

    /**
     * 清空输出内容
     */
    public clear(): void {
        if (this.outputChannel) {
            this.outputChannel.clear();
        }
    }

    /**
     * 追加一行内容
     */
    public appendLine(value: string): void {
        if (this.outputChannel) {
            this.outputChannel.appendLine(value);
        }
    }

    /**
     * 追加内容（不换行）
     */
    public append(value: string): void {
        if (this.outputChannel) {
            this.outputChannel.append(value);
        }
    }

    /**
     * 显示并输出内容
     */
    public showAndAppendLine(value: string): void {
        this.show();
        this.appendLine(value);
    }

    /**
     * 显示并清空后输出内容
     */
    public showAndClearAndAppendLine(value: string): void {
        this.show();
        this.clear();
        this.appendLine(value);
    }

    /**
     * 输出分隔线
     */
    public appendSeparator(title?: string): void {
        if (title) {
            this.appendLine(`=== ${title} ===`);
        } else {
            this.appendLine('='.repeat(50));
        }
    }

    /**
     * 输出时间戳
     */
    public appendWithTimestamp(value: string): void {
        const timestamp = new Date().toISOString();
        this.appendLine(`[${timestamp}] ${value}`);
    }

    /**
     * 释放资源
     */
    public dispose(): void {
        if (this.outputChannel) {
            this.outputChannel.dispose();
        }
    }
}

/**
 * 便捷的全局输出函数
 * 延迟初始化，避免在测试环境中过早创建实例
 */
export const globalOutput = (() => {
    try {
        return OutputChannelManager.getInstance();
    } catch (error) {
        // 在测试环境中，如果 vscode API 不可用，返回一个模拟对象
        return {
            getOutputChannel: () => ({
                appendLine: () => {},
                show: () => {},
                clear: () => {},
                dispose: () => {},
                append: () => {},
                replace: () => {},
                hide: () => {}
            }),
            show: () => {},
            clear: () => {},
            appendLine: () => {},
            append: () => {},
            showAndAppendLine: () => {},
            showAndClearAndAppendLine: () => {},
            appendSeparator: () => {},
            appendWithTimestamp: () => {},
            dispose: () => {}
        };
    }
})(); 