import { globalOutput } from './outputChannel';

export type LogLevel = 'DEBUG' | 'INFO' | 'WARN' | 'ERROR';

export interface LogOptions {
	toOutput?: boolean; // 是否输出到outputChannel
}

const DEFAULT_OUTPUT_LEVELS: LogLevel[] = ['ERROR', 'WARN', 'INFO']; // 默认ERROR/WARN/INFO都输出到outputChannel

export class Logger {
	private moduleName: string;
	private static outputLevels: Set<LogLevel> = new Set(DEFAULT_OUTPUT_LEVELS);

	constructor(moduleName: string) {
		this.moduleName = moduleName;
	}

	/**
	 * 配置哪些级别输出到outputChannel
	 */
	static setOutputLevels(levels: LogLevel[]) {
		Logger.outputLevels = new Set(levels);
	}

	private shouldOutputToChannel(level: LogLevel, toOutput?: boolean) {
		if (toOutput) { return true; }
		return Logger.outputLevels.has(level);
	}

	private format(level: LogLevel, msg: string, error?: Error | any, data?: any) {
		const timestamp = new Date().toISOString();
		let formatted = `[${timestamp}] [${level}] [${this.moduleName}] ${msg}`;
		if (data) {
			formatted += `\nData: ${JSON.stringify(data, null, 2)}`;
		}
		if (error) {
			if (error instanceof Error) {
				formatted += `\nError: ${error.message}\nStack: ${error.stack}`;
			} else {
				formatted += `\nError: ${JSON.stringify(error)}`;
			}
		}
		return formatted;
	}

	info(msg: string, dataOrErrorOrOpts?: any, opts?: LogOptions) {
		this.log('INFO', msg, dataOrErrorOrOpts, opts);
	}
	warn(msg: string, dataOrErrorOrOpts?: any, opts?: LogOptions) {
		this.log('WARN', msg, dataOrErrorOrOpts, opts);
	}
	error(msg: string, dataOrErrorOrOpts?: any, opts?: LogOptions) {
		this.log('ERROR', msg, dataOrErrorOrOpts, opts);
	}
	debug(msg: string, dataOrErrorOrOpts?: any, opts?: LogOptions) {
		this.log('DEBUG', msg, dataOrErrorOrOpts, opts);
	}

	private log(level: LogLevel, msg: string, dataOrErrorOrOpts?: any, opts?: LogOptions) {
		let error: Error | any = undefined;
		let data: any = undefined;
		let options: LogOptions | undefined = opts;

		if (dataOrErrorOrOpts instanceof Error) {
			error = dataOrErrorOrOpts;
		} else if (typeof dataOrErrorOrOpts === 'object' && dataOrErrorOrOpts !== null) {
			// 检查是否是LogOptions
			if ('toOutput' in dataOrErrorOrOpts) {
				options = dataOrErrorOrOpts;
			} else {
				data = dataOrErrorOrOpts;
			}
		}

		const formatted = this.format(level, msg, error, data);
		// 输出到console
		switch (level) {
			case 'DEBUG':
				if (process.env.NODE_ENV === 'development') { console.debug(formatted); }
				break;
			case 'INFO':
				console.info(formatted);
				break;
			case 'WARN':
				console.warn(formatted);
				break;
			case 'ERROR':
				console.error(formatted);
				break;
		}
		// 输出到outputChannel
		if (this.shouldOutputToChannel(level, options?.toOutput)) {
			globalOutput.appendLine(formatted);
		}
	}

	/**
	 * 显示日志面板
	 */
	static show(): void {
		globalOutput.show();
	}

	/**
	 * 清空日志
	 */
	static clear(): void {
		globalOutput.clear();
	}
}

// 创建全局日志记录器实例
export const globalLogger = new Logger('Global');