import { CompletionRequestPayload } from '../../../components/types/code_completion';
import { getConfig } from '../../config';
import { ICompletionClient } from "./inferface";
import { OpenAIClient } from "./openAI";
import { ModelParams } from './params';
import { PythonClient } from "./server";

export class CompletionClientFactory {
    static create(config: { type: 'openai'; apiKey: string } | { type: 'python'; endpoint: string }): ICompletionClient {
      if (config.type === 'openai') {
        return new OpenAIClient(config.apiKey);
      } else {
        return new PythonClient(config.endpoint);
      }
    }
} 


export class LoggingClientDecorator implements ICompletionClient {
    private wrapped: ICompletionClient;
  
    constructor(wrapped: ICompletionClient) {
      this.wrapped = wrapped;
    }
    async getCompletion(promptOrPayload: CompletionRequestPayload, abortSig?: AbortSignal, stream?: boolean, params?: Partial<ModelParams>): Promise<string | ReadableStream> {
      return this.wrapped.getCompletion(promptOrPayload, abortSig, stream);
    }
    getDefaultParams(): ModelParams{
      return this.wrapped.getDefaultParams();
    }
    updateDefaultParams(params: Partial<ModelParams>): void{
      return this.wrapped.updateDefaultParams(params);
    }
}


let sharedClient: ICompletionClient | null = null;
export function getSharedCompletionClient(): ICompletionClient {
  if (!sharedClient) {
    // 使用新的配置管理器
    const config = getConfig();
    if (config.usePythonService) {
      sharedClient = new PythonClient(config.pythonEndpoint);
    } else {
      sharedClient = new OpenAIClient(config.openAIApiKey);
    }
    // 可选：装饰
    sharedClient = new LoggingClientDecorator(sharedClient);
  }
  return sharedClient;
}
  