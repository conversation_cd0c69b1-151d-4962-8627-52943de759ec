import axios, { AxiosRequestHeaders } from 'axios';
import { StatisticsEventType, StatisticsService } from '../../../components/statistics';
import { CompletionRequestPayload } from '../../../components/types/code_completion';
import { ConfigurationManager } from '../../config/ConfigurationManager';
import { ICompletionClient } from './inferface';
import { ModelParams } from './params';

export class PythonClient implements ICompletionClient {
  private endpoint: string;
  private defaultParams: ModelParams;
  private statisticsService: StatisticsService;
  private configManager: ConfigurationManager;

  constructor(endpoint: string) {
    this.configManager = ConfigurationManager.getInstance();
    this.endpoint = endpoint || this.configManager.get('pythonEndpoint');
    this.statisticsService = StatisticsService.getInstance();

    // 从配置中读取默认参数
    this.defaultParams = {
      model: this.configManager.get('model'),
      max_tokens: this.configManager.get('maxTokens'),
      temperature: this.configManager.get('temperature'),
      stream: false
    };
  }

  getDefaultParams(): ModelParams {
    // 每次获取时都从最新配置中读取
    return {
      model: this.configManager.get('model'),
      max_tokens: this.configManager.get('maxTokens'),
      temperature: this.configManager.get('temperature'),
      stream: false
    };
  }

  updateDefaultParams(params: Partial<ModelParams>): void {
    this.defaultParams = { ...this.defaultParams, ...params };
  }

  async getCompletion(promptOrPayload: string | CompletionRequestPayload, abortSig?: AbortSignal, stream?: boolean, params?: Partial<ModelParams>): Promise<string | ReadableStream> {
  
      const payload = promptOrPayload as CompletionRequestPayload;
      const body = JSON.stringify(payload);

      console.log('===========Request prompt===============');
      console.log('Prompt (raw):', payload.prompt);
      console.log('----------------------------------------');
      try {
        const res =await axios({
          method: 'POST',
          url: this.endpoint,
          headers: {
            'Content-Type': 'application/json'
          } as AxiosRequestHeaders,
          data: body,
          signal: abortSig,
        });
        console.log('Request body (JSON):', body);
        const responseData: any = res.data;

        // 处理UnifiedResponseWrapper结构
        if (responseData.success && responseData.data) {
          const completionData = responseData.data;
          // 处理UnifiedCompletionResponse结构
          if (completionData.choices && completionData.choices.length > 0) {
            return completionData.choices[0].text || '';
          } else {
            console.error('No choices in completion response');
            return '';
          }
        } else {
          console.error('Python server error:', responseData.message, responseData.error_code);
          return '';
        }
      } catch (error) {
        console.error('Error in PythonClient:', error);
        return '';
      }
  }

  /**
   * 上报续写接受事件
   */
  async reportCompletionAccepted(suggestionText: string, originalText?: string, feedback?: string): Promise<void> {
    await this.statisticsService.reportCurrentCompletionFeedback(
      StatisticsEventType.COMPLETION_ACCEPTED,
      suggestionText,
      originalText,
      feedback
    );
  }

  /**
   * 上报续写拒绝事件
   */
  async reportCompletionRejected(suggestionText: string, originalText?: string, feedback?: string): Promise<void> {
    await this.statisticsService.reportCurrentCompletionFeedback(
      StatisticsEventType.COMPLETION_REJECTED,
      suggestionText,
      originalText,
      feedback
    );
  }
}