
export interface ModelParams {
    model?: string;
    max_tokens?: number;
    temperature?: number;
    top_p?: number;
    top_k?: number;
    repeat_penalty?: number;
    stop?: string[];
    stream?: boolean;
}

export interface ChatMessage {
    role: string;
    content: string;
}

export interface ChatParams extends ModelParams {
    messages: ChatMessage[];
}

// export class OllamaParams implements ModelParams {
//     model: string;
//     max_tokens?: number;
//     temperature?: number;
//     top_p?: number;
//     top_k?: number;
//     repeat_penalty?: number;
//     stop?: string[];
//     stream?: boolean;
//     private configManager: ConfigurationManager;

//     constructor(params: Partial<ModelParams> = {}) {
//         this.configManager = ConfigurationManager.getInstance();
//         this.model = params.model || 'llama3.2';
//         this.max_tokens = params.max_tokens || this.configManager.get('maxTokens');
//         this.temperature = params.temperature || this.configManager.get('temperature');
//         this.top_p = params.top_p;
//         this.top_k = params.top_k;
//         this.repeat_penalty = params.repeat_penalty;
//         this.stop = params.stop;
//         this.stream = params.stream || false;
//     }

//     toJSON(): any {
//         return {
//             model: this.model,
//             options: {
//                 ...(this.max_tokens && { max_tokens: this.max_tokens }),
//                 ...(this.temperature && { temperature: this.temperature }),
//                 ...(this.top_p && { top_p: this.top_p }),
//                 ...(this.top_k && { top_k: this.top_k }),
//                 ...(this.repeat_penalty && { repeat_penalty: this.repeat_penalty }),
//                 ...(this.stop && { stop: this.stop }),
//             },
//             stream: this.stream
//         };
//     }
// }

// export class OllamaChatParams extends OllamaParams implements ChatParams {
//     messages: ChatMessage[];

//     constructor(params: Partial<ChatParams> = {}) {
//         super(params);
//         this.messages = params.messages || [];
//     }

//     toJSON(): any {
//         return {
//             ...super.toJSON(),
//             messages: this.messages
//         };
//     }
// }