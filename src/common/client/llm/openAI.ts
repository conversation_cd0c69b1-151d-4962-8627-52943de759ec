import axios, { AxiosRequestHeaders } from 'axios';
import { StatisticsEventType, StatisticsService } from '../../../components/statistics';
import { CompletionRequestPayload } from '../../../components/types/code_completion';
import { ConfigurationManager } from '../../config/ConfigurationManager';
import { ICompletionClient } from './inferface';
import { ModelParams } from './params';

// https://github.com/ollama/ollama/blob/main/docs/openai.md
// const defaultEndpoint = "http://localhost:11434/api/generate";
const defaultEndpoint = "http://localhost:11434/v1/chat/completions";

interface OpenAIResponse {
  choices: Array<{
    message: {
      content: string;
    };
  }>;
}

export class OpenAIClient implements ICompletionClient {
  private apiKey: string;
  private endpoint = defaultEndpoint;
  private defaultParams: ModelParams;
  private configManager: ConfigurationManager;
  private statisticsService: StatisticsService;

  constructor(apiKey: string, endpoint?: string) {
    this.configManager = ConfigurationManager.getInstance();
    this.apiKey = apiKey;
    this.endpoint = endpoint || defaultEndpoint;
    this.statisticsService = StatisticsService.getInstance();

    // 从配置中读取默认参数
    this.defaultParams = {
      model: 'qwen2.5-coder:3b',
      max_tokens: this.configManager.get('maxTokens'),
      temperature: this.configManager.get('temperature'),
      stream: false
    };
  }

  getDefaultParams(): ModelParams {
    // 每次获取时都从最新配置中读取
    return {
      model: 'qwen2.5-coder:3b',
      max_tokens: this.configManager.get('maxTokens'),
      temperature: this.configManager.get('temperature'),
      stream: false
    };
  }

  updateDefaultParams(params: Partial<ModelParams>): void {
    this.defaultParams = { ...this.defaultParams, ...params };
  }

  async getCompletion(promptOrPayload: string | CompletionRequestPayload, abortSig?: AbortSignal, stream: boolean = false, params?: Partial<ModelParams>): Promise<string | ReadableStream> {
    // 新重载：支持 CompletionRequestPayload
    if (typeof promptOrPayload !== 'string') {
      const payload = promptOrPayload as CompletionRequestPayload;
      const mergedParams = { ...this.defaultParams, ...payload.modelConfig, stream };
      const prompt = payload.prompt || '';
      try {
        const response = await axios.post(this.endpoint, {
          ...mergedParams,
          messages: [{ role: 'user', content: prompt }]
        }, {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.apiKey}`
          },
          signal: abortSig
        });
        return response.data.choices[0]?.message?.content || "";
      } catch (error: any) {
        if (error?.name === 'CanceledError') {
          console.warn("请求已取消");
          return "";
        }
        console.error('Error in OpenAIClient:', error);
        if (axios.isAxiosError(error)) {
          console.error('Axios error details:', {
            status: error.response?.status,
            data: error.response?.data,
            message: error.message
          });
        }
        return "";
      }
    }
    // 兼容旧用法
    const prompt = promptOrPayload;
    try {
      const mergedParams = { ...this.defaultParams, ...params, stream };
      console.log('Sending request to OpenAI:', {
        endpoint: this.endpoint,
        params: mergedParams
      });

      if (stream) {
        const response = await axios({
          method: 'POST',
          url: this.endpoint,
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.apiKey}`
          } as AxiosRequestHeaders,
          data: {
            ...mergedParams,
            messages: [{ role: 'user', content: prompt }]
          },
          signal: abortSig,
          responseType: 'stream'
        });

        if (!response.data) {
          throw new Error('Stream request failed');
        }

        // 创建一个新的 TransformStream 来处理响应
        const { readable, writable } = new TransformStream();
        const writer = writable.getWriter();

        const processStream = async () => {
          const reader = response.data;

          try {
            reader.on('data', async (chunk: Buffer) => {
              const chunkStr = chunk.toString();
              const lines = chunkStr.split('\n').filter(line => line.trim() !== '');

              for (const line of lines) {
                if (line.startsWith('data: ')) {
                  try {
                    const data = JSON.parse(line.slice(6));
                    if (data.choices?.[0]?.delta?.content) {
                      await writer.write(new TextEncoder().encode(`data: ${JSON.stringify(data)}\n\n`));
                    }
                  } catch (e) {
                    console.error('Error parsing stream data:', e);
                  }
                }
              }
            });

            reader.on('end', async () => {
              await writer.close();
            });
          } catch (error) {
            console.error('Error processing stream:', error);
            await writer.close();
          }
        };

        processStream();
        return readable;
      } else {
        const response = await axios.post(this.endpoint, {
          ...mergedParams,
          messages: [{ role: 'user', content: prompt }]
        }, {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.apiKey}`
          } as AxiosRequestHeaders,
          signal: abortSig
        });

        console.log('Received response from OpenAI API Client:', {
          status: response.status,
          data: response.data
        });
        return response.data.choices[0]?.message?.content || "";
      }
    } catch (error: any) {
      if (error?.name === 'CanceledError') {
        console.warn("请求已取消");
        return "";
      }
      console.error('Error in OpenAIClient:', error);
      if (axios.isAxiosError(error)) {
        console.error('Axios error details:', {
          status: error.response?.status,
          data: error.response?.data,
          message: error.message
        });
      }
      return "";
    }
  }

  /**
   * 上报续写接受事件
   */
  async reportCompletionAccepted(suggestionText: string, originalText?: string, feedback?: string): Promise<void> {
    await this.statisticsService.reportCurrentCompletionFeedback(
      StatisticsEventType.COMPLETION_ACCEPTED,
      suggestionText,
      originalText,
      feedback
    );
  }

  /**
   * 上报续写拒绝事件
   */
  async reportCompletionRejected(suggestionText: string, originalText?: string, feedback?: string): Promise<void> {
    await this.statisticsService.reportCurrentCompletionFeedback(
      StatisticsEventType.COMPLETION_REJECTED,
      suggestionText,
      originalText,
      feedback
    );
  }
}