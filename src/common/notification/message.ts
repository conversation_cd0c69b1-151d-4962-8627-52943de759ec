
import * as vscode from 'vscode';
export const info = vscode.commands.registerCommand('code-partner.showMessage', () => {
    vscode.window.showInformationMessage('这是一个自定义消息组件！');
});


export function notifyInfo(message:string){
    vscode.window.showInformationMessage(message);
}

export function notifyWarn(message:string){
    vscode.window.showWarningMessage(message);
}

export function notifyError(message:string){
    vscode.window.showErrorMessage(message);
}