import * as vscode from 'vscode';

// 定义消息类型的枚举
enum MessageType {
    Information = 'information',
    Warning = 'warning',
    Error = 'error',
    QuickPick = 'quickPick',
    OpenDialog = 'openDialog'
}

// 定义消息选项的接口
interface MessageOptions {
    message: string;
    actions?: string[];
    placeHolder?: string;
    filters?: { [key: string]: string[] };
    canSelectMany?: boolean;
}

// 定义消息弹出框工厂类
class MessageDialogFactory {
    static createMessageDialog(type: MessageType, options: MessageOptions): Thenable<string | string[] | vscode.Uri[] | undefined> {
        switch (type) {
            case MessageType.Information:
                return vscode.window.showInformationMessage(options.message, ...(options.actions || []));
            case MessageType.Warning:
                return vscode.window.showWarningMessage(options.message, ...(options.actions || []));
            case MessageType.Error:
                return vscode.window.showErrorMessage(options.message, ...(options.actions || []));
            case MessageType.QuickPick:
                return vscode.window.showQuickPick(options.actions || [], { placeHolder: options.placeHolder });
            case MessageType.OpenDialog:
                const openOptions: vscode.OpenDialogOptions = {
                    canSelectMany: options.canSelectMany,
                    openLabel: '选择文件',
                    filters: options.filters
                };
                return vscode.window.showOpenDialog(openOptions);
            default:
                throw new Error('不支持的消息类型');
        }
    }
}