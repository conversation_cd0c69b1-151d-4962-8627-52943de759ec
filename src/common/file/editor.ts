import * as vscode from 'vscode';
import { globalLogger } from '../log/logger';

function getWorkspaceFolders(): string[] {
    const workspaceFolders = vscode.workspace.workspaceFolders;
    const folders: string[] = [];
    if (workspaceFolders) {
        workspaceFolders.forEach((folder) => {
            folders.push(folder.uri.fsPath);
        });
    } else {
        globalLogger.info('当前没有打开的工作区文件夹');
    }
    return folders;
}

async function findFiles(exts: string[]): Promise<vscode.Uri[]> {
    const pattern = `**/*.{${exts.join(',')}}`;
    const uris = await vscode.workspace.findFiles(pattern);
    return uris;
}

async function readFileContent(fileURI: vscode.Uri): Promise<string> {
    let text = "";
    try {
        const content = await vscode.workspace.fs.readFile(fileURI);
        text = Buffer.from(content).toString('utf-8');
    } catch (error) {
        throw error;
    }
    return text;
}

function getFileName(filePath: string): string {
    const parts = filePath.split(/[\/\\]/);
    return parts[parts.length - 1];
}

function readCurrentFileContent(): string {
    const editor = vscode.window.activeTextEditor;
    if (!editor) {
        throw new Error('没有打开的文件');
    }
    const document = editor.document;
    const text = document.getText();
    return text;
}

export {
    findFiles, getFileName, getWorkspaceFolders, readCurrentFileContent, readFileContent
};

