import * as vscode from 'vscode';

export class StatusManager {
  private static contextKeys = new Map<string, boolean>();

  /**
   * 设置某个上下文 key 的值，例如 'npAssistant.active'
   */
  static async set(key: string, value: boolean) { 
    this.contextKeys.set(key, value);
    await vscode.commands.executeCommand('setContext', key, value);
  }

  /**
   * 获取某个上下文 key 的当前状态（缓存值）
   */
  static get(key: string): boolean | undefined {
    return this.contextKeys.get(key);
  }

  /**
   * 设置 npAssistant.active 开关
   */
  static async setActive(value: boolean) {
    await this.set('npAssistant.active', value);
  }

  /**
   * 是否当前处于 active 状态
   */
  static isActive(): boolean {
    return this.get('npAssistant.active') === true;
  }
  /**
   * 设置任意状态的开关
   */
  static async setStatus(key: string, value: boolean) {
    await this.set(key, value);
  }
}
