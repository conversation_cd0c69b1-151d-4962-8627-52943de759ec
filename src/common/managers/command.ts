import * as vscode from 'vscode';

export class CommandManager {
  private static commands: Map<string, vscode.Disposable> = new Map();

  /**
   * 注册命令，返回 Disposable 以便外部可手动清理
   * @param command 命令 ID
   * @param callback 命令执行函数
   * @param onDispose 可选 dispose 钩子
   * @returns 返回 Disposable，调用可注销命令
   */
  static register(
    command: string,
    callback: (...args: any[]) => void,
    onDispose?: () => void
  ): vscode.Disposable {
    // 如果已有命令，先清理
    if (this.commands.has(command)) {
      this.commands.get(command)?.dispose();
    }

    const rawDisposable = vscode.commands.registerCommand(command, callback);

    // 封装 Disposable：清理时执行钩子 + 移除自身
    const wrappedDisposable: vscode.Disposable = {
      dispose: () => {
        try {
          onDispose?.();
        } catch (err) {
          console.error(`Dispose hook for "${command}" failed:`, err);
        }
        rawDisposable.dispose();
        this.commands.delete(command);
      }
    };

    this.commands.set(command, wrappedDisposable);
    return wrappedDisposable;
  }

  static disposeAll() {
    for (const [cmd, disposable] of this.commands.entries()) {
      try {
        disposable.dispose();
      } catch (err) {
        console.error(`Error disposing command "${cmd}":`, err);
      }
    }
    this.commands.clear();
  }
}
