import * as vscode from 'vscode';

/**
 * 插件配置接口定义
 */
export interface CodePartnerConfiguration {
  // LLM服务配置
  usePythonService: boolean;
  pythonEndpoint: string;
  openAIApiKey: string;
  model: string; // 新增
  
  // 统计服务配置
  statisticsEndpoint: string;
  enableStatistics: boolean;
  
  // 代码补全配置
  maxTokens: number;
  temperature: number;
  enableInlineCompletion: boolean;
  enableDiffHighlightCompletion: boolean;
  
  // 代码索引配置
  enableIndexing: boolean;
  autoIndexing: boolean;
  languageExtensions: string[];
  
  // 上下文融合配置
  graphWeight: number;
  bm25Weight: number;
  embeddingWeight: number;
  
  // 日志配置
  logLevel: 'DEBUG' | 'INFO' | 'WARN' | 'ERROR';
  enableDebugLog: boolean;
}

/**
 * 默认配置值
 */
const DEFAULT_CONFIG: CodePartnerConfiguration = {
  usePythonService: true,
  pythonEndpoint: 'http://127.0.0.1:5555/api/v1/completion/code_completion',
  openAIApiKey: '',
  model: 'qwen2.5-coder:3b', // 新增
  statisticsEndpoint: 'http://127.0.0.1:5555/api/v1/statistics/events',
  enableStatistics: true,
  maxTokens: 256,
  temperature: 0.2,
  enableInlineCompletion: true,
  enableDiffHighlightCompletion: true,
  enableIndexing: true,
  autoIndexing: true,
  languageExtensions: ['*'],
  graphWeight: 0.4,
  bm25Weight: 0.3,
  embeddingWeight: 0.3,
  logLevel: 'INFO',
  enableDebugLog: false
};

/**
 * 配置管理器
 * 提供类型安全的配置读取和更新功能
 */
export class ConfigurationManager {
  private static instance: ConfigurationManager;
  private config: vscode.WorkspaceConfiguration;
  private _onDidChangeConfiguration = new vscode.EventEmitter<void>();

  private constructor() {
    this.config = vscode.workspace.getConfiguration('code-partner');
    this.setupConfigurationListener();
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): ConfigurationManager {
    if (!ConfigurationManager.instance) {
      ConfigurationManager.instance = new ConfigurationManager();
    }
    return ConfigurationManager.instance;
  }

  /**
   * 配置变更事件
   */
  get onDidChangeConfiguration() {
    return this._onDidChangeConfiguration.event;
  }

  /**
   * 设置配置变更监听器
   */
  private setupConfigurationListener(): void {
    vscode.workspace.onDidChangeConfiguration((event) => {
      if (event.affectsConfiguration('code-partner')) {
        this.config = vscode.workspace.getConfiguration('code-partner');
        this._onDidChangeConfiguration.fire();
      }
    });
  }

  /**
   * 获取完整配置
   */
  public getConfiguration(): CodePartnerConfiguration {
    return {
      usePythonService: this.config.get<boolean>('usePythonService') ?? DEFAULT_CONFIG.usePythonService,
      pythonEndpoint: this.config.get<string>('pythonEndpoint') ?? DEFAULT_CONFIG.pythonEndpoint,
      openAIApiKey: this.config.get<string>('openAIApiKey') ?? DEFAULT_CONFIG.openAIApiKey,
      model: this.config.get<string>('model') ?? DEFAULT_CONFIG.model, // 新增
      statisticsEndpoint: this.config.get<string>('statisticsEndpoint') ?? DEFAULT_CONFIG.statisticsEndpoint,
      enableStatistics: this.config.get<boolean>('enableStatistics') ?? DEFAULT_CONFIG.enableStatistics,
      maxTokens: this.config.get<number>('maxTokens') ?? DEFAULT_CONFIG.maxTokens,
      temperature: this.config.get<number>('temperature') ?? DEFAULT_CONFIG.temperature,
      enableInlineCompletion: this.config.get<boolean>('enableInlineCompletion') ?? DEFAULT_CONFIG.enableInlineCompletion,
      enableDiffHighlightCompletion: this.config.get<boolean>('enableDiffHighlightCompletion') ?? DEFAULT_CONFIG.enableDiffHighlightCompletion,
      enableIndexing: this.config.get<boolean>('enableIndexing') ?? DEFAULT_CONFIG.enableIndexing,
      autoIndexing: this.config.get<boolean>('autoIndexing') ?? DEFAULT_CONFIG.autoIndexing,
      languageExtensions: this.config.get<string[]>('languageExtensions') ?? DEFAULT_CONFIG.languageExtensions,
      graphWeight: this.config.get<number>('graphWeight') ?? DEFAULT_CONFIG.graphWeight,
      bm25Weight: this.config.get<number>('bm25Weight') ?? DEFAULT_CONFIG.bm25Weight,
      embeddingWeight: this.config.get<number>('embeddingWeight') ?? DEFAULT_CONFIG.embeddingWeight,
      logLevel: this.config.get<'DEBUG' | 'INFO' | 'WARN' | 'ERROR'>('logLevel') ?? DEFAULT_CONFIG.logLevel,
      enableDebugLog: this.config.get<boolean>('enableDebugLog') ?? DEFAULT_CONFIG.enableDebugLog
    };
  }

  /**
   * 获取单个配置项
   */
  public get<K extends keyof CodePartnerConfiguration>(key: K): CodePartnerConfiguration[K] {
    const config = this.getConfiguration();
    return config[key];
  }

  /**
   * 更新配置项
   */
  public async update<K extends keyof CodePartnerConfiguration>(
    key: K, 
    value: CodePartnerConfiguration[K]
  ): Promise<void> {
    await this.config.update(key, value, vscode.ConfigurationTarget.Global);
  }

  /**
   * 更新工作区配置项
   */
  public async updateWorkspace<K extends keyof CodePartnerConfiguration>(
    key: K, 
    value: CodePartnerConfiguration[K]
  ): Promise<void> {
    await this.config.update(key, value, vscode.ConfigurationTarget.Workspace);
  }

  /**
   * 重置配置到默认值
   */
  public async resetToDefaults(): Promise<void> {
    for (const [key, value] of Object.entries(DEFAULT_CONFIG)) {
      await this.update(key as keyof CodePartnerConfiguration, value);
    }
  }

  /**
   * 验证配置有效性
   */
  public validateConfiguration(): { isValid: boolean; errors: string[] } {
    const config = this.getConfiguration();
    const errors: string[] = [];

    // 验证权重配置
    const totalWeight = config.graphWeight + config.bm25Weight + config.embeddingWeight;
    if (Math.abs(totalWeight - 1.0) > 0.01) {
      errors.push(`权重配置总和应为1.0，当前为${totalWeight.toFixed(2)}`);
    }

    // 验证端点配置
    if (config.usePythonService && !config.pythonEndpoint) {
      errors.push('启用Python服务时必须配置pythonEndpoint');
    }

    if (!config.usePythonService && !config.openAIApiKey) {
      errors.push('使用OpenAI服务时必须配置openAIApiKey');
    }

    // 验证数值范围
    if (config.temperature < 0 || config.temperature > 2) {
      errors.push('temperature值应在0-2之间');
    }

    if (config.maxTokens < 1 || config.maxTokens > 4000) {
      errors.push('maxTokens值应在1-4000之间');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * 获取配置摘要信息（用于调试）
   */
  public getConfigurationSummary(): string {
    const config = this.getConfiguration();
    return `
Code Partner 配置摘要:
├── LLM服务: ${config.usePythonService ? 'Python' : 'OpenAI'}
├── 统计服务: ${config.enableStatistics ? '启用' : '禁用'}
├── 行内补全: ${config.enableInlineCompletion ? '启用' : '禁用'}
├── 差异高亮: ${config.enableDiffHighlightCompletion ? '启用' : '禁用'}
├── 代码索引: ${config.enableIndexing ? '启用' : '禁用'}
├── 日志级别: ${config.logLevel}
└── 权重配置: 图谱(${config.graphWeight}) + BM25(${config.bm25Weight}) + 向量(${config.embeddingWeight})
    `.trim();
  }
}

/**
 * 便捷的配置访问函数
 */
export const getConfig = () => ConfigurationManager.getInstance().getConfiguration();
export const getConfigValue = <K extends keyof CodePartnerConfiguration>(key: K) => 
  ConfigurationManager.getInstance().get(key);
export const updateConfig = <K extends keyof CodePartnerConfiguration>(key: K, value: CodePartnerConfiguration[K]) =>
  ConfigurationManager.getInstance().update(key, value); 