import type { JestConfigWithTsJest } from "ts-jest";

const config: JestConfigWithTsJest = {
  preset: "ts-jest",
  testEnvironment: "node",
  transform: {
    "^.+\\.ts$": ["ts-jest", {
      tsconfig: "tsconfig.test.json"
    }],
  },
  moduleFileExtensions: ["ts", "js", "json", "wasm"],
  moduleNameMapper: {
    '^src/(.*)$': '<rootDir>/src/$1',
    '^vscode$': '<rootDir>/__mocks__/vscode.js',
    // 移除 tree-sitter mock，使用真实的 WASM 加载
    // '^web-tree-sitter$': '<rootDir>/__mocks__/tree-sitter-mock.js',
  },
  moduleDirectories: ['node_modules', 'src', 'dist'],
  testMatch: [
    '**/tests/**/*.test.ts',
    '**/tests/**/*.test.js'
  ],
  collectCoverageFrom: [
    'src/**/*.ts',
    '!src/**/*.d.ts',
    '!src/typings/**'
  ],
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'html'],
  // 增强测试隔离
  clearMocks: true,
  resetMocks: false,
  restoreMocks: true
};

export default config; 