/* 跨板转板接收的消息依赖携带的跟踪使能标记来标识是否需要上报 */
inline IpuUsrTrcBtoBRxSetTrcDir()
{
    bitcmp cc1 = rsPaeExt.UserTrcUpFlag, ONE_BIT_MASK;
    if (cc1.nz) {
        move rsUpfDesc.UserTrcDir = NPUSERTRC_TRCTYPE_UP_OUT_FROM_OTHERNP;
        UpfExceptStatNoDiscard(CAUSE_UPF_NORMAL_USRTRC_UP_OUT_BTOBRX);
    } else {
        move rsUpfDesc.UserTrcDir = NPUSERTRC_TRCTYPE_DN_OUT_FROM_OTHERNP;
        UpfExceptStatNoDiscard(CAUSE_UPF_NORMAL_USRTRC_DN_OUT_BTOBRX);
    }
}

void eUserTrcNpOutGetDstTb()
{
    (ETM_ETH_PAE_S) q0b qsEtmEthPaeHdr;
    (ETM_ETHONETAG_PAE_S) q0b qsEtmEth1TagPaeHdr;
    (ETM_ETHTWOTAG_PAE_S) q0b qsEtmEth2TagPaeHdr;


    cmp cc0 = qsEtmEthPaeHdr.NoTag.Type, ETH_TYPE_PAE;
    cmp cc1 = qsEtmEth1TagPaeHdr.OneTag.TAG1.TPID, ETH_TPID_TAGGED_STD;
    cmp cc2 = qsEtmEth1TagPaeHdr.OneTag.Type, ETH_TYPE_PAE;
    if (cc0.eq)
    {
        move rsFuncRtnData.Data0 = PAE_LOW_16BIT(qsEtmEthPaeHdr.PaeHdr.DstTbL32);move rsFuncRtnData.Data0 = PAE_LOW_16BIT(qsEthPaeHdr.PaeHdr.DstTbL32);
        move rsFuncRtnData.Data0 = PAE_LOW_16BIT(qsEthPaeHdr.PaeHdr.DstTbL32);
        move rsFuncRtnData.Data1 = TRUE;
        move rsFuncRtnData.Data0 = PAE_LOW_16BIT(qsEth1TagPaeHdr.PaeHdr.DstTbL32);
        return;
    }


    if (cc1.eq)
    {
        if (cc2.eq)
        {
            move rsFuncRtnData.Data0 = PAE_LOW_16BIT(qsEtmEth1TagPaeHdr.PaeHdr.DstTbL32);
            move rsFuncRtnData.Data1 = TRUE;
            return;
        }

        cmp cc0 = qsEtmEth2TagPaeHdr.TwoTag.TAG2.TPID, ETH_TPID_TAGGED_STD;
        cmp cc1 = qsEtmEth2TagPaeHdr.TwoTag.Type, ETH_TYPE_PAE;
        if (cc0.eq)
        {
            if (cc1.eq)
            {
                move rsFuncRtnData.Data0 = PAE_LOW_16BIT(qsEtmEth2TagPaeHdr.PaeHdr.DstTbL32);
                move rsFuncRtnData.Data1 = TRUE;
                return;
            }
        }
    }

    move rsFuncRtnData.Data0 = 0;
    move rsFuncRtnData.Data1 = FALSE;
    return;
}

void eUserTrcNpInGetDstTb()
{
    (ETH_PAE_S) q0b qsEthPaeHdr;
    (ETHONETAG_PAE_S) q0b qsEth1TagPaeHdr;
    (ETHTWOTAG_PAE_S) q0b qsEth2TagPaeHdr;


    cmp cc0 = qsEthPaeHdr.NoTag.Type, ETH_TYPE_PAE;
    cmp cc1 = qsEth1TagPaeHdr.OneTag.TAG1.TPID, ETH_TPID_TAGGED_STD;
    cmp cc2 = qsEth1TagPaeHdr.OneTag.Type, ETH_TYPE_PAE;
    if (cc0.eq)
    {
        move rsFuncRtnData.Data0 = PAE_LOW_16BIT(qsEthPaeHdr.PaeHdr.DstTbL32);
        move rsFuncRtnData.Data1 = TRUE;
        return;
    }

    if (cc1.eq)
    {
        if (cc2.eq)
        {
            move rsFuncRtnData.Data0 = PAE_LOW_16BIT(qsEth1TagPaeHdr.PaeHdr.DstTbL32);
            move rsFuncRtnData.Data1 = TRUE;
            return;
        }

        cmp cc0 = qsEth2TagPaeHdr.TwoTag.TAG2.TPID, ETH_TPID_TAGGED_STD;
        cmp cc1 = qsEth2TagPaeHdr.TwoTag.Type, ETH_TYPE_PAE;
        if (cc0.eq)
        {
            if (cc1.eq)
            {
                move rsFuncRtnData.Data0 = PAE_LOW_16BIT(qsEth2TagPaeHdr.PaeHdr.DstTbL32);
                move rsFuncRtnData.Data1 = TRUE;
                return;
            }
        }
    }

    move rsFuncRtnData.Data0 = 0;
    move rsFuncRtnData.Data1 = FALSE;
    return;
}