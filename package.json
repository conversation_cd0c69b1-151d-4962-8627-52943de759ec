{"name": "code-partner", "displayName": "code_partner", "publisher": "test_publisher", "description": "AI代码助手，提供智能代码补全、上下文融合检索和数据提取功能", "version": "0.0.2", "engines": {"vscode": "^1.96.0"}, "categories": ["Other"], "activationEvents": ["*"], "main": "./dist/extension.js", "contributes": {"languages": [{"id": "np", "aliases": ["NP", "np"], "extensions": [".asm"]}, {"id": "c", "aliases": ["C"], "extensions": [".c", ".h"]}], "commands": [{"command": "code-partner.tools.text.escapeString", "title": "工具: 转义字符串"}, {"command": "code-partner.completionFeedback", "title": "补全反馈"}, {"command": "code-partner.helloWorld", "title": "Hello World"}, {"command": "code-partner.test", "title": "AAA代码助手测试命令"}, {"command": "code-partner.complete", "title": "补全"}, {"command": "code-partner.triggerInlineCompletion", "title": "行内补全"}, {"command": "code-partner.triggerDiffHighlightCompletion", "title": "差异高亮补全"}, {"command": "code-partner.reinitializeWorkspaceIndex", "title": "Code Partner: 重新初始化工作区索引"}, {"command": "code-partner.loadRepoIndex", "title": "Code Partner: 加载代码仓库索引"}, {"command": "code-partner.showRepoIndex", "title": "Code Partner: 查看索引内容"}, {"command": "code-partner.testLog", "title": "Code Partner: 测试日志输出"}, {"command": "code-partner.queryRelatedFunctions", "title": "Code Partner: 查询相关函数"}, {"command": "code-partner.updateRepoIndex", "title": "Code Partner: 更新代码仓库索引"}, {"command": "code-partner.cleanIndexData", "title": "Code Partner: 清理索引数据"}, {"command": "code-partner.debugRepoIndexer", "title": "Code Partner: 调试RepoIndexer状态"}, {"command": "code-partner.resetIndex", "title": "Code Partner: 重置索引"}, {"command": "code-partner.testUserVscode", "title": "Code Partner: 测试获取当前用户信息"}, {"command": "code-partner.getAllExtensions", "title": "Code Partner: 获取所有插件信息"}, {"command": "code-partner.getSystemStatus", "title": "Code Partner: 获取系统状态"}, {"command": "code-partner.getExtensionDetails", "title": "Code Partner: 查看插件详情"}, {"command": "code-partner.searchCommands", "title": "Code Partner: 搜索命令"}, {"command": "code-partner.showConfiguration", "title": "Code Partner: 显示配置"}, {"command": "code-partner.validateConfiguration", "title": "Code Partner: 验证配置"}, {"command": "code-partner.resetConfiguration", "title": "Code Partner: 重置配置"}, {"command": "code-partner.configureLLMService", "title": "Code Partner: 配置LLM服务"}, {"command": "code-partner.configureCompletionParams", "title": "Code Partner: 配置补全参数"}, {"command": "code-partner.configureFusionWeights", "title": "Code Partner: 配置融合权重"}, {"command": "code-partner.openSettings", "title": "Code Partner: 打开设置"}, {"command": "code-partner.showUserCustomContentPanel", "title": "Code Partner: 用户自定义内容管理"}, {"command": "code-partner.quickAddSnippet", "title": "Code Partner: 快速添加代码片段"}, {"command": "code-partner.quickAddRule", "title": "Code Partner: 快速添加规则"}, {"command": "code-partner.exportUserCustomConfig", "title": "Code Partner: 导出用户自定义配置"}, {"command": "code-partner.importUserCustomConfig", "title": "Code Partner: 导入用户自定义配置"}], "keybindings": [{"command": "code-partner.complete", "key": "alt+p", "when": "editorTextFocus"}, {"command": "code-partner.triggerInlineCompletion", "key": "alt+o", "when": "editorTextFocus"}, {"command": "code-partner.triggerDiffHighlightCompletion", "key": "ctrl+shift+;", "mac": "cmd+;", "when": "editorTextFocus"}, {"command": "code-partner.reinitializeWorkspaceIndex", "key": "ctrl+shift+i", "mac": "cmd+shift+i", "when": "editorTextFocus"}, {"command": "code-partner.queryRelatedFunctions", "key": "ctrl+shift+f", "mac": "cmd+shift+f", "when": "editorTextFocus"}, {"command": "code-partner.updateRepoIndex", "key": "ctrl+shift+u", "mac": "cmd+shift+u", "when": "editorTextFocus"}, {"command": "code-partner.rejectSuggestion", "key": "escape", "when": "editorTextFocus && code-partner.hasSuggestion"}], "configuration": {"title": "Code Partner", "properties": {"code-partner.usePythonService": {"type": "boolean", "default": true, "description": "是否使用Python服务而不是OpenAI服务"}, "code-partner.pythonEndpoint": {"type": "string", "default": "http://127.0.0.1:5555/api/v1/completion/code_completion", "description": "Python服务的端点地址"}, "code-partner.openAIApiKey": {"type": "string", "default": "", "description": "OpenAI API密钥"}, "code-partner.statisticsEndpoint": {"type": "string", "default": "http://127.0.0.1:5555/api/v1/statistics/events", "description": "统计服务的端点地址"}, "code-partner.enableStatistics": {"type": "boolean", "default": true, "description": "是否启用统计功能"}, "code-partner.maxTokens": {"type": "number", "default": 256, "minimum": 1, "maximum": 4000, "description": "代码补全的最大token数量"}, "code-partner.temperature": {"type": "number", "default": 0.2, "minimum": 0, "maximum": 2, "description": "代码补全的创造性程度（0-2）"}, "code-partner.enableInlineCompletion": {"type": "boolean", "default": true, "description": "是否启用行内代码补全"}, "code-partner.enableDiffHighlightCompletion": {"type": "boolean", "default": true, "description": "是否启用差异高亮补全"}, "code-partner.enableIndexing": {"type": "boolean", "default": true, "description": "是否启用代码索引功能"}, "code-partner.autoIndexing": {"type": "boolean", "default": true, "description": "是否启用自动索引和自动增量索引（仅在启用索引功能时有效）"}, "code-partner.languageExtensions": {"type": "array", "items": {"type": "string"}, "default": ["*"], "description": "支持的语言文件扩展名"}, "code-partner.graphWeight": {"type": "number", "default": 0.4, "minimum": 0, "maximum": 1, "description": "代码图谱检索的权重"}, "code-partner.bm25Weight": {"type": "number", "default": 0.3, "minimum": 0, "maximum": 1, "description": "BM25关键词检索的权重"}, "code-partner.embeddingWeight": {"type": "number", "default": 0.3, "minimum": 0, "maximum": 1, "description": "向量检索的权重"}, "code-partner.logLevel": {"type": "string", "enum": ["DEBUG", "INFO", "WARN", "ERROR"], "default": "INFO", "description": "日志级别"}, "code-partner.enableDebugLog": {"type": "boolean", "default": false, "description": "是否启用调试日志"}, "code-partner.model": {"type": "string", "default": "qwen2.5-coder:3b", "description": "LLM模型名称"}}}}, "scripts": {"vscode:prepublish": "npm run package", "compile": "npm run check-types && npm run lint && node scripts/build.js && node copy-modules.js", "build": "node scripts/build.js", "build:main": "node scripts/build-main.js", "build:workers": "node scripts/build-workers.js", "build:separated": "node scripts/build.js", "validate:build": "node scripts/validate-build.js", "watch": "npm-run-all -p watch:*", "watch:esbuild": "node scripts/build.js --watch", "watch:workers": "node scripts/build-workers.js --watch", "watch:tsc": "tsc --noEmit --watch --project tsconfig.json", "watch:copy": "node copy-modules.js --watch", "package": "npm run check-types && npm run lint && node scripts/build.js --production", "compile-tests": "tsc -p . --outDir out", "watch-tests": "tsc -p . -w --outDir out", "pretest": "npm run compile-tests && npm run compile && npm run lint", "check-types": "tsc --noEmit", "lint": "eslint src", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:data-extraction": "jest src/components/data_extraction/__tests__/", "test:code-graph": "jest src/components/code_graph/__tests__/", "test:suggestion": "jest src/components/suggestion/__tests__/", "test:common": "jest src/common/__tests__/", "vsce": "rm -rf dist && npm run compile && vsce package --target=universal --allow-star-activation --allow-missing-repository --allow-unused-files-pattern --no-dependencies"}, "devDependencies": {"@electron/rebuild": "^3.2.9", "@types/jest": "^30.0.0", "@types/node": "20.x", "@types/node-fetch": "^2.6.12", "@types/vscode": "^1.101.0", "@typescript-eslint/eslint-plugin": "^8.17.0", "@typescript-eslint/parser": "^8.17.0", "@vscode/test-cli": "^0.0.10", "@vscode/test-electron": "^2.5.2", "chokidar": "^4.0.3", "esbuild": "^0.25.8", "eslint": "^9.21.0", "jest": "^30.0.3", "jest-transform-stub": "^2.0.0", "node-fetch": "^2.7.0", "npm-run-all": "^4.1.5", "tree-sitter-c": "^0.24.1", "tree-sitter-cli": "^0.19.4", "ts-jest": "^29.4.0", "typescript": "^5.7.2"}, "dependencies": {"@dagrejs/graphlib": "^2.2.4", "@types/ws": "^8.18.1", "axios": "^1.8.4", "fs-extra": "^11.3.0", "minipass": "^7.1.2", "ts-node": "^10.9.2", "web-tree-sitter": "^0.25.8", "ws": "^8.18.1"}, "files": ["dist/**"]}