{
	"compilerOptions": {
		"target": "ES2020",
		"module": "commonjs",
		"lib": [
			"ES2020"
		],
		"outDir": "dist",
		"rootDir": "src",
		"sourceMap": true,
		"declaration": true,
		"declarationMap": true,
		"strict": true,   /* enable all strict type-checking options */
		"esModuleInterop": true, /* Enable interop between CommonJS and ES Modules */
		"skipLibCheck": true, /* Skip type checking of declaration files */
		"forceConsistentCasingInFileNames": true
	},
	"include": [
		"src/**/*"
	],
	"exclude": [
		"node_modules",
		"dist",
		"**/*.test.ts",
		"**/__tests__/**"
	]
}
